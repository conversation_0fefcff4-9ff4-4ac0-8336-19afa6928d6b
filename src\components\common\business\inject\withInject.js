const React = require('react');
const Inject = require('./Inject');

/**
 * 用于根组件的props注入，仅限于不可变数据的注入 如表码和用户信息
 * @param Component
 * @param loader
 * @param loadingComponent
 * @param errorComponent
 */
function withInject(Component, loader, loadingComponent, errorComponent) {
    const loaders = Array.isArray(loader) ? loader : [loader];
    const C = (props) => {
        return (
            <Inject loaders={loaders} loadingComponent={loadingComponent} errorComponent={errorComponent}>
                <Component {...props} />
            </Inject>
        );
    };

    C.displayName = `withInject(${Component.displayName || Component.name})`;
    C.WrappedComponent = Component;

    return C;
}

module.exports = withInject;
