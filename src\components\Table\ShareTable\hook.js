import React, { Fragment, useCallback } from 'react';
import DetectPagePagination from '@/components/business/Table/ui/Pagination/DetectPagePagination';
import Pagination from '@/components/business/Table/ui/Pagination/Pagination';

const useHook = (props) => {
    const {
        // 数据
        footer = () => '', // 表格尾部扩展展示函数
        rowSelection = null, // 前置可选栏参数
        listState = null,
        dataSource = [],
    } = props;

    const rowKey = listState?.uniqKey || ((r) => listState?.list.findIndex((i) => i === r));
    const $dataSource = listState?.list || dataSource || [];
    const selectOnChange = useCallback(
        (selectedRowKeys, selectedRows) => {
            const { uniqKey, selected } = listState;
            selected.clear();
            selectedRows.forEach((item) => {
                const idx = uniqKey ? -1 : listState?.list.findIndex((i) => i === item);
                selected.add(idx, item);
            });
        },
        [listState]
    );
    const rowSelectionCustom = rowSelection
        ? {
              ...props.rowSelection,
              selectedRowKeys: listState?.selected.value,
              onChange: selectOnChange,
          }
        : null;

    const footerCustom = (currentPageData) => {
        const paginationProps = {
            ...listState?.page,
            current: listState?.page?.currentPage,
            pageSize: listState?.page?.linesPerPage,
            total: listState?.page?.totalNum,
            onChange: (current, pageSize) => {
                const { currentPage, linesPerPage } = listState.page;

                if (currentPage !== current) {
                    listState?.jump(current);
                }
                if (linesPerPage !== pageSize) {
                    listState?.lines(pageSize);
                }
            },
        };
        const PaginationComponent = paginationProps.detectPage ? DetectPagePagination : Pagination;

        return (
            <Fragment>
                {footer && footer(currentPageData)}
                <PaginationComponent {...paginationProps} style={{ float: 'right' }} />
            </Fragment>
        );
    };

    return {
        ...props,
        rowKey,
        dataSource: $dataSource,
        rowSelection: rowSelectionCustom,
        pagination: false,
        footer: footerCustom,
    };
};

export default useHook;
