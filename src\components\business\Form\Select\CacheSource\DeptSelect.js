import React, { Component } from 'react';
import * as Store from '@/components/common/store/Store';
import CommonDeptSelect from './CommonDeptSelect';
import TreeDeptSelect from './TreeDeptSelect';
import AreaDeptSelect from './AreaDeptSelect';

class DeptSelect extends Component {
    constructor(props) {
        super(props);
        const local = Store.get('local') || {};
        let Com;

        switch (local.local) {
            case 'dl':
                Com = TreeDeptSelect;
                break;
            case 'xm':
            case 'ly':
                Com = AreaDeptSelect;
                break;
            default:
                Com = CommonDeptSelect;
                break;
        }

        this.state = { Com };
    }

    render() {
        const { Com } = this.state;

        return <Com {...this.props} />;
    }
}

export default DeptSelect;
