/*
 * @(#) MyAlert.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-03-14 15:36:19
 */

import React from 'react';
import ModalTool from '@share/shareui/es/components/ModalTool';
import { Panel, Icon } from '@share/shareui';
import { message } from 'antd';

export default class ScurdAlert {
    /**
     * 提示消息框
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static msg(content, callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            backdrop: 'static',
            bsSize: 'sm',
            cancelText: null,
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_problem"
                            style={{
                                fontSize: '67px',
                                color: '#09d',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: '#09d',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            {content}
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 操作成功提示
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static ok(content, callBack) {
        return new ModalTool({
            title: '提示',
            bsStyle: 'success',
            closeBtn: false,
            backdrop: 'static',
            content,
            cancelText: null,
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 操作失败提示
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static fail(content, callBack) {
        return new ModalTool({
            title: '提示',
            bsStyle: 'warning',
            closeBtn: false,
            backdrop: 'static',
            content,
            cancelText: null,
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 警告提示
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static warning(content, callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            backdrop: 'static',
            bsSize: 'sm',
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_prompt"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            {content}
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 删除确认框
     * @param callBack 确定按钮回调
     */
    static delConfirm(callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            bsSize: 'sm',
            backdrop: 'static',
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_doubt"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            请确认是否删除？
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 操作确认框
     * @param callBack 确定按钮回调
     */
    static operateConfirm(callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            bsSize: 'sm',
            backdrop: 'static',
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_doubt"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            请确认是否执行该操作？
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 内容自定义确认框
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static confirm(content, callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            bsSize: 'sm',
            backdrop: 'static',
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_doubt"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            {content}
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 错误提示框
     * @param content 提示内容
     */
    static errorModal(content) {
        return new ModalTool({
            title: '提示',
            bsStyle: 'warning',
            closeBtn: false,
            backdrop: 'static',
            content,
            cancelText: null,
        });
    }

    static timingInformation(content) {
        message.error(content);
    }
}
