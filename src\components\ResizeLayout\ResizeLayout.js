/*
 * @(#)  ResizeLayout.js   ---- 可调节宽度侧边栏的布局组件
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 * Copyright: Copyright (c) 2022
 * Company: 厦门畅享信息技术有限公司
 * Author: yujy (<EMAIL>)
 * File Created: 2022-11-16 17:05:14
 */
import React, { useState } from 'react';
import styles from './ResizeLayout.scss';

const ResizeLayout = ({ sider, content, sliderWidth = 320 }) => {
    const [siderWidth, setSiderWidth] = useState(parseInt(localStorage.getItem('siderWidth'), 10) || sliderWidth);
    const [dragging, setDragging] = useState(false);
    const [startPageX, setStartPageX] = useState(0);
    const pxWidth = `${siderWidth}px`;
    const handleMouseDown = (event) => {
        setStartPageX(event.pageX);
        setDragging(true);
    };
    const handleMouseMove = (event) => {
        const currentSiderWidth = siderWidth + event.pageX - startPageX;
        if (currentSiderWidth > 0 && currentSiderWidth > sliderWidth) {
            setSiderWidth(currentSiderWidth);
            setStartPageX(event.pageX);
        }
    };
    const handleMouseUp = () => {
        setDragging(false);
        localStorage.setItem('siderWidth', siderWidth);
    };

    return (
        <div className={styles.layout} style={{ paddingLeft: pxWidth }}>
            <div className={styles.sider} style={{ width: pxWidth }}>
                {sider}
            </div>
            <div className={styles.content}>{content}</div>
            <div className={styles['sider-resizer']} style={{ left: pxWidth }} onMouseDown={handleMouseDown}>
                {dragging && <div className={styles['resize-mask']} onMouseMove={handleMouseMove} onMouseUp={handleMouseUp} />}
            </div>
        </div>
    );
};

export default ResizeLayout;
