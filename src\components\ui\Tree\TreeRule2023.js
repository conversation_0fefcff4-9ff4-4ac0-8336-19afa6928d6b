import React, { Component, Fragment } from 'react';
import <PERSON><PERSON>ree, { TreeNode as RCTreeNode } from 'rc-tree';
import { Icon } from 'antd';
import 'rc-tree/assets/index.css';

// styles
import './style/index.scss';

class Tree extends Component {
    render() {
        const {
            operation,
            treeData = [],
            // onAdd,
            // onEdit,
            // onDel,
            // extend,
            // extendBqGroup,
            searchField,
            deleteDir,
            addFiled,
            ...restProps
        } = this.props;

        // const editGroup = item => {
        //     if (item.groupName) {
        //         return (
        //             <ul className="title_operate clearfix">
        //                 {extendBqGroup && extendBqGroup(item)}
        //             </ul>
        //         );
        //     }
        //     return (
        //         <ul className="title_operate clearfix">
        //             {item.root && onAdd && <li className="operate_icon operate_add" onClick={() => onAdd(item)} />}
        //             {onEdit && <li className="operate_icon operate_edit" onClick={() => onEdit(item)} />}
        //             {onDel && <li className="operate_icon operate_del" onClick={() => onDel(item.key)} />}
        //             {extend && extend(item)}
        //         </ul>
        //     );
        // };
        const loop = (data = []) => {
            return data.map((item) => {
                return (
                    <RCTreeNode
                        {...item}
                        key={item.key}
                        title={
                            <span className="title_pane" style={{ display: 'flex', alignItems: 'center' }}>
                                <span className="title_content" onClick={() => searchField && searchField({ item })}>
                                    {item.title}
                                    {item.ruleNum > 0 && <span style={{ color: '#d9001b', fontSize: 12 }}>&nbsp;已配置</span>}
                                </span>
                                {!item.isField && (
                                    <Fragment>
                                        &nbsp;
                                        {deleteDir && <Icon type="close-circle" onClick={() => deleteDir({ item })} />}
                                    </Fragment>
                                )}
                                {operation !== 'detail' && item.isField && (
                                    <Fragment>
                                        &nbsp;
                                        {addFiled && <Icon type="plus-circle" onClick={() => addFiled({ item })} />}
                                    </Fragment>
                                )}
                            </span>
                        }
                    >
                        {item.children && loop(item.children)}
                    </RCTreeNode>
                );
            });
        };
        const convertTreeData = loop(treeData);

        return (
            <div className="share-tree_container" style={{ maxHeight: 300, overflowY: 'auto' }}>
                {treeData && treeData.length === 0 && (
                    <div
                        style={{
                            color: '#ccc',
                            textAlign: 'center',
                            marginTop: '60px',
                        }}
                    >
                        暂无数据
                    </div>
                )}
                <RCTree {...restProps}>{convertTreeData}</RCTree>
            </div>
        );
    }
}

export default Tree;
