import React, { Component } from 'react';
// 样式
import styles from '@/pages/metaData/styles/ApplicationCodeManage.scss';
// 服务接口
import * as MetaTableApi from '@/services/data/meta/MetaTableApi';
import * as MetaCategoryApi from '@/services/data/meta/MetaCategoryApi';
// 工具类
import * as StringUtils from '@/components/common/common/StringUtils';
import * as TreeUtil from '@/components/common/common/TreeUtil';
// 组件
import MyAlert, { antdMessage } from '@/components/ui/MyAlert/MyAlert';
import { NoPageTable as Table } from '@/components/business/Table';
import { Panel, FormItem, Button, Icon, FormControl } from '@share/shareui';
import { CacheShareForm, getComponents } from '@/components/business/Form';

const { Input, Select } = getComponents('div');

const objectTypeOptions = [
    { label: '法人', value: '0' },
    { label: '自然人', value: '1' },
    { label: '不限', value: '2' },
];

const typeOptions = [
    { label: '数据汇集及应用', value: '0' },
    { label: '数据应用', value: '1' },
];

const statusOptions = [
    { label: '启用', value: '1' },
    { label: '停用', value: '0' },
];

const confirmOptions = [
    { label: '存在', value: '1' },
    { label: '不存在', value: '0' },
];

// 默认搜索条件
const defaultSearchBody = {
    name: '',
    id: '',
    type: '',
    objectType: '',
    dataTableIdExist: '',
    dataTableId: '',
    status: '',
};

// 默认编辑数据
const defaultEditBody = {
    id: '',
    pid: '',
    name: '',
    type: '0',
    objectType: '2',
    dataTableId: '',
    status: '0',
    sort: 1,
};

function convertShowName(convertStr, matchStr) {
    const index = convertStr.indexOf(matchStr);
    const beforeStr = convertStr.substr(0, index);
    const afterStr = convertStr.substr(index + matchStr.length);

    return index > -1 ? (
        <span>
            {beforeStr}
            <span style={{ color: '#f59a23' }}>{matchStr}</span>
            {afterStr}
        </span>
    ) : (
        <span>{convertStr}</span>
    );
}

class CategoryCodeManage extends Component {
    state = {
        // 下拉选项
        dataTableIdOptions: [],
        // 搜索参数
        searchForm: { ...defaultSearchBody },
        searchBody: { ...defaultSearchBody },
        // 列表参数
        dataList: [],
        dataTree: [],
        expandedRowKeys: [],
        // 编辑参数
        operateRecord: {},
    };

    componentDidMount = async () => {
        // 获取所有表
        const dataTableIdList = await MetaTableApi.getDataTableList();
        // 过滤表
        const dataTableIdOptions = dataTableIdList.map((item) => ({ label: item.tableId, value: item.tableId }));

        this.setState({ dataTableIdOptions }, this.refreshData);
    };

    handleSearch = (callback) => {
        const { searchForm, dataList } = this.state;
        const searchBody = StringUtils.deleteSpace(searchForm);
        const dataTree = this.filterTree(dataList, searchBody);

        this.setState({ searchBody, dataTree, operateRecord: {} }, () => callback && callback());
    };

    handleReset = () => {
        this.setState({ searchForm: { ...defaultSearchBody } });
    };

    handleExpandedAllRow = () => {
        const { dataTree } = this.state;
        const expandedRowKeys = TreeUtil.treeToArray(dataTree).map((item) => item.id);

        this.setState({ expandedRowKeys });
    };

    handleSpreadAllRow = () => {
        this.setState({ expandedRowKeys: [] });
    };

    handleAdd = (data) => {
        const { dataTree } = this.state;

        data.mode = 'add';
        if (data.pid) {
            const parentNode = TreeUtil.findOne(dataTree, (item) => item.id === data.pid);

            parentNode.children = parentNode.children || [];
            parentNode.children.unshift(data);
        } else {
            dataTree.unshift(data);
        }
        this.setState({ dataTree: [...dataTree], operateRecord: { ...data, mode: 'add' } });
    };

    handleEdit = (data) => {
        data.mode = 'update';

        this.setState({ operateRecord: { ...data, mode: 'update' } });
    };

    handleCancel = (data) => {
        const { dataTree } = this.state;

        if (data.pid) {
            const parentNode = TreeUtil.findOne(dataTree, (item) => item.id === data.pid);

            parentNode.children = parentNode.children
                .filter((item) => item.id)
                .map((item) => {
                    delete item.mode;

                    return item;
                });
            if (parentNode.children.length === 0) {
                parentNode.children = null;
            }
            this.setState({ dataTree: [...dataTree] });
        } else {
            const newDataTree = dataTree
                .filter((item) => item.id)
                .map((item) => {
                    delete item.mode;

                    return item;
                });

            this.setState({ dataTree: newDataTree });
        }
        this.setState({ operateRecord: {} });
    };

    handleEditSubmit = async () => {
        const {
            dataList,
            operateRecord: { children, mode, ...submitData },
        } = this.state;

        if (!submitData.name && !submitData.name.trim()) {
            antdMessage.error('类别名称不能为空！');

            return;
        }
        if (submitData.name.length > 50) {
            antdMessage.error('类别名称的字数必须在0-50之间！');

            return;
        }
        if (!submitData.type) {
            antdMessage.error('类别用途不能为空！');

            return;
        }
        if (!submitData.objectType) {
            antdMessage.error('主体类别不能为空！');

            return;
        }
        if (mode === 'add') {
            if (dataList.filter((item) => item.name === submitData.name).length > 0) {
                antdMessage.error('类别名称已存在！');

                return;
            }
            if (dataList.filter((item) => item.id === submitData.id).length > 0) {
                antdMessage.error('类别代码已存在！');

                return;
            }
            await MetaCategoryApi.add(submitData);
        } else {
            if (dataList.filter((item) => item.id !== submitData.id).filter((item) => item.name === submitData.name).length > 0) {
                antdMessage.error('类别名称已存在！');

                return;
            }
            await MetaCategoryApi.update(submitData);
        }
        MyAlert.ok('操作成功');
        this.refreshData();
        this.setState({ operateRecord: {} });
    };

    handleMove = async (data, up) => {
        const { dataList } = this.state;
        let brother;

        if (data.pid) {
            brother = dataList.filter((item) => item.pid === data.pid);
        } else {
            brother = dataList.filter((item) => !item.pid || dataList.every((one) => one.id !== item.pid));
        }
        const index = brother.findIndex((item) => item.id === data.id);

        if (up && index !== 0) {
            brother = [...(brother.slice(0, index - 1) || []), brother[index], brother[index - 1], ...(brother.slice([index + 1]) || [])];
        }
        if (!up && index !== brother.length - 1) {
            brother = [...(brother.slice(0, index) || []), brother[index + 1], brother[index], ...(brother.slice([index + 2]) || [])];
        }
        brother.forEach((item, i) => {
            item.sort = i + 1;
        });
        await MetaCategoryApi.sort(brother);
        this.refreshData();
    };

    handleToggleStatus = async (data) => {
        await MetaCategoryApi.switchStatus(data.id);
        MyAlert.ok(data.status === '1' ? '停用成功' : '启用成功');
        this.refreshData();
    };

    filterTree = (dataList, searchBody) => {
        // 匹配搜索条件
        function matchSearchBody(data, condition) {
            const nameValid = !condition.name || (data.name || '').includes(condition.name);
            const idValid = !condition.id || (data.id || '').includes(condition.id);
            const typeValid = !condition.type || data.type === condition.type;
            const objectTypeValid = !condition.objectType || data.objectType === condition.objectType;
            const statusValid = !condition.status || data.status === condition.status;
            const dataTableIdValid = !condition.dataTableId || (data.dataTableId || '').includes(condition.dataTableId);
            const dataTableIdExistValid =
                !condition.dataTableIdExist ||
                (condition.dataTableIdExist === '1' && data.dataTableId) ||
                (condition.dataTableIdExist === '0' && !data.dataTableId);

            return nameValid && idValid && typeValid && objectTypeValid && statusValid && dataTableIdValid && dataTableIdExistValid;
        }
        // 获取树状数据
        function filterTreeData(tree, searchParam) {
            if (!Array.isArray(tree) || tree.length === 0) {
                return [];
            }

            return tree.filter((item) => {
                const matchSuccess = matchSearchBody(item, searchParam);

                if (!matchSuccess) {
                    item.children = item.children ? filterTreeData(item.children, searchParam) : null;
                }

                return matchSuccess || (Array.isArray(item.children) && item.children.length > 0);
            });
        }
        // 将数据转换成树结构的数据
        const dataTree = TreeUtil.arrayToTree2(
            dataList,
            (item) => !item.pid || dataList.every((one) => one.id !== item.pid),
            (one, two) => one.id === two.pid,
            (a, b) => (a.sort || 0) - (b.sort || 0),
            (item, children) => ({
                ...item,
                key: item.id,
                title: item.name,
                value: item.id,
                children: children.length > 0 ? children.map((i) => ({ ...i, brotherNum: children.length })) : null,
            })
        );

        dataTree.forEach((item) => {
            item.brotherNum = dataTree.length;
        });

        // 进行筛选
        return filterTreeData(dataTree, searchBody);
    };

    refreshData = async () => {
        const dataList = await MetaCategoryApi.allList();

        this.setState({ dataList }, () => this.handleSearch());
    };

    render() {
        const { history } = this.props;
        const { dataTableIdOptions, searchForm, searchBody, dataTree, expandedRowKeys, operateRecord } = this.state;
        const editMode = Object.keys(operateRecord).length > 0;
        const columns = [
            {
                title: '类别名称',
                dataIndex: 'name',
                render: (text, data) => {
                    if (data.mode) {
                        return (
                            <FormControl
                                style={{ width: 'auto' }}
                                inline
                                placeholder="类别名称"
                                type="text"
                                value={operateRecord.name}
                                onChange={(e) =>
                                    this.setState({
                                        operateRecord: {
                                            ...operateRecord,
                                            name: e.target.value,
                                        },
                                    })
                                }
                            />
                        );
                    }
                    if (!searchBody.name) {
                        return <span title={text}>{text}</span>;
                    }

                    return <span title={text}>{convertShowName(text, searchBody.name)}</span>;
                },
            },
            {
                title: '类别代码',
                dataIndex: 'id',
                width: 240,
                render: (text, data) => {
                    if (data.mode && !data.id) {
                        return (
                            <FormControl
                                style={{ width: 'auto' }}
                                inline
                                placeholder="类别代码"
                                type="text"
                                value={operateRecord.id}
                                onChange={(e) =>
                                    this.setState({
                                        operateRecord: {
                                            ...operateRecord,
                                            id: e.target.value,
                                        },
                                    })
                                }
                            />
                        );
                    }
                    if (!searchBody.id) {
                        return <span title={text}>{text}</span>;
                    }

                    return <span title={text}>{convertShowName(text, searchBody.id)}</span>;
                },
            },
            {
                title: '类别用途',
                dataIndex: 'type',
                width: 120,
                render: (text, data) => {
                    // 是否有子类别
                    const hasChildren = Array.isArray(data.children) && data.children.length > 0;
                    // 是否有父类别
                    const hasParent = Boolean(data.pid);
                    // 有子类别或者有父类别的时候不允许修改类别用途
                    const isDisabled = hasChildren || hasParent;

                    if (data.mode) {
                        return (
                            <Select.View
                                value={operateRecord.type}
                                options={typeOptions}
                                disabled={isDisabled}
                                clearable={!data.id}
                                onChange={(e) =>
                                    this.setState({
                                        operateRecord: {
                                            ...operateRecord,
                                            type: e.target.value,
                                        },
                                    })
                                }
                            />
                        );
                    }
                    const value = (typeOptions.find((item) => item.value === text) || {}).label;
                    const color = searchBody.type && searchBody.type === text ? '#f59a23' : '#666';

                    return (
                        <span title={value} style={{ color }}>
                            {value}
                        </span>
                    );
                },
            },
            //  {
            //     title: '主体类型',
            //     dataIndex: 'objectType',
            //     width: 80,
            //     render: (text, data) => {
            //         if (!data.id || data.id === operateRecord.id) {
            //             return (
            //                 <Select.View
            //                     value={operateRecord.objectType}
            //                     options={objectTypeOptions}
            //                     clearable={!data.id}
            //                     onChange={e =>
            //                         this.setState({
            //                             operateRecord: {
            //                                 ...operateRecord,
            //                                 objectType: e.target.value
            //                             }
            //                         })
            //                     }
            //                 />
            //             );
            //         }
            //         const value = (objectTypeOptions.find(item => item.value === text) || {}).label;
            //         const color = searchBody.objectType && searchBody.objectType === text ? '#f59a23' : '#666';
            //
            //         return <span title={value} style={{ color }} >{value}</span>;
            //     },
            // },
            {
                title: '数据表名',
                dataIndex: 'dataTableId',
                width: 200,
                render: (text, data) => {
                    if (data.mode) {
                        return (
                            <Select.View
                                options={dataTableIdOptions}
                                value={operateRecord.dataTableId}
                                clearable={!data.id}
                                onChange={(e) =>
                                    this.setState({
                                        operateRecord: {
                                            ...operateRecord,
                                            dataTableId: e.target.value,
                                        },
                                    })
                                }
                            />
                        );
                    }
                    if (!text) {
                        return '';
                    }
                    if (!searchBody.dataTableId) {
                        return (
                            <a title={text} href="javascript:void(0)" onClick={() => history.push(`/DatabaseTableDetail/${text}/1`)}>
                                {text}
                            </a>
                        );
                    }
                    return (
                        <a title={text} href="javascript:void(0)" onClick={() => history.push(`/databaseTableInfoDetail/${text}/1`)}>
                            {convertShowName(text, searchBody.dataTableId)}
                        </a>
                    );
                },
            },
            {
                title: '状态',
                dataIndex: 'status',
                width: 50,
                render: (text, data) => {
                    if (data.mode) {
                        return <Select.View options={statusOptions} value={operateRecord.status} clearable={false} disabled />;
                    }
                    const value = text === '1' ? '启用' : '停用';
                    const color = text === '1' ? '#01ba88' : '#ff6655';

                    return (
                        <span title={value} style={{ color }}>
                            {value}
                        </span>
                    );
                },
            },
            {
                title: '操作',
                key: 'operate',
                // align: 'center',
                width: 180,
                render: (text, data, i) => {
                    const editTarget = data.mode;
                    const children = data.children || [];

                    return (
                        <div className="tableBtn">
                            {!editMode && (
                                <a href="javascript:void(0)" onClick={() => this.handleEdit(data)}>
                                    修改
                                </a>
                            )}
                            {!editMode && (
                                <a
                                    href="javascript:void(0)"
                                    onClick={() => {
                                        const sort = children.length > 0 ? children[children.length - 1].sort + 1 : 1;

                                        if (!expandedRowKeys.includes(data.id)) {
                                            this.setState({ expandedRowKeys: [...expandedRowKeys, data.id] });
                                        }
                                        this.handleAdd({
                                            ...defaultEditBody,
                                            pid: data.id,
                                            type: data.type,
                                            status: '0',
                                            sort,
                                        });
                                    }}
                                >
                                    新增
                                </a>
                            )}
                            {!editMode && (
                                <a
                                    href="javascript:void(0)"
                                    onClick={() => {
                                        MyAlert.operateConfirm(() => this.handleToggleStatus(data));
                                    }}
                                    style={{ color: data.status === '1' ? '#ff6655' : '#01ba88' }}
                                >
                                    {data.status === '1' ? '停用' : '启用'}
                                </a>
                            )}
                            {!editMode && i !== 0 && (
                                <a
                                    href="javascript:void(0)"
                                    onClick={() => {
                                        this.handleMove(data, true);
                                    }}
                                >
                                    上移
                                </a>
                            )}
                            {!editMode && i !== data.brotherNum - 1 && (
                                <a
                                    href="javascript:void(0)"
                                    onClick={() => {
                                        this.handleMove(data, false);
                                    }}
                                >
                                    下移
                                </a>
                            )}
                            {editMode &&
                                editTarget &&
                                ((data.name || '') !== operateRecord.name ||
                                    (data.type || '') !== operateRecord.type ||
                                    (data.objectType || '') !== operateRecord.objectType ||
                                    (data.dataTableId || '') !== operateRecord.dataTableId) && (
                                    <a href="javascript:void(0)" onClick={() => this.handleEditSubmit()}>
                                        确认
                                    </a>
                                )}
                            {editMode && editTarget && (
                                <a
                                    href="javascript:void(0)"
                                    onClick={() => {
                                        this.handleCancel(data);
                                    }}
                                >
                                    取消
                                </a>
                            )}
                        </div>
                    );
                },
            },
        ];

        return (
            <div>
                <Panel>
                    <Panel.Body full>
                        <CacheShareForm
                            formData={searchForm}
                            onChange={(data, callback) => this.setState({ searchForm: data }, callback)}
                            searchFn={() => this.handleSearch(this.handleExpandedAllRow)}
                            namespace="ApplicationCodeManageForm"
                        >
                            <Input label="类别名称" field="name" labelCol={3} col={10} />
                            <Input label="类别代码" field="id" labelCol={3} col={10} />
                            <Select label="类别用途" field="type" options={typeOptions} labelCol={3} col={10} />
                            {/* <Select
                                label="主体类型" field="objectType"
                                options={objectTypeOptions}
                                labelCol={3} col={10}
                            /> */}
                            <Select label="数据表存在" field="dataTableIdExist" options={confirmOptions} labelCol={3} col={10} />
                            <Input label="数据表名" field="dataTableId" labelCol={3} col={10} />
                            <Select label="启用状态" field="status" options={statusOptions} labelCol={3} col={10} />
                            <FormItem className="btn-item clearfix pull-right">
                                <Button type="submit" bsStyle="primary" onClick={() => this.handleSearch(this.handleExpandedAllRow)}>
                                    查询
                                </Button>
                                <Button type="reset" onClick={this.handleReset}>
                                    重置
                                </Button>
                            </FormItem>
                        </CacheShareForm>
                    </Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Head title="查询结果">
                        <Panel.HeadRight>
                            <ul className="ui-list-horizontal">
                                <li>
                                    <Button type="button" className="btn-xs" border={false} onClick={this.handleExpandedAllRow}>
                                        <Icon className="si si-com_sxx" />
                                        展开
                                    </Button>
                                </li>
                                <li>
                                    <Button type="button" className="btn-xs" border={false} onClick={this.handleSpreadAllRow}>
                                        <Icon className="si si-com_sxs" />
                                        折叠
                                    </Button>
                                </li>
                                {!editMode && (
                                    <li>
                                        <Button
                                            type="button"
                                            className="btn-xs"
                                            border={false}
                                            onClick={() => {
                                                this.handleAdd({
                                                    ...defaultEditBody,
                                                    sort: dataTree.length === 0 ? 1 : dataTree[dataTree.length - 1].sort + 1,
                                                });
                                            }}
                                        >
                                            <Icon className="fa-plus" />
                                            新增
                                        </Button>
                                    </li>
                                )}
                            </ul>
                        </Panel.HeadRight>
                    </Panel.Head>
                    <Panel.Body full>
                        <Table
                            className={styles.applicationCodeManageTable}
                            dataSource={dataTree}
                            expandedRowKeys={expandedRowKeys}
                            onExpandedRowsChange={(expandedRows) => this.setState({ expandedRowKeys: expandedRows })}
                            rowKey="id"
                            columns={columns}
                        />
                    </Panel.Body>
                </Panel>
            </div>
        );
    }
}
export default CategoryCodeManage;
