/*
 * @(#) TextUtil.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2023
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2023-09-22 09:36:44
 */

import MyHint from '@/components/ui/MyAlert/MyHint';

function copyText(text, hitContent) {
    const $textarea = $('<textarea style="position: fixed; top: 0; left: 0;"></textarea>');

    $('body').append($textarea);
    $textarea.val(text);
    $textarea.focus();
    $textarea.select();
    if (document.execCommand('copy')) {
        MyHint.success(hitContent ? `复制成功，内容[${text}]` : '复制成功！');
    } else {
        MyHint.success('复制失败！');
    }
    $textarea.remove();
}

module.exports = {
    copyText,
};
