import React, { Component, Fragment } from 'react';
// 样式
import styles from '@/pages/metaData/styles/index.scss';
// 工具类
import * as Entity from '@/components/common/common/Entity';
import * as StringUtils from '@/components/common/common/StringUtils';
import * as formRule from '@/components/common/common/formRule';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
// 组件
import { Modal, Button } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';

const shareFormCons = getComponents();
const { Form, Row, Input, Select, RadioGroup, ArrayFormItem } = shareFormCons;

class FieldComponentEdit extends Component {
    state = {
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
        fieldComponentOptional: [],
    };

    componentWillReceiveProps(nextProps) {
        const {
            show,
            data: { detail, fieldComponentList },
        } = nextProps;
        const { editForm } = this.state;

        if (!this.props.show && show) {
            const paramRuleKey = detail.componentType.replace(/^(.*)Param$/, '$1ParamRule');
            const fieldComponentOptional = fieldComponentList
                .filter((item) => item[paramRuleKey])
                .map((item) => ({ label: item.componentName, value: item.componentId }));

            editForm.setFormData(Entity.simpleDeepCopy(detail));
            editForm.cleanValidError();
            this.setState({ fieldComponentOptional });
        }
    }

    onComponentIdChange = (componentId) => {
        const {
            data: { detail, fieldComponentList },
        } = this.props;
        const { editForm } = this.state;
        const formData = editForm.getFormData();

        if (!componentId) {
            editForm.setFieldValue({ ...formData, componentId: '' });

            return;
        }
        const paramRuleKey = detail.componentType.replace(/^(.*)Param$/, '$1ParamRule');
        const component = fieldComponentList.find((item) => item.componentId === componentId) || {};
        const componentParamRule = component[paramRuleKey] || [];
        const componentParam = componentParamRule.reduce((obj, item) => {
            obj[item.key] = item.value;

            return obj;
        }, {});

        if (formData.componentParam.placeholder) {
            componentParam.placeholder = formData.componentParam.placeholder;
        }
        editForm.setFormData({ ...formData, componentId, componentParam });
    };

    calculationComponent = (param, parentParam) => {
        let Con = Input;
        const props = {};
        const rule = [];

        if (param.valueType === 'string') {
            const minLength = Number.parseFloat(param.minLength);
            const maxLength = Number.parseFloat(param.maxLength);

            if (minLength === 0 || minLength) {
                rule.push(formRule.checkLength(minLength, null));
            }
            if (maxLength) {
                rule.push(formRule.checkLength(null, maxLength));
            }
        } else if (param.valueType === 'int' || param.valueType === 'number') {
            props.type = 'number';
            if (param.valueType === 'int') {
                props.step = 1;
                rule.push(formRule.checkIsInteger());
            } else {
                rule.push(formRule.checkIsNum());
            }
            const minValue = Number.parseFloat(param.minValue);
            const maxValue = Number.parseFloat(param.maxValue);

            if (minValue === 0 || minValue) {
                props.min = minValue;
                rule.push(formRule.checkNumRange(minValue, null));
            }
            if (maxValue === 0 || maxValue) {
                props.max = maxValue;
                rule.push(formRule.checkNumRange(null, maxValue));
            }
        } else if (param.valueType === 'boolean') {
            Con = RadioGroup;
            props.options = [
                { label: '是', value: true },
                { label: '否', value: false },
            ];
        } else if (param.valueType === 'bm') {
            Con = parentParam ? Select : RadioGroup;
            props.options = param.options || [];
        } else if (param.valueType === 'array') {
            Con = ArrayFormItem;
            props.defaultChildrenData = param.children.reduce((result, item) => {
                result[item.key] = '';

                return result;
            }, {});
            props.children = (itemProps) => (
                <Fragment>
                    {param.children.map((node) => {
                        const calculationResult = this.calculationComponent(node, param);
                        const NodeCom = calculationResult.Con;
                        const nodeProps = calculationResult.props;

                        return (
                            <NodeCom.View
                                value={itemProps.value[node.key]}
                                onChange={({ target: { value } }) =>
                                    itemProps.onChange({ target: { value: { ...itemProps.value, [node.key]: value } } })
                                }
                                {...nodeProps}
                            />
                        );
                    })}
                </Fragment>
            );
            rule.push(
                formRule.checkFunction((value) => !value || value.every((item) => Object.values(item).every((one) => one)), '不能存在空项')
            );
        }
        props.placeholder = param.tip || `请填充${param.name}`;

        return { Con, props, rule };
    };

    buildComponentParamFormEdit = (componentId) => {
        const {
            data: { detail, fieldComponentList },
        } = this.props;
        const paramRuleKey = detail.componentType.replace(/^(.*)Param$/, '$1ParamRule');
        const component = fieldComponentList.find((item) => item.componentId === componentId) || {};
        const componentParamRule = component[paramRuleKey] || [];

        if (!Array.isArray(componentParamRule)) {
            return '';
        }

        return componentParamRule.map((item) => {
            const { Con, props, rule } = this.calculationComponent(item);

            return (
                <Row>
                    <Con {...props} label={item.name} field={`componentParam.${item.key}`} rule={rule} />
                </Row>
            );
        });
    };

    submit = async () => {
        const { successFn } = this.props;
        const { editForm } = this.state;

        if (FormVaildHelper.isValid(await editForm.valid())) {
            const editBody = StringUtils.deleteSpace(editForm.getFormData());

            successFn && successFn(editBody);
        }
    };

    render() {
        const {
            show,
            cancelFn,
            data: { detail, componentTypeOptions, metaFieldList },
        } = this.props;
        const { editForm, fieldComponentOptional } = this.state;
        const fieldCodeOptions = metaFieldList.map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));
        const component = editForm.getFormData();

        return (
            <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>组件编辑</Modal.Header>
                <Modal.Body>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <Select
                                label="组件字段"
                                field="fieldCode"
                                rule={formRule.checkRequiredNotBlank()}
                                options={fieldCodeOptions}
                                disabled
                                required
                                placeholder="请选择组件字段"
                            />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="组件类型"
                                field="componentType"
                                rule={formRule.checkRequiredNotBlank()}
                                options={componentTypeOptions}
                                disabled
                                required
                            />
                        </Row>
                        <Row>
                            <Select
                                label="组件名称"
                                field="componentId"
                                options={fieldComponentOptional}
                                onChange={({ target: { value } }) => this.onComponentIdChange(value)}
                                rule={formRule.checkRequiredNotBlank()}
                                required
                                placeholder="请选择组件名称"
                            />
                        </Row>
                        {component.componentId && (
                            <Row>
                                <Input
                                    label="默认值"
                                    field="defaultValue"
                                    rule={formRule.checkLength(null, 50)}
                                    placeholder="请输入默认值"
                                />
                            </Row>
                        )}
                        {component.componentId && detail.componentType.includes('edit') && (
                            <Row>
                                <Input
                                    label="输入内容示例"
                                    field="tip"
                                    rule={formRule.checkLength(null, 300)}
                                    placeholder="请输入输入内容示例"
                                />
                            </Row>
                        )}
                        {component.componentId && this.buildComponentParamFormEdit(component.componentId)}
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        确认
                    </Button>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default FieldComponentEdit;
