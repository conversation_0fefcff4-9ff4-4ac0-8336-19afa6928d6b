import React, { Component } from 'react';
// styles
import './style/Select.scss';
import { Input } from 'antd';

class PageSizeSelect extends Component {
    state = {
        inputValue: '',
    };

    handleInputChange = ({ target: { value } }) => {
        this.setState({ inputValue: value });
    };

    submitValue = (inputValue) => {
        const { onChange } = this.props;
        const inputNum = Number.parseInt(inputValue);

        if (inputNum) {
            onChange(inputNum);
            this.setState({ inputValue: '' });
        } else {
            this.setState({ inputValue });
        }
    };

    render() {
        const { pageSizeOptions = [] } = this.props;
        const { inputValue } = this.state;

        return (
            <ul className="share-select_options">
                {pageSizeOptions.map((item) => (
                    <li key={item} className="share-select_option" onClick={() => this.submitValue(item)}>
                        每页{item}条
                    </li>
                ))}
                <li>
                    <Input
                        type="number"
                        min={1}
                        value={inputValue}
                        onChange={this.handleInputChange}
                        onPressEnter={() => this.submitValue(inputValue)}
                        placeholder="请输入..."
                        style={{ width: 80, paddingRight: 0 }}
                    />
                </li>
            </ul>
        );
    }
}

export default PageSizeSelect;
