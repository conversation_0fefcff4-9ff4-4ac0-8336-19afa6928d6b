/* eslint-disable react/no-unstable-nested-components */
/*
 *@(#) EnterpriseVisit.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-10-12
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import NoData from '@/components/NoData';
import ShareList, { useList } from '@share/list';
import { Image } from 'antd';
import React from 'react';
import TimeLine from '@/components/TimeLine';
import PaginationComponent from '@/components/PaginationComponent';
import CardDataView from '@/components/CardDataView';

const tempUrl =
    'https://ts1.cn.mm.bing.net/th/id/R-C.********************************?rik=sQ%2fKoYAcr%2bOwsw&riu=http%3a%2f%2fwww.quazero.com%2fuploads%2fallimg%2f140305%2f1-140305131415.jpg&ehk=Hxl%2fQ9pbEiuuybrGWTEPJOhvrFK9C3vyCcWicooXfNE%3d&risl=&pid=ImgRaw&r=0';

const TableComponent = ({ api }) => {
    if (api.list.length === 0) {
        return <NoData />;
    }

    return (
        <TimeLine
            data={api.list}
            contentRender={(data, index) => {
                return (
                    <CardDataView
                        title="创建人：火炬管委会（林夕）"
                        grid={1}
                        trigger
                        defaultClose={index !== 0}
                        data={[
                            {
                                label: '走访情况',
                                value: '关于成立专班，若政府支持该项目落地，建议由市发改委牵头，市财政局、金融办、银监局、翔安区',
                            },
                            {
                                label: '图片',
                                value: (
                                    <Image.PreviewGroup>
                                        {[{}, {}].map((v) => {
                                            return (
                                                <span style={{ marginRight: '4px' }}>
                                                    <Image width={88} src={tempUrl} />
                                                </span>
                                            );
                                        })}
                                    </Image.PreviewGroup>
                                ),
                            },
                        ]}
                    />
                );
            }}
            className="timelineCover"
        />
    );
};

const EnterpriseVisit = () => {
    const listState = useList({ dataSource: [{ date: '2019-04-25' }, { date: '2019-14-25' }] });

    return (
        <div>
            <ShareList listState={listState} TableComponent={TableComponent} PaginationComponent={PaginationComponent} />
        </div>
    );
};

export default EnterpriseVisit;
