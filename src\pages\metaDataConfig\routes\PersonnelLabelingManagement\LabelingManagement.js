import React, { useState } from 'react';

import NoData from '@/components/NoData/NoData';
import Highlighter from 'react-highlight-words';
import ShareList from '@share/list';

import { fieldTranslate, moneyFormat } from '@/utils/format';
import { useLogic } from './hooks';
import styles from './LabelingManagement.scss';
import Search from './Search';
import PersonTag from './PersonTag';

const TableComponent = (props) => {
    const { api, goDetail, bm, searchVal, handleRemoveTagPerson, refresh, tagList } = props;
    const { RYGMLXDM } = bm || {};

    if (api.list.length === 0) {
        return <NoData />;
    }

    return api.list.map((v, index) => {
        return (
            <div key={v.id} className={styles.listItem} /*onClick={() => goDetail(v.tyshxydm)}*/>
                <div className={styles.listItemTitle}>
                    <Highlighter
                        key={v.id}
                        className={styles.title}
                        highlightClassName={styles.titleHighLight}
                        searchWords={[searchVal]}
                        autoEscape
                        textToHighlight={`${v.xm} `}
                    />
                    ({v.zjhm})
                </div>
                <div className={styles.listItemInfo}>
                    <div>
                        <span>现居住地详址：</span>
                        {v.jzdxz}
                    </div>
                </div>

                <PersonTag
                    rowIndex={index}
                    accInfoList={v.accInfoList}
                    idNum={v.zjhm}
                    handleRemoveTagPerson={handleRemoveTagPerson}
                    refresh={refresh}
                    tagList={tagList}
                />
            </div>
        );
    });
};

const LabelingManagement = () => {
    const { listState, keyWordParam, handlerQuery, goDetail, handleRemoveTagPerson, refresh, tagList } = useLogic();

    const renderTableComponent = (props) => (
        <TableComponent
            {...props}
            // goDetail={goDetail}
            searchVal={keyWordParam}
            refresh={refresh}
            handleRemoveTagPerson={handleRemoveTagPerson}
            tagList={tagList}
        />
    );

    return (
        <div className={styles.LabelingManagement}>
            <div className={styles['LabelingManagement-warp']}>
                <Search onSearch={handlerQuery} placeholder="输入人员名称/证件号码检索" />
                <div className={styles.listBox}>
                    <ShareList uniqKey="id" listState={listState} TableComponent={renderTableComponent} refresh={refresh} />
                </div>
            </div>
        </div>
    );
};
export default LabelingManagement;
