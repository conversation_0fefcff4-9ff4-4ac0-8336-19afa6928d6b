/*
 *@(#) EnterpriseService.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-10-12
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React, { Fragment } from 'react';
import TagTitle from '../TagTitle';
import SupportInfo from './components/SupportInfo';
import EnterpriseVisit from './components/EnterpriseVisit/EnterpriseVisit';
import EnterpriseAppeal from './components/EnterpriseAppeal/EnterpriseAppeal';

const EnterpriseService = () => {
    return (
        <div>
            <TagTitle
                title={
                    <Fragment>
                        扶持信息<span>3</span>
                    </Fragment>
                }
                id="supportInfo"
            />
            <SupportInfo />
            <TagTitle
                title={
                    <Fragment>
                        企业走访<span>3</span>
                    </Fragment>
                }
                id="enterpriseVisit"
            />
            <EnterpriseVisit />
            <TagTitle
                title={
                    <Fragment>
                        企业诉求<span>3</span>
                    </Fragment>
                }
                id="enterpriseAppeal"
            />
            <EnterpriseVisit />
            {/* 两个组件类似，开发的时候拷贝一份过去改 */}
            {/* <EnterpriseAppeal /> */}
        </div>
    );
};

export default EnterpriseService;
