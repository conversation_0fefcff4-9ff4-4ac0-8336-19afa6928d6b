import { RemoteService } from '@share/framework';

class Service extends RemoteService {
    metaConfig = async (categoryId) => {
        return this.network.formGet(`/meta/category/meta/config/${categoryId}/2.do`);
    };

    templateDownload = async (categoryId) => {
        return this.network.fakeFormGet(`${window.SHARE.CONTEXT_PATH}/data/save/template/download/category/${categoryId}.do`);
    };

    addFromPage = async (categoryId, param) => {
        return this.network.json(`/data/save/add/from_page/category/${categoryId}.do`, param);
    };

    addFromExcel = (categoryId, file) => {
        return this.network.upload(`/data/save/add/from_excel/category/${categoryId}.do`, file);
    };

    updateFromPage = async (categoryId, param) => {
        return this.network.json(`/data/save/update/from_page/category/${categoryId}.do`, param);
    };

    switchStatus = async (categoryId, param) => {
        return this.network.json(`/data/save/status/category/${categoryId}.do`, param);
    };

    list = async (categoryId, param) => {
        return this.network.json(`/data/query/list/category/${categoryId}.do`, param);
    };

    detail = async (categoryId, recordId) => {
        return this.network.formGet(`/data/query/detail/category/${categoryId}/${recordId}.do`);
    };

    editDetail = async (categoryId, recordId) => {
        return this.network.formGet(`/data/query/edit_detail/category/${categoryId}/${recordId}.do`);
    };

    // 获取数据类别列表所有
    getCategoryListAll = async () => {
        return this.network.formGet(`/meta/category/list/all.do`);
    };

    // 获取数据表的数据
    getTableData = async (tableId) => {
        return this.network.formGet(`/meta/field/config/table_id/${tableId}.do`);
    };
}

export default Service;
