import React, { Component } from 'react';
import CommonFileUpload from './com/CommonFileUpload';
import ShareUiFileUpload from './com/ShareUiFileUpload';
import getSingleUploadComponent from './com/getSingleUploadComponent';

class FileUpload extends Component {
    constructor(props) {
        super(props);
        const { styleType, singleValue } = props;
        let Com;

        if (styleType === 'shareui') {
            Com = ShareUiFileUpload;
        } else {
            Com = CommonFileUpload;
        }
        if (singleValue) {
            Com = getSingleUploadComponent(Com);
        }
        this.state = { Com };
    }

    render() {
        const { Com } = this.state;
        const { styleType, singleValue, ...restProps } = this.props;

        return <Com {...restProps} />;
    }
}

export default FileUpload;
