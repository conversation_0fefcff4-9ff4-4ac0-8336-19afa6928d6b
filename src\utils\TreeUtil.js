/*
 * @(#) TreeUtil.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-12-23 16:41:37
 */

/**
 * 数组数据转成树状数据
 * @param array 资源列表数组
 * @param parentArray 目标列表数组
 * @param childrenPredicateFn 子元素判定（入参：父元素、子元素，出参：是否为父子关系）
 * @param childrenSortFn 子元数数组排序函数
 * @param treeDataGenFn 数状数据创建函数（入参：单节点、子节点数组，出参：树状节点）
 * @returns {Array} 树状数据
 */
const arrayToTree1 = (array, parentArray, childrenPredicateFn, childrenSortFn, treeDataGenFn) => {
    if (!Array.isArray(array) || array.length === 0) {
        return [];
    }
    if (childrenSortFn) {
        parentArray.sort(childrenSortFn);
    }

    return parentArray.map((item) => {
        const childrenArray = array.filter((one) => childrenPredicateFn(item, one));
        const childrenTree = arrayToTree1(array, childrenArray, childrenPredicateFn, childrenSortFn, treeDataGenFn);

        return treeDataGenFn(item, childrenTree);
    });
};

/**
 * 数组数据转成树状数据
 * @param array 资源列表数组
 * @param topPredicateFn 顶层元数判定函数（入参：元素，出参：是否顶层元数）
 * @param childrenPredicateFn 子元素判定（入参：父元素、子元素，出参：是否为父子关系）
 * @param childrenSortFn 子元数数组排序函数
 * @param treeDataGenFn 数状数据创建函数（入参：单节点、子节点数组，出参：树状节点）
 * @returns {Array} 树状数据
 */
const arrayToTree2 = (array, topPredicateFn, childrenPredicateFn, childrenSortFn, treeDataGenFn) => {
    if (!Array.isArray(array) || array.length === 0) {
        return [];
    }
    const topArray = array.filter((item) => topPredicateFn(item));

    return arrayToTree1(array, topArray, childrenPredicateFn, childrenSortFn, treeDataGenFn);
};

/**
 * 将树状数据摊平成数组
 * @param tree 树
 * @param childrenKey 子元素属性key
 * @returns {Array}
 */
const treeToArray = (tree, childrenKey = 'children') => {
    if (!Array.isArray(tree) || tree.length === 0) {
        return [];
    }

    return tree.map((item) => [item, ...treeToArray(item[childrenKey], childrenKey)]).reduce((resultArr, one) => resultArr.concat(one), []);
};

export { arrayToTree1, arrayToTree2, treeToArray };
