import { useMount, useSetState, useDeepCompareEffect } from 'ahooks';
import { useParams, useHistory } from 'react-router-dom';
import { Modal } from 'antd';
import MetaTableApi from '@/services/MetaTableApi';
import MetaFieldApi from '@/services/MetaFieldApi';
import { useService } from '@share/framework';

export const useServiceMap = () => {
    const metaTableApi = useService(MetaTableApi);
    const metaFieldApi = useService(MetaFieldApi);

    return {
        metaTableApi,
        metaFieldApi,
    };
};
const useHook = () => {
    const { metaTableApi, metaFieldApi } = useServiceMap();
    const history = useHistory();
    const { tabActiveKey = '1', tableId } = useParams();
    const [state, setState] = useSetState({
        tabActiveKey: '1',
        renderTabMap: {},
        tableInfo: {},
        metaFieldList: [],
        showExportModal: false,
        showCopyModal: false,
        fieldCode: '',
        eventKey: '',
    });

    const switchTab = (eventKey) => {
        const { renderTabMap } = state;

        setState({
            tabActiveKey: eventKey,
            renderTabMap: { ...renderTabMap, [eventKey]: true },
        });
    };
    const refreshTableFieldList = async () => {
        const metaFieldList = await metaFieldApi.tableMetaByTableName(tableId);

        if (metaFieldList.length === 0) {
            switchTab('1');
        }
        setState({ metaFieldList });
    };
    const requestDatabaseTableInfo = async () => {
        const tableInfo = await metaTableApi.getDataTableDetail(tableId);
        const recordCount = await metaTableApi.getDataTableRecordCount(tableId, { noMask: false });
        // setState({ tableInfo: { ...state.tableInfo, recordCount } });
        setState({
            tableInfo: { ...tableInfo, recordCount },
        });
    };
    const refreshTabledMetaConfig = () => {
        Modal.confirm({
            content: '该操作会根据数据库表信息自动初始化或更正配置，请确认是否操作？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
                const success = await metaFieldApi.refreshTableMeta(tableId);

                if (success) {
                    Modal.success('刷新成功', 2, () => {
                        refreshTableFieldList();
                    });
                }
            },
        });
    };
    const importConfigSuccess = () => {
        Modal.success.ok('导入成功', 2, () => {
            refreshTableFieldList();
        });
    };
    const handleSetShowModal = (key, bool) => {
        setState({
            [key]: bool,
        });
    };

    const handleSetFieldCodeEventKey = (d) => {
        setState(d);
    };
    const handleGoBack = () => {
        history.go(-1);
    };
    useMount(() => {
        switchTab(tabActiveKey);
        requestDatabaseTableInfo();
        refreshTableFieldList();
    });

    return {
        tableId,
        state,
        refreshTabledMetaConfig,
        importConfigSuccess,
        refreshTableFieldList,
        switchTab,
        handleSetShowModal,
        handleGoBack,
        handleSetFieldCodeEventKey,
    };
};

export default useHook;
