import React, { Component } from 'react';
import * as formRule from '@/components/common/common/formRule';
import { Form } from '@share/shareui';
import { getComponents } from '@/components/business/Form';
import FieldRuleCom from '../FieldRuleCom';

const { ArrayFormItem } = getComponents();

class CombinationFormItem extends Component {
    render() {
        const { field, editForm, ruleOptional, bmTableOption, metaFieldList, onRuleIdChange, ...restProps } = this.props;

        return (
            <ArrayFormItem
                field={field}
                label="组合规则"
                rule={formRule.checkFunction((value) => Array.isArray(value) && value.length > 0, '不能存在空项')}
                defaultChildrenData={{ ruleId: '' }}
                {...restProps}
            >
                {(props) => {
                    const { value, index } = props;

                    return (
                        <Form pageType="addPage">
                            <Form.Table style={{ border: '1px solid #ddd' }}>
                                <FieldRuleCom
                                    editForm={editForm}
                                    keyPrefix={`${field}.${index}.`}
                                    rule={value}
                                    ruleOptional={ruleOptional}
                                    bmTableOption={bmTableOption}
                                    metaFieldList={metaFieldList}
                                    onRuleIdChange={onRuleIdChange}
                                />
                            </Form.Table>
                        </Form>
                    );
                }}
            </ArrayFormItem>
        );
    }
}

export default CombinationFormItem;
