import React, { Component } from 'react';
// 样式
import zhCN from 'antd/es/locale/zh_CN';
import '../style/Upload.scss';
// 组件
import { ConfigProvider, Modal, Icon } from 'antd';
import ImgCrop from 'antd-img-crop';
import CommonFileUpload from './CommonFileUpload';

class CommonImageUpload extends Component {
    static defaultProps = {
        fileType: 'image/jpg,image/jpeg,image/gif,image/png,image/bmp', // 服务端文件上传地址
        listType: 'picture-card', // 图片展示类型
    };

    state = { previewVisible: false, previewImage: '' };

    handlePreview = (file) => {
        this.setState({
            previewImage: file.url || file.thumbUrl,
            previewVisible: true,
        });
    };

    handleCancelPreview = () => {
        this.setState({ previewVisible: false });
    };

    render() {
        const { previewVisible, previewImage } = this.state;
        const { crop, ...restProps } = this.props;
        // 暂时无法兼容IE
        // const versionSupport = !document.documentMode;  /* || document.documentMode >= 11*/
        const uploadButton = (
            <div>
                <Icon type="upload" />
                <div className="ant-upload-text">点击上传</div>
            </div>
        );
        const uploadProps = { onPreview: this.handlePreview, uploadButton, ...restProps };

        return (
            <div className="clearfix">
                {/* { */}
                {/* crop && versionSupport ? */}
                {/* (<ConfigProvider locale={zhCN}> */}
                {/* <ImgCrop */}
                {/* modalWidth={480} */}
                {/* localeCode="zh-cn" */}
                {/* {...this.props} */}
                {/* > */}
                {/* <CommonFileUpload {...uploadProps} /> */}
                {/* </ImgCrop> */}
                {/* </ConfigProvider>) */}
                {/* : */}
                {/* (<CommonFileUpload {...uploadProps} />) */}
                {/* } */}
                <CommonFileUpload {...uploadProps} />
                <Modal visible={previewVisible} footer={null} onCancel={this.handleCancelPreview} width={1000}>
                    <img alt="example" style={{ width: '100%' }} src={previewImage} />
                </Modal>
            </div>
        );
    }
}

export default CommonImageUpload;
