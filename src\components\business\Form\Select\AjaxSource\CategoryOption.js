/*
 * @Author: your name
 * @Date: 2021-09-08 10:09:33
 * @LastEditTime: 2021-10-11 16:11:11
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /credit-management-front-3.1/credit_common_front/src/components/business/Form/Select/AjaxSource/CategoryOption.js
 */
import React, { Component } from 'react';
// 服务接口
import * as categoryApi from '@/services/data/meta/MetaCategoryApi';
// 增强工具类
import * as TreeUtil from '@/components/common/common/TreeUtil';
import { getComponents } from '@share/shareui-form';
import { getAjaxComponent } from '../../Custom';
// 被封装组件
import TreeSelect from '../custom/TreeSelect';
import CheckboxGroupCustom from '../../Checkbox/CheckboxGroupCustom';
import SelectCustom from '../custom/SelectCustom';

const { Select, RadioGroup } = getComponents();
const AjaxTreeSelect = getAjaxComponent(TreeSelect);
const AjaxCustomSelect = getAjaxComponent(SelectCustom);
const AjaxSelect = getAjaxComponent(Select.View);
const AjaxRadioGroup = getAjaxComponent(RadioGroup.View);
const AjaxCheckBoxGroup = getAjaxComponent(CheckboxGroupCustom);

/**
 * 类别下拉框
 */
class CategoryOption extends Component {
    getCustomCom = () => {
        const { allowValue, multi, treeMode = true } = this.props;

        if (allowValue) {
            if (multi) {
                return AjaxCheckBoxGroup;
            }

            return AjaxRadioGroup;
        }
        if (treeMode) {
            return AjaxTreeSelect;
        }
        if (multi) {
            return AjaxCustomSelect;
        }

        return AjaxSelect;
    };

    getCustomProps = () => {
        const { allowValue, multi, treeMode = true } = this.props;
        const allowValueCount = Array.isArray(allowValue) ? allowValue.length : allowValue ? allowValue.split(',').length : 999;

        if (allowValue) {
            if (multi) {
                return {
                    handlerFn: this.handlerFn,
                    optionsKey: 'options',
                    startAllChecked: allowValueCount > 1,
                    startReverseChecked: allowValueCount > 1,
                };
            }

            return {
                handlerFn: this.handlerFn,
                optionsKey: 'options',
            };
        }
        if (treeMode) {
            return {
                handlerFn: (data) => this.buildTree(this.handlerFn(data)),
                optionsKey: 'treeData',
                checkStrictly: true,
            };
        }

        return {
            handlerFn: this.handlerFn,
            optionsKey: 'options',
        };
    };

    handlerFn = (data) => {
        const { status, type, objectType, allowValue = [], relateTable } = this.props;
        const allowValueArray = Array.isArray(allowValue) ? allowValue : allowValue ? allowValue.split(',') : [];

        return data
            .filter((item) => !status || item.status === status)
            .filter((item) => !type || item.type === type)
            .filter((item) => !objectType || item.objectType === '2' || item.objectType === objectType)
            .filter((item) => allowValueArray.length === 0 || allowValueArray.includes(item.id))
            .filter((item) => !relateTable || item.dataTableId)
            .map((i) => ({
                label: i.name,
                value: i.id || i.value || '',
                // disabled: !i.allowUsed,
                ...i,
            }));
    };

    buildTree = (data) =>
        TreeUtil.arrayToTree2(
            data,
            (item) => !item.pid || data.every((one) => one.id !== item.pid),
            (one, two) => one.id === two.pid,
            (a, b) => (a.sort || 999) - (b.sort || 999),
            (item, children) => ({
                ...item,
                key: item.id,
                title: item.name,
                value: item.id,
                children: children.length > 0 ? [...children] : null,
            })
        );

    render() {
        const { type, allowValue, ...restProps } = this.props;
        const Com = this.getCustomCom();
        const comProps = {
            ...restProps,
            service: categoryApi.allList,
            params: {},
            labelKey: 'name',
            valueKey: 'id',
            ...this.getCustomProps(),
        };

        return <Com {...comProps} />;
    }
}

export default CategoryOption;
