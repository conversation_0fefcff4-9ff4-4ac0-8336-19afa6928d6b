const chnNumChar = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
const chnUnitChar = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿'];

function toChinese(number) {
    let section = Number.parseInt(number) || 0;
    let chnStr = '';
    let unitPos = 0;

    while (section > 0) {
        const v = section % 10;

        if (v === 0) {
            // 数字是0，判定当前不是个位，且前一位是不是零
            if (unitPos !== 0 && !chnStr.startsWith('零')) {
                chnStr = chnNumChar[v] + chnStr;
            }
        } else if (v === 1 && unitPos === 1) {
            // 十位是1
            chnStr = chnUnitChar[unitPos] + chnStr;
        } else {
            chnStr = chnNumChar[v] + chnUnitChar[unitPos] + chnStr;
        }
        unitPos += 1;
        section = Math.floor(section / 10);
    }

    return chnStr || '零';
}

module.exports = {
    toChinese,
};
