/*
 *@(#) useYearOption.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2024
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2024-01-26
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import { useService } from '@share/framework';
import { useMount } from 'ahooks';
import { useState } from 'react';
import EconomicSpatialDistributionService from '@/services/EconomicSpatialDistributionService';

const useYearOption = (props) => {
    const { defaultFirst = true } = props || {};

    const services = useService(EconomicSpatialDistributionService);
    const [yearOptions, setYearOptions] = useState([]);
    const [curYear, setCurYear] = useState('');

    const getYearOption = async () => {
        const key = 'yearOptions';
        const sessionValue = window.sessionStorage.getItem(key);
        let yearOptionsValue = null;

        if (sessionValue) {
            yearOptionsValue = JSON.parse(sessionValue);
        } else {
            const res = await services.getYearOption();

            window.sessionStorage.setItem(key, JSON.stringify(res));
            yearOptionsValue = res;
        }

        setYearOptions(
            yearOptionsValue.map((v) => ({
                label: `${v}年`,
                value: v,
            }))
        );

        // 没有全部，默认选第一个
        if (defaultFirst) {
            setCurYear(yearOptionsValue[0]);
        }
    };

    const yearOnChange = (v, onChange) => {
        setCurYear(v);
        if (onChange) {
            onChange(v);
        }
    };

    useMount(() => {
        getYearOption();
    });

    return {
        yearOptions,
        yearOnChange,
        curYear,
    };
};

export default useYearOption;
