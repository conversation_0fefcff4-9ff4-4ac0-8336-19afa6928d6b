import React, { Component, Fragment } from 'react';
// 接口
import * as DataTrcApi from '@/services/data/data/DataTrcApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 组件
import RoleAuthControl from '@/components/business/auth/RoleAuthControl';
import MultiClamp from '@/components/ui/MultiClamp';
import QueryList from '@/components/business/metadata/QueryList';
import { Panel } from '@share/shareui';

const ywTypeOptions = [
    { label: '新增', value: '1' },
    { label: '修改', value: '2' },
    { label: '修复', value: '3' },
    { label: '恢复', value: '4' },
];

const collTypeOptions = [
    { label: '页面填报', value: '1' },
    { label: 'Excel导入', value: '2' },
    { label: '接口填报', value: '4' },
    { label: '数据库对接', value: '3' },
    { label: '系统定时任务', value: '5' },
];

class DataTraceabilityTable extends Component {
    // 数据列表请求接口
    buildDataListApi = (searchBody) => {
        const { categoryCode } = this.props;

        return DataTrcApi.listByCategory(categoryCode, '2', searchBody);
    };

    // 列表展示列
    extendHeadColumns = () => {
        return [
            {
                title: '信息类别',
                width: 200,
                // fixed: 'left',
                dataIndex: ['system', 'CATEGORY_NAME'],
                render: (value) => <MultiClamp title={value}>{value}</MultiClamp>,
            },
        ];
    };

    extendTailColumns = () => {
        const { history, metaConfigList } = this.props;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);

        return [
            {
                title: '操作类型',
                width: 75,
                fixed: 'right',
                dataIndex: ['system', 'YW_TYPE'],
                render: (value) => (ywTypeOptions.find((item) => item.value === value) || {}).label,
            },
            {
                title: '填报方式',
                width: 80,
                fixed: 'right',
                dataIndex: ['system', 'COLL_TYPE'],
                render: (value) => (collTypeOptions.find((item) => item.value === value) || {}).label,
            },
            {
                title: '操作',
                width: 115,
                fixed: 'right',
                key: 'operate',
                dataIndex: 'system',
                render: (rowData) => (
                    <div className="tableBtn">
                        <a onClick={() => history.push(`/DataTraceabilityDetail/${rowData.CATEGORY_CODE}/${rowData[primaryKey]}`)}>详情</a>
                        {/* Excel填报 */}
                        {rowData.COLL_TYPE === '2' && rowData.TRACE_ID && (
                            <RoleAuthControl buttonKey="meta-data-trc-excel-export">
                                <a
                                    onClick={() => {
                                        DataTrcApi.exportTrcFile(rowData.TRACE_ID);
                                    }}
                                >
                                    原始Excel
                                </a>
                            </RoleAuthControl>
                        )}
                        {/* 接口填报 */}
                        {rowData.COLL_TYPE === '4' && rowData.TRACE_ID && (
                            <RoleAuthControl buttonKey="meta-data-trc-json-export">
                                <a
                                    onClick={() => {
                                        DataTrcApi.exportTrcFile(rowData.TRACE_ID);
                                    }}
                                >
                                    原始报文
                                </a>
                            </RoleAuthControl>
                        )}
                    </div>
                ),
            },
        ];
    };

    render() {
        const { categoryCode, categoryCodeList, metaConfigList, body, ...otherProps } = this.props;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);
        const listConfig = MetaConfigUtils.filterListConfig(metaConfigList);

        return (
            <Panel>
                <Panel.Head title="数据溯源" />
                <Panel.Body full>
                    <QueryList
                        namespace="TraceabilityList"
                        service={{
                            api: this.buildDataListApi,
                            body,
                        }}
                        rowKey={(data) => data.system[primaryKey]}
                        metadataConfigList={listConfig}
                        extendHeadColumns={this.extendHeadColumns()}
                        extendTailColumns={this.extendTailColumns()}
                        pagination={{ detectPage: true, showTotal: true }}
                        {...otherProps}
                    />
                </Panel.Body>
            </Panel>
        );
    }
}

export default DataTraceabilityTable;
