import React, { Component } from 'react';
// 样式
import styles from '@/pages/metaData/styles/index.scss';
// 列表组件
import { Form, Modal, Button } from '@share/shareui';

const componentTypeMap = {
    editParam: '编辑组件',
    queryParam: '查询组件',
    listParam: '列表组件',
    detailParam: '详情组件',
};

class FieldComponentDetail extends Component {
    buildComponentParamFormShow = (componentId, componentParam) => {
        const { data: { detail, fieldComponentList } = {} } = this.props;
        const paramRuleKey = detail.componentType.replace(/^(.*)Param$/, '$1ParamRule');
        const component = fieldComponentList.find((item) => item.componentId === componentId) || {};
        const componentParamRule = component[paramRuleKey] || [];

        return componentParamRule.map((item) => {
            let value = componentParam[item.key];

            if (item.valueType === 'boolean') {
                value = value ? '是' : '否';
            } else if (item.valueType === 'bm' && Array.isArray(item.options)) {
                value = (item.options.find((one) => one.value === value) || {}).label || value;
            } else if (item.valueType === 'array') {
                value = (value || []).map((valueItem) => (
                    <div>
                        {item.children
                            .map((node) => {
                                let childrenValue = valueItem[node.key];

                                if (node.valueType === 'boolean') {
                                    childrenValue = childrenValue ? '是' : '否';
                                } else if (node.valueType === 'bm' && Array.isArray(node.options)) {
                                    childrenValue = (node.options.find((one) => one.value === childrenValue) || {}).label || childrenValue;
                                }

                                return `${node.name}(${childrenValue})`;
                            })
                            .join('、')}
                    </div>
                ));
            }

            return (
                <Form.Tr>
                    <Form.Label>{item.name}</Form.Label>
                    <Form.Content>
                        <span className="textShow" title={value}>
                            {value}
                        </span>
                    </Form.Content>
                </Form.Tr>
            );
        });
    };

    render() {
        const {
            data: { detail, fieldComponentList },
            show,
            cancelFn,
        } = this.props;
        const { fieldCode, componentType, componentId, defaultValue, tip, componentParam = {} } = detail;
        const componentName = (fieldComponentList.find((item) => item.componentId === componentId) || {}).componentName || '';

        return (
            <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={cancelFn} backdrop="static">
                <Modal.Header closeButton>组件详情</Modal.Header>
                <Modal.Body>
                    <Form pageType="detailPage">
                        <Form.Table>
                            <Form.Tr>
                                <Form.Label>组件字段</Form.Label>
                                <Form.Content>
                                    <span className="textShow">{fieldCode}</span>
                                </Form.Content>
                            </Form.Tr>
                            <Form.Tr>
                                <Form.Label>组件类型</Form.Label>
                                <Form.Content>
                                    <span className="textShow">{componentTypeMap[componentType]}</span>
                                </Form.Content>
                            </Form.Tr>
                            <Form.Tr>
                                <Form.Label>组件名称</Form.Label>
                                <Form.Content>
                                    <span className="textShow" title={componentName}>
                                        {componentName}
                                    </span>
                                </Form.Content>
                            </Form.Tr>
                            <Form.Tr>
                                <Form.Label>默认值</Form.Label>
                                <Form.Content>
                                    <span className="textShow" title={defaultValue}>
                                        {defaultValue}
                                    </span>
                                </Form.Content>
                            </Form.Tr>
                            {componentId && componentType.includes('edit') && (
                                <Form.Tr>
                                    <Form.Label>输入内容示例</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={tip}>
                                            {tip}
                                        </span>
                                    </Form.Content>
                                </Form.Tr>
                            )}
                            {componentId && this.buildComponentParamFormShow(componentId, componentParam)}
                        </Form.Table>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default FieldComponentDetail;
