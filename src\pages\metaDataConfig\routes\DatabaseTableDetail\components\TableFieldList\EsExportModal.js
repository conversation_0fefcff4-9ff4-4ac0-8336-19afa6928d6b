import React, { useState } from 'react';
import dayjs from 'dayjs';
import { <PERSON><PERSON>, Modal } from '@share/shareui';
import {
    useForm,
    TableForm,
    Row,
    // 表单项组件
    Input,
    RadioGroup,
} from '@share/shareui-form';
import * as formRule from '@/utils/formRule';
import { useUpdateEffect } from 'ahooks';
import { useServiceMap } from '../../hooks';

const defaultBody = {
    tableIds: [],
    esIndex: '',
    esAlias: '',
    version: dayjs().format('YYYYMMDD'),
    relateCatalog: true,
    generateKeyword: true,
    generateTableName: true,
    humpHump: false,
};

const booleanOptions = [
    { label: '是', value: true },
    { label: '否', value: false },
];
const EsExportModal = (props) => {
    const {
        show,
        data: { tableIds, esAlias },
        successFn,
        cancelFn,
    } = props;

    const [, form] = useForm({ ...defaultBody });
    const { metaFieldApi } = useServiceMap();

    const handleCancel = () => {
        // eslint-disable-next-line no-unused-expressions
        cancelFn && cancelFn();
    };
    const submit = async () => {
        const data = form.getFormData();

        if (!(await form.validHelper())) {
            return;
        }
        metaFieldApi.exportEsFile(data);

        // eslint-disable-next-line no-unused-expressions
        successFn && successFn();
        handleCancel();
    };

    useUpdateEffect(() => {
        form.setFormData({ ...defaultBody, tableIds, esAlias, esIndex: `${esAlias}_v${defaultBody.version}` });
        form.cleanValidError();
    }, [show]);

    return (
        <Modal className="modal-full" show={show} onHide={handleCancel} bsSize="large" backdrop="static">
            <Modal.Header closeButton>导出ES配置</Modal.Header>
            <Modal.Body>
                <TableForm pageType="addPage" formState={form}>
                    <Row>
                        <Input label="索引名称" field="esIndex" disabled required />
                    </Row>
                    <Row>
                        <Input label="索引别名" field="esAlias" disabled required />
                    </Row>
                    <Row>
                        <Input
                            label="版本号"
                            field="version"
                            rule={[
                                formRule.checkRequiredNotBlank(),
                                formRule.checkFunction((value) => /^[a-z0-9\\-]+$/.test(value), '仅能“数字”、“小写字母”、“-”组成'),
                            ]}
                            required
                            onChange={({ target: { value } }) => form.setFieldValue('esIndex', `${esAlias}_v${value}`)}
                        />
                    </Row>
                    <Row>
                        <RadioGroup
                            label="关联目录"
                            field="relateCatalog"
                            options={booleanOptions}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                    <Row>
                        <RadioGroup
                            label="生成关键字"
                            field="generateKeyword"
                            options={booleanOptions}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                    <Row>
                        <RadioGroup
                            label="生成表名"
                            field="generateTableName"
                            options={booleanOptions}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                </TableForm>
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={handleCancel}>关闭</Button>

                <Button bsStyle="primary" onClick={submit}>
                    应用
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default EsExportModal;
