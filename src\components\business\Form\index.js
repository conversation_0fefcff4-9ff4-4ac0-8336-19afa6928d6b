// 表单组件
import { FormState, Timing, registerComponent, getComponents } from '@share/shareui-form';
import { ShareForm, CacheShareForm, StoreShareForm } from './Form';

import Text from './Text';
// 输入组件
import { SearchInput as SearchInputView, MultiInput as MultiInputView, AddonInput as AddonInputView } from './Input';
// 下拉组件
import {
    Select as SelectView,
    CreatableSelect as CreatableSelectView,
    MultiSelectCustomEdit as MultiSelectCustomEditView,
    DoubleSelect as DoubleSelectView,
    TreeSelect as TreeSelectView,
    AsyncSelect as AsyncSelectView,
    TreeSelectAntd as TreeSelectAntdView,
    AutoAreaSelect as AutoAreaSelectView,
    AreaSelect as AreaSelectView,
    AreaCustomizedSelect as AreaCustomizedSelectView,
    CitySelect as CitySelectView,
    ProvinceSelect as ProvinceSelectView,
    CommonDeptSelect as CommonDeptSelectView,
    CommonDeptSelectCustom as CommonDeptSelectCustomView,
    AreaDeptSelect as AreaDeptSelectView,
    TreeDeptSelect as TreeDeptSelectView,
    DeptSelect as DeptSelectView,
    CategoryOption as CategoryOptionView,
} from './Select';
// 多选框组件
import { CheckboxGroup as CheckboxGroupView, CheckboxGroupCustom as CheckboxGroupCustomView } from './Checkbox';
// 时间组件
import {
    Date as DateView,
    Time as TimeView,
    RangeDate as RangeDateView,
    RangeTime as RangeTimeView,
    RangeDateCustom as RangeDateCustomView,
    TimePicker as TimePickerView,
} from './Calendar';
// 上传组件
import { FileUpload as FileUploadView, ImageUpload as ImageUploadView } from './Upload';
// 开关组件
import { Switch as SwitchView } from './Switch';
// 评分组件
import { Rate as RateView } from './Rate';
// 富文本框组件
// import { RichText as RichTextView } from './RichText';
// 数组组件
import { ArrayFormItem as ArrayFormItemView } from './Array';
// 组合组件
import {
    Tip, // 提示组件
    getComposeCustom, // 定制组合组件高阶函数
    getAdaptComponent, // 适配组件高阶函数
    getBmComponent, // 表码组件高阶函数
    getAjaxComponent, // Ajax组件高阶函数
    getUserComponent, // 用户组件高阶函数
    getDefaultValueComponent, // 默认值组件高阶函数
    getFocusBlurComponent, // 焦点托管组件高阶函数
} from './Custom';

const { RadioGroup } = getComponents();

// 统一注册自定义组件
const registerCustomizedComponent = () => {
    // 普通组件
    registerComponent('SearchInput', SearchInputView); // 搜索输入框
    registerComponent('MultiInput', MultiInputView); // 多文本输入框
    registerComponent('AddonInput', AddonInputView); // 多文本输入框
    registerComponent('Text', Text); // 详情展示组件
    registerComponent('Select', getBmComponent(SelectView)); // 下拉框（功能增强）
    registerComponent('AsyncSelect', AsyncSelectView); // 异步下拉框
    registerComponent('SelectCustom', getBmComponent(SelectView, { custom: true })); // 下拉框（功能增强）
    registerComponent('CreatableSelect', getBmComponent(CreatableSelectView, {})); // 可自定义下拉框
    registerComponent('MultiSelect', getBmComponent(SelectView, { multi: true })); // 多选下拉框
    registerComponent(
        'MultiSelectSimpleCustom',
        getBmComponent(SelectView, { multi: true, custom: true, searchAble: false, controlAble: false })
    ); // 多选下拉框（简单功能增强）
    registerComponent('MultiSelectCustom', getBmComponent(SelectView, { multi: true, custom: true })); // 多选下拉框（功能增强）
    registerComponent('MultiSelectCustomEdit', MultiSelectCustomEditView); // 多选下拉框（功能增强，编辑类型）
    registerComponent('DoubleSelect', DoubleSelectView); // 双下拉框
    registerComponent('TreeSelect', TreeSelectView); // 树状下拉框
    registerComponent('TreeSelectAntd', TreeSelectAntdView); // 树状下拉框-antd
    registerComponent('RadioGroup', getBmComponent(RadioGroup.View)); // 码表radio选择框
    registerComponent('CheckboxGroup', getBmComponent(CheckboxGroupView)); // checkbox选择框（功能增强）
    registerComponent('CheckboxGroupCustom', getBmComponent(CheckboxGroupCustomView)); // checkbox选择框（功能增强）
    registerComponent('Date', DateView); // 日期（天）
    registerComponent('Time', TimeView); // 时间（秒）
    registerComponent('TimePicker', TimePickerView); // 时间（秒）
    registerComponent('RangeDate', RangeDateView); // 范围日期
    registerComponent('RangeDateCustom', RangeDateCustomView); // 范围日期
    registerComponent('RangeTime', RangeTimeView); // 范围时间
    registerComponent('FileUpload', FileUploadView); // 文件上传
    registerComponent('ImageUpload', ImageUploadView); // 图片上传
    registerComponent('Switch', SwitchView); // 开关
    registerComponent('RateCustom', RateView); // 评级
    // registerComponent('Json', JsonView); // json编辑
    // registerComponent('RichText', RichTextView); // 富文本框
    // 数组组件
    registerComponent('ArrayFormItem', ArrayFormItemView);
    // 表码数据源组件
    registerComponent('AutoAreaSelect', AutoAreaSelectView); // 自动区域（码表）下拉框
    registerComponent('ProvinceSelect', ProvinceSelectView); // 省份（码表）下拉框
    registerComponent('CitySelect', CitySelectView); // 城市（码表）下拉框
    registerComponent('AreaSelect', AreaSelectView); // 区域（码表）下拉框
    registerComponent('AreaCustomizedSelect', AreaCustomizedSelectView); // 定制区域（码表）三下拉框
    // 缓存数据源组件
    registerComponent('CommonDeptSelect', CommonDeptSelectView); // 普通部门（ajax缓存）下拉框
    registerComponent('CommonDeptSelectCustom', CommonDeptSelectCustomView); // 普通部门（ajax缓存）下拉框（区域过滤栏）
    registerComponent('AreaDeptSelect', AreaDeptSelectView); // 区域部门（ajax缓存）下拉框（树状）
    registerComponent('TreeDeptSelect', TreeDeptSelectView); // 树状部门（ajax缓存）下拉框（树状）
    registerComponent('DeptSelect', DeptSelectView); // 动态部门下拉（根据配置自动选择组件）
    registerComponent(
        'AutoDeptSelect',
        getDefaultValueComponent(DeptSelectView, {
            userManageFilter: true,
            onlyValueAutoCheckedAlways: true,
            onlyValueAutoDisabled: true,
        })
    ); // 自动部门（ajax缓存）下拉框
    // Ajax数据源组件
    registerComponent('AjaxRadioGroup', getAjaxComponent(RadioGroup.View)); // ajax请求下拉框
    registerComponent('AjaxSelect', getAjaxComponent(SelectView)); // ajax请求下拉框
    registerComponent('CategoryOption', CategoryOptionView); // 信用类别ajax请求下拉框
};

registerCustomizedComponent();

// 暴露封装后shareui-form
export {
    FormState,
    Timing,
    registerComponent,
    getComponents,
    ShareForm,
    CacheShareForm,
    StoreShareForm,
    Tip,
    getComposeCustom,
    getAdaptComponent,
    getBmComponent,
    getAjaxComponent,
    getUserComponent,
    getDefaultValueComponent,
    getFocusBlurComponent,
};
