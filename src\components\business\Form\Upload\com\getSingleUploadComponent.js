import React from 'react';

const getSingleUploadComponent = (Component) => {
    return class extends React.Component {
        static defaultProps = {
            value: '', // 上传文件地址
            filedName: '附件', // 上传文件名称
        };

        constructor(props) {
            super(props);
            const { value } = props;
            const fileList = this.convertToFileList(value);

            this.state = { fileList };
        }

        componentWillReceiveProps(nextProps) {
            const { fileList } = this.state;
            const url = Array.isArray(fileList) && fileList.length > 0 && fileList[0].url ? fileList[0].url : '';
            const newUrl = nextProps.value || '';

            if (newUrl !== url) {
                const newFileList = this.convertToFileList(newUrl);

                this.setState({ fileList: newFileList });
            }
        }

        onChange = ({ target: { value } }) => {
            const { onChange } = this.props;
            const file = Array.isArray(value) && value.length > 0 ? value[0] : {};

            this.setState({ fileList: value }, () => {
                if (file.status !== 'uploading') {
                    onChange && onChange({ target: { value: file.url || '' } });
                }
            });
        };

        convertToFileList = (url) => {
            const { filedName } = this.props;

            return url ? [{ name: filedName, url }] : [];
        };

        render() {
            const { fileList } = this.state;
            const { singleValue, value, onChange, ...restProps } = this.props;

            return <Component {...restProps} fileMaxNum={1} multiple={false} value={fileList} onChange={this.onChange} />;
        }
    };
};

export default getSingleUploadComponent;
