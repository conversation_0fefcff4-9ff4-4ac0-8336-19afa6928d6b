.arrayFormItem{
    :global {
        .item .sub-item {
            padding: 10px !important;
        }
    }
    table {
        border: 1px solid #ddd;
    }
    i {
        cursor: pointer;
    }
    &-content {
        display: flex;
        align-items: center;
        &_children {
            width: calc(100% - 56px);
            display: flex;
            align-items: center;
            >* {
                flex: 1;
            }
        }
        &_btns {
            width: 56px ;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            i {
                font-size: 20px;
                margin-left: 8px;
                &.add {
                    color: #01ba88;
                }
                &.delete {
                    color: #f65;
                }
                &.up,&.down {
                    margin-top: 8px;
                    color: #09d;
                }
        }

        }
    }
    &-addBtn {
        font-size: 22px;
        color:#01ba88;
    }
}
