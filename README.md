# 项目脚手架 application-v1

基于@share/framework@1及@share/kit@2搭建的PC应用脚手架

application脚手架在share-kit的基础上进一步做了一层封装，提供了application.js的配置文件.

## 环境
* node >= 16.22.0，建议通过nvm管理node版本
* yarn 1.22.19 建议通过msi安装，如果是通过npm安装，在nvm切换版本后需求重新安装
* npm >= 8 node16默认绑定的版本

## 目录结构说明

```
+-- bin      脚本目录，请不要删除，不会出现在编译结果中
+-- public   前端非编译资源目录，该目录下文件不会经过编译，直接拷贝至编译目录
+-- src
    +-- assets     前端资源目录
    +-- components 公共组件
    +-- framework  项目@share/framework公共配置
            +-- authExpression.js 权限表达式定义
            +-- code.js           前端表码配置
            +-- config.js         前端配置
            +-- constant.js       常量定义
            +-- global.scss       全局样式
            +-- mount.js          框架入口
    +-- pages html页面目录 
        +-- example         无需额外配置，页面目录编译后为/example.html
            +-- index.html  html页面模板
            +-- index.js    入口文件
            +-- router.js   每个html页面的路由定义文件
            +-- routes      单页目录
                +-- List    列表页单页组件目录
                +-- Detail  详细页单页组件目录
    +-- services   后端接口层
    +-- utils      工具定义
+-- application.js 项目配置
+-- webpack.config.js share-kit编译工具配置
```

## 控制台命令

yarn start 启动前端开发服务器

yarn build 编译

## 主要配置文件

* application.js 
    * port 前端开发服务器端口
    * scurd 是否启用pc版在线表单
    * mscurd 是否启用mobile版在线表单
    * context 前端开发服务器文根 /开头,未配置的话将使用动态文根
    * development 服务端配置，用于代理ajax请求到指定服务端
        * server 服务端ip或域名
        * port 服务端端口
        * context 服务端文根，需要/开头
    * yapi yapi服务器配置，用于代理ajax请求到yapi模拟服务，
        * server yapi服务器，不需要修改
        * port yapi服务器端口，不需要修改
        * projectId yapi中的项目ID
* webpack.config.js
    * 如要修改share-kit配置，**请不要修改原有逻辑**

更多配置的使用信息详见[@share/kit文档](http://*************:4000/share-framework/kit)

## 主要约定

### 动态文根
* 未配置context的情况下将使用动态文根，相关的静态资源路径将会使用相对路径
* 代码中可通过window.SHARE.CONTEXT_PATH获取文根，或者使用@share/framework的getContextPath和getAbsoluteContextPath方法

### 服务代理与mock
默认对.do及remote.action结尾的请求做了后端代理

* 如果前后服务器的context不一致，默认会重写cookie路径
* 全局模拟服务：如果application.js中配置了mock:true，全局的.do请求会被代理到yapi
* 单接口模拟服务：如果接口请求中的headers设置了mock，也会被代理到yapi
* 该规则外的代理配置，参考share-kit文档

### 技术栈

* 对接了@share/framework
* 使用基于sass的css module，防止样式冲突
* 使用hash路由
* 使用yarn进行包管理，只有yarn才支持覆盖嵌套依赖项版本，同时能保证依赖的一致性

### 组件目录结构
* 组件放在components目录
* 组件由组件目录，入口文件，组件文件，组件样式组成，组件的子组件继续创建components目录嵌套
    示例，一个Head组件结构
    ```
       +-- components
           +-- Head 组件目录
               +-- index.js  入口文件
               +-- Head.js  组件文件
               +-- Head.scss 组件样式
               +-- components 子组件目录
                   +-- HeadLeft 
                       +-- index.js  入口文件
                       +-- HeadLeft.js  组件文件
                       +-- HeadLeft.scss 组件样式
    ```
  
### 页面目录结构
* 页面放在pages中
* 一个文件夹代表一个html页面，对应的访问地址为 http://{server}:{port}/{目录名}.html 
* pages目录中在标准的页面结构下，无需额外配置，会自动编译html页面
* 一个页面由页面目录，入口文件，路由文件，模板页面，单页目录及n个单页组件组成（单页组件目录参考组件目录）
    
    示例, example页面结构
    ```
       +-- pages 
           +-- example 页面目录编译后为/example.html
               +-- index.js 入口文件
               +-- router.js 每个html页面的路由定义文件
               +-- routes 单页目录
                   +-- List 列表页单页组件目录
                   +-- Detail 详细页单页组件目录
    ```
  
## tips
* 新建组件和页面如果觉得繁琐，可以通过idea的【文件和代码模板】配置模板，只有新版才支持子模板。
* 更多说明参考[在线文档](http://*************:4000/share-framework)，主要是编译工具和前端框架部分
