/*
 * @(#) index.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-11-04 14:40:43
 */

// shareui
import 'bootstrap/dist/css/bootstrap.min.css';
import 'font-awesome/css/font-awesome.min.css'; // shareui-font版本迁移完成之后删除该依赖
import '@share/shareui-html';
import '@share/shareui-html/dist/patch.css';
import '@share/shareui-font';
import '@/styles/reset.scss';

import { createApplication } from '@share/framework';
import { PresetShareui } from '@share/framework-preset-shareui';
import { routes } from './framework.router';

createApplication({
    appId: 'economicbrain-enterpriseArchives', // 必填
    presets: [
        PresetShareui({
            router: {
                routes,
            },
            code: {
                init: {},
            },
            watermark: false,
            // config: false,
            config: {
                init: {},
            },
        }),
    ], // 预设插件
    plugins: [
        // RabbitPlugin({
        //     systemId: 'system'
        // }),
        // TenantPlugin({
        //     systemId: 'system'
        // }),
    ], // 自定义插件
});
