/**  版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 *  @Copyright:  Copyright (c) 2020
 *  @Company:厦门畅享信息技术有限公司
 *  @Author: 李家其
 *  Date: 2023/11/24 18:38
 */
export const usePayTaxesTrendsHandle = ({ czsrLineData, czzcLineData, fczcLineData, xAxisData }) => {
    const lineDataMax = Math.ceil(Math.max.apply(null, [...czsrLineData, ...czzcLineData, ...fczcLineData]) / 10) * 10; // 最大值向上取整十位数

    const option = {
        color: ['#6390F6', '#00FF00', '#FFAA33'],
        // tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //         type: 'cross',
        //         crossStyle: {
        //             color: '#999',
        //         },
        //     },
        // },
        tooltip: {
            trigger: 'axis',
            formatter(params) {
                let result = `${params[0].axisValue}<br/>`;
                params.forEach((item) => {
                    result += `${item.marker} ${item.seriesName}: ${item.value}<br/>`;
                });

                return result;
            },
        },
        legend: {
            data: [
                {
                    name: '税收收入总额',
                },
                {
                    name: '业务支出总额',
                },
                {
                    name: '企业扶持支出总额',
                },
            ],
            bottom: 0,
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '7%',
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData,
        },
        // xAxis: [
        //     {
        //         type: 'category',
        //         data: xAxisData,
        //         // axisPointer: {
        //         //     type: 'shadow',
        //         // },
        //         axisLine: {
        //             lineStyle: {
        //                 color: '#ddd',
        //                 type: 'dashed',
        //             },
        //         },
        //         axisTick: {
        //             show: false,
        //         },
        //         // axisLabel: {
        //         //     show: true,
        //         //     textStyle: {
        //         //         color: '#74767A',
        //         //     },
        //         // },
        //     },
        // ],
        yAxis: [
            {
                type: 'value',
                name: '（元）',
                nameTextStyle: {
                    align: 'left',
                },
                min: 0,
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: 'dashed',
                        // color: '#74767A',
                    },
                },
                max: lineDataMax,
                interval: lineDataMax / 5,
                textStyle: { color: '#74767A' },
            },
        ],
        series: [
            {
                name: '税收收入总额',
                type: 'line',
                tooltip: {
                    valueFormatter(value) {
                        return `${value} 元`;
                    },
                },
                data: czsrLineData,
            },
            {
                name: '业务支出总额',
                type: 'line',
                tooltip: {
                    valueFormatter(value) {
                        return `${value} 元`;
                    },
                },
                data: czzcLineData,
            },
            {
                name: '企业扶持支出总额',
                type: 'line',
                tooltip: {
                    valueFormatter(value) {
                        return `${value} 元`;
                    },
                },
                data: fczcLineData,
            },
        ],
    };

    return {
        option,
    };
};
