export const convertMap = (options = []) => {
    return options.reduce((r, i) => {
        r[i.value || i.code] = i[i.label || i.name];

        return r;
    }, {});
};

export const convertLabel = (options = [], value, def = '') => {
    const i = options.find((item) => item.value === value || item.code === value);

    return i?.label || i?.name || def;
};

export const convertValue = (options = [], label, def = '') => {
    const i = options.find((item) => item.label === label || item.name === label);

    return i?.value || i?.code || def;
};
