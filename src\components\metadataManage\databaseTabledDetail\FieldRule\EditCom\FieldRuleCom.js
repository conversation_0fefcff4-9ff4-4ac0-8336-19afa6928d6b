import React, { Component, Fragment } from 'react';
// 工具类
import * as formRule from '@/components/common/common/formRule';
import { getComponents } from '@/components/business/Form';
import BmOptionsFormItem from './FormItem/BmOptionsFormItem';
import PreConditionsFormItem from './FormItem/PreConditionsFormItem';
import CombinationFormItem from './FormItem/CombinationFormItem';

const { Row, Input, Textarea, Select, CreatableSelect, RadioGroup, CheckboxGroupCustom } = getComponents();

class FieldRuleCom extends Component {
    render() {
        const { editForm, keyPrefix = '', ruleOptional, bmTableOption, metaFieldList, onRuleIdChange } = this.props;
        const rule = keyPrefix ? editForm.getFieldValue(keyPrefix.substring(0, keyPrefix.length - 1)) : editForm.getFormData();
        const ruleOptions = ruleOptional.map((item) => ({ label: item.ruleName, value: item.ruleId }));
        const fieldOptions = metaFieldList.map((item) => ({
            label: `字段：${item.fieldCode}（${item.showLabel}）`,
            value: item.fieldCode,
        }));
        const fieldsOptions = [...fieldOptions];

        if (rule.ruleId) {
            if (['compareCheck', 'diffValueCheck'].includes(rule.ruleId)) {
                fieldsOptions.unshift({ label: '变量：填报当天', value: 'currentDate' });
            }
            if (['batchOnlyCheck', 'dbOnlyCheck', 'dbRecordCountCheck'].includes(rule.ruleId)) {
                fieldsOptions.unshift({ label: '变量：重复字段', value: 'identityFieldCode' });
                fieldsOptions.unshift({ label: '变量：编辑字段', value: 'editFieldCode' });
            }
            const targetRule = ruleOptional.find((item) => item.ruleId === rule.ruleId) || {};

            rule.ruleParamFillKeys = Array.isArray(targetRule.ruleParamFillKeys) ? targetRule.ruleParamFillKeys : [];
            if (Array.isArray(rule.ruleParam.checkFields) && rule.ruleParam.checkFields.length > 0) {
                const extendOptions = rule.ruleParam.checkFields
                    .filter((item) => fieldsOptions.every((one) => one.value !== item))
                    .map((item) => ({ label: `常量：${item}`, value: item }));

                fieldsOptions.push(...extendOptions);
            }
        }
        const parentKey = keyPrefix.replace(/^(.*?)(\.)?ruleParam.combinationRule\.\d+\.$/, '$1');
        const parentRule = !keyPrefix ? null : parentKey ? editForm.getFieldValue(parentKey) : editForm.getFormData();

        return (
            <Fragment>
                <Row>
                    <Select
                        label="规则名称"
                        field={`${keyPrefix}ruleId`}
                        options={ruleOptions}
                        onChange={({ target: { value } }) => onRuleIdChange(value, keyPrefix)}
                        rule={[formRule.checkRequiredNotBlank()]}
                        required
                    />
                </Row>
                {rule.ruleId && rule.ruleParamFillKeys.includes('describe') && (
                    <Row>
                        <Textarea
                            label="规则描述"
                            field={`${keyPrefix}ruleParam.describe`}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('preConditions') && (
                    <Row>
                        <PreConditionsFormItem label="前置条件" field={`${keyPrefix}ruleParam.preConditions`} options={fieldOptions} />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('bmName') && (
                    <Row>
                        <Select
                            label="系统码表"
                            field={`${keyPrefix}ruleParam.bmName`}
                            options={bmTableOption}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('bmOptions') && (
                    <Row>
                        <BmOptionsFormItem label="码表配置" field={`${keyPrefix}ruleParam.bmOptions`} />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('checkField') && (
                    <Row>
                        <Select
                            label="检验字段"
                            field={`${keyPrefix}ruleParam.checkField`}
                            options={[{ label: '当前字段', value: '' }, ...fieldOptions]}
                            placeholder="默认当前字段"
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('checkFields') && (
                    <Row>
                        <CreatableSelect
                            label="检验字段"
                            field={`${keyPrefix}ruleParam.checkFields`}
                            multi
                            options={fieldsOptions}
                            rule={
                                rule.ruleId === 'diffValueCheck'
                                    ? formRule.checkArray(2, 2)
                                    : rule.ruleId === 'comparisonCheck' ||
                                      rule.ruleId === 'batchOnlyCheck' ||
                                      rule.ruleId === 'dbOnlyCheck' ||
                                      rule.ruleId === 'dbRecordCountCheck'
                                    ? formRule.checkArray(1)
                                    : formRule.checkArray(2)
                            }
                            onChange={({ target: { value } }) =>
                                editForm.setFieldValue(
                                    `${keyPrefix}ruleParam.checkFields`,
                                    value.map((item) => item.value)
                                )
                            }
                            required
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('comparisonSource') && (
                    <Row>
                        <Select
                            label="比对源头"
                            field={`${keyPrefix}ruleParam.comparisonSource`}
                            options={[
                                { label: '名称查询统一社会信用代码国家接口', value: 'countryShxymComparisonSource' },
                                { label: '厦门-内网名称、统一社会信用代码比对接口', value: 'xmIntranetShxydmComparisonSource' },
                            ]}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('extendSql') && (
                    <Row>
                        <Input label="扩展SQL" field={`${keyPrefix}ruleParam.extendSql`} />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('databaseTable') && (
                    <Row>
                        <Input
                            label="匹配表名"
                            field={`${keyPrefix}ruleParam.databaseTable`}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('matchRegex') && (
                    <Row>
                        <Input
                            label="匹配正则"
                            field={`${keyPrefix}ruleParam.matchRegex`}
                            rule={[formRule.checkLength(1)]}
                            autoTrim={false}
                            required
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('convertRegex') && (
                    <Row>
                        <Input label="转换正则" field={`${keyPrefix}ruleParam.convertRegex`} autoTrim={false} />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('ignoreCase') && (
                    <Row>
                        <RadioGroup
                            field={`${keyPrefix}ruleParam.ignoreCase`}
                            label="匹配敏感"
                            options={[
                                { label: '区分大小写', value: false },
                                { label: '忽略大小写', value: true },
                            ]}
                            required
                            rule={[formRule.checkRequiredNotBlank()]}
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('compareSymbols') && (
                    <Row>
                        <CheckboxGroupCustom
                            label="匹配类型"
                            field={`${keyPrefix}ruleParam.compareSymbols`}
                            options={[
                                { label: '>', value: 'gt' },
                                { label: '<', value: 'lt' },
                                { label: '=', value: 'eq' },
                            ]}
                            rule={[formRule.checkArray(1)]}
                            required
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('number') && (
                    <Row>
                        <Input
                            label="匹配数值"
                            field={`${keyPrefix}ruleParam.number`}
                            type="number"
                            rule={[formRule.checkRequiredNotBlank(), formRule.checkIsNum()]}
                            required
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('unit') && (
                    <Row>
                        <RadioGroup
                            label="匹配单位"
                            field={`${keyPrefix}ruleParam.unit`}
                            options={[
                                { label: '数字', value: 'number' },
                                { label: '年', value: 'year' },
                                { label: '月', value: 'month' },
                                { label: '天', value: 'day' },
                            ]}
                            required
                            rule={[formRule.checkRequiredNotBlank()]}
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('combinationLogic') && (
                    <Row>
                        <RadioGroup
                            label="组合逻辑"
                            field={`${keyPrefix}ruleParam.combinationLogic`}
                            options={[
                                { label: '同时满足', value: 'and' },
                                { label: '单个满足', value: 'or' },
                            ]}
                            required
                            rule={[formRule.checkRequiredNotBlank()]}
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('combinationRule') && (
                    <Row>
                        <CombinationFormItem
                            label="组合规则"
                            field={`${keyPrefix}ruleParam.combinationRule`}
                            editForm={editForm}
                            ruleOptional={ruleOptional}
                            bmTableOption={bmTableOption}
                            metaFieldList={metaFieldList}
                            onRuleIdChange={onRuleIdChange}
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('checkReverse') && (
                    <Row>
                        <RadioGroup
                            field={`${keyPrefix}ruleParam.checkReverse`}
                            label="错误判定"
                            options={[
                                { label: '符合规则', value: true },
                                { label: '违反规则', value: false },
                            ]}
                            required
                            rule={[formRule.checkRequiredNotBlank()]}
                        />
                    </Row>
                )}
                {rule.ruleId && rule.ruleParamFillKeys.includes('checkLevel') && !parentRule && (
                    <Row>
                        <Select
                            label="错误级别"
                            field={`${keyPrefix}ruleParam.checkLevel`}
                            options={[
                                { label: '严重错误', value: 'S' },
                                { label: '明确错误', value: '0' },
                                { label: '疑似错误', value: '1' },
                                { label: '暂缓错误', value: '2' },
                            ]}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                )}
                {rule.ruleId &&
                    rule.ruleParamFillKeys.includes('checkFailType') &&
                    rule.ruleParam.combinationLogic !== 'and' &&
                    (!parentRule || parentRule.ruleParam.combinationLogic === 'and') && (
                        <Row>
                            <Select
                                label="错误类型"
                                field={`${keyPrefix}ruleParam.checkFailType`}
                                bmName="DM_DATA_VALID_ERROR_TYPE"
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                    )}
                {rule.ruleId &&
                    rule.ruleParamFillKeys.includes('checkFailMsg') &&
                    rule.ruleParam.combinationLogic !== 'and' &&
                    (!parentRule || parentRule.ruleParam.combinationLogic === 'and') && (
                        <Row>
                            <Input
                                label="错误提示"
                                field={`${keyPrefix}ruleParam.checkFailMsg`}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                    )}
                {/* {
                    rule.ruleId &&
                    rule.ruleParamFillKeys.includes('useScenes') &&
                    !parentRule &&
                    <Row>
                        <CheckboxGroupCustom
                            label="适用范围"
                            field="ruleParam.useScenes"
                            bmName={rule.ruleType === '3' ? 'DM_CREDIT_APPLICATION_SCENE' : 'BM_OBJECT_TYPE'}
                            rule={[formRule.checkArray(1)]}
                            required
                        />
                    </Row>
                } */}
                {rule.ruleId && rule.ruleParamFillKeys.includes('sort') && !parentRule && (
                    <Row>
                        <Input
                            label="规则排序"
                            field="ruleParam.sort"
                            type="number"
                            rule={[formRule.checkRequiredNotBlank(), formRule.checkIsNum()]}
                            required
                        />
                    </Row>
                )}
            </Fragment>
        );
    }
}

export default FieldRuleCom;
