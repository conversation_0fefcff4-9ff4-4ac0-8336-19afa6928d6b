class Service {
    network;

    constructor(network) {
        this.network = network;
    }

    // 获取企业档案记录
    searchBasicList = (body) => {
        return this.network.json('/enterprise/searchBasicList.do', body);
    };

    // 获取企业基本信息详情
    detailBasic = (body) => {
        return this.network.formGet('/enterprise/detailBasic.do', body);
    };

    // 用户关注企业
    focusEnterprise = (body) => {
        return this.network.formGet('/enterprise/focusEnterprise.do', body);
    };

    // 用户取消关注企业
    unFocusEnterprise = (body) => {
        return this.network.formGet('/enterprise/unFocusEnterprise.do', body);
    };

    // 根据年份和统一信用代码获取经营信息
    operateInfoSearchOperateInfoList = (body) => {
        return this.network.json('/operateInfo/searchOperateInfoList.do', body);
    };

    // 获取企业经营地址集合
    business_address = (tyshxydm) => {
        return this.network.formGet(`/address/business_address/${tyshxydm}.do`);
    };

    // 保存企业经营地址信息
    business_address_save = (body) => {
        return this.network.json(`/address/business_address_save.do`, body);
    };

    // 企业就业人数列表
    employedNumberList = (body) => {
        return this.network.json('/enterprise/employed_number_list.do', body);
    };

    // 企业详情-经营信息-年份范围
    getYearRange = (rangeType, creditCode) => {
        return this.network.formGet(`/enterprise/year_range.do?rangeType=${rangeType}&creditCode=${creditCode}`);
    };

    // 企业详情-经营信息-获取经营信息
    getBusinessInfo = (params) => {
        return this.network.json(`/enterprise/business_info.do`, params);
    };

    // 企业详情-经营信息-获取纳税信息
    getTaxInfo = (params) => {
        return this.network.json(`/enterprise/tax_info.do`, params);
    };
}
export default Service;
