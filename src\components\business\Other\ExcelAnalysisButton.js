/*
 * @(#) StandardInfoEdit.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-07-02 11:56:46
 */
import React, { Component } from 'react';
// 样式
// 组件
import { Button, message, Icon } from 'antd';
import * as ExcelUtil from '@/components/common/common/ExcelUtil';
import styles from './style/ExcelAnalysisButton.less';

// 组件

class ExcelAnalysisButton extends Component {
    onImportExcel = (file) => {
        const { successFn, formatMap } = this.props;
        // 获取上传的文件对象
        const { files } = file.target;
        // 通过FileReader对象读取文件
        const fileReader = new FileReader();

        // 定义解析事件
        fileReader.onload = ({ target: { result } }) => {
            let workbookData;

            try {
                // 读取得到整份excel表格对象
                workbookData = ExcelUtil.readerExcel(result, { type: 'binary' }, formatMap);
            } catch (e) {
                // 解析文件异常相关提示
                message.error('文件类型不正确！');
            }
            // 存储获取到的数据
            workbookData && successFn && successFn(workbookData, result);
        };
        // 以二进制方式读取文件
        fileReader.readAsBinaryString(files[0]);
    };

    excelUpload = () => {
        document.getElementById(this.props.key || 'excelUpload').click();
    };

    render() {
        const { children, buttonText = '上传', key = 'excelUpload', successFn, formatMap, ...restProps } = this.props;
        let button;

        if (children) {
            button = React.Children.map(children, (child) => {
                if (child && child.props) {
                    return React.cloneElement(child, { ...restProps, onClick: this.excelUpload });
                }

                return child;
            });
        } else {
            button = (
                <Button className={styles['upload-wrap']} {...restProps} onClick={this.excelUpload}>
                    <Icon type="upload" />
                    <span className={styles['upload-text']}>{buttonText}</span>
                </Button>
            );
        }

        return [
            <input
                id={key}
                key={key}
                type="file"
                accept=".xlsx, .xls"
                value=""
                style={{ display: 'none' }}
                onChange={this.onImportExcel}
            />,
            button,
        ];
    }
}

export default ExcelAnalysisButton;
