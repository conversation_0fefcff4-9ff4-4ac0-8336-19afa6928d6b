@charset "utf-8";

.detialHeader {
    padding: 15px;
    .img {
        display: inline-block;
        width: 10%;
        vertical-align: top;
        img {
            width: 100%;
            min-width: 110px;
        }
    }
    .info {
        display: inline-block;
        width: 90%;
        padding-left: 15px;
        .title {
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: bold;
            color: #404346;
        }
        .detailInfo {
            .line {
                margin-bottom: 6px;
                font-size: 12px;
                &:last-child {
                    margin-bottom: 0;
                }
                div {
                    margin-bottom: 0;
                }
                .label {
                    display: inline-block;
                    //width: 85px;
                    vertical-align: top;
                    color: #74767a;
                }
                .value {
                    display: inline-block;
                    max-width: 80%;
                    color: #404348;
                    word-break: break-all;
                }
                .item {
                    display: inline-block;
                }
            }
        }
    }
}

.statisticLine {
    padding: 15px 0;
    .statisticItem {
        display: inline-block;
        text-align: left;
        span {
            font-size: 12px;
            color: #74767a;
        }
        .count {
            padding: 0 5px;
            font-size: 18px;
            color: #09d;
        }
    }
}
