// 接口请求
const commonApi = require('@/services/system/commonApi');
// 工具类
const store = require('../../store/Store');
const Log = require('../../log/Log');
// 常量
const BM_KEY = 'bm';

/**
 * 表码转换 { code,name } => {code,label}
 * @param bmList
 */
function transCodeNameToCodeLabel(bmList) {
    return bmList.map((bmItem) => ({ label: bmItem.label || bmItem.name, code: bmItem.code }));
}

/**
 * 单一表码助手
 */
class BmHelper {
    constructor(bmList) {
        this.bmList = bmList.map((bmItem) => ({ ...bmItem }));
    }

    /**
     * 根据表码的code 获取对应的表码“键值对”，
     * @param code "350203000000"
     * @returns {*} 如{code:"350203000000",label:"思明区"}
     */
    getItem(code) {
        const item = this.bmList.find((bmItem) => bmItem.code === code);

        if (!item) {
            return item;
        }

        return {
            ...item,
        };
    }

    /**
     * 根据表码的code 获取对应的表码“值”，
     * @param code String "350203"
     * @returns {String} "思明区"
     */
    getCodeLabel(code) {
        const item = this.getItem(code);

        return item ? item.label : code;
    }

    /**
     * 根据表码“值” 获取对应的表码的code，
     * @param label String "思明区"
     * @returns {String} "350203"
     */
    getLabelCode(label) {
        const item = this.bmList.find((bmItem) => bmItem.label === label);

        if (!item) {
            return null;
        }

        return item.code;
    }
}

/**
 * 表码缓存管理器
 */
class BmManager {
    constructor() {
        this.data = {};
        this.setLoader = this.setLoader.bind(this);
        this.getLoader = this.getLoader.bind(this);
        this.register = this.register.bind(this);
        this.registerAll = this.registerAll.bind(this);
        this.load = this.load.bind(this);
        this.loadSingle = this.loadSingle.bind(this);
        this.bmHelper = this.bmHelper.bind(this);
        this.getBmLabel = this.getBmLabel.bind(this);
        this.getBmMultiLabel = this.getBmMultiLabel.bind(this);
        this.getBmCode = this.getBmCode.bind(this);
        this.getBmList = this.getBmList.bind(this);
    }

    /**
     * 清除表码数据缓存
     */
    static cleanCache() {
        store.del(BM_KEY);
    }

    /**
     *  外部设置表码缓存加载器
     * @param loader 如果是函数返回promise
     */
    setLoader(loader) {
        this.loader = loader;
    }

    /**
     * 获取表码缓存加载器
     * @returns {*} 加载器对象promise
     */
    getLoader() {
        if (typeof this.loader === 'function') {
            return this.loader();
        }

        return commonApi.getAllCode();
    }

    /**
     * 手动注册表码
     * @param bmAlias 表码的key 如 XZQH_BM
     * @param bmList 表码的List如 [{code:"3502030000",label:"思明区"}]
     */
    register(bmAlias, bmList) {
        if (typeof this.data[bmAlias] !== 'undefined') {
            return;
        }
        this.data[bmAlias] = bmList;
    }

    /**
     * 注册所有表码
     * @param fullBm
     */
    registerAll(fullBm) {
        fullBm.forEach((item) => {
            const codeList = item.codeList.filter((i) => i.enable === '1');

            this.register(item.alias, transCodeNameToCodeLabel(codeList));
        });
    }

    /**
     * 一般不会手动执行，从服务器加载配置对象，new XXManager() 的时候会执行load
     * @returns {Promise.<{}>}
     */
    load() {
        const fullBm = store.get(BM_KEY);

        if (fullBm) {
            Log.debug('加载缓存表码');
            this.registerAll(fullBm);

            return Promise.resolve();
        }

        return this.getLoader()
            .then((fullBms) => {
                this.registerAll(fullBms);
                store.set(BM_KEY, fullBms);
            })
            .catch((e) => {
                Log.error('表码加载失败', e);
                throw e;
            });
    }

    loadSingle(alias) {
        return commonApi
            .getBmByAlias(alias)
            .then(({ alia, codeList }) => this.register(alia, transCodeNameToCodeLabel(codeList)))
            .catch((e) => Log.error('获取单个表码失败', e));
    }

    /**
     * 获取单一表码工具
     * @param bmKey
     * @returns {*}
     */

    bmHelper(bmKey) {
        const bmList = this.data[bmKey];

        if (typeof bmList === 'undefined') {
            Log.warn(`表码key=${bmKey}不存在`);

            return null;
        }

        return new BmHelper(bmList);
    }

    /**
     * 根据表码的key获得对应的表码
     * @param bmAlias 表码的key 如 BM_AREA
     * @returns {*} [{code:"3502030000",label:"思明区"}]
     */
    getBmList(bmAlias) {
        const result = this.data[bmAlias];

        if (!result) {
            Log.warn(`未找到对应表码bmAlias = ${bmAlias}`);

            return [];
        }

        return result.map((item) => ({ ...item }));
    }

    /**
     * 根据表码的名字key和code 获取对应的label
     * @param bmAlias String 如 XZQH_BM
     * @param code String 如 350203000000
     * @returns {String} String 如思明区
     */
    getBmLabel(bmAlias, code) {
        const bmHelper = this.bmHelper(bmAlias);

        if (bmHelper) {
            return bmHelper.getCodeLabel(code);
        }

        return code;
    }

    /**
     * 根据表码的名字key和code 获取对应的label
     * @param bmAlias String 如 XZQH_BM
     * @param codes String 如 350203000000
     * @returns {String} String 如思明区
     */
    getBmMultiLabel(bmAlias, codes) {
        if (!codes) {
            return codes;
        }
        const bmHelper = this.bmHelper(bmAlias);

        return codes
            .split(',')
            .map((code) => bmHelper.getCodeLabel(code))
            .join('、');
    }

    /**
     * 根据表码的名字key和label 获取对应的code
     * @param bmAlias String 如 XZQH_BM
     * @param label String 如 思明区
     * @returns {String} String 如350203000000
     */
    getBmCode(bmAlias, label) {
        const bmHelper = this.bmHelper(bmAlias);

        if (bmHelper) {
            return bmHelper.getLabelCode(label);
        }

        return label;
    }
}

module.exports = new BmManager();
