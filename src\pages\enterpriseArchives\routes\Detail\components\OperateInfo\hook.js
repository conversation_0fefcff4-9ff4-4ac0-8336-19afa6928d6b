import { useService } from '@share/framework';
import EnterpriseService from '@/services/EnterpriseService';
import { useState } from 'react';
import { useMount } from 'ahooks';

export const useFetchHooks = () => {
    const enterpriseService = useService(EnterpriseService);
    // 获取年份范围
    const getYearRange = (rangeType, tyshxydm) => {
        return enterpriseService.getYearRange(rangeType, tyshxydm);
    };
    // 获取经营信息
    const getBusinessInfo = (params) => {
        return enterpriseService.getBusinessInfo(params);
    };
    // 纳税信息
    const getTaxInfo = (params) => {
        return enterpriseService.getTaxInfo(params);
    };
    // // 人事信息
    // const getPersonInfo = (params) => {
    //     return enterpriseService.getPersonInfo(params);
    // };

    return {
        getYearRange,
        getBusinessInfo,
        getTaxInfo,
        // getPersonInfo,
    };
};

const useOperateInfoHooks = ({ tyshxydm }) => {
    const { getYearRange } = useFetchHooks();
    const [options, setOptions] = useState({
        0: [],
        1: [],
        2: [],
    }); // 范围类型（0：经营范围|1：税收范围,2:人事信息的年份范围）

    const getYearRangeData = async () => {
        const res1 = await getYearRange(0, tyshxydm);
        const res2 = await getYearRange(1, tyshxydm);
        const options1 = (res1 || []).map((item) => {
            return {
                label: item,
                value: item,
            };
        });
        const options2 = (res2 || []).map((item) => {
            return {
                label: item,
                value: item,
            };
        });
        setOptions({
            0: options1,
            1: options2,
            // 2: options1,
        });
    };

    useMount(() => {
        getYearRangeData();
    });

    return {
        options,
    };
};

export default useOperateInfoHooks;
