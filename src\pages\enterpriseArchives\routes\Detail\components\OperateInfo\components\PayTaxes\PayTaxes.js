/**  版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 *  @Copyright:  Copyright (c) 2020
 *  @Company:厦门畅享信息技术有限公司
 *  @Author: 李家其
 *  Date: 2023/11/24 17:01
 */
// 经营信息
import React from 'react';
import { Panel, Select } from '@share/shareui';
import { useDeepCompareEffect, useSetState } from 'ahooks';
import { emptyDefault, moneyFormatUnit } from '@/utils/format';
import { useFetchHooks } from '../../hook';
import styles from './PayTaxes.scss';

const PayTaxes = ({ options, tyshxydm }) => {
    const { getTaxInfo } = useFetchHooks();
    const [state, setState] = useSetState({
        info: {},
        year: '',
    });

    const getTaxInfoData = async (params) => {
        const res = await getTaxInfo(params);
        setState({
            info: res,
            year: params.year,
        });
    };

    useDeepCompareEffect(() => {
        if (options?.[0]?.value) {
            getTaxInfoData({
                year: options?.[0]?.value,
                creditCode: tyshxydm,
            });
        }
    }, [options]);

    return (
        <div className={styles.payTaxes}>
            <Panel toggle>
                <Panel.Head
                    title="纳税信息"
                    extra={
                        <div className={styles.selectWrap}>
                            <Select
                                options={options}
                                value={state.year}
                                onChange={({ value }) => {
                                    setState({ year: value });
                                    getTaxInfoData({
                                        year: value,
                                        creditCode: tyshxydm,
                                    });
                                }}
                                placeholder="选择年份"
                                resetValue=""
                            />
                        </div>
                    }
                />
                <Panel.Body full>
                    <div className="operateInfoTableRow">
                        <div className="operateInfoDataFromText">
                            数据来源：<span>{emptyDefault(state?.info?.dataSource)}</span>
                        </div>
                        <div className="formTableStyleCover">
                            <table className={styles.singleRowTable}>
                                <thead>
                                    <tr>
                                        <th>财政总收入</th>
                                        <th>去年同期财政总收入</th>
                                        <th>增额财政总收入</th>
                                        <th>增幅财政总收入</th>
                                        <th>地方级收入</th>
                                        <th>去年同期地方级收入</th>
                                        <th>增额地方级收入</th>
                                        <th>增幅地方级收入</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{moneyFormatUnit(state?.info?.rshj, '元')}</td>
                                        <td>{moneyFormatUnit(state?.info?.qntqczzsr, '元')}</td>
                                        <td>{moneyFormatUnit(state?.info?.zeczzsr, '元')}</td>
                                        <td>{state?.info?.zfczzsr ? state?.info?.zfczzsr : '--'}</td>
                                        <td>{moneyFormatUnit(state?.info?.dfjsr, '元')}</td>
                                        <td>{moneyFormatUnit(state?.info?.qntqdfjsr, '元')}</td>
                                        <td>{moneyFormatUnit(state?.info?.zedfjsr, '元')}</td>
                                        <td>{state?.info?.zfdfjsr ? state?.info?.zfdfjsr : '--'}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div className="operateInfoTableRow">
                        <div className="formTableStyleCover">
                            <table className={styles.singleRowTable}>
                                <thead>
                                    <tr>
                                        <th>区级收入</th>
                                        <th>去年同期区级收入</th>
                                        <th>增额区级收入</th>
                                        <th>增幅区级收入</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{moneyFormatUnit(state?.info?.qjsr, '元')}</td>
                                        <td>{moneyFormatUnit(state?.info?.qntqqjsr, '元')}</td>
                                        <td>{moneyFormatUnit(state?.info?.zeqjsr, '元')}</td>
                                        <td>{state?.info?.zfqjsr ? state?.info?.zfqjsr : '--'}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </Panel.Body>
            </Panel>
        </div>
    );
};

export default PayTaxes;
