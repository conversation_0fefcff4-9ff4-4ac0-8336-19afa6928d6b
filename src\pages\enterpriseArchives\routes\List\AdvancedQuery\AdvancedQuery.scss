$leftWidth: 520px;

.advancedQuery {
    position: absolute;
    width: $leftWidth;
    height: 100%;
    transform: translate(-100%);
    // transition: transform 1s ease-in-out;
    box-shadow: 1px 0 1px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    background-color: #fff;
    z-index: 2;
    &_head {
        padding: 8px 16px;
        border-bottom: 1px solid #f5f5f5;
    }
    .trigger {
        position: absolute;
        right: -10px;
        top: 0;
        width: 10px;
        height: 48px;
        background-color: #c2dcff;
        border-radius: 0 10px 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 14px;
        color: #1677ff;
    }
    &_form {
        flex: auto;
        overflow-y: auto;
        margin-bottom: 0;
        :global {
            .ant-cascader-menu-item-content {
                max-width: 80px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }
            .form-item {
                label {
                    &.label-item {
                        max-width: 100%;
                    }
                }
                .sub-item {
                    display: initial;
                }
                .ant-select {
                    width: 100%;
                }
            }
        }
    }
    &_show {
        width: $leftWidth;
        transform: translate(0);
    }
    &_footer {
        border-top: 1px solid #f5f5f5;
        padding: 16px;
        text-align: right;
        button {
            margin-left: 12px;
        }
    }
    .zczbwyRange {
        display: flex;
        align-items: center;
        > span {
            padding: 0 8px;
        }
    }
}
