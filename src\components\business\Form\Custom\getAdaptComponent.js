import React from 'react';

const getAdaptComponent = (Component, defaultProps) => {
    return class extends React.Component {
        static defaultProps = {
            adaptValueKey: 'value',
            ...defaultProps,
        };

        onChange = (value) => {
            const { onChange } = this.props;

            onChange && onChange({ target: { value } });
        };

        render() {
            const { adaptValueKey, value, ...restProps } = this.props;

            restProps[adaptValueKey] = value;

            return <Component {...restProps} onChange={this.onChange} />;
        }
    };
};

export default getAdaptComponent;
