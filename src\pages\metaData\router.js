/*
 * @(#) router.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2018
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2018-08-15 11:12:44
 *
 */
import React from 'react';
import dynamic from 'dva/dynamic';
import { Router, Switch, Route } from 'dva/router';
import { withInjectDefLoader } from '@/components/common/business/inject';

const App = ({ children }) => <div className="ui-box">{children}</div>;

export function RouterConfig({ history, app }) {
    const routes = [
        // 数据表管理
        {
            // 数据表-列表
            key: 'DatabaseTableList',
            path: '/DatabaseTableList',
            component: dynamic({
                app,
                component: () => import('./routes/MetadataConfig/DatabaseTableList'),
            }),
            exact: true,
        },
        {
            // 数据表-详情
            key: 'DatabaseTableDetail',
            path: '/DatabaseTableDetail/:tableId/:tabActiveKey?',
            component: dynamic({
                app,
                component: () => import('./routes/MetadataConfig/DatabaseTableDetail'),
            }),
            exact: true,
        },
        // 类别管理
        {
            // 类别管理
            key: 'CategoryCodeManage',
            path: '/CategoryCodeManage',
            component: dynamic({
                app,
                component: () => import('./routes/MetadataConfig/CategoryCodeManage'),
            }),
            exact: true,
        },
        // 数据填报
        {
            // 数据填报-列表
            key: 'DataFillingList',
            path: '/DataFillingList',
            component: dynamic({
                app,
                component: () => import('./routes/DataFilling/DataFillingList'),
            }),
            exact: true,
        },
        {
            // 数据填报-在线填报
            key: 'DataFillingAdd',
            path: '/DataFillingAdd/:categoryId',
            component: dynamic({
                app,
                component: () => import('./routes/DataFilling/DataFillingAdd'),
            }),
            exact: true,
        },
        // 数据修复
        {
            // 数据修复-修复列表
            key: 'DataRepairList',
            path: '/DataRepairList',
            component: dynamic({
                app,
                component: () => import('./routes/DataRepair/DataRepairList'),
            }),
            exact: true,
        },
        {
            // 数据修复-详情
            key: 'DataRepairDetail',
            path: '/DataRepairDetail/:categoryId/:recordId',
            component: dynamic({
                app,
                component: () => import('./routes/DataRepair/DataRepairDetail'),
            }),
            exact: true,
        },
        {
            // 数据修复-修复
            key: 'DataRepairEdit',
            path: '/DataRepairEdit/:categoryId/:recordId',
            component: dynamic({
                app,
                component: () => import('./routes/DataRepair/DataRepairEdit'),
            }),
            exact: true,
        },
        {
            // 数据修复-审计
            key: 'DataRepairAudit',
            path: '/DataRepairAudit/:categoryId/:ywid',
            component: dynamic({
                app,
                component: () => import('./routes/DataRepair/DataRepairAudit'),
            }),
            exact: true,
        },
        // 状态管理
        {
            // 状态管理-管理列表
            key: 'DataManageList',
            path: '/DataManageList',
            component: dynamic({
                app,
                component: () => import('./routes/DataManage/DataManageList'),
            }),
            exact: true,
        },
        {
            // 状态管理-详情
            key: 'DataManageDetail',
            path: '/DataManageDetail/:categoryId/:recordId',
            component: dynamic({
                app,
                component: () => import('./routes/DataManage/DataManageDetail'),
            }),
            exact: true,
        },
        {
            // 状态管理-修改
            key: 'DataManageEdit',
            path: '/DataManageEdit/:categoryId/:recordId',
            component: dynamic({
                app,
                component: () => import('./routes/DataManage/DataManageEdit'),
            }),
            exact: true,
        },
        // 溯源管理
        {
            // 溯源管理-列表
            key: 'DataTraceabilityList',
            path: '/DataTraceabilityList',
            component: dynamic({
                app,
                component: () => import('./routes/DataTraceability/DataTraceabilityList'),
            }),
            exact: true,
        },
        {
            // 溯源管理-详情
            key: 'DataTraceabilityDetail',
            path: '/DataTraceabilityDetail/:categoryId/:recordId',
            component: dynamic({
                app,
                component: () => import('./routes/DataTraceability/DataTraceabilityDetail'),
            }),
            exact: true,
        },
        // 审计管理
        {
            // 审计管理-列表
            key: 'DataAuditList',
            path: '/DataAuditList',
            component: dynamic({
                app,
                component: () => import('./routes/DataAudit/DataAuditList'),
            }),
            exact: true,
        },
        {
            // 审计管理-详情
            key: 'DataAuditDetail',
            path: '/DataAuditDetail/:categoryId/:objectType/:ywid',
            component: dynamic({
                app,
                component: () => import('./routes/DataAudit/DataAuditDetail'),
            }),
            exact: true,
        },
    ];

    return (
        <Router history={history}>
            <App>
                <Switch>
                    {routes.map((route) => {
                        route.component = withInjectDefLoader(route.component);

                        return <Route {...route} />;
                    })}
                </Switch>
            </App>
        </Router>
    );
}
