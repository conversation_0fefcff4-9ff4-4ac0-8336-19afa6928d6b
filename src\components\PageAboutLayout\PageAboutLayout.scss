.container {
    display: flex;
    line-height: 1;
    .leftBox {
        position: relative;
        width: 290px;
        background: url(@/assets/images/common/layoutLeftBg.png) no-repeat top center #f8fbff;
        background-size: 100% 80px;
        min-height: 100vh;
        &::before {
            content: '';
            position: absolute;
            right: 0;
            top: 76px;
            display: flex;
            width: 66px;
            height: 46px;
            background: url(@/assets/images/common/layoutLeftBall.png) no-repeat center center;
            background-size: 66px 46px;
            z-index: 0;
        }
    }
    .rightBox {
        width: calc(100% - 290px);
    }
}
