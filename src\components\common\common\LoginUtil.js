import * as Store from '@/components/common/store/Store';
import * as Constants from '@/components/common/business/Constants';
import * as ConfigConstant from '@/components/common/common/ConfigConstant';
import { getUrlString } from '@share/utils';

function getAppKey(defValue = Constants.DEFAULT_APP_KEY) {
    return getUrlString(top.location.href, 'appKey') || Store.get('appKey') || defValue;
}

function getLoginUrl(appKey = getAppKey()) {
    const from = Store.get('from') || '';
    let returnUrl = getUrlString(top.location.href, 'returnUrl');

    if (returnUrl) {
        returnUrl = decodeURIComponent(returnUrl);
    } else {
        returnUrl = `${ConfigConstant.portalFrontContextPath}/login/?appKey=${appKey}#/${from}`;
    }
    if (returnUrl.startsWith('www')) {
        returnUrl = `http://${returnUrl}`;
    }

    return returnUrl;
}

function getPortalUrl(appKey, { returnKeepLogin, returnUrl } = {}) {
    let url = `${ConfigConstant.portalFrontContextPath}/portal/?appKey=${appKey}`;

    if (returnKeepLogin) {
        url += `&returnKeepLogin=${returnKeepLogin}`;
    }
    if (returnUrl) {
        url += `&returnUrl=${encodeURIComponent(returnUrl)}`;
    }
    url += '#/';

    return url;
}

function clearCache() {
    Store.del('bm');
    Store.del('me');
    Store.del('dept');
    Store.del('local');
    Store.del('form');
    Store.del('table');
    window.localStorage.clear();
}

export { getAppKey, getLoginUrl, getPortalUrl, clearCache };
