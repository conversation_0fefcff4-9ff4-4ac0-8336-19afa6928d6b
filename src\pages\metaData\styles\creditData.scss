@charset "utf-8";

.onlineDataRepaire {
    .table {
        width: 100%;
        table-layout: fixed;
        font-size: 12px;

        .tableHead th {
            padding: 0 10px;
            line-height: 34px;
            background: #f7f8f9;
            font-weight: bold;
            color: #404348;

            &:first-child {
                text-align: right;
            }
        }

        .tbody {
            >.tr {
                border-bottom: 1px solid #eee;

                >.td {
                    padding: 10px;

                    &:nth-child(2) {
                        border-right: 1px solid #eee;
                    }

                    &:first-child {
                        text-align: right;
                        background: #f7f8f9;
                    }

                    :global {
                        .labelItem {
                            display: none;
                        }

                        .sub-item {
                            display: inline-block;
                            width: 100%
                        }
                    }
                }
            }
        }
    }

    .tips {
        color: #a4a5a9;

        i {
            padding-right: 5px;
            font-size: 16px;
            color: #00ccff;

            &:before {
                vertical-align: middle;
            }
        }

        .tipText {
            display: inline-block;
            width: 90%;
            vertical-align: text-top;
        }

        em {
            font-style: normal;
        }
    }

    textarea {
        resize: none;
    }

    .bottomTip {
        padding: 16px 12px;
        font-size: 12px;
        color: #a4a5a9;
    }

    .bottomBtn {
        padding: 20px 15px;
        background: #f7f8f9;
    }

    :global {
        .text-danger {
            color: #ee5544 !important;
        }
    }

    .errorBorder {
        border: 1px solid #a94442;
    }

    .pl12 {
        padding-left: 12px;
    }
}

.creditDataDetail{
    .title{
        margin-bottom: 0;
        line-height: 2;
        text-align: center;
        font-size: 16px;
        font-weight: bold;
        color: #388adf;

    }
}

.creditDataTraceanilityDetail {
    .title {
        margin-bottom: 0;
        line-height: 2;
        text-align: center;
        font-size: 16px;
        font-weight: bold;
        color: #388adf;
    }
    .table-list {
        display: flex;
        gap: 12px;
        align-items: flex-start;
        &_item {
            flex: 1;
        }
    }
}

:global {
    .dataRepaireFormFix {
        display: flex;
        align-items: center;
        >td {
            flex: 1;
        }
        .sub-item {
            //width: 300px !important;

            .Select {
                width: 100% !important;
            }
        }
    }
}

.btnStyle {
    font-size: 12px;
    vertical-align: bottom;
    color: #404348;

    &:hover {
        color: #0077bb;
    }
}

.noData {
    padding: 100px 0 140px 0;
    text-align: center;
    p {
        color: #999;
    }
}
