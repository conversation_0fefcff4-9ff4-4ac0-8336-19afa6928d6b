// shareui
import 'bootstrap/dist/css/bootstrap.min.css';
import 'font-awesome/css/font-awesome.min.css'; // shareui-font版本迁移完成之后删除该依赖
import '@share/shareui/es/style/shareui.css';
import '@share/shareui/es/style/patch.css';
import '@share/shareui-font';

import '@/framework/global.scss';
import { version, ConfigProvider } from 'antd';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import dayjs from 'dayjs';
import { mount } from '@/framework/mount';
import { routes } from './router';
import 'dayjs/locale/zh-cn';

const majorVersion = version?.split('.')?.[0];

if (`${majorVersion}` === '4') {
    // eslint-disable-next-line import/no-extraneous-dependencies
    import('dayjs/locale/zh-cn');
    dayjs.extend(weekday);
    dayjs.extend(localeData);
    dayjs.extend(quarterOfYear);
    dayjs.extend(advancedFormat);
}

// 挂载应用
mount(routes);
