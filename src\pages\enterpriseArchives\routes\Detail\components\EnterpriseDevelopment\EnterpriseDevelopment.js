import React, { Fragment } from 'react';
import NoData from '@/components/NoData';
import TagTitle from '../TagTitle';
import PayTaxesTrends from './components/PayTaxesTrends';

const EnterpriseDevelopment = ({ data: { operateInfoList } }) => {
    return Array.isArray(operateInfoList) && operateInfoList.length > 0 ? (
        <div>
            {/* <TagTitle title={<Fragment>企业发展</Fragment>} id="enterpriseDevelopment" /> */}
            <PayTaxesTrends data={operateInfoList} />
        </div>
    ) : (
        <NoData />
    );
};

export default EnterpriseDevelopment;
