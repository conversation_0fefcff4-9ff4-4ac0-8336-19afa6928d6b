import React, { Component } from 'react';
// 工具类
import * as TreeUtil from '@/components/common/common/TreeUtil';
import { getUserComponent } from '../../Custom/index';
// 被封装组件
import TreeSelect from '../custom/TreeSelect';

const UserTreeSelect = getUserComponent(TreeSelect);
// 用户管理器
const meManager = require('@/components/common/business/manager/MeManager');
const bmManager = require('@/components/common/business/manager/BmManager');
const deptListManager = require('@/components/common/business/manager/DeptListManager');

class AreaDeptSelect extends Component {
    static defaultProps = {
        ...TreeSelect.defaultProps,
        labelKey: 'shortName', // label属性键
        valueKey: 'deptId', // value属性键
        areaIdKey: 'regionId', // 区域属性键
        data: null, // 外部数据源

        userRegionManageFilter: false, // 过滤与用户管理区域部门
        userRegionValueFilter: false, // 过滤与用户相同区域部门
        userManageFilter: false, // 是否过滤获取用户管理部门
        userChildFilter: false, // 是否过滤获取用户子部门
        userValueFilter: false, // 是否过滤与用户相同部门
        valueFilter: null, // 过滤获取指定部门
        existFilter: true, // 过滤去除已删除部门
        handlerFn: null, // 处理函数（函数）

        autoCheckedInitial: false, // 任何用户初始化自动选中自身
        autoCheckedAlways: false, // 任何用户始终始自动选中自身
        deptAutoCheckedInitial: false, // 部门用户初始化自动选中自身
        deptAutoCheckedAlways: false, // 部门用户始终自动选中自身
        deptAutoDisabled: false, // 部门用户自动锁死下拉框
        onlyValueAutoCheckedInitial: false, // 唯一值初始化自动选中自身
        onlyValueAutoCheckedAlways: false, // 唯一值始终自动选中自身
        onlyValueAutoDisabled: false, // 唯一值自动锁死
    };

    componentWillMount() {
        const { data, areaData = [] } = this.props;

        this.me = meManager.getMe();
        this.deptData = data || deptListManager.getDeptList();
        this.areaData = [...areaData, ...(bmManager.getBmList('BM_AREA') || [])];
        this.areaCode = this.areaData.map((item) => item.code);
    }

    onChange = (e) => {
        const { multi, onChange, acceptString } = this.props;

        if (!multi) {
            onChange && onChange(e);

            return;
        }
        let val = e.target.value;

        if (acceptString) {
            val = val ? val.split(',') : [];
        }
        let filterValue = val.filter((item) => !this.areaCode.includes(item));

        if (acceptString) {
            filterValue = filterValue.join(',');
        }
        onChange && onChange({ target: { value: filterValue } });
    };

    get treeData() {
        const {
            data,
            userRegionManageFilter,
            userRegionValueFilter,
            userManageFilter,
            userChildFilter,
            userValueFilter,
            valueFilter,
            existFilter,
            handlerFn,
        } = this.props;
        let dataList = data || this.deptData;

        // 过滤获取用户管理区域部门
        if (userRegionManageFilter) {
            dataList = this.handleUserRegionManageFilter(dataList);
        }
        // 过滤获取用户自身区域部门
        if (userRegionValueFilter) {
            dataList = this.handleUserRegionValueFilter(dataList);
        }
        // 过滤用户管理部门
        if (userManageFilter) {
            dataList = this.handleUserManageFilter(dataList);
        }
        // 过滤用户子部门
        if (userChildFilter) {
            dataList = this.handleUserChildFilter(dataList);
        }
        // 过滤用户自身部门
        if (userValueFilter) {
            dataList = this.handleUserValueFilter(dataList);
        }
        // 过滤获取指定部门
        if (valueFilter) {
            dataList = this.handleValueFilter(dataList, valueFilter);
        }
        // 过滤去除删除部门
        if (existFilter) {
            dataList = this.handleExistFilter(dataList);
        }
        // 自定义处理逻辑
        if (handlerFn) {
            dataList = handlerFn(dataList);
        }

        return this.handleListToTree(dataList);
    }

    handleListToTree = (list) => {
        const { labelKey, valueKey, areaIdKey, multi } = this.props;

        return this.areaData
            .map((areaOption) => {
                const deptList = list
                    .filter((dept) => dept[areaIdKey] === areaOption.code)
                    .map((dept) => ({ key: dept[valueKey], title: dept[labelKey], value: dept[valueKey] }));

                return {
                    key: areaOption.code,
                    title: areaOption.label,
                    value: areaOption.code,
                    children: deptList,
                    disableCheckbox: !multi,
                };
            })
            .filter((item) => item.children.length > 0);
    };

    handleUserRegionManageFilter = (deptList) => {
        const { areaIdKey } = this.props;
        const { manager, regionNo } = this.me;
        const minSimpleRegionNo = regionNo.replace(/^(\d*?)(00)+$/, '$1');

        return manager ? deptList : deptList.filter((dept) => dept[areaIdKey] && dept[areaIdKey].startsWith(minSimpleRegionNo));
    };

    handleUserRegionValueFilter = (deptList) => {
        const { areaIdKey } = this.props;
        const { regionNo } = this.me;

        return deptList.filter((dept) => dept[areaIdKey] === regionNo);
    };

    handleUserManageFilter = (deptList) => {
        const { valueKey } = this.props;
        const { manager, manageDeptIds } = this.me;

        return manager ? deptList : deptList.filter((dept) => manageDeptIds.includes(dept[valueKey]));
    };

    handleUserChildFilter = (deptList) => {
        const { valueKey } = this.props;
        const { manager, deptId, manageDeptIds } = this.me;

        return manager ? deptList : deptList.filter((dept) => deptId !== dept[valueKey] && manageDeptIds.includes(dept[valueKey]));
    };

    handleUserValueFilter = (deptList) => {
        const { deptId } = this.me;

        return this.handleValueFilter(deptList, deptId);
    };

    handleValueFilter = (deptList, deptId) => {
        const { valueKey } = this.props;

        return deptList.filter((dept) => dept[valueKey] === deptId);
    };

    handleExistFilter = (deptList) => {
        return deptList.filter((dept) => !dept.deleted);
    };

    render() {
        const {
            labelKey,
            valueKey,
            areaIdKey,
            data,
            userRegionManageFilter,
            userRegionValueFilter,
            userManageFilter,
            userChildFilter,
            userValueFilter,
            valueFilter,
            existFilter,
            handlerFn,
            ...restProps
        } = this.props;
        const { deptId } = this.me;
        const treeData = this.treeData || [];
        const options = TreeUtil.treeToArray(treeData).filter((option) => !this.areaCode.includes(option.value));

        return (
            <UserTreeSelect
                {...restProps}
                searchPlaceholder="请输入部门名称"
                onChange={this.onChange}
                treeData={treeData}
                options={options}
                operateDefaultValue={deptId}
            />
        );
    }
}

export default AreaDeptSelect;
