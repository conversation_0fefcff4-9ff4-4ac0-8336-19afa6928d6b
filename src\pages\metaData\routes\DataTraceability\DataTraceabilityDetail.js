/*
 * @(#) DataTraceabilityDetail.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-09-04 15:31:04
 */
import React, { Component, Fragment } from 'react';
// 样式
import styles from '@/pages/metaData/styles/creditData.scss';
// 服务接口
import * as DataTrcApi from '@/services/data/data/DataTrcApi';
// 组件
import Detail from '@/components/business/metadata/Detail';
import { ButtonToolBar, Button, Panel } from '@share/shareui';

class DataTraceabilityDetail extends Component {
    state = {
        detailBody: {},
        databaseDetail: null,
    };

    // 请求配置信息
    componentDidMount = async () => {
        const {
            match: {
                params: { categoryId, recordId },
            },
        } = this.props;
        const detailBody = await DataTrcApi.detailByCategory(categoryId, '2', recordId);
        const { COLL_TYPE, SJLYDX, YBZJ } = detailBody.system;

        // 数据库填报需要比对
        if (COLL_TYPE === '3') {
            const databaseDetail = await DataTrcApi.sourceDetail(SJLYDX, YBZJ);

            if (databaseDetail) {
                this.dataHandle(databaseDetail, detailBody);
            }
            this.setState({ detailBody, databaseDetail });
        } else {
            this.setState({ detailBody });
        }
    };

    dataHandle = (databaseDetail, dataDetail) => {
        const currentData = dataDetail.data;
        // 源头数据排除当前数据
        const moreDatabaseData = databaseDetail.data.filter((item) => !dataDetail.data.find((one) => one.fieldCode === item.fieldCode));
        // 当前数据推算源头数据(按当前数据排序)
        const sourceData = currentData.map(
            (item) =>
                databaseDetail.data.find((one) => one.fieldCode === item.fieldCode) || { fieldCode: item.fieldCode, label: '--', value: '' }
        );

        sourceData.push(...moreDatabaseData);
        currentData.push(...moreDatabaseData.map((item) => ({ fieldCode: item.fieldCode, label: '--', value: '' })));
        // 标红当前数据中截取数据
        for (let index = 0; index < currentData.length; index++) {
            const sourceValue = Array.isArray(sourceData[index].value) ? sourceData[index].value.join('；') : sourceData[index].value;
            const currentValue = Array.isArray(currentData[index].value) ? currentData[index].value.join('；') : currentData[index].value;

            if (currentValue && sourceValue && currentValue !== sourceValue && sourceValue.startsWith(currentValue)) {
                sourceData[index].value = <span style={{ color: '#f65' }}>{sourceValue}</span>;
                currentData[index].value = <span style={{ color: '#f65' }}>{currentValue}</span>;
            }
        }
        databaseDetail.data = sourceData;
        dataDetail.data = currentData;
    };

    // 取消
    cancel = () => {
        const { history } = this.props;

        history.go(-1);
    };

    render() {
        const { detailBody, databaseDetail } = this.state;

        return (
            <Fragment>
                <div className={styles.creditDataTraceanilityDetail}>
                    <div className={styles['table-list']}>
                        {databaseDetail && (
                            <Panel className={styles['table-list_item']}>
                                <Panel.Head title="前置库存储" />
                                <Panel.Body full>
                                    <Detail detail={databaseDetail} />
                                </Panel.Body>
                            </Panel>
                        )}
                        <Panel className={styles['table-list_item']}>
                            <Panel.Head title="数据详情" />
                            <Panel.Body full>
                                <Detail detail={detailBody} />
                            </Panel.Body>
                        </Panel>
                    </div>
                </div>
                <ButtonToolBar>
                    <Button type="button" onClick={this.cancel}>
                        返回
                    </Button>
                </ButtonToolBar>
            </Fragment>
        );
    }
}

export default DataTraceabilityDetail;
