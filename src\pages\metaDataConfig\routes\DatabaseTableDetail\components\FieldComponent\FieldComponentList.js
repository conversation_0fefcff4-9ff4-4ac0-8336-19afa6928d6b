import React, { Fragment } from 'react';
import { Panel, Button, Icon } from '@share/shareui';
import {
    SearchForm,
    // 表单项组件
    Input,
    Select,
    CheckboxGroup,
} from '@share/shareui-form';
import MultiClamp from '@/components/MultiClamp';
import ShareTable from '@/components/Table/ShareTable';
import FieldComponentEdit from './FieldComponentEdit';
import useLogic from './logic';

const componentTypeOptions = [
    { label: '编辑组件', value: 'editParam' },
    { label: '查询组件', value: 'queryParam' },
    { label: '列表组件', value: 'listParam' },
    { label: '详情组件', value: 'detailParam' },
];

const FieldComponentList = (props) => {
    const { metaFieldList } = props;
    const { state, form, handleSearch, listState, saveComponent, handleShowEditModal } = useLogic(props);
    const { fieldComponentList, showEditModal, showDetailModal, operateData } = state;
    const componentOptions = fieldComponentList.map((item) => ({ label: item.componentName, value: item.componentId }));
    const fieldCodeOptions = metaFieldList.map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));

    // 列表展示内容
    const columns = [
        {
            title: '组件名称',
            key: 'componentId',
            dataIndex: 'componentId',
            width: '20%',
            render: (rowData) => {
                const value = (componentOptions.find((item) => item.value === rowData) || {}).label;

                return <MultiClamp title={value}>{value}</MultiClamp>;
            },
        },
        {
            title: '组件类型',
            key: 'componentType',
            dataIndex: 'componentType',
            width: '20%',
            render: (rowData) => componentTypeOptions.find((item) => item.value === rowData).label,
        },
        {
            title: '组件字段',
            key: 'fieldCode',
            width: '30%',
            render: (rowData) => {
                const value = `${rowData.field.fieldCode}（${rowData.field.showLabel}）`;

                return <MultiClamp title={value}>{value}</MultiClamp>;
            },
        },
        {
            title: '默认值',
            key: 'defaultValue',
            dataIndex: 'defaultValue',
            render: (rowData) => <MultiClamp title={rowData}>{rowData}</MultiClamp>,
        },
        {
            title: '操作',
            key: 'operate',
            width: '8%',
            render: (rowData) => {
                return (
                    <div className="tableBtn">
                        <a
                            href="javascript:void(0)"
                            onClick={() => {
                                const { id, field, ...component } = rowData;

                                this.setState({ operateData: component, showDetailModal: true });
                            }}
                        >
                            查看
                        </a>
                        <a
                            href="javascript:void(0)"
                            onClick={() => {
                                const { id, field, ...component } = rowData;
                                handleShowEditModal(true, component);
                            }}
                        >
                            修改
                        </a>
                    </div>
                );
            },
        },
    ];

    return (
        <Fragment>
            <Panel>
                <Panel.Body full>
                    <SearchForm
                        formState={form}
                        fixWidth={4}
                        query={(data) => {
                            console.info('触发查询', data);
                            handleSearch(data);
                        }}
                        resetRetry
                    >
                        <Select label="组件名称" field="componentId" options={componentOptions} />
                        <Select label="组件字段" field="fieldCode" options={fieldCodeOptions} />
                        <Input label="默认值" field="defaultValue" col={5} />
                        <CheckboxGroup label="组件类型" field="componentTypes" options={componentTypeOptions} col={9} />
                    </SearchForm>
                </Panel.Body>
            </Panel>
            <Panel>
                <Panel.Head title="组件信息" />
                <Panel.Body full>
                    <ShareTable rowKey="id" listState={listState} columns={columns} cachePage />
                </Panel.Body>
            </Panel>

            {/* 规则编辑 */}

            <FieldComponentEdit
                data={{
                    detail: operateData,
                    fieldComponentList,
                    componentTypeOptions,
                    metaFieldList,
                }}
                show={showEditModal}
                successFn={saveComponent}
                cancelFn={() => handleShowEditModal(false, {})}
            />
        </Fragment>
    );
};

export default FieldComponentList;
