.body {
    width: 100%;
}

.operate {
    display: flex;
    align-items: flex-start;
}

.switch {
    > i {
        margin-top: 8px;
        color: green;
    }
}

.addButton {
    > i {
        margin-top: 8px;
        color: blue;
    }
}

.composeLogic{
    width: 524px;
    > div {
        width: inherit;
        display: inline-block;
    }
}

.composeNodes {
    padding: 10px 0 10px 20px;
}

.composeNode {
    border: 1px solid rgb(217, 218, 220);
    padding: 10px 0 10px 10px;
}

.deleteButton {
    > i {
        margin-top: 8px;
        color: red;
    }
}
