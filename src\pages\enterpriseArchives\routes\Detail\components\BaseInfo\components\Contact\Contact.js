/*
 *@(#) Contact.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-10
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import NoData from '@/components/NoData/NoData';
import ShareList, { Column, NumberColumn, useList } from '@share/list';
import React from 'react';
import { maskPhoneNumber } from '@/utils';

const Contact = ({ data = [] }) => {
    const listState = useList({ dataSource: data });

    return (
        <div className="shareListStyleCover">
            <ShareList listState={listState} usePageBar={false} emptyText={<NoData />}>
                <NumberColumn />
                <Column label="姓名" field="lxrxm" align="center" />
                <Column
                    label="联系电话"
                    field="yddhhm"
                    align="center"
                    render={(v) => {
                        if (!v) {
                            return '--';
                        }

                        return maskPhoneNumber(v);
                    }}
                />
            </ShareList>
        </div>
    );
};

export default Contact;
