import React from 'react';

import { ModalTool } from '@share/shareui-polyfill';

import { Icon, Panel } from '@share/shareui';
// import { message as antdMessage } from 'antd';
//
export { message as antdMessage } from 'antd';

export default class MyAlert {
    /**
     * 提示消息框
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static msg(content, callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            backdrop: 'static',
            bsSize: 'sm',
            cancelText: null,
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_problem"
                            style={{
                                fontSize: '67px',
                                color: '#09d',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: '#09d',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            {content}
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 提示消息框--包含取消按钮
     * @param content 提示内容
     * @param callBack 取消按钮回调
     */
    static msgCancel(content, callBack, okCallBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            backdrop: 'static',
            bsSize: 'sm',
            cancelText: '取消',
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_problem"
                            style={{
                                fontSize: '67px',
                                color: '#09d',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: '#09d',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            {content}
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onCancel: () => {
                if (callBack) {
                    callBack();
                }
            },
            onOk: () => {
                if (okCallBack) {
                    okCallBack();
                }
            },
        });
    }

    /**
     * 操作成功提示
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static ok(content, callBack, onCancel) {
        return new ModalTool({
            title: '提示',
            bsStyle: 'success',
            closeBtn: false,
            backdrop: 'static',
            content,
            cancelText: null,
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
            onCancel: () => {
                if (onCancel) {
                    onCancel();
                }
            },
        });
    }

    /**
     * 操作失败提示
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static fail(content, callBack) {
        return new ModalTool({
            title: '提示',
            bsStyle: 'warning',
            closeBtn: false,
            backdrop: 'static',
            content,
            cancelText: null,
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 警告提示
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static warning(content, callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            backdrop: 'static',
            bsSize: 'sm',
            cancelText: callBack ? '取消' : null,
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_prompt"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            {content}
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 警告提示(筛查申请)
     * @param content 提示内容
     * @param callBack 确定按钮回调
     */
    static applyWarning(content, callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            backdrop: 'static',
            bsSize: 'sm',
            cancelText: null,
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_prompt"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            {content}
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 删除确认框
     * @param callBack 确定按钮回调
     */
    static delConfirm(callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            bsSize: 'sm',
            backdrop: 'static',
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_doubt"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            请确认是否删除？
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 操作确认框
     * @param callBack 确定按钮回调
     */
    static operateConfirm(callBack) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            bsSize: 'sm',
            backdrop: 'static',
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_doubt"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            请确认是否执行该操作？
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (callBack) {
                    callBack();
                }
            },
        });
    }

    /**
     * 内容自定义确认框
     * @param content 提示内容
     * @param onOk 确定按钮回调
     * @param onCancel 确定按钮回调
     */
    static confirm(content, onOk, onCancel) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            bsSize: 'sm',
            backdrop: 'static',
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_doubt"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            {content}
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (onOk) {
                    onOk();
                }
            },
            onCancel: () => {
                if (onCancel) {
                    onCancel();
                }
            },
        });
    }

    /**
     * 错误提示框
     * @param content 提示内容
     * @param customAttr
     */
    static errorModal(content, customAttr) {
        const defaultType = {
            title: '提示',
            bsStyle: 'warning',
            closeBtn: false,
            backdrop: 'static',
            content,
            cancelText: null,
        };

        return new ModalTool({
            ...defaultType,
            ...customAttr,
        });
    }

    static successModal(content, customAttr) {
        const defaultType = {
            title: '温馨提示',
            bsStyle: 'success',
            closeBtn: false,
            backdrop: 'static',
            content,
            cancelText: null,
        };

        return new ModalTool({
            ...defaultType,
            ...customAttr,
        });
    }

    /**
     * 自定义提示
     * @param content 提示内容
     * @param okBtnText 确认按钮名称
     * @param cancelBtnText 取消按钮名称
     * @param onCancel  取消按钮回调
     * @param onOk 确定按钮回调
     */
    static confirmAll(content, okBtnText, cancelBtnText, onOk, onCancel) {
        return new ModalTool({
            title: '提示',
            closeBtn: false,
            bsSize: 'sm',
            okText: okBtnText || '确定',
            cancelText: cancelBtnText || '取消',
            backdrop: 'static',
            content: (
                <Panel>
                    <Panel.Body>
                        <Icon
                            className="si si-com_problem"
                            style={{
                                fontSize: '67px',
                                color: 'orange',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        />
                        <dd
                            style={{
                                fontSize: '18px',
                                color: 'orange',
                                fontFamily: 'Microsoft YaHei',
                                marginTop: '5px',
                                textAlign: 'center',
                                display: 'block',
                            }}
                        >
                            {content}
                        </dd>
                    </Panel.Body>
                </Panel>
            ),
            onOk: () => {
                if (onOk) {
                    onOk();
                }
            },
            onCancel: () => {
                if (onCancel) {
                    onCancel();
                }
            },
        });
    }

    static timingInformation(content) {
        antdMessage.error(content);
    }
}
