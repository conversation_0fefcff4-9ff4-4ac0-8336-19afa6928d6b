import React from 'react';
// 用户管理器
const meManager = require('@/components/common/business/manager/MeManager');

const getUserComponent = (Component) => {
    return class extends React.Component {
        static defaultProps = {
            operateDefaultValue: '', // 操作默认值
            autoCheckedInitial: false, // 任何用户初始化自动选中自身
            autoCheckedAlways: false, // 任何用户始终始自动选中自身
            deptAutoCheckedInitial: false, // 部门用户初始化自动选中自身
            deptAutoCheckedAlways: false, // 部门用户始终自动选中自身
            deptAutoDisabled: false, // 部门用户自动锁死下拉框
            onlyValueAutoCheckedInitial: false, // 唯一值初始化自动选中自身
            onlyValueAutoCheckedAlways: false, // 唯一值始终自动选中自身
            onlyValueAutoDisabled: false, // 唯一值自动锁死
        };

        state = {
            disabled: false,
        };

        // 根据用户信息初始化值
        componentDidMount() {
            const {
                options,
                operateDefaultValue,
                autoCheckedInitial,
                autoCheckedAlways,
                deptAutoCheckedInitial,
                deptAutoCheckedAlways,
                deptAutoDisabled,
                onlyValueAutoCheckedInitial,
                onlyValueAutoCheckedAlways,
                onlyValueAutoDisabled,
            } = this.props;

            this.me = meManager.getMe();
            // 自动选中自身
            (autoCheckedInitial || autoCheckedAlways) && this.handleAutoChecked(this.props, operateDefaultValue);
            // 部门用户自动选中自身
            (deptAutoCheckedInitial || deptAutoCheckedAlways) && this.handleDeptAutoChecked(this.props, operateDefaultValue);
            // 部门用户自动锁死下拉框
            deptAutoDisabled && this.handleDeptDisabled();
            // 唯一值自动选中
            (onlyValueAutoCheckedInitial || onlyValueAutoCheckedAlways) && this.handleOnlyValueAutoChecked(this.props, options);
            // 唯一值自动锁死下拉框
            onlyValueAutoDisabled && this.handleOnlyValueAutoDisabled(options);
        }

        // 用于重置时仍回显固定自动选定值
        componentWillReceiveProps(nextProps) {
            const { options } = this.props;
            const { operateDefaultValue, autoCheckedAlways, deptAutoCheckedAlways, onlyValueAutoCheckedAlways, onlyValueAutoDisabled } =
                nextProps;

            autoCheckedAlways && this.handleAutoChecked(nextProps, operateDefaultValue);
            deptAutoCheckedAlways && this.handleDeptAutoChecked(nextProps, operateDefaultValue);
            onlyValueAutoCheckedAlways && this.handleOnlyValueAutoChecked(nextProps, options);
            onlyValueAutoDisabled && this.handleOnlyValueAutoDisabled(options);
        }

        // 自动选中默认值
        handleAutoChecked = (props, checkedValue) => {
            const { value, onChange, multi, acceptString } = props;

            if (!multi || acceptString) {
                value !== checkedValue && onChange && onChange({ target: { value: checkedValue } });

                return;
            }
            if (!Array.isArray(value) || value.length !== 1 || value[0] !== checkedValue) {
                onChange && onChange({ target: { value: [checkedValue] } });
            }
        };

        // 部门自动选中默认值
        handleDeptAutoChecked = (props, operateDefaultValue) => {
            !this.me.manager && this.handleAutoChecked(props, operateDefaultValue);
        };

        // 部门自动锁死
        handleDeptDisabled = () => {
            !this.me.manager && this.setState({ disabled: true });
        };

        // 单值自动选中
        handleOnlyValueAutoChecked = (props, options) => {
            options.length === 1 && this.handleAutoChecked(props, options[0].value);
        };

        // 单值自动锁死
        handleOnlyValueAutoDisabled = (options) => {
            options.length === 1 && this.setState({ disabled: true });
        };

        render() {
            const { disabled } = this.state;
            const {
                operateDefaultValue,
                autoCheckedInitial,
                autoCheckedAlways,
                deptAutoCheckedInitial,
                deptAutoCheckedAlways,
                deptAutoDisabled,
                onlyValueAutoCheckedInitial,
                onlyValueAutoCheckedAlways,
                onlyValueAutoDisabled,
                ...restProps
            } = this.props;

            return <Component disabled={disabled} {...restProps} />;
        }
    };
};

export default getUserComponent;
