import React, { Component } from 'react';
// 工具类
import * as Entity from '@/components/common/common/Entity';
// 子组件
import Table from '../ui/CommonPageTable';

class FrontPageTable extends Component {
    static defaultProps = {
        ...Table.defaultProps,
        cachePage: false, // 缓存分页
    };

    state = {
        dataSource: [],
        page: {
            currentPage: this.props.pagination.defaultCurrent || 1,
            pageSize: this.props.pagination.defaultPageSize || 10,
        },
    };

    // 初始化组件
    componentWillMount() {
        const { dataSource } = this.props;

        this.setState({ dataSource: [...dataSource] }, this.refreshTable);
    }

    // 自身引用传递给父组件
    componentDidMount() {
        const { onRef } = this.props;

        onRef && onRef(this);
    }

    // 当数据源发生变化时，当前页置为1
    componentWillReceiveProps(nextProps) {
        const {
            dataSource,
            page: { pageSize },
        } = this.state;
        const { dataSource: newDataSource, cachePage } = nextProps;

        if (!Entity.isEqualObject(dataSource, newDataSource)) {
            // 无分页缓存或数据长度不同，页面切换为第一页
            if (!cachePage || dataSource.length !== newDataSource.length) {
                this.setState({ page: { currentPage: 1, pageSize } });
            }
            this.setState({ dataSource: newDataSource }, this.refreshTable);
        }
    }

    // 列表变更事件
    onTableDataChange = (data) => {
        const { onTableDataChange } = this.props;

        onTableDataChange && onTableDataChange(data);
    };

    // 页数参数发生变化时（分页条点击事件）调用函数
    handlePagination = (currentPage, pageSize) => {
        const { page } = this.state;

        this.setState({ page: { ...page, currentPage, pageSize } }, this.refreshTable);
    };

    // 刷新数据
    refreshTable = (newDataSource) => {
        if (newDataSource) {
            this.setState({ dataSource: newDataSource }, this.refreshTable);

            return;
        }
        const {
            dataSource,
            page: { currentPage, pageSize },
        } = this.state;
        const pageCount = Math.ceil(dataSource.length / pageSize);

        if (currentPage > 1 && currentPage > pageCount) {
            this.setState({ page: { currentPage: pageCount, pageSize } }, this.refreshTable);

            return;
        }
        this.onTableDataChange(this.state);
    };

    render() {
        const { pagination, ...restProps } = this.props;
        const {
            dataSource,
            page: { currentPage, pageSize },
        } = this.state;
        const showDataSource = dataSource.slice((currentPage - 1) * pageSize, currentPage * pageSize);

        return (
            <Table
                {...restProps}
                dataSource={showDataSource}
                pagination={{
                    ...pagination,
                    current: currentPage,
                    pageSize,
                    total: dataSource.length,
                    onChange: this.handlePagination,
                }}
            />
        );
    }
}

export default FrontPageTable;
