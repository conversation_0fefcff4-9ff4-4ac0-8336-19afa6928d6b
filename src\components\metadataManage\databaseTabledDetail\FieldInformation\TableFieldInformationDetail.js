import React, { Component } from 'react';
// 列表组件
import { Panel, Form, Modal, Button } from '@share/shareui';
// 表码管理器
// const bmManager = require('@/components/common/business/manager/BmManager');

class TableFieldInformationDetail extends Component {
    render() {
        const {
            show,
            cancelFn,
            data: { detail },
        } = this.props;
        const {
            tableId,
            fieldCode,
            primaryKey,
            fieldDataType,
            fieldLength,
            fieldComment,
            fieldOrientedObjectType,
            openStyle,
            businessField,
            enabled,
            showLabel,
        } = detail;
        const primaryKeyName = primaryKey ? '是' : '否';
        // const fieldOrientedObjectTypeName = bmManager.getBmLabel('BM_OBJECT_TYPE', fieldOrientedObjectType);
        // const openStyleName = bmManager.getBmLabel('BM_OPEN_STYLE', openStyle);
        const businessFieldName = businessField ? '是' : '否';
        const enabledName = enabled ? '启用' : '停用';

        return (
            <Modal className="modal-full databaseTableFieldEditModal" show={show} onHide={cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>属性详情</Modal.Header>
                <Panel>
                    <Panel.Head title="字段基本信息" />
                    <Panel.Body full>
                        <Form pageType="detailPage">
                            <Form.Table>
                                <tr>
                                    <th />
                                    <td />
                                    <th />
                                    <td />
                                </tr>
                                <Form.Tr>
                                    <Form.Label>数据库表名</Form.Label>
                                    <Form.Content colSpan={3}>
                                        <span className="textShow" title={tableId}>
                                            {tableId}
                                        </span>
                                    </Form.Content>
                                </Form.Tr>
                                <Form.Tr>
                                    <Form.Label>数据表字段名</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={fieldCode}>
                                            {fieldCode}
                                        </span>
                                    </Form.Content>
                                    <Form.Label>是否主键</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={primaryKeyName}>
                                            {primaryKeyName}
                                        </span>
                                    </Form.Content>
                                </Form.Tr>
                                <Form.Tr>
                                    <Form.Label>数据类型</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={fieldDataType}>
                                            {fieldDataType}
                                        </span>
                                    </Form.Content>
                                    <Form.Label>数据长度</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={fieldLength}>
                                            {fieldLength}
                                        </span>
                                    </Form.Content>
                                </Form.Tr>
                                <Form.Tr>
                                    <Form.Label>字段备注</Form.Label>
                                    <Form.Content colSpan={3}>
                                        <span className="textShow" title={fieldComment}>
                                            {fieldComment}
                                        </span>
                                    </Form.Content>
                                </Form.Tr>
                            </Form.Table>
                        </Form>
                    </Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Head title="业务配置" />
                    <Panel.Body full>
                        <Form pageType="detailPage">
                            <Form.Table>
                                <tr>
                                    <th />
                                    <td />
                                    <th />
                                    <td />
                                </tr>
                                {/* <Form.Tr>
                                    <Form.Label>面向主体</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={fieldOrientedObjectTypeName}>
                                            {fieldOrientedObjectTypeName}
                                        </span>
                                    </Form.Content>
                                    <Form.Label>公开类型</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={openStyleName}>{openStyleName}</span>
                                    </Form.Content>
                                 </Form.Tr> */}
                                <Form.Tr>
                                    <Form.Label>是否业务字段</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={businessFieldName}>
                                            {businessFieldName}
                                        </span>
                                    </Form.Content>
                                    <Form.Label>停用/启用状态</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={enabledName}>
                                            {enabledName}
                                        </span>
                                    </Form.Content>
                                </Form.Tr>
                                <Form.Tr>
                                    <Form.Label>业务名称</Form.Label>
                                    <Form.Content>
                                        <span className="textShow" title={showLabel}>
                                            {showLabel}
                                        </span>
                                    </Form.Content>
                                </Form.Tr>
                            </Form.Table>
                        </Form>
                    </Panel.Body>
                </Panel>
                <Modal.Footer>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default TableFieldInformationDetail;
