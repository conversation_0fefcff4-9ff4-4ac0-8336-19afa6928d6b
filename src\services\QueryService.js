import { RemoteService } from '@share/framework';

class QueryService extends RemoteService {
    entityQuery = (request) => {
        return this.network.json('/query/entity.do', request);
    };

    detailQuery = (request) => {
        return this.network.json('/query/detail.do', request);
    };

    taskResult = (taskId) => {
        return this.network.formGet('/query/task.do', { taskId });
    };
}
export default QueryService;
