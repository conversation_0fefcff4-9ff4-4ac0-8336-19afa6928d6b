/*
 * @(#)  RuleItem.js  ---规则配置项
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 * Copyright: Copyright (c) 2024
 * Company: 厦门畅享信息技术有限公司
 * Author: yujy (<EMAIL>)
 * File Created: 2024-05-14 09:14:52
 */
import React, { Fragment, useCallback, useState, useEffect } from 'react';
import * as FormComponents from '@share/shareui-form';
import classNames from 'classnames';
import ArrayFormItemWrap from './ArrayFormItem';
import styles from './RuleMarking.scss';

const { Select, Input, RadioGroup } = FormComponents;

const RuleItem = ({ form, formKey = '', dataTableIdData = [], index }) => {
    const { column } = formKey ? form.getFieldValue(formKey) : form.getCurrentFormData();

    const [editParam, setEditParam] = useState({});

    const renderComponentItem = useCallback(() => {
        if (editParam?.componentId) {
            const ComponentItem = FormComponents[editParam.componentId];

            return (
                <Fragment>
                    {ComponentItem ? (
                        <ComponentItem
                            label="配置值"
                            {...editParam.componentParam}
                            rule="required"
                            noView
                            field={`${formKey ? `${formKey}.` : ``}value`}
                        />
                    ) : (
                        <Input
                            label="配置值"
                            // {...editParam.componentParam}
                            rule="required"
                            noView
                            field={`${formKey ? `${formKey}.` : ``}value`}
                        />
                    )}

                    <FormComponents.ErrorTip field={`${formKey ? `${formKey}.` : ``}value`} />
                </Fragment>
            );
        }

        return null;
    }, [editParam.componentId, editParam.componentParam, formKey]);

    useEffect(() => {
        if (column) {
            const findItem = dataTableIdData.find((item) => item.fieldCode === column) || {};
            setEditParam(findItem?.editParam || {});
        } else {
            setEditParam({});
            form.setFieldValue(`${formKey ? `${formKey}.` : ``}value`, '');
        }
    }, [column, dataTableIdData, form, formKey]);

    return (
        <div className={styles['RuleItem-rules']}>
            <div className={styles['RuleItem-rules_categoryItem']}>
                <div className={styles.table}>
                    <Select
                        options={dataTableIdData}
                        noView
                        field={formKey ? `${formKey}.column` : `column`}
                        labelKey="showLabel"
                        label="字段"
                        valueKey="fieldCode"
                        menuStyle={{ zIndex: '99999' }}
                        rule={['required']}
                    />
                    <FormComponents.ErrorTip field={formKey ? `${formKey}.column` : `column`} />
                </div>
                {editParam?.componentId && (
                    <Fragment>
                        <div className={styles.type}>
                            <Select
                                options={[
                                    { label: '取值', value: 'value' },
                                    { label: '计数', value: 'count' },
                                    { label: '求和', value: 'sum' },
                                    { label: '平均值', value: 'avg' },
                                    { label: '最大值', value: 'max' },
                                    { label: '最小值', value: 'min' },
                                ]}
                                noView
                                field={formKey ? `${formKey}.type` : `type`}
                                label="类型"
                                menuStyle={{ zIndex: '99999' }}
                                rule={['required']}
                            />
                            <FormComponents.ErrorTip field={formKey ? `${formKey}.type` : `type`} />
                        </div>
                        {/* 第三列 */}
                        <div className={styles.symbol}>
                            <Select
                                noView
                                rule="required"
                                options={[
                                    {
                                        label: '大于',
                                        value: 'gt',
                                    },
                                    {
                                        label: '小于',
                                        value: 'lt',
                                    },
                                    {
                                        label: '大于等于',
                                        value: 'gte',
                                    },
                                    {
                                        label: '小于等于',
                                        value: 'lte',
                                    },
                                    {
                                        label: '等于',
                                        value: 'eq',
                                    },
                                    {
                                        label: '不等于',
                                        value: 'ne',
                                    },
                                    {
                                        label: '含有',
                                        value: 'like',
                                    },
                                    {
                                        label: '结束于',
                                        value: 'likeLeft',
                                    },
                                    {
                                        label: '开始于',
                                        value: 'likeRight',
                                    },
                                    {
                                        label: '不包含',
                                        value: 'notLike',
                                    },
                                    {
                                        label: '在',
                                        value: 'in',
                                    },
                                    {
                                        label: '不在',
                                        value: 'notIn',
                                    },
                                ]}
                                // field={`condition.${p.index}.symbol`}
                                label=""
                                field={formKey ? `${formKey}.symbol` : `symbol`}
                            />

                            <FormComponents.ErrorTip field={formKey ? `${formKey}.symbol` : `symbol`} />
                        </div>
                        {/* 第四列 */}
                        <div className={styles.value}>{renderComponentItem()}</div>
                    </Fragment>
                )}
            </div>
        </div>
    );
};

const RuleWrap = (props) => {
    const { state, formKey = '', event = {}, form } = props;
    const { categoryList = [] } = state;
    const { getTableData } = event;
    const [dataTableIdData, setDataTableIdData] = useState([]);
    const { id, combinationRule, table } = (formKey ? form.getFieldValue(formKey) : form.getCurrentFormData()) || {};

    useEffect(() => {
        if (id && table) {
            const getData = async () => {
                const res = await getTableData(table);
                setDataTableIdData(res);
            };
            getData();
        } else {
            setDataTableIdData([]);
        }
    }, [getTableData, id, table]);

    return (
        <div
            className={classNames(styles.RuleItem, {
                [styles.displayFlex]: id && id !== 'combination',
            })}
        >
            <div
                className={classNames(styles['RuleItem-group'], {
                    [styles.w100]: id === 'combination',
                })}
            >
                <Select
                    options={categoryList}
                    noView
                    field={formKey ? `${formKey}.id` : 'id'}
                    labelKey="name"
                    label="规则"
                    valueKey="id"
                    menuStyle={{ zIndex: '99999' }}
                    rule={['required']}
                    className={styles['RuleItem-rules_select']}
                    onChange={async (e) => {
                        const findItem = categoryList.find((item) => item.id === e.target.value) || {};
                        console.log('🚀 ~ onChange={ ~ findItem:', findItem);

                        form.setFieldValue(formKey ? `${formKey}.combinationRule` : `combinationRule`, []);
                        // eslint-disable-next-line no-unused-expressions
                        formKey
                            ? form.setFieldValue(formKey, { id: findItem.id, table: findItem.dataTableId })
                            : form.setFormData({ id: findItem.id, table: findItem.dataTableId });
                    }}
                />
                <FormComponents.ErrorTip field={formKey ? `${formKey}.id` : 'id'} />

                {id === 'combination' && (
                    <div>
                        <RadioGroup
                            field={formKey ? `${formKey}.combinationLogic` : 'combinationLogic'}
                            noView
                            label="条件"
                            rule="required"
                            options={[
                                {
                                    label: '满足其一',
                                    value: 'or',
                                },
                                {
                                    label: '同时满足',
                                    value: 'and',
                                },
                            ]}
                        />
                        <FormComponents.ErrorTip field={formKey ? `${formKey}.combinationLogic` : 'combinationLogic'} />
                    </div>
                )}
            </div>

            {id && id !== 'combination' && (
                <Fragment>
                    <ArrayFormItemWrap
                        noView
                        rule={[
                            // eslint-disable-next-line max-params
                            (value, formData, field, timing) => {
                                if (timing === FormComponents.Timing.submit) {
                                    // 组合不能为空
                                    if ((value || []).length > 0) {
                                        return true;
                                    }

                                    return new Error('请添加具体的组合规则！');
                                }

                                return true;
                            },
                        ]}
                        field={formKey ? `${formKey}.condition` : 'condition'}
                        defaultChildrenData={{ type: 'value', symbol: 'eq' }}
                    >
                        {(p) => {
                            return (
                                <RuleItem
                                    {...p}
                                    form={form}
                                    state={state}
                                    event={event}
                                    dataTableIdData={dataTableIdData}
                                    formKey={formKey ? `${formKey}.condition.${p.index}` : `condition.${p.index}`}
                                />
                            );
                        }}
                    </ArrayFormItemWrap>
                    <FormComponents.ErrorTip field={formKey ? `${formKey}.condition` : 'condition'} />
                </Fragment>
            )}
            {id === 'combination' && (
                <div className={styles['RuleItem-rules']}>
                    <ArrayFormItemWrap
                        noView
                        rule={[
                            // eslint-disable-next-line max-params
                            (value, formData, field, timing) => {
                                if (timing === FormComponents.Timing.submit) {
                                    if (id === 'combination') {
                                        // 组合不能为空
                                        if (combinationRule?.length > 0) {
                                            return true;
                                        }

                                        return new Error('请添加具体的组合规则！');
                                    }

                                    return true;
                                }

                                return true;
                            },
                        ]}
                        field={formKey ? `${formKey}.combinationRule` : 'combinationRule'}
                        defaultChildrenData={{}}
                    >
                        {(p) => {
                            return (
                                <RuleWrap
                                    {...p}
                                    form={form}
                                    state={state}
                                    event={event}
                                    formKey={formKey ? `${formKey}.combinationRule.${p.index}` : `combinationRule.${p.index}`}
                                />
                            );
                        }}
                    </ArrayFormItemWrap>
                    <FormComponents.ErrorTip field={formKey ? `${formKey}.combinationRule` : 'combinationRule'} />
                </div>
            )}
        </div>
    );
};
export default RuleWrap;
