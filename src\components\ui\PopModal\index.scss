.popPromptBox {
    position: relative;

}

:global(.popPromptModel) {
    position: absolute;
    display: none;
    background: #fff;
    padding: 15px;
    right: 0;
    border: 1px solid #ccc;
    top: 30px;
    border-radius: 5px;
    z-index: 10;
}

:global(.ifShow) {
    display: block;
}

.input {
    width: 250px;
    height: 40px;
    padding-left: 5px;
    font-weight: normal;
}

:global(.popPromptBtn) {
    margin-top: 10px;
    text-align: right;

    >button {
        width: 50px;
        height: 30px;
        cursor: pointer;
    }

    button+button {
        margin-left: 10px;
    }
}
