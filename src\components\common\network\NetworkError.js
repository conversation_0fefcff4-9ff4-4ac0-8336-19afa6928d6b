class NetworkError extends Error {
    constructor(message, status, errorDetail, extra) {
        let msg = `[${message}][status=${status}]`;

        if (typeof errorDetail !== 'undefined') {
            msg += `[${errorDetail}]`;
        }
        if (typeof extra !== 'undefined') {
            msg += `[${extra}]`;
        }
        super(msg);
        this.orginMessage = message;
        this.status = status;
        this.errorDetail = errorDetail;
        this.extra = extra;
    }
}

module.exports = {
    NetworkError,
};
