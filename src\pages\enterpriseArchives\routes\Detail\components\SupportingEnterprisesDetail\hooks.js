/*
 *@(#) hooks.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-25
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import { useState, useMemo } from 'react';
import { useMount, useUpdateEffect } from 'ahooks';
import Decimal from 'decimal.js';
import { ObjectUtil } from '@share/common';
import { openTab } from '@/utils';

import { useService, useQuery, useCodeMapping } from '@share/framework';

import PolicySupportService from '@/services/PolicySupportService';

export const useModel = ({ id }) => {
    const { id: urlId } = useQuery();
    const creditCode = urlId || id;
    const [ZXBMDM, ZCFLDM] = useCodeMapping('ZXBMDM', 'ZCFLDM');
    const services = useService(PolicySupportService);
    const [activeFilter, setActiveFilter] = useState('');

    // 详情信息
    const [detailInfo, setDetailInfo] = useState({});

    // 用来控制‘点击查看更多’按钮是否显示
    const [showLookMore, setShowLookMore] = useState(false);

    // 扶持记录
    const [supportRecList, setSupportRecList] = useState([]);
    // {
    //     isYear: true, // 是否是统计的年
    //     time: '2022',
    //     hidden: false,
    //     isClose: false,
    //     yearNumber: 1 // 第几年，点击查看更多时，只有这个字段大于3的数据的hidden才改成true
    // },
    // {
    //     isYear: false, // 是否是统计的年
    //     time: '2023-03-01',
    //     hidden: false,
    // },

    // 年的模板数据
    const yearTemplateObj = useMemo(() => {
        return {
            isYear: true, // 是否是统计的年
            auditTime: '', // 年份
            hidden: false,
            isClose: false,
            yearNumber: 0, // 第几年，点击查看更多时，只有这个字段大于3的数据的hidden才改成true
        };
    }, []);

    // 初始化扶持记录(生成年份统计数据)
    const handleSupportRecList = (data) => {
        const dataPushYear = ObjectUtil.cloneDeep(data);
        const yearReadyList = []; // 用来存放已经生成的年份数据

        data.forEach((item, index) => {
            const { auditTime } = item;
            const date = new Date(auditTime);
            // 遇到一个新的年份，需要在当前数据之前插入年数据
            const fullYear = date.getFullYear();

            if (!yearReadyList.includes(fullYear)) {
                dataPushYear.splice(
                    index + yearReadyList.length,
                    0,
                    ObjectUtil.cloneDeep({
                        ...yearTemplateObj,
                        auditTime: fullYear,
                        yearNumber: yearReadyList.length + 1,
                        hidden: yearReadyList.length > 2,
                    })
                );
                yearReadyList.push(fullYear);
                // 超过3年的数据隐藏
                if (yearReadyList.length === 4) {
                    setShowLookMore(true);
                    dataPushYear.forEach((itemInner, indexInner) => {
                        itemInner.hidden = indexInner + 1 >= index + yearReadyList.length;
                    });
                }
            }
        });
        setSupportRecList(dataPushYear);
    };

    useMount(async () => {
        const res = await services.getDetailCompany({
            creditCode,
        });

        setDetailInfo(res);
        handleSupportRecList(res.supportRecList || []);
    });

    // 查看更多
    const lookMore = () => {
        // 思路：将年数据的isClose字段为false的相关数据的hidden全部改成false
        let canOpenHidden = false;

        supportRecList.forEach((item) => {
            const { isYear, isClose } = item;

            if (isYear) {
                canOpenHidden = !isClose;
                item.hidden = false;
            }

            if (canOpenHidden) {
                item.hidden = false;
            }
        });
        setShowLookMore(false);
    };

    // 收起/展开 年
    const toggleYear = (currentYear) => {
        // 思路：找到年份相等的数据，如果是年数据，isClose取反，如果不是，hidden取反
        supportRecList.forEach((item) => {
            const { isYear, auditTime } = item;

            if (currentYear === auditTime) {
                item.isClose = !item.isClose;

                return;
            }

            if (isYear) {
                return;
            }

            const dayFullYear = new Date(auditTime).getFullYear();

            if (currentYear === dayFullYear) {
                item.hidden = !item.hidden;
            }
        });
        setSupportRecList([...supportRecList]);
    };

    const getYearSupportTimes = (item) => {
        let supportTimes = 0;

        supportRecList.forEach(({ auditTime }) => {
            if (item.auditTime === new Date(auditTime).getFullYear()) {
                supportTimes += 1;
            }
        });

        return supportTimes;
    };

    const getYearSupportMoney = (item) => {
        let supportMoney = 0;

        supportRecList.forEach(({ auditTime, cashAmount }) => {
            if (item.auditTime === new Date(auditTime).getFullYear()) {
                supportMoney = new Decimal(supportMoney).add(new Decimal(cashAmount)).toNumber();
            }
        });

        return supportMoney;
    };

    // 跳转企业档案详情
    const goEnterpriseArchivesDetail = () => {
        const url = `${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/detail?id=${creditCode}`;

        // window.open(url); // mock
        openTab({ url, label: '企业档案', appId: creditCode });
    };

    const filterList = async (params) => {
        const resParams = params?.key === activeFilter ? '' : params;

        const res = await services.getComSupportList({
            ...(resParams || {}),
            creditCode,
        });

        if (!resParams) {
            setActiveFilter('');
        }

        if (resParams?.key) {
            setActiveFilter(resParams?.key);
        }

        if (Array.isArray(res)) {
            handleSupportRecList(res);
        }
    };

    return {
        ZXBMDM,
        ZCFLDM,
        detailInfo,
        supportRecList,
        setSupportRecList,
        lookMore,
        showLookMore,
        toggleYear,
        getYearSupportTimes,
        getYearSupportMoney,
        goEnterpriseArchivesDetail,
        filterList,
        activeFilter,
    };
};
