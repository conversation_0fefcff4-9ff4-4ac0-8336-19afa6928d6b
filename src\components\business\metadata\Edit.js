import React, { Component, Fragment } from 'react';
// 工具类
import * as formRule from '@/components/common/common/formRule';
// 组件
import { getComponents, Tip } from '../Form/index';
import './index.scss';

const { Form, Row, ...shareFormCons } = getComponents();

class Edit extends Component {
    buildComponent = (config) => {
        const { editType = 'add' } = this.props;
        const { componentId, componentParam, tip } = config.editParam;
        const Con = shareFormCons[componentId];

        if (!Con) {
            return '';
        }
        // 扩展参数
        const extendProps = componentParam
            ? Object.entries(componentParam).reduce((obj, [key, value]) => {
                  if (typeof value !== 'undefined' && value !== null) {
                      obj[key] = value;
                  }

                  return obj;
              }, {})
            : {};
        // 基础参数
        const comProps = {
            label: config.showLabel,
            field: config.fieldCode,
            rule: [],
            ...extendProps,
        };

        // 更新编辑锁死判定
        if (!comProps.disabled && comProps.editDisabled) {
            comProps.disabled = editType === 'update';
        }
        // 填充options参数
        if (Array.isArray(config.bmRule) && config.bmRule.length > 0) {
            const targetBmRule = config.bmRule[0] || { ruleParam: {} };

            comProps.options = targetBmRule.ruleParam.bmOptions || [];
        } else {
            comProps.options = [];
        }
        // 填充必填校验
        const checkRequired =
            Array.isArray(config.checkRule) &&
            config.checkRule.find(
                (rule) =>
                    rule.ruleId === 'notBlankCheck' &&
                    rule.ruleParam &&
                    (!Array.isArray(rule.ruleParam.preConditions) ||
                        (rule.ruleParam.preConditions.length === 0 && rule.ruleParam.checkLevel !== '1'))
            );

        if (checkRequired) {
            comProps.rule.push(formRule.checkRequiredNotBlank());
            comProps.required = true;
        }
        // 填充字段长度校验
        if (Array.isArray(config.fieldLength) && config.fieldLength.length > 0) {
            if (config.fieldLength.length === 1 && Number.isInteger(config.fieldLength[0])) {
                comProps.rule.push(formRule.checkLength(null, config.fieldLength[0]));
            } else if (
                Number.isInteger(config.fieldLength[0]) &&
                Number.isInteger(config.fieldLength[1]) &&
                config.fieldLength[0] > config.fieldLength[1]
            ) {
                const integerLength = config.fieldLength[0] - config.fieldLength[1];
                const decimalLength = config.fieldLength[1] || 0;
                const regex = `^-?[0-9]{0,${integerLength}}$|^-?[0-9]{0,${integerLength}}\\.[0-9]{0,${decimalLength}}$`;
                const errorMsg = `请输入整数不大于${integerLength}位，小数不大于${decimalLength}位的数字`;

                comProps.rule.push(formRule.checkRegex(regex, errorMsg));
            }
        }
        // 添加提示示例
        comProps.rowRatio = comProps.rowRatio || 1;
        const tipColSpn = tip ? (comProps.rowRatio === 1 ? 3 : 1) : 0;
        const tipCon =
            tipColSpn > 0 ? (
                <Tip
                    colSpan={tipColSpn}
                    style={{ padding: '0 20px' }}
                    message={/<.*?>.*?<\/.*?>/.test(tip) ? <div dangerouslySetInnerHTML={{ __html: tip }} /> : tip}
                />
            ) : (
                ''
            );
        const colSpan = comProps.rowRatio * 6 - 1 - tipColSpn;

        return {
            colSpan: comProps.rowRatio * 6,
            Com: (
                <Fragment>
                    <Con {...comProps} colSpan={colSpan} />
                    {tipCon}
                </Fragment>
            ),
        };
    };

    buildComponents = (configList) => {
        // 计算单行组件个数
        const row = { colSpan: 0, nodes: [] };
        const result = [];

        configList
            .map((item) => this.buildComponent(item))
            .forEach((item) => {
                if (row.colSpan + item.colSpan > 6) {
                    result.push({ ...row });
                    row.colSpan = item.colSpan;
                    row.nodes = [item.Com];
                } else {
                    row.colSpan += item.colSpan;
                    row.nodes.push(item.Com);
                }
            });
        if (row.colSpan > 0) {
            result.push({ ...row });
        }

        return result.map((item) => item.nodes).map((item) => <Row>{item}</Row>);
    };

    render() {
        const { formState, metadataConfigList = [] } = this.props;
        const editCons = this.buildComponents(metadataConfigList);

        return (
            <Form pageType="addPage" formState={formState}>
                <tr>
                    <th />
                    <td />
                    <td />
                    <th />
                    <td />
                    <td />
                </tr>
                {editCons}
            </Form>
        );
    }
}

export default Edit;
