// 工具类
// import * as LoginUtil from '../common/LoginUtil';
import MyAlert from '@/components/ui/MyAlert';

const { NetError } = require('../common/Errors');
const { NetworkError } = require('./NetworkError');
const { RESPONSE_STATUS } = require('../common/Enum');

export function turnLoginPage(url) {
    let loginUrl;

    if (/^https?:\/\/.*$|^([1-9]|[1-9]\d|1\d\d|2[01]\d|22[0-3])(\.([1-9]|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])){3}.*$/.test(url)) {
        loginUrl = url;
    }
    if (!loginUrl) {
        loginUrl = '/credit-rabbit-front/#/';
    }
    // 判断是否在iframe中
    if (window !== top) {
        top.location.href = loginUrl;
    } else {
        window.location.href = loginUrl;
    }
}

export function assertNetWork(jsonObject) {
    const { status, orginMessage } = jsonObject;

    // 请求路径不存在
    if (status === 404) {
        MyAlert.fail('请求路径不存在，请联系开发人员');
        throw new NetError(`[网络异常][${status}][${orginMessage}]`, jsonObject);
    }
    // 请求方式错误
    if (status === 405) {
        MyAlert.fail('请求方式错误，请联系开发人员');
        throw new NetError(`[网络异常][${status}][${orginMessage}]`, jsonObject);
    }
    // 服务器异常
    if (status === 500) {
        MyAlert.fail('服务器异常，请联系管理员');
        throw new NetError(`[网络异常][${status}][${orginMessage}]`, jsonObject);
    }
    // 无法连接服务器
    if (status === 502 || status === 504 || !status) {
        MyAlert.fail('无法连接服务器，请联系管理员');
        throw new NetError(`[网络异常][${status}][${orginMessage}]`, jsonObject);
    }
}

export function assertServerException(jsonObject, abort, errorInfoResolver = (data) => MyAlert.fail(data.errorDetail)) {
    const { status, data, errorDetail } = jsonObject;

    // 网络状况判定
    assertNetWork(jsonObject);
    // 校验成功
    if (status === RESPONSE_STATUS.SUCCESS) {
        return Promise.resolve(data);
    }
    // 登录超时
    if (status === RESPONSE_STATUS.NOT_AUTH) {
        errorInfoResolver({ status, data, errorDetail: '登录超时，请重新登录' });
        window.setTimeout(() => turnLoginPage(errorDetail), 2000);
        throw new NetError(`[后台错误][${RESPONSE_STATUS.NOT_AUTH}][${errorDetail}]`, jsonObject);
    }
    // 被挤下线
    if (status === RESPONSE_STATUS.LOGIN_ON_OTHER_DEVICE) {
        errorInfoResolver({ status, data, errorDetail: '该账号已在其他地方登录，请重新登录' });
        window.setTimeout(() => turnLoginPage(errorDetail), 2000);
        throw new NetError(`[后台错误][${RESPONSE_STATUS.LOGIN_ON_OTHER_DEVICE}][${errorDetail}]`, jsonObject);
    }
    // 字段校验异常
    if (status === RESPONSE_STATUS.VALID_FIELD_ILLEGAL) {
        const errorMessage = errorDetail.map((item) => `${item.field}${item.message}`).join(',');

        errorInfoResolver({ status, data, errorDetail: errorMessage });
        throw new NetError(`[后台错误][${RESPONSE_STATUS.VALID_FIELD_ILLEGAL}][${errorMessage}]`, jsonObject);
    }
    // 未知异常
    errorInfoResolver(jsonObject);
    throw new NetError(`[后台错误][${status}][${errorDetail}]`, jsonObject);
}

export function resolverServerException(respnseObject) {
    const { status, data, errorDetail } = respnseObject;

    if (status === '1200') {
        return data;
    }
    throw new NetworkError('后台错误', status, errorDetail, '');
}

export function resolverOldServerException(respnseObject) {
    const { code, data, msg = '后台错误' } = respnseObject;

    if (code === 0) {
        return data;
    }
    if (code === '404') {
        throw new NetworkError(msg, RESPONSE_STATUS.NOT_AUTH, '授权已过期，禁止访问');
    }
    if (code === '603') {
        throw new NetworkError(msg, RESPONSE_STATUS.LOGIN_ON_OTHER_DEVICE, '该账号已在其他地方登录，请重新登录');
    }
    throw new NetworkError(msg, '1500', msg, '');
}
