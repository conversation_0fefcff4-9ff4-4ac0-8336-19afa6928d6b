/*
 * @(#) DataBaseInfoEdit.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-07-31 10:05:10
 */
import React, { Component } from 'react';
// 服务接口
import * as MetaTableApi from '@/services/data/meta/MetaTableApi';
// 工具类
import * as StringUtils from '@/components/common/common/StringUtils';
import * as ArrayUtil from '@/components/common/common/ArrayUtil';
import * as formRule from '@/components/common/common/formRule';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
// 组件
import MyAlert from '@/components/ui/MyAlert/MyAlert';
import { Button, Modal } from '@share/shareui';
import { FormState, getComponents, Tip } from '@/components/business/Form';

const { Form, Row, Input, RadioGroup, Select } = getComponents();

class DatabaseInfoEdit extends Component {
    state = {
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
        databaseTableList: [],
        databaseIdOptions: [],
        tableIdOptions: [],
    };

    componentDidMount() {
        this.requestInit();
    }

    componentWillReceiveProps(nextProps) {
        const { showModal, editBody, tableIdList } = nextProps;

        if (!this.props.showModal && showModal) {
            this.requestInit(() => {
                const { editForm, databaseTableList } = this.state;
                const tableIdOptions = databaseTableList.map((item) => ({
                    label: `${item.tableName}(${item.tableComment || ''})`,
                    value: `${item.databaseName}.${item.tableName}`,
                    disabled: tableIdList.includes(item.tableName),
                }));

                editForm.setFormData(editBody);
                editForm.cleanValidError();
                this.setState({ tableIdOptions });
            });
        }
    }

    requestInit = async (callback) => {
        const databaseTableList = await MetaTableApi.getDatabaseSource();
        const databaseIdOptions = ArrayUtil.distinct(databaseTableList.map((item) => item.databaseName)).map((item) => ({
            label: item,
            value: item,
        }));

        this.setState({ databaseTableList, databaseIdOptions }, () => callback && callback());
    };

    submitEdit = async () => {
        const { editForm } = this.state;
        const { onSuccessFn, onHide } = this.props;
        const valids = await editForm.valid();

        if (!FormVaildHelper.isValid(valids)) {
            return;
        }
        const data = StringUtils.deleteSpace(editForm.getFormData());

        if (data.isModify) {
            await MetaTableApi.updateDataTable(data);
            MyAlert.ok('保存成功');
        } else {
            await MetaTableApi.addDataTable(data);
            MyAlert.ok('新增成功');
        }
        onSuccessFn && onSuccessFn();
        onHide && onHide();
    };

    render() {
        const { showModal, onHide, tableIdList } = this.props;
        const { editForm, databaseTableList, databaseIdOptions, tableIdOptions } = this.state;
        const { isModify } = editForm.getFormData();

        return (
            <Modal bsSize="large" show={showModal} onHide={onHide}>
                <Modal.Header closeButton>{isModify ? '修改库表' : '新增库表'}</Modal.Header>
                <Modal.Body full>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <Select
                                label="库名"
                                field="databaseId"
                                options={databaseIdOptions}
                                rule={formRule.checkRequiredNotBlank()}
                                required
                                onChange={({ target: { value } }) => {
                                    const newTableIdOptions = databaseTableList
                                        .filter((item) => !value || item.databaseName === value)
                                        .map((item) => ({
                                            label: `${item.tableName}(${item.tableComment || ''})`,
                                            value: `${item.databaseName}.${item.tableName}`,
                                            disabled: tableIdList.includes(`${item.databaseName}.${item.tableName}`),
                                        }));

                                    this.setState({ tableIdOptions: newTableIdOptions });
                                    editForm.setFieldValue('tableId', '');
                                    editForm.setFieldValue('tableDescription', '');
                                }}
                                disabled={isModify}
                            />
                            <Tip message="请选择数据库名称" />
                        </Row>
                        <Row>
                            <Select
                                label="数据表"
                                field="tableId"
                                options={tableIdOptions}
                                rule={formRule.checkRequiredNotBlank()}
                                required
                                onChange={({ target: { value } }) => {
                                    if (value) {
                                        const table =
                                            databaseTableList.find((item) => `${item.databaseName}.${item.tableName}` === value) || {};

                                        editForm.setFieldValue('databaseId', table.databaseName || '');
                                        editForm.setFieldValue('tableDescription', table.tableComment || '');
                                    } else {
                                        editForm.setFieldValue('tableDescription', '');
                                    }
                                }}
                                disabled={isModify}
                            />
                            <Tip message="请选择数据表名称" />
                        </Row>
                        <Row>
                            <Input
                                label="数据表说明"
                                field="tableDescription"
                                placeholder="请输入"
                                rule={[formRule.checkRequiredNotBlank(), formRule.checkLength(1, 300)]}
                                required
                            />
                            <Tip message="请填写数据表说明" />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="是否支持人工填报"
                                field="manualFilled"
                                options={[
                                    { value: '1', label: '支持' },
                                    { value: '0', label: '不支持' },
                                ]}
                                rule={formRule.checkRequiredNotBlank()}
                                required
                            />
                            <Tip message="请选择是否支持人工填报" />
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submitEdit}>
                        保存
                    </Button>
                    <Button onClick={onHide}>取消</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default DatabaseInfoEdit;
