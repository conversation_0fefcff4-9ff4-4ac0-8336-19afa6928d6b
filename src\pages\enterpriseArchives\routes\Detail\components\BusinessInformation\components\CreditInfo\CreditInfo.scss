.creditInfo {
    .siteCustomTabBar {
        background-color: #f2f8ff;
        padding-left: 16px;
        // border-bottom: 1px solid #d8e7ff;
        position: relative;
        &:after {
            content: '';
            height: 1px;
            background-color: #d8e7ff;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 0;
        }
        :global {
            .ant-tabs-nav-wrap {
                z-index: 1;
            }
            .ant-tabs-tab {
                border-top: none;
                border-color: #d8e7ff;
                background: #f2f8ff;
                font-family: Microsoft YaHei;
                font-size: 14px;
                font-weight: normal;
                color: #8c8c8c;
                margin: 0 !important;
                &.ant-tabs-tab-active {
                    color: #0061e8;
                    background: #fff;
                    border-bottom-color: #fff;
                    border-top-color: #fff;
                }
            }
        }
    }
 .alert {
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            padding: 12px 32px 12px 12px;
            position: relative;
            border: 1px solid transparent;
            border-radius: 4px;

            color: #096;
            border-color: #0b8;
            background-color: #e5fcf3;
            i {
                font-size: 16px;
                margin-right: 8px;
            }
        }
    .tabContent {
        :global {
            .ant-collapse {
                border: none;
                background-color: #fff;
                &-item {
                    border: none;
                    margin-bottom: 20px;
                    .ant-collapse-header {
                        padding: 0;
                    }
                    .ant-collapse-header-text {
                        flex: 1;
                    }
                    .ant-collapse-content {
                        border-top: none;
                    }
                }
            }
        }
       
        .infoTable {
            border-top: solid 1px #d2e4ff;
            border-left: solid 1px #d2e4ff;
            border-right: solid 1px #d2e4ff;
            &-row {
                display: flex;
          align-items: stretch;
                border-bottom: solid 1px #d2e4ff;
                &_label,
                &_value {
                    font-family: Microsoft YaHei;
                    font-size: 14px;
                    font-weight: normal;
                    font-stretch: normal;
                    color: rgba(0, 0, 0, 0.85);
                    padding: 13px 16px;
                }
                &_label {
                    background-color: #f2f8ff;
                    	width: 144px;
                        text-align: left;
                }
                &_value {
                    background-color: #ffffff;
                    flex:1;
                }
            }
        }
    }
}
