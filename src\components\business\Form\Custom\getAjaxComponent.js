import React from 'react';
// 工具类
import * as Entity from '@/components/common/common/Entity';

const getAjaxComponent = (Component, defaultProps) => {
    return class extends React.Component {
        static defaultProps = {
            labelKey: 'label', // label属性键
            valueKey: 'code', // value属性键
            optionsKey: 'options', // 数据属性值
            handlerFn: (data) => data, // 数据处理函数
            ...defaultProps,
        };

        state = {
            options: [],
        };

        componentDidMount() {
            this.request(this.props);
        }

        componentWillReceiveProps(nextProps) {
            const { service, params, data } = this.props;
            const { service: newService, params: newParams, data: newData } = nextProps;

            if (service !== newService || !Entity.isEqualObject(params, newParams) || data !== newData) {
                this.request(nextProps);
            }
        }

        request = async (props) => {
            const { data, service, params = {}, handlerFn } = props;
            const options = this.formatOptions(handlerFn(data || (await service(params))));

            this.setState({ options });
        };

        formatOptions = (data) => {
            const { labelKey, valueKey } = this.props;

            return data.map((i) => ({ ...i, label: i[labelKey], value: i[valueKey] || i.value || '' }));
        };

        render() {
            const { options } = this.state;
            const { labelKey, valueKey, optionsKey, handlerFn, ...restProps } = this.props;

            restProps[optionsKey] = options;

            return <Component {...restProps} />;
        }
    };
};

export default getAjaxComponent;
