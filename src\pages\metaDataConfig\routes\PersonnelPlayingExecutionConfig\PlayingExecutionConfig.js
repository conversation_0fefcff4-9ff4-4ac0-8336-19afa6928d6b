/*
 * @(#)  PlayingExecutionConfig.js  ---PlayingExecutionConfig
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 * Copyright: Copyright (c) 2024
 * Company: 厦门畅享信息技术有限公司
 * Author: yujy (<EMAIL>)
 * File Created: 2024-05-08 16:12:34
 */

import React, { Fragment } from 'react';
import { Panel } from '@share/shareui';
import ResizeLayout from '@/components/ResizeLayout';
import Sidebar from '@/pages/metaDataConfig/components/PersonSidebar';
import useTreeHooks from '@/pages/metaDataConfig/publicHooks/usePersonTreeHooks';
import styles from './PlayingExecutionConfig.scss';
import LabelRecord from './components/LabelRecord';

const PlayingExecutionConfig = (props) => {
    const { onSelect, selectNode, treeDataSource, refresh } = useTreeHooks(props);

    return (
        <Panel>
            <Panel.Body>
                <div className={styles.PlayingExecutionConfig}>
                    <ResizeLayout
                        sider={
                            <div className={styles.leftTree}>
                                {treeDataSource.treeData.length > 0 && (
                                    <Sidebar
                                        refresh={refresh}
                                        treeDataSource={treeDataSource}
                                        onSelect={onSelect}
                                        selectNode={selectNode}
                                        isEdit={false}
                                    />
                                )}
                            </div>
                        }
                        content={
                            <div className={styles.rightForm}>
                                <LabelRecord selectNode={selectNode} />
                                {/* <ConfigForm {...props} selectNode={selectNode} key={selectNode.id} refresh={refresh} /> */}
                            </div>
                        }
                    />
                </div>
            </Panel.Body>
        </Panel>
    );
};
export default PlayingExecutionConfig;
