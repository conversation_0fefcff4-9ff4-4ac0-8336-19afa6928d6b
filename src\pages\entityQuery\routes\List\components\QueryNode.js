import React, { useEffect } from 'react';
import { Timing, Input } from '@share/shareui-form';
import { addErrorTip, registerFormItem, validFunction } from '@/utils/shareFormUtil';
import style from './QueryNode.scss';
import DataNode from './DataNode';

const InputCus = addErrorTip(Input);

const QueryNode = (props) => {
    const { value, onChange, field, inventoryList, queryNodeList, suffix } = props;

    useEffect(() => {
        onChange({
            target: { value: { ...value, alias: value?.data?.result?.id ? `${value?.data?.id}-${value?.data?.result?.id}` : '' } },
        });
    }, [value?.data?.result?.id]);

    return (
        <div className={style.body}>
            <DataNode
                field={`${field}.data`}
                label="数据节点"
                inventoryList={inventoryList}
                noView
                suffix={
                    <div className={style.operate}>
                        {(value?.data?.result?.id || value?.data?.composeLogic) && (
                            <InputCus
                                className={style.alias}
                                field={`${field}.alias`}
                                label="查询别名"
                                noView
                                rule={[
                                    'required',
                                    validFunction({
                                        fn: (v) => !v || queryNodeList.map((item) => item.alias).filter((item) => item === v).length < 2,
                                        errMsg: '存在重复',
                                        timing: Timing.blur,
                                    }),
                                    validFunction({
                                        fn: (v) => !v || !/^.*\."+.*$/.test(v),
                                        errMsg: '不允许存在点和双引号',
                                        timing: Timing.blur,
                                    }),
                                ]}
                                placeholder="查询别名"
                            />
                        )}
                        <div>{suffix}</div>
                    </div>
                }
            />
        </div>
    );
};

export default registerFormItem(QueryNode);
