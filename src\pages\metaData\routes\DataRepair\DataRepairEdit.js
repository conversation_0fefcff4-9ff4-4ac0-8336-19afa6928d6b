/*
 *@(#) DataRepairEdit.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-26
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import React from 'react';
// styles
import styles from '@/pages/metaData/styles/creditData.scss';
import classnames from 'classnames';
// 服务接口
import * as DataRepairApi from '@/services/data/data/DataRepairApi';
import * as DataQueryApi from '@/services/data/data/DataQueryApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
import * as formRule from '@/components/common/common/formRule';
import * as TimeUtil from '@/components/common/common/TimeUtil';
// 组件
import DataFilling from '@/components/credit_data/DataFilling';
import RoleAuthControl from '@/components/business/auth/RoleAuthControl/RoleAuthControl';
import MultiClamp from '@/components/ui/MultiClamp/index';
import { Panel, ButtonToolBar, Button } from '@share/shareui';
import { getComponents } from '@/components/business/Form';

const { Form, ...shareFormCons } = getComponents();

class DataRepairEdit extends DataFilling {
    state = {
        ...this.state,
        // 目录信息
        pageData: [],
        information: ['修正前', '修正后'],
    };

    initData = async () => {
        const { categoryId, recordId } = this.props.match.params;
        const { primaryKey, editConfigList } = this.state;
        const data = await DataRepairApi.editDetailByCategory(categoryId, '2', recordId);
        let consultData = data;

        // 重复数据
        if (data.YW_ID && data.ERROR_TYPES && data.ERROR_TYPES.includes('suspectedRepeat')) {
            const dataEdit = await DataQueryApi.editDetailByCategoryYwId(categoryId, '2', data.YW_ID);

            if (dataEdit[primaryKey]) {
                consultData = dataEdit;
                this.setState({ information: ['已入库', '待确认'] });
            }
        }
        this.fillPageData(editConfigList, consultData);
        this.fillFormData(editConfigList, data);
        this.fillErrorMsg(data);
    };

    // 填充页面信息
    fillPageData = (editConfig, questionData) => {
        const checkRequired = (configItem) =>
            Array.isArray(configItem.checkRule) &&
            configItem.checkRule.find(
                (rule) =>
                    rule.ruleId === 'notBlankCheck' &&
                    rule.ruleParam &&
                    (!Array.isArray(rule.ruleParam.preConditions) ||
                        (rule.ruleParam.preConditions.length === 0 && rule.ruleParam.checkLevel !== '1'))
            );
        const buildComponent = (config) => {
            const { componentId, componentParam } = config.editParam;
            const Con = shareFormCons[componentId];

            if (!Con) {
                return '';
            }
            // 扩展参数
            const extendProps = componentParam
                ? Object.entries(componentParam).reduce((obj, [key, value]) => {
                      if (typeof value !== 'undefined' && value !== null && value !== '') {
                          obj[key] = value;
                      }

                      return obj;
                  }, {})
                : {};
            // 基础参数
            const comProps = {
                label: config.showLabel,
                field: config.fieldCode,
                rule: [],
                showLongTimeButton: true,
                ...extendProps,
            };

            // 填充options参数
            if (Array.isArray(config.bmRule) && config.bmRule.length > 0) {
                const targetBmRule = config.bmRule[0] || { ruleParam: {} };

                comProps.options = targetBmRule.ruleParam.bmOptions || [];
            }
            // 填充必填校验
            if (checkRequired(config)) {
                comProps.rule.push(formRule.checkRequiredNotBlank());
                comProps.required = true;
            }
            // 填充字段长度校验
            if (Array.isArray(config.fieldLength) && config.fieldLength.length > 0) {
                if (config.fieldLength.length === 1 && Number.isInteger(config.fieldLength[0])) {
                    comProps.rule.push(formRule.checkLength(null, config.fieldLength[0]));
                } else if (
                    Number.isInteger(config.fieldLength[0]) &&
                    Number.isInteger(config.fieldLength[1]) &&
                    config.fieldLength[0] > config.fieldLength[1]
                ) {
                    const integerLength = config.fieldLength[0] - config.fieldLength[1];
                    const decimalLength = config.fieldLength[1] || 0;
                    const regex = `^-?[0-9]{0,${integerLength}}$|^-?[0-9]{0,${integerLength}}\\.[0-9]{0,${decimalLength}}$`;
                    const errorMsg = `请输入整数不大于${integerLength}位，小数不大于${decimalLength}位的数字`;

                    comProps.rule.push(formRule.checkRegex(regex, errorMsg));
                }
            }

            return <Con {...comProps} />;
        };
        const bmValueToLabel = (configItem, value) => {
            const targetBmRule = configItem.bmRule[0] || { ruleParam: {} };
            const options = targetBmRule.ruleParam.bmOptions || [];
            const singleOpt = options.find((one) => one.value === value) || {};

            return singleOpt.label || value;
        };
        const errorMsg = JSON.parse(questionData.ERROR_MSG || '{}');
        const pageData = editConfig.map((item) => {
            let currentValue = questionData[item.fieldCode] || '';

            // 表码转值（value转label）填充
            if (Array.isArray(item.bmRule) && item.bmRule.length > 0) {
                // 多值校验
                if (item.editParam.componentId.includes('MultiSelect') || item.editParam.componentId.includes('CheckboxGroup')) {
                    currentValue = Array.isArray(currentValue) ? currentValue : currentValue.split(',');
                    currentValue = currentValue
                        .map((one) => bmValueToLabel(item, one))
                        .filter((one) => one !== '')
                        .join(',');
                } else {
                    currentValue = bmValueToLabel(item, currentValue);
                }
            }

            return {
                require: checkRequired(item),
                itemLabel: item.showLabel,
                currentValue,
                color: errorMsg[item.fieldCode] ? 'red' : 'black',
                input: () => buildComponent(item),
                tips: item.editParam ? <div dangerouslySetInnerHTML={{ __html: item.editParam.tip }} /> : '',
            };
        });

        this.setState({ pageData });
    };

    // 填充表单初始默认值
    fillFormData = (editConfig, questionData) => {
        const { editForm, primaryKey } = this.state;
        const errorMsg = JSON.parse(questionData.ERROR_MSG);
        const bmLabelToValue = (configItem, label) => {
            const targetBmRule = configItem.bmRule[0] || { ruleParam: {} };
            const options = targetBmRule.ruleParam.bmOptions || [];
            const singleOpt = options.find((one) => one.value === label) || {};

            return singleOpt.value || '';
        };
        const originalFormData = {};

        editConfig.forEach((item) => {
            let value = questionData[item.fieldCode] || '';

            // 表码转值（label转value）填充
            if (Array.isArray(item.bmRule) && item.bmRule.length > 0) {
                // 多值校验
                if (item.editParam.componentId.includes('MultiSelect') || item.editParam.componentId.includes('CheckboxGroup')) {
                    value = Array.isArray(value) ? value : value.split(',');
                    value = value
                        .map((one) => bmLabelToValue(item, one))
                        .filter((one) => one !== '')
                        .join(',');
                } else {
                    value = bmLabelToValue(item, value);
                }
            }
            // 时间组件校验 ，时间错误，清空
            if (item.editParam.componentId.includes('Date') && !TimeUtil.isValidDateFormat(value)) {
                if (TimeUtil.isValidDateTimeFormat(value)) {
                    value = value.replace(/^(\d{4}-\d{2}-\d{2}) \d{2}:\d{2}:\d{2}$/g, '$1');
                } else {
                    value = '';
                }
            }
            if (item.editParam.componentId.includes('Time') && !TimeUtil.isValidDateTimeFormat(value)) {
                if (TimeUtil.isValidDateFormat(value)) {
                    value += ' 00:00:00';
                } else {
                    value = '';
                }
            }
            originalFormData[item.fieldCode] = value;
            // 添加初始错误信息提示
            if (Array.isArray(errorMsg[item.fieldCode]) && errorMsg[item.fieldCode].length > 0) {
                editForm.setValidError(item.fieldCode, errorMsg[item.fieldCode].join(';'));
            }
        });
        originalFormData[primaryKey] = questionData[primaryKey];
        editForm.setFormData(originalFormData);
    };

    // 填充联合校验信息
    fillErrorMsg = (questionData) => {
        const { primaryKey } = this.state;
        const errorMsg = JSON.parse(questionData.ERROR_MSG);
        const composeCheckErrorMsg = Array.isArray(errorMsg[primaryKey]) ? errorMsg[primaryKey] : [];
        const checkErrorLevel = questionData.ERROR_LEVEL;

        this.setState({ checkErrorLevel, composeCheckErrorMsg });
    };

    // 提交Api
    submitApi = (ignoreSuspectedError, ignoreQuestionData) => {
        const { categoryId } = this.props.match.params;
        const { editForm } = this.state;
        const data = editForm.getFormData();

        return DataRepairApi.repairByCategory(categoryId, '2', data, ignoreSuspectedError, ignoreQuestionData);
    };

    // 提交成功
    afterSuccess = () => {
        this.cancel();
    };

    render() {
        const { editConfigList, editForm, checkErrorLevel, composeCheckErrorMsg, pageData, information } = this.state;

        return (
            <div className={styles.onlineDataRepaire}>
                <Panel>
                    <Panel.Head title="数据修正" />
                    <Panel.Body full>
                        <div
                            className={classnames({
                                [styles.errorBorder]: composeCheckErrorMsg.length !== 0,
                            })}
                        >
                            <Form pageType="addPage" formState={editForm}>
                                <table className={styles.table}>
                                    <tbody className={styles.tbody}>
                                        <tr className={styles.tableHead}>
                                            <th width="17%">数据项</th>
                                            <th width="28%">{information['0']}</th>
                                            <th width="28%">{information['1']}</th>
                                            <th width="27%" />
                                        </tr>
                                        {pageData &&
                                            pageData.map((item, index) => (
                                                <tr key={index} className={styles.tr}>
                                                    <td className={styles.td}>
                                                        {item.require && <em className="ico-require">*</em>}
                                                        {item.itemLabel}
                                                    </td>
                                                    <td className={styles.td}>
                                                        <MultiClamp
                                                            className={classnames({
                                                                'text-danger': item.textDanger,
                                                            })}
                                                            clamp={item.lineOver ? item.lineOver : 1}
                                                            title={item.currentValue}
                                                        >
                                                            <span style={{ color: item.color }}>{item.currentValue}</span>
                                                        </MultiClamp>
                                                    </td>
                                                    <td className={styles.td}>
                                                        <div className="dataRepaireFormFix">{item.input()}</div>
                                                    </td>
                                                    <td className={styles.td}>
                                                        {item.tips && (
                                                            <span className={styles.tips}>
                                                                <i className="si si-com_problem" />
                                                                <em className={styles.tipText}>{item.tips}</em>
                                                            </span>
                                                        )}
                                                    </td>
                                                </tr>
                                            ))}
                                    </tbody>
                                </table>
                            </Form>
                        </div>
                        <div className={styles.bottomTip}>
                            <span className={styles.tips}>
                                <i className="si si-com_problem" />
                                <em className={styles.tipText}>
                                    1、带“
                                    <em className="text-danger">*</em>
                                    ”的为必填项； 2、
                                    <em className="text-danger">标红</em>
                                    的地方为问题出现的地方，输入框下方红色字体文字表示数据错误信息；
                                </em>
                            </span>
                        </div>
                        {composeCheckErrorMsg.length !== 0 && (
                            <div className={`${styles.pl12} input-hasTip has-error`}>
                                {composeCheckErrorMsg.map((item, index) => (
                                    <p className="text text-tip pull-left" key={index}>
                                        <i className="fa fa-times-circle" />
                                        {item}
                                    </p>
                                ))}
                            </div>
                        )}
                    </Panel.Body>
                </Panel>
                <ButtonToolBar>
                    <Button type="button" onClick={this.cancel}>
                        取消
                    </Button>
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspend && (
                        <RoleAuthControl buttonKey="meta-data-repair-suspend-question">
                            <Button type="button" bsSize="large" bsStyle="warning" onClick={() => this.submit(true, false)}>
                                暂缓上报
                            </Button>
                        </RoleAuthControl>
                    )}
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                        <RoleAuthControl buttonKey="meta-data-repair-suspected-question">
                            <Button type="button" bsSize="large" bsStyle="warning" onClick={() => this.submit(false, false)}>
                                疑问暂存
                            </Button>
                        </RoleAuthControl>
                    )}
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                        <RoleAuthControl buttonKey="meta-data-repair-confirm">
                            <Button type="button" bsSize="large" bsStyle="success" onClick={() => this.submit(true, true)}>
                                确认
                            </Button>
                        </RoleAuthControl>
                    )}
                    {checkErrorLevel !== MetaConfigUtils.RULE_CHECK_LEVEL.suspend &&
                        checkErrorLevel !== MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                            <Button
                                type="button"
                                bsStyle="primary"
                                onClick={() => this.submit(false, true)}
                                disabled={editConfigList.length === 0}
                            >
                                修正
                            </Button>
                        )}
                </ButtonToolBar>
            </div>
        );
    }
}

export default DataRepairEdit;
