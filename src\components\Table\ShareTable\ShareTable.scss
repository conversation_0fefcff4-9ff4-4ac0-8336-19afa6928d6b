@charset "UTF-8";
.linOver {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    word-wrap: break-word;
    word-break: break-all;
    //white-space: nowrap;
}
.oneLineOver {
    -webkit-line-clamp: 1;
    @extend .linOver;
}
.twoLineOver {
    -webkit-line-clamp: 2;
    @extend .linOver;
}
.threeLineOver {
    -webkit-line-clamp: 3;
    @extend .linOver;
}
.sevenLineOver {
    -webkit-line-clamp: 7;
    @extend .linOver;
}
@mixin title {
    font-family: 'microsoft yahei';
    font-size: 14px;
    font-weight: bold;
    color: #323538;
}

// 改写antd的样式
.share-table_container {
    background: #fff;
    margin: 0 0 12px;

    :global {
        // title
        .ant-table-title {
            top: 0;
            padding: 0 12px 0 12px;
            line-height: 34px;
            min-height: 36px;
            border-bottom: 2px solid #09d;
            background-color: #fff;

            // title
            .share-table_title {
                margin-bottom: 0;
                font-family: 'microsoft yahei';
                font-size: 12px;
                font-weight: bold;
                color: #323538;
            }
        }

        // footer
        .ant-table-footer {
            padding: 10px;
            overflow: hidden;
        }

        .ant-table th,
        .ant-table td {
            text-align: left;
            height: 34px;
            line-height: 1.2;
            padding: 2px 5px;
            vertical-align: middle;
        }

        .ant-table th {
            @include title;
            font-size: 12px;

            &:first-child {
                padding-left: 20px;
            }

            &:last-child {
                padding-right: 20px;
            }
        }
        .ant-table-thead {
            tr {
                background: #fafafa;
            }
        }
        .ant-table-tbody {
            tr.ant-table-row {
                text-align: left;
                height: 34px;
                line-height: 1.2;
                padding: 2px 5px;
                vertical-align: middle;
                color: #666;
                font-size: 12px;

                &:nth-of-type(2n) {
                    background-color: #f7f8f9;
                }

                &:nth-of-type(2n + 1) {
                    background-color: #fff;
                }

                td {
                    &:first-child {
                        padding-left: 20px;
                    }

                    &:last-child {
                        padding-right: 20px;
                    }
                }

                &:hover {
                    background-color: #e0f6ff;
                }
            }
        }

        .ant-table-placeholder {
            z-index: 0;
        }
        // title
        .share-table_title {
            margin-bottom: 0;
            font-size: 12px;
            font-weight: 700;
            color: #323538;
        }

        .ant-table-footer {
            margin-top: 5px;
        }

        .ant-table-cell-fix-left,
        .ant-table-cell-fix-right {
            background: inherit;
        }
        .ant-table .ant-table-expanded-row-fixed {
            margin: 0;
            text-align: center;
        }
    }
}
