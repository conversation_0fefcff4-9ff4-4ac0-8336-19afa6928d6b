import React, { Component } from 'react';
// 列表组件
import * as formRule from '@/components/common/common/formRule';
import * as Entity from '@/components/common/common/Entity';
import { Button, Modal, Panel } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
import * as StringUtils from '@/components/common/common/StringUtils';

const { Form, Row, Input, Select, RadioGroup } = getComponents();

class DatabaseTableFieldInformationEdit extends Component {
    state = {
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
    };

    componentWillReceiveProps(nextProps) {
        const {
            show,
            data: { detail },
        } = nextProps;
        const { editForm } = this.state;

        if (!this.props.show && show) {
            editForm.setFormData(Entity.simpleDeepCopy(detail));
        }
    }

    submit = async () => {
        const { successFn } = this.props;
        const { editForm } = this.state;

        if (FormVaildHelper.isValid(await editForm.valid())) {
            const editBody = StringUtils.deleteSpace(editForm.getFormData());

            successFn && successFn(editBody);
        }
    };

    render() {
        const {
            show,
            cancelFn,
            data: { metaFieldList },
        } = this.props;
        const { editForm } = this.state;
        const { fieldId, fieldCode, primaryKey } = editForm.getFormData();

        return (
            <Modal className="modal-full databaseTableFieldEditModal" show={show} onHide={cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>属性编辑</Modal.Header>
                <Modal.Body>
                    <Panel>
                        <Panel.Head title="字段基本信息" />
                        <Panel.Body full>
                            <Form pageType="addPage" formState={editForm}>
                                <tr>
                                    <th />
                                    <td />
                                    <th />
                                    <td />
                                </tr>
                                <Row>
                                    <Input label="数据库表名" field="tableId" disabled colSpan={3} />
                                </Row>
                                <Row>
                                    <Input label="数据表字段名" field="fieldCode" disabled />
                                    <Select
                                        label="是否主键"
                                        field="primaryKey"
                                        disabled
                                        options={[
                                            { label: '是', value: true },
                                            { label: '否', value: false },
                                        ]}
                                    />
                                </Row>
                                <Row>
                                    <Input label="数据类型" field="fieldDataType" disabled />
                                    <Input label="数据长度" field="fieldLength" disabled />
                                </Row>
                                <Row>
                                    <Input label="字段备注" field="fieldComment" disabled colSpan={3} />
                                </Row>
                            </Form>
                        </Panel.Body>
                    </Panel>
                    <Panel>
                        <Panel.Head title="业务配置" />
                        <Panel.Body full>
                            <Form pageType="addPage" formState={editForm}>
                                <tr>
                                    <th />
                                    <td />
                                    <th />
                                    <td />
                                </tr>
                                <Row>
                                    <Input
                                        label="业务名称"
                                        field="showLabel"
                                        rule={[formRule.checkRequiredNotBlank(), formRule.checkLength(0, 30)]}
                                        required
                                    />
                                    <Input
                                        label="字段别名"
                                        field="fieldAlias"
                                        rule={[
                                            formRule.checkRequiredNotBlank(),
                                            formRule.checkLength(0, 50),
                                            formRule.checkRegex('^[a-zA-Z][a-zA-Z0-9_]*$', '只支持英文和数字和_,且只能以字母开头'),
                                            formRule.checkFunction(
                                                (value) =>
                                                    value === fieldCode ||
                                                    metaFieldList
                                                        .filter((config) => config.fieldId !== fieldId)
                                                        .every(
                                                            (config) =>
                                                                config.fieldCode.toUpperCase() !== value.toUpperCase() &&
                                                                config.fieldAlias.toUpperCase() !== value.toUpperCase()
                                                        ),
                                                '在其他字段名称或别名重复'
                                            ),
                                        ]}
                                        required
                                    />
                                </Row>
                                {/* <Row>
                                    <Select
                                        label="面向主体" field="fieldOrientedObjectType"
                                        bmName="BM_OBJECT_TYPE"
                                        rule={formRule.checkRequiredNotBlank()}
                                        required
                                        disabled={primaryKey}
                                    />
                                    <Select
                                        label="公开类型" field="openStyle"
                                        bmName="BM_OPEN_STYLE"
                                        rule={formRule.checkRequiredNotBlank()}
                                        required
                                        disabled={primaryKey}
                                    />
                                </Row> */}
                                <Row>
                                    <RadioGroup
                                        label="停用/启用状态"
                                        field="enabled"
                                        options={[
                                            { label: '启用', value: true },
                                            { label: '停用', value: false },
                                        ]}
                                        rule={formRule.checkRequiredNotBlank()}
                                        required
                                        disabled={primaryKey}
                                    />
                                    <RadioGroup
                                        label="是否业务字段"
                                        field="businessField"
                                        options={[
                                            { label: '是', value: true },
                                            { label: '否', value: false },
                                        ]}
                                        rule={formRule.checkRequiredNotBlank()}
                                        required
                                    />
                                </Row>
                            </Form>
                        </Panel.Body>
                    </Panel>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        确认
                    </Button>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default DatabaseTableFieldInformationEdit;
