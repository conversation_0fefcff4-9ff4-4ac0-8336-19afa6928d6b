import React, { Component } from 'react';
// 样式
import '../style/Upload.scss';
// 工具类
import { Spin } from '@share/shareui';
import { fileUploadUrl, fileResourceRoot } from '@/components/common/common/ConfigConstant';
import { assertServerException } from '@/components/common/network/AnalysisServerData';
// 组件
import { Upload, Button, message, Icon } from 'antd';

class CommonFileUpload extends Component {
    static defaultProps = {
        root: fileResourceRoot, // 服务端文件代理前缀
        url: fileUploadUrl, // 服务端文件上传地址
        fileType: '', // 允许上传文件类型（空值不做限制，多值逗号隔开）
        limitSize: 20, // 允许上传单文件大小（单位M，允许小数）
        fileNameSize: 50, // 允许上传单文件名称最大长度
        fileMaxNum: 5, // 允许上传文件最大个数
        extendCheckFn: null, // 扩展上传服务器前校验函数(入参：文件体，出参：错误信息，无错误返回空串)
        extendCheckFailFn: null, // 扩展上传服务器前校验失败函数(入参：文件体)
        uploadButton: null, // 自定义上传按钮
        loadingMaskPage: false, // 上传过程是否页面loading遮罩
    };

    // 拷贝数据并为文件添加文根
    copyDataAndAddRoot = (data) => {
        const { root } = this.props;
        const newData = Array.isArray(data) ? data : [];
        const fileList = newData.map((file) => ({ ...file }));

        fileList.forEach((file, index) => {
            if (file.url && !file.url.startsWith(root)) {
                file.url = `${root}${file.url}`;
            }
            // 无uid或无status说明是父组件第一次带进来
            if (!file.uid || !file.status) {
                file.uid = index;
                file.status = 'done';
            }
        });

        return fileList;
    };

    // 检验是否为符合要求的上传文件
    beforeUpload = (file) => {
        const { fileType, limitSize, fileNameSize, extendCheckFn, extendCheckFailFn, loadingMaskPage } = this.props;
        // 参数处理
        const uploadFileType = /^.*\.(.*)$/.test(file.name) ? /^.*\.(.*)$/.exec(file.name)[1] : '';
        const finallyLimitSize = Number.parseFloat(limitSize) || 5;
        const finallyFileNameSize = Number.parseFloat(fileNameSize);
        // 校验
        const uploadFileTypeToLocaleLowerCase = uploadFileType.toLocaleLowerCase();
        const checkFileType =
            !fileType ||
            (uploadFileTypeToLocaleLowerCase &&
                fileType
                    .split(',')
                    .some((type) => type.replace(/^.*\/(.*)$/, '$1').toLocaleLowerCase() === uploadFileTypeToLocaleLowerCase));
        const checkFileSize = file.size / 1024 / 1024 <= finallyLimitSize;
        const checkFileName = file.name.length <= finallyFileNameSize;
        const extendValidErrorMsg = typeof extendCheckFn === 'function' ? extendCheckFn(file) : '';
        const isEmpty = file.size === 0;

        if (!checkFileType) {
            message.error(`上传文件类型不符合要求（${fileType}）`);
        }
        if (!checkFileSize) {
            message.error(`上传文件大小禁止超过${limitSize}M`);
        }
        if (!checkFileName) {
            message.error(`上传文件名禁止超过${fileNameSize}个字`);
        }
        if (extendValidErrorMsg) {
            message.error(extendValidErrorMsg);
        }
        if (isEmpty) {
            message.error('文件损坏，请重新上传');
        }
        const checkResult = checkFileType && checkFileSize && checkFileName && !extendValidErrorMsg && !isEmpty;

        !checkResult && typeof extendCheckFailFn === 'function' && extendCheckFailFn(file);
        checkResult && loadingMaskPage && Spin.show('上传中，请等待');

        return checkResult;
    };

    // 上传数据发生改变
    handleChange = ({ fileList }) => {
        const { root, onChange, fileMaxNum = 5, loadingMaskPage } = this.props;
        const finallyFileMaxNum = Number.parseInt(fileMaxNum) || 5;
        let uploading = false;
        // 去除多选上传时过多的文件（截取后几个文件），过滤不符合上传要求和上传失败文件，并对上传成功文件去文根操作
        const finallyFileList = fileList
            .filter((file) => file.status && file.status !== 'error')
            .slice(-finallyFileMaxNum)
            .map((file) => {
                if (file.status === 'uploading') {
                    uploading = true;

                    return file;
                }
                if (file.response) {
                    try {
                        // 校验返回服务器状态
                        assertServerException(file.response);

                        return {
                            uid: file.uid,
                            url: file.response.data.relativeName.replace(root, ''),
                            name: file.response.data.originFileName,
                            status: file.status,
                            size: file.size,
                            originFileObj: file.originFileObj,
                        };
                    } catch (e) {
                        return null;
                    }
                }

                return {
                    uid: file.uid,
                    url: file.url.replace(root, ''),
                    name: file.name,
                    status: file.status,
                    size: file.size,
                    originFileObj: file.originFileObj,
                };
            })
            .filter((file) => file);

        loadingMaskPage && !uploading && Spin.hide();
        onChange && onChange({ target: { value: finallyFileList } });
    };

    // 获取上传参数
    uploadProps = () => {
        const { url, ...restProps } = this.props;

        return {
            ...restProps,
            action: `${CONTEXT_PATH}${url}`,  //eslint-disable-line
            multiple: restProps.multiple !== false && restProps.multiple !== 'false',
            beforeUpload: this.beforeUpload,
            onChange: this.handleChange,
        };
    };

    render() {
        const { fileMaxNum, uploadButton, disabled, value, onClick } = this.props;
        const finallyFileMaxNum = Number.parseFloat(fileMaxNum) || 5;
        // 拷贝数据并为文件添加文根
        const fileList = this.copyDataAndAddRoot(value);
        const uploadProps = this.uploadProps();
        const showUploadButton = uploadButton || (
            <Button disabled={disabled}>
                <Icon type="upload" /> 上传
            </Button>
        );

        return (
            <Upload {...uploadProps} fileList={fileList}>
                {fileList.length < finallyFileMaxNum && <span onClick={onClick}>{showUploadButton}</span>}
            </Upload>
        );
    }
}

export default CommonFileUpload;
