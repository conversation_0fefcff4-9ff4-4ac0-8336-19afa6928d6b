import React, { Fragment } from 'react';
import { Panel, Button, Icon } from '@share/shareui';
import {
    SearchForm,
    // 表单项组件
    Input,
    Select,
    Switch,
} from '@share/shareui-form';
import MultiClamp from '@/components/MultiClamp';
import ShareTable from '@/components/Table/ShareTable';
import EsExportModal from './EsExportModal';
import useLogic from './logic';

const TableFieldList = (props) => {
    const { tableId, metaFieldList } = props;
    const { state, handleSearch, defaultEsConfig, handleToggleShowEsExportModal, listState, form, updateConfig } = useLogic(props);
    const { showEsExportModal } = state;
    const fieldDataTypeOptions = Array.from(new Set(metaFieldList.map((item) => item.fieldDataType))).map((item) => ({
        label: item,
        value: item,
    }));
    // 过滤搜索条件
    // const $filterDataList = filterDataList(metaFieldList);
    // 列表展示内容
    const columns = [
        {
            title: '数据库字段名',
            key: 'fieldCode',
            dataIndex: 'fieldCode',
            width: '20%',
            render: (value) => <MultiClamp title={value}>{value}</MultiClamp>,
        },
        {
            title: '是否主键',
            key: 'primaryKey',
            dataIndex: 'primaryKey',
            width: '6%',
            render: (value) => {
                return <span>{value ? '是' : '否'}</span>;
            },
        },
        {
            title: '数据类型',
            key: 'fieldDataType',
            dataIndex: 'fieldDataType',
            width: '10%',
        },
        {
            title: '数据长度',
            key: 'fieldLength',
            dataIndex: 'fieldLength',
            width: '10%',
            render: (value) => {
                if (!Array.isArray(value)) {
                    return '--';
                }
                switch (value.length) {
                    case 1:
                        return value[0];
                    case 2:
                        return `(${value[0]},${value[1]})`;
                    default:
                        return '--';
                }
            },
        },
        {
            title: '字段备注',
            key: 'fieldComment',
            dataIndex: 'fieldComment',
            render: (value) => <MultiClamp title={value}>{value}</MultiClamp>,
        },
        {
            title: 'ES字段',
            key: 'enabled',
            dataIndex: 'esConfig.enabled',
            width: '8%',
            render: (value, data) => (
                <Switch.View
                    value={value}
                    onChange={() => {
                        // data.esConfig.enabled = !data.esConfig.enabled;

                        updateConfig([
                            {
                                ...data,
                                esConfig: {
                                    ...data.esConfig,
                                    enabled: !data.esConfig.enabled,
                                },
                            },
                        ]);
                    }}
                />
            ),
        },
        {
            title: 'ES主体关键字',
            key: 'objectKeyword',
            dataIndex: 'esConfig',
            width: '8%',
            render: (value, data) => (
                <Switch.View
                    value={value.objectKeyword}
                    disabled={!value.enabled}
                    onChange={() => {
                        updateConfig([
                            {
                                ...data,
                                esConfig: {
                                    ...data.esConfig,
                                    objectKeyword: !data.esConfig.objectKeyword,
                                },
                            },
                        ]);
                    }}
                />
            ),
        },
        {
            title: 'ES业务关键字',
            key: 'businessKeyword',
            dataIndex: 'esConfig',
            width: '9%',
            render: (value, data) => (
                <Switch.View
                    value={value.businessKeyword}
                    disabled={!value.enabled}
                    onChange={() => {
                        updateConfig([
                            {
                                ...data,
                                esConfig: {
                                    ...data.esConfig,
                                    businessKeyword: !data.esConfig.businessKeyword,
                                },
                            },
                        ]);
                    }}
                />
            ),
        },
    ];
    const esColumns = metaFieldList.filter((item) => item.esConfig.enabled);

    return (
        <Fragment>
            <Panel>
                <Panel.Body full>
                    <SearchForm
                        formState={form}
                        fixWidth={4}
                        query={(data) => {
                            console.info('触发查询', data);
                            handleSearch(data);
                        }}
                        resetRetry
                    >
                        <Input label="数据库字段名" field="fieldCode" />
                        <Select label="数据类型" field="fieldDataType" options={fieldDataTypeOptions} />
                        <Input label="字段备注" field="fieldComment" />
                        {/* <Row>
                            <div className="g-30">
                                <div className="btn-item pull-right">
                                    <Button type="submit" bsStyle="primary" onClick={handleSearch}>
                                        查询
                                    </Button>
                                    <Button type="reset" onClick={handleReset}>
                                        重置
                                    </Button>
                                </div>
                            </div>
                        </Row> */}
                    </SearchForm>
                </Panel.Body>
            </Panel>
            <Panel>
                <Panel.Head title="字段信息">
                    <Panel.HeadRight>
                        <ul className="ui-list-horizontal">
                            {metaFieldList.length > 0 && (
                                <li>
                                    <Button type="button" className="btn-xs" border={false} onClick={() => defaultEsConfig()}>
                                        <Icon className="si si-com_backward" />
                                        使用ES默认配置
                                    </Button>
                                </li>
                            )}
                            {metaFieldList.length > 0 && (
                                <li>
                                    <Button
                                        type="button"
                                        className="btn-xs"
                                        border={false}
                                        onClick={() => handleToggleShowEsExportModal(true)}
                                        disabled={esColumns.length === 0}
                                    >
                                        <Icon className="si si-gxxt_zyml" />
                                        导出Es文件
                                    </Button>
                                </li>
                            )}
                        </ul>
                    </Panel.HeadRight>
                </Panel.Head>
                <Panel.Body full>
                    <ShareTable listState={listState} columns={columns} cachePage />
                </Panel.Body>
            </Panel>
            <EsExportModal
                show={showEsExportModal}
                cancelFn={() => {
                    handleToggleShowEsExportModal(false);
                }}
                data={{
                    tableIds: [tableId],
                    esAlias: tableId.toLowerCase(),
                }}
            />
        </Fragment>
    );
};

export default TableFieldList;
