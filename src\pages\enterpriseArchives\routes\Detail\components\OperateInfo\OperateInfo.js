/**  版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 *  @Copyright:  Copyright (c) 2020
 *  @Company:厦门畅享信息技术有限公司
 *  @Author: 李家其
 *  Date: 2023/11/24 17:01
 */
// 经营信息
import React, { Fragment } from 'react';
import TagTitle from '../TagTitle';
import Operate from './components/Operate';
// import PayTaxes from './components/PayTaxes';
// import Personnel from './components/Personnel';
import './OperateInfo.scss';
import useOperateInfoHooks from './hook';
import PayTaxes from "@/pages/enterpriseArchives/routes/Detail/components/OperateInfo/components/PayTaxes";
import Personnel from "@/pages/enterpriseArchives/routes/Detail/components/OperateInfo/components/Personnel";

const OperateInfo = ({ formState, tyshxydm = '' }) => {
    const { options } = useOperateInfoHooks({ tyshxydm });

    return (
        <div>
            <TagTitle title={<Fragment>经营信息</Fragment>} id="operateInfo" />
            <Operate formState={formState} options={options[0]} tyshxydm={tyshxydm} />
            <PayTaxes formState={formState} options={options[1]} tyshxydm={tyshxydm} />
        </div>
    );
};

export default OperateInfo;
