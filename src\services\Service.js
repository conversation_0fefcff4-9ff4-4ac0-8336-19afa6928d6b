/*
 * @(#) Service.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2021
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2021-03-16 10:41:46
 */
import { BaseService } from '@share/framework';

class Service extends BaseService {
    // 根据父ID获取标签列表
    getTagInfoTree = () => {
        return this.network.formGet(`/tagInfo/zhTree.do`);
    };

    // 获取所属行业
    getHyCode = (parentId) => {
        return this.network.formGet(`/code/getHyCode.do?parentId=${parentId}`);
    };

    // 获取所属辖区
    getArea = (parentId) => {
        return this.network.formGet(`/code/getArea.do?parentId=${parentId}`);
    };

    // 获取企业详情地址配置
    getEnterpriseJumpUrl = () => {
        return this.network.formGet('/interview/interviewConfig/getEnterpriseJumpUrl.do');
    };

    getExpressionsAuthState = (params) => {
        return this.network.json('/permissions/config/getExpressionsAuthState.do', params);
    };

    getDetailCommonData = (url, params) => {
        return this.network.json(`${url}`, params);
    };
}
export default Service;
