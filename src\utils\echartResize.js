// 绑定echart缩放
import { useEffect, useCallback } from 'react';

export const useBindEchartResize = (echartspie) => {
    const echartResizeHandle = useCallback(() => {
        const echartInstance = echartspie.current ? echartspie.current.getEchartsInstance() : null;

        if (echartInstance) {
            echartInstance.resize();
        }
    }, []);

    useEffect(() => {
        window.onresize = echartResizeHandle;
        window.addEventListener('resize', echartResizeHandle, false);

        return () => {
            window.removeEventListener('resize', echartResizeHandle);
        };
    }, [echartResizeHandle]);
};
