const React = require('react');
const TextManager = require('../manager/TextManager');

const { Component, Fragment } = React;
const { parse } = require('./TextParser');

class TextFormatter extends Component {
    constructor(props) {
        super(props);
        this.renderByConfig = this.renderByConfig.bind(this);
    }

    renderByConfig(config) {
        const RowComponent = this.props.row;
        const ItemComponent = this.props.item;

        if (Array.isArray(config)) {
            return config.map((item, idx) => {
                return <RowComponent key={idx}>{this.renderByConfig(item)}</RowComponent>;
            });
        }
        if (typeof config === 'string') {
            return config;
        }
        if (typeof config.render === 'function') {
            return config.render(config.text, config.props);
        }

        return <ItemComponent {...config.props}>{config.text}</ItemComponent>;
    }

    render() {
        const { xzqh, tipKey } = this.props;

        if (xzqh === '' || tipKey === '') {
            return '';
        }

        const text = TextManager.getTextConfig(xzqh, tipKey);
        // const text = this.props.text;

        const handleResult = parse(text);

        if (handleResult === '') {
            return '';
        }

        const RootComponent = this.props.root;

        return <RootComponent>{this.renderByConfig(handleResult)}</RootComponent>;
    }
}

const TextBlock = (props) => {
    const { children, ...otherProps } = props;

    return <span {...otherProps}>{children}</span>;
};
const RowBlock = (props) => {
    const { children, ...otherProps } = props;

    return <div {...otherProps}>{children}</div>;
};

TextFormatter.defaultProps = {
    root: Fragment,
    row: RowBlock,
    item: TextBlock,
    tipKey: '', // 文本配置key
    xzqh: '', // 行政区划

    text: '', // 用于组件测试
};

module.exports = TextFormatter;
