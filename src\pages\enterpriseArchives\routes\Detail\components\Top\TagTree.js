import React, { useState, useContext } from 'react';
import { Button } from '@share/shareui';
import { Tree, message } from 'antd';
import { useService, useQuery } from '@share/framework';
import MetaFieldApi from '@/services/MetaFieldApi';
import styles from './Top.scss';
import { DetailContext } from '../../hooks';

const TagTree = ({ treeDataSource = {}, onHide, defaultExpandedKeys = [] }) => {
    const { treeData = [], checkedKeys = [] } = treeDataSource;
    const services = useService(MetaFieldApi);
    const { id } = useQuery();
    const [checkedIds, setCheckedIds] = useState([]);
    const { refresh } = useContext(DetailContext);
    const onCheck = (checkKeys, info) => {
        console.log('🚀 ~ onCheck ~ info:', checkKeys, info);
        const ids = (info?.checkedNodes || [])
            .filter((item) => {
                return (item?.children || [])?.length === 0;
            })
            .map((item) => item.id);
        setCheckedIds(ids);
    };
    const handleSave = async () => {
        if (checkedIds.length === 0) {
            message.error('未勾选标签，无法保存！', 1);

            return;
        }
        const res = await services.markTag({
            ids: checkedIds,
            tyshxydms: [id],
        });
        if (res) {
            message.success('产业标签添加成功！', 1, () => {
                onHide();
                refresh();
            });
        } else {
            message.error('出错了，请稍后再试！', 1);
        }
    };

    return (
        <div className={styles.tagTree}>
            <Tree
                checkable
                defaultExpandedKeys={defaultExpandedKeys}
                defaultCheckedKeys={checkedKeys}
                onCheck={onCheck}
                treeData={treeData}
                autoExpandParent
            />
            <div className={styles['tagTree-footer']}>
                <Button bsStyle="primary" preventDuplicateClick onClick={handleSave}>
                    确定
                </Button>
            </div>
        </div>
    );
};

export default TagTree;
