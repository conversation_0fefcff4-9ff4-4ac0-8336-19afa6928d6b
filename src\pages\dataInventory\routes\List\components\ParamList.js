import React from 'react';
import { ArrayCom } from '@/components/form';
import ParamItem from './ParamItem';

const ParamList = (props) => {
    const { field, label, rule, ...restProps } = props;

    return (
        <ArrayCom field={field} label={label} rule={rule} defaultChildrenData={{}}>
            {(pro) => (
                <ParamItem
                    {...restProps}
                    {...pro}
                    field={`${field}.${pro.index}`}
                    existId={pro?.list?.filter((item, index) => index !== pro.index)?.map((i) => i.id)}
                    width="270px"
                />
            )}
        </ArrayCom>
    );
};

export default ParamList;
