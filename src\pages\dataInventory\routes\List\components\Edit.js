import React, { Fragment, useState, useEffect } from 'react';
import { TableForm, Timing, Input, RadioGroup, Row, Select } from '@share/shareui-form';
import { useService } from '@share/framework';
import { validFunction } from '@/utils/shareFormUtil';
import DatabaseService from '@/services/DatabaseService';
import ParamList from './ParamList';

const Edit = (props) => {
    const { editForm, inventoryList, objectTypeOptions, sourceTypeOptions, codeOptions, booleanOptions } = props;
    const databaseService = useService(DatabaseService);
    const [tableOptions, setTableOptions] = useState([]);
    const [sourceFieldList, setSourceFieldList] = useState([]);
    const {
        originalId,
        source: { id: sourceId, type: sourceType },
    } = editForm.getFormData();
    const requestTable = async () => {
        const result = await databaseService.getTableInfo({});
        const options = result.map((item) => ({
            value: item.tableName.toUpperCase(),
            label: `${item.tableName.toUpperCase()}(${item.tableComment})`,
            extend: item.tableName,
        }));
        setTableOptions(options);
    };
    const requestTableField = async (table) => {
        const tableName = tableOptions.find((item) => item.value === table)?.extend;
        if (sourceType === 'sql' && tableName) {
            const result = await databaseService.getTableField({ tableName });

            setSourceFieldList(result);
        } else {
            setSourceFieldList([]);
        }
    };
    useEffect(() => {
        requestTable();
    }, []);
    useEffect(() => {
        requestTableField(sourceId);
    }, [tableOptions, sourceId]);

    return (
        <TableForm formState={editForm}>
            <Row>
                <Input
                    field="id"
                    label="清单ID"
                    rule={[
                        'required',
                        validFunction({
                            fn: (v) => {
                                return v === originalId || inventoryList.every((item) => item.id !== v);
                            },
                            errMsg: '已存在',
                            timing: Timing.blur,
                        }),
                    ]}
                    maxLength={50}
                    disabled={originalId}
                />
            </Row>
            <Row>
                <Input field="name" label="清单名称" rule="required" maxLength={100} />
            </Row>
            <Row>
                <RadioGroup field="objectType" label="主体类型" rule="required" options={objectTypeOptions} />
            </Row>
            <Row>
                <RadioGroup
                    field="source.type"
                    label="来源类型"
                    rule="required"
                    options={sourceTypeOptions}
                    onChange={({ target: { value } }) => {
                        editForm.setFieldValues({ source: { type: value }, result: [], param: [] });
                    }}
                />
            </Row>
            {sourceType === 'sql' ? (
                <Row>
                    <Select
                        field="source.id"
                        label="来源ID"
                        rule="required"
                        maxLength={100}
                        options={tableOptions}
                        onChange={() => {
                            editForm.setFieldValues({ result: [], param: [] });
                        }}
                    />
                </Row>
            ) : (
                <Row>
                    <Input
                        field="source.id"
                        label="来源ID"
                        rule="required"
                        maxLength={100}
                        onChange={() => {
                            editForm.setFieldValues({ result: [], param: [] });
                        }}
                    />
                </Row>
            )}
            {sourceId && (
                <Fragment>
                    <Row>
                        <ParamList
                            field="param"
                            label="参数"
                            sourceFieldList={sourceFieldList}
                            codeOptions={codeOptions}
                            booleanOptions={booleanOptions}
                        />
                    </Row>
                    <Row>
                        <ParamList
                            field="result"
                            label="结果"
                            rule={[
                                'required',
                                validFunction({
                                    fn: (v) => v?.length > 0,
                                    errMsg: '至少一个节点',
                                }),
                            ]}
                            sourceFieldList={sourceFieldList}
                            codeOptions={codeOptions}
                            booleanOptions={booleanOptions}
                        />
                    </Row>
                </Fragment>
            )}
            <Row>
                <Input field="sortNum" label="排序字段" rule="required" maxLength={11} type="number" />
            </Row>
            <Row>
                <RadioGroup field="enabled" label="是否启用" rule="required" options={booleanOptions} />
            </Row>
        </TableForm>
    );
};

export default Edit;
