import React, { Fragment } from 'react';
import { Panel } from '@share/shareui';
import ResizeLayout from '@/components/ResizeLayout';
import Sidebar from '@/pages/metaDataConfig/components/PersonSidebar';
import useTreeHooks from '@/pages/metaDataConfig/publicHooks/usePersonTreeHooks';
import styles from './TagDefinition.scss';
import ConfigForm from './components/ConfigForm';

const TagDefinition = (props) => {
    const { onSelect, selectNode, treeDataSource, refresh } = useTreeHooks(props);

    return (
        <Panel>
            <Panel.Body>
                <div className={styles.TagDefinition}>
                    <ResizeLayout
                        sider={
                            <div className={styles.leftTree}>
                                <Sidebar
                                    refresh={refresh}
                                    treeDataSource={treeDataSource}
                                    onSelect={onSelect}
                                    selectNode={selectNode}
                                    key={treeDataSource.length}
                                />
                                {/* {treeDataSource.treeData.length > 0 && (
                                    <Sidebar
                                        refresh={refresh}
                                        treeDataSource={treeDataSource}
                                        onSelect={onSelect}
                                        selectNode={selectNode}
                                    />
                                )} */}
                            </div>
                        }
                        content={
                            <div className={styles.rightForm}>
                                <ConfigForm {...props} selectNode={selectNode} key={selectNode.id} refresh={refresh} />
                            </div>
                        }
                    />
                </div>
            </Panel.Body>
        </Panel>
    );
};

export default TagDefinition;
