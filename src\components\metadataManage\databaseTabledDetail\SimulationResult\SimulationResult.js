import React, { Component } from 'react';
// 样式
import classnames from 'classnames';
import noData from '@/assets/images/image-noData.png';
// 服务接口
import * as MetaFieldApi from '@/services/data/meta/MetaFieldApi';
// 工具类
import * as Entity from '@/components/common/common/Entity';
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
// 组件
import { antdMessage } from '@/components/ui/MyAlert/MyAlert';
import QueryCondition from '@/components/business/metadata/QueryCondition';
import QueryList from '@/components/business/metadata/QueryList';
import Edit from '@/components/business/metadata/Edit';
import Detail from '@/components/business/metadata/Detail';
import { Panel, ButtonToolBar, Button, Icon } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';
import styles from './SimulationMetaResult.scss';

const { Form, RadioGroup } = getComponents('div');

class SimulationMetaResult extends Component {
    state = {
        // 查询参数
        filterForm: new FormState({ objectType: '2', scene: 'manager' }, (filterForm, callback) => this.setState({ filterForm }, callback)),
        // 模拟随机命名空间
        simulationNamespace: Math.random(),
        // 模拟服务器数据
        simulationDataList: [],
        // 搜索参数状态
        searchBody: {},
        // 详情模态框参数
        showDetailModal: false,
        detailBody: {},
        // 编辑模态框参数参数
        showEditModal: false,
        editType: 'add',
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
        // 联合校验错误信息
        composeCheckErrorMsg: [],
    };

    filterMetadataConfigList = () => {
        const { metaFieldList } = this.props;
        const { filterForm } = this.state;
        const { objectType } = filterForm.getFormData();
        const copyMetaFieldList = Entity.simpleDeepCopy(metaFieldList);

        return MetaConfigUtils.filterObjectTypeConfig(copyMetaFieldList, objectType);
    };

    // 创建模拟表单请求API
    buildSimulationListApi = ({ currentPage = 1, linesPerPage = 10 }) => {
        const { metaFieldList } = this.props;
        const { filterForm, simulationDataList, searchBody } = this.state;
        const { objectType, scene } = filterForm.getFormData();
        const filterDataSource = simulationDataList.filter((data) =>
            Object.entries(searchBody).every(([key, value]) => {
                const dataValue = data[key];

                // 过滤条件为空
                if (
                    (typeof value === 'string' && value.trim().length === 0) ||
                    (typeof value === 'object' && !Array.isArray(value) && (value === null || (!value.start && !value.end))) ||
                    (Array.isArray(value) && value.length === 0)
                ) {
                    return true;
                }
                // 参数值为空
                if (!dataValue || dataValue.length === 0) {
                    return false;
                }
                if (Array.isArray(value)) {
                    if (Array.isArray(dataValue)) {
                        return dataValue.some((item) => value.includes(item));
                    }

                    return value.includes(dataValue);
                }
                if (typeof value === 'string') {
                    if (Array.isArray(dataValue)) {
                        return dataValue.some((item) => item.includes(value));
                    }

                    return dataValue.includes(value);
                }
                if (typeof value === 'object') {
                    return (!value.start || value.start <= dataValue) && (!value.end || value.end >= dataValue);
                }

                return true;
            })
        );
        const pageDataSource = filterDataSource.slice((currentPage - 1) * linesPerPage, currentPage * linesPerPage);

        if (pageDataSource.length === 0) {
            return new Promise((resolve) => resolve({ page: { totalNum: filterDataSource.length, currentPage, linesPerPage }, list: [] }));
        }

        return MetaFieldApi.testList(metaFieldList, objectType, scene, pageDataSource).then((data) => ({
            page: { totalNum: filterDataSource.length, currentPage, linesPerPage },
            list: data,
        }));
    };

    // 详情展示
    detail = async (key) => {
        const { metaFieldList } = this.props;
        const { filterForm, simulationDataList } = this.state;
        const { objectType, scene } = filterForm.getFormData();
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaFieldList);
        const detail = simulationDataList.find((item) => item[primaryKey] === key) || {};
        const convertDetail = await MetaFieldApi.testDetail(metaFieldList, objectType, scene, detail);

        this.setState({ showDetailModal: true, detailBody: convertDetail });
    };

    // 编辑展示
    edit = (key) => {
        const { simulationDataList, editForm } = this.state;
        const metadataConfigList = this.filterMetadataConfigList();
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metadataConfigList);
        const editConfigList = MetaConfigUtils.filterEditConfig(metadataConfigList);
        let detail;

        if (key) {
            detail = simulationDataList.find((item) => item[primaryKey] === key) || {};
        } else {
            detail = MetaConfigUtils.getEditDefaultData(editConfigList);
        }
        editForm.cleanValidError();
        editForm.setFormData({ ...detail });
        this.setState({ showEditModal: true, editType: key ? 'update' : 'add', composeCheckErrorMsg: [] });
    };

    // 提交上报
    submit = async () => {
        const { metaFieldList } = this.props;
        const { filterForm, simulationDataList, editForm } = this.state;
        const data = editForm.getFormData();
        const { objectType, scene } = filterForm.getFormData();
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaFieldList);

        if (!FormVaildHelper.isValid(await editForm.valid())) {
            FormVaildHelper.scrollToError();

            return;
        }
        const { checkSuccess, checkFailMsg = {}, checkData } = await MetaFieldApi.testAddFromPage(metaFieldList, objectType, scene, data);

        if (checkSuccess) {
            let newSimulationDataList;

            if (!data[primaryKey]) {
                newSimulationDataList = [{ ...checkData, [primaryKey]: Math.random() }, ...simulationDataList];
            } else {
                newSimulationDataList = simulationDataList.map((item) => {
                    return item[primaryKey] === data[primaryKey] ? checkData : item;
                });
            }
            this.setState({ showEditModal: false, simulationDataList: newSimulationDataList }, () => this.table.refreshTable());
            antdMessage.success('提交成功');

            return;
        }
        Object.entries(checkFailMsg).forEach(([key, value]) => {
            key && value && editForm.setValidError(key, Array.isArray(value) ? value.join('；') : value);
        });
        const composeCheckErrorMsg = Array.isArray(checkFailMsg[primaryKey]) ? checkFailMsg[primaryKey] : [];

        this.setState({ composeCheckErrorMsg });
        FormVaildHelper.scrollToError();
    };

    // 删除
    delete = (key) => {
        const { metaFieldList } = this.props;
        const { simulationDataList } = this.state;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaFieldList);
        const newSimulationDataList = simulationDataList.filter((item) => item[primaryKey] !== key);

        this.setState({ simulationDataList: newSimulationDataList }, () => this.table.refreshTable());
    };

    // 下载Excel
    downloadExcel = () => {
        const { metaFieldList } = this.props;
        const { filterForm } = this.state;
        const { objectType, scene } = filterForm.getFormData();

        MetaFieldApi.testTemplateDownload(metaFieldList, objectType, scene);
    };

    render() {
        const {
            filterForm,
            simulationNamespace,
            searchBody,
            showDetailModal,
            detailBody,
            showEditModal,
            editType,
            editForm,
            composeCheckErrorMsg,
        } = this.state;
        const filterMetadataConfigList = this.filterMetadataConfigList();
        const queryConfigList = MetaConfigUtils.filterQueryConfig(filterMetadataConfigList);
        const listConfigList = MetaConfigUtils.filterListConfig(filterMetadataConfigList);
        const editConfigList = MetaConfigUtils.filterEditConfig(filterMetadataConfigList);
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(filterMetadataConfigList);
        const extendTailColumns = [
            {
                title: '操作',
                width: 150,
                fixed: 'right',
                key: 'operations',
                dataIndex: `system.${primaryKey}`,
                render: (key) => {
                    return (
                        <div className="tableBtn">
                            <a onClick={() => this.detail(key)}>查看</a>
                            <a onClick={() => this.edit(key)}>编辑</a>
                            <a onClick={() => this.delete(key)}>删除</a>
                        </div>
                    );
                },
            },
        ];

        return (
            <div>
                {/* <Form pageType="queryPage" formState={filterForm}>
                    <RadioGroup
                        label="模拟主体" field="objectType"
                        bmName="BM_OBJECT_TYPE"
                        labelCol={3} col={10}
                    />
                    <RadioGroup
                        label="模拟场景" field="scene"
                        bmName="DM_CREDIT_APPLICATION_SCENE"
                        labelCol={3} col={20}
                    />
                </Form> */}
                {!showDetailModal && !showEditModal && (
                    <Panel>
                        {/* <Panel.Head title="模拟列表" /> */}
                        <Panel.Body full>
                            {/* 搜索条件 */}
                            {queryConfigList.length !== 0 && listConfigList.length !== 0 && (
                                <QueryCondition
                                    defaultSearchBody={searchBody}
                                    metadataConfigList={queryConfigList}
                                    namespace={simulationNamespace}
                                    searchFn={(searchForm) => this.setState({ searchBody: searchForm })}
                                />
                            )}
                            {/* 搜索列表 */}
                            {listConfigList.length === 0 ? (
                                <div className={styles.noData}>
                                    <img src={noData} alt="no data" />
                                    <p>未配置列表展示列信息</p>
                                </div>
                            ) : (
                                <Panel>
                                    <Panel.Head title="查询结果">
                                        <Panel.HeadRight>
                                            <ul className="ui-list-horizontal">
                                                <li>
                                                    <Button type="button" className="btn-xs" border={false}>
                                                        <a href="javascript:void(0)" onClick={() => this.downloadExcel()}>
                                                            <Icon className="fa-plus" />
                                                            模板下载
                                                        </a>
                                                    </Button>
                                                </li>
                                                <li>
                                                    <Button type="button" className="btn-xs" border={false}>
                                                        <a href="javascript:void(0)" onClick={() => this.edit()}>
                                                            <Icon className="fa-plus" />
                                                            新增
                                                        </a>
                                                    </Button>
                                                </li>
                                            </ul>
                                        </Panel.HeadRight>
                                    </Panel.Head>
                                    <Panel.Body full className={styles.queryList}>
                                        <QueryList
                                            metadataConfigList={listConfigList}
                                            service={{ api: this.buildSimulationListApi, body: searchBody }}
                                            rowKey={(data) => data.system[primaryKey]}
                                            extendTailColumns={extendTailColumns}
                                            namespace={simulationNamespace}
                                            onRef={(tableRef) => {
                                                this.table = tableRef;
                                            }}
                                        />
                                    </Panel.Body>
                                </Panel>
                            )}
                        </Panel.Body>
                    </Panel>
                )}
                {showDetailModal && (
                    <Panel>
                        {/* 详情 */}
                        <Panel.Head title="模拟详情" />
                        <Panel.Body full>
                            <Detail detail={detailBody} />
                            <ButtonToolBar>
                                <Button type="button" onClick={() => this.setState({ showDetailModal: false })}>
                                    返回
                                </Button>
                            </ButtonToolBar>
                        </Panel.Body>
                    </Panel>
                )}
                {/* 编辑模态框 */}
                {showEditModal && (
                    <Panel>
                        <Panel.Head title="模拟上报" />
                        <Panel.Body full>
                            <div
                                className={classnames({
                                    [styles.isError]: composeCheckErrorMsg.length !== 0,
                                })}
                            >
                                <Edit formState={editForm} editType={editType} metadataConfigList={editConfigList} />
                            </div>
                            {composeCheckErrorMsg.length !== 0 && (
                                <div className={`${styles.pl20} input-hasTip has-error`}>
                                    {composeCheckErrorMsg.map((item, index) => (
                                        <p className="text text-tip pull-left" key={index}>
                                            <i className="fa fa-times-circle" />
                                            {item}
                                        </p>
                                    ))}
                                </div>
                            )}
                            <ButtonToolBar>
                                <Button type="button" bsStyle="primary" onClick={this.submit}>
                                    提交
                                </Button>
                                <Button type="button" onClick={() => this.setState({ showEditModal: false })}>
                                    取消
                                </Button>
                            </ButtonToolBar>
                        </Panel.Body>
                    </Panel>
                )}
            </div>
        );
    }
}

export default SimulationMetaResult;
