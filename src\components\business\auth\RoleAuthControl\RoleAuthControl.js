import React from 'react';
import * as MeManager from '@/components/common/business/manager/MeManager';

class RoleAuthControl extends React.Component {
    render() {
        const { buttonKey = '', children, unAuthDefaultEl = '' } = this.props;
        const { manager, buttonKeys = [] } = MeManager.getMe();
        const buttonKeyArr = typeof buttonKey === 'string' ? buttonKey.split(',') : [];

        return children;
        // // 如果是超级管理员或者区承诺文根（区没用角色体系），直接授权
        // if (manager) {
        //     return children;
        // }
        // // 如果非超级管理员，校验是否有相应的按钮权限
        // if (buttonKeyArr.some(canKey => buttonKeys.includes(canKey))) {
        //     return children;
        // }
        // return unAuthDefaultEl;
    }
}
export default RoleAuthControl;
