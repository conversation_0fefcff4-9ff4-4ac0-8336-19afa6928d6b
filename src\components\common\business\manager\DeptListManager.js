// 接口请求
const commonApi = require('@/services/system/commonApi');
// 工具类
const store = require('../../store/Store');
const Log = require('../../log/Log');
// 常量
const DEPT_KEY = 'dept';

/**
 * 部门缓存管理器
 */
class DeptListManager {
    constructor() {
        this.data = [];
        this.setLoader = this.setLoader.bind(this);
        this.getLoader = this.getLoader.bind(this);
        this.load = this.load.bind(this);
        this.setDeptList = this.setDeptList.bind(this);
        this.getDeptByDeptId = this.getDeptByDeptId.bind(this);
        this.getDeptNameByDeptId = this.getDeptNameByDeptId.bind(this);
        this.getDeptList = this.getDeptList.bind(this);
    }

    /**
     * 清除部门数据缓存
     */
    static cleanCache() {
        store.del(DEPT_KEY);
    }

    /**
     *  外部设置部门缓存加载器
     * @param loader 如果是函数返回promise
     */
    setLoader(loader) {
        this.loader = loader;
    }

    /**
     * 获取部门缓存加载器
     * @returns {*} 加载器对象promise
     */
    getLoader() {
        if (typeof this.loader === 'function') {
            return this.loader();
        }

        return commonApi.getAllDeptSimple();
    }

    /**
     * 从服务器加载所有部门数据
     * @returns {Promise.<{}>}
     */
    load() {
        // 从sessionStorage中获取，没有的话读取接口
        const deptList = store.get(DEPT_KEY);

        if (deptList) {
            Log.debug('加载缓存部门列表');
            this.setDeptList(deptList);

            return Promise.resolve();
        }

        return this.getLoader()
            .then((data) => {
                const sortData = data.sort((a, b) => a.regionId.localeCompare(b.regionId));

                this.setDeptList(sortData);
                store.set(DEPT_KEY, sortData);
            })
            .catch((e) => {
                Log.error('表码加载失败', e);
                throw e;
            });
    }

    setDeptList(deptList) {
        this.data = deptList.map((dept) => ({ ...dept }));
    }

    /**
     * 根据deptId获取部门信息
     * @param deptId 部门主键
     * @returns {*} 如 {name:"中心部门",regionNo:"350200",...}
     */
    getDeptByDeptId(deptId) {
        const result = this.data;

        if (!result) {
            Log.warn('部门列表信息为空');

            return null;
        }
        const dept = result.find((item) => item.deptId === deptId);

        return dept ? { ...dept } : null;
    }

    /**
     * 根据deptId获取部门名称
     * @param deptId 部门主键
     * @returns String 如 "中心部门"
     */
    getDeptNameByDeptId(deptId) {
        const dept = this.getDeptByDeptId(deptId);

        return dept ? dept.name : '';
    }

    getDeptShortNameByDeptId(deptId) {
        const dept = this.getDeptByDeptId(deptId);

        return dept ? dept.shortName : '';
    }

    /**
     * 根据外部条件获取部门了你报
     * @param predicate 判定规则函数 如 (dept) => dept.regionNo==="350200"
     * @returns {*} [{name:"中心部门",regionNo:"350200",...}]
     */
    getDeptList(predicate) {
        let result = this.data;

        if (!result) {
            Log.warn('部门列表信息为空');

            return [];
        }
        result = result.map((item) => ({ ...item }));
        if (predicate) {
            return result.filter((item) => predicate(item));
        }

        return result;
    }
}

module.exports = new DeptListManager();
