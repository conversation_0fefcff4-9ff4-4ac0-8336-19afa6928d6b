{"name": "sunsharing-application-v1", "version": "1.0.1", "private": true, "scripts": {"start": "sharekit server", "start:dev": "sharekit server", "build": "sharekit build", "build:analyze": "sharekit build --analyze", "lint": "eslint --ext .js src > eslintErrorLog.txt", "prepare": "node ./bin/huskyInstall.js"}, "devDependencies": {"@share/kit": "^2.9.0", "husky": "^8.0.0", "lint-staged": "^13.2.2"}, "dependencies": {"@share/common": "^1.6.2", "@share/form": "^3.3.0", "@share/framework": "^1.4.2", "@share/framework-preset-shareui": "^1.4.0", "@share/list": "^1.9.4", "@share/network": "^2.7.4", "@share/portal-messenger": "^2.5.1", "@share/shareui": "^2.52.6", "@share/shareui-font": "^3.42.3", "@share/shareui-form": "^3.6.1", "@share/shareui-html": "^2.38.25", "@share/shareui-polyfill": "^0.0.4", "@share/shareui3-polyfill-plugin": "^0.0.5", "@share/utils": "^1.0.2", "ahooks": "^3.7.8", "antd": "4", "antd-img-crop": "^2.1.2", "bootstrap": "^3.3.4", "classnames": "^2.3.2", "dayjs": "^1.11.11", "dva": "2.1.0", "echarts": "^5.4.2", "echarts-for-react": "^3.0.2", "font-awesome": "^4.7.0", "jquery": "^3.7.1", "moment": "^2.29.4", "react": "17", "react-dom": "17", "react-highlight-words": "^0.20.0", "react-multi-clamp": "^2.0.6", "react-router": "5", "react-router-dom": "5", "xlsx": "^0.14.3", "zustand": "^4.3.7"}, "resolutions": {"react": "17", "react-dom": "17", "react-router": "5", "react-router-dom": "5"}, "scaffold-version": "1.0.0", "volta": {"node": "16.20.2"}}