import React, { Component } from 'react';
import { Form } from '@share/shareui';
import MultiClamp from '@/components/ui/MultiClamp';
import styles from './index.scss';
import { FileUpload, ImageUpload } from '../Form/Upload';

/**
 * 元数据详情展示
 */
class Detail extends Component {
    render() {
        const {
            detail: { data = [] },
            extendDetailCons,
        } = this.props;
        const detailCons = data.map((item) => {
            const { type = 'string', label, value = '' } = item;
            let showResult = '';
            let title = '';

            switch (type) {
                case 'file':
                    if (Array.isArray(value)) {
                        showResult = <FileUpload value={value} styleType="shareui" disabled />;
                    }
                    break;
                case 'image':
                    if (Array.isArray(value)) {
                        showResult = <ImageUpload value={value} disabled />;
                    }
                    break;
                case 'custom':
                    showResult = value;
                    break;
                default:
                    title = Array.isArray(value) ? value.join(',') : value;
                    showResult = <div title={title}>{title}</div>;
            }

            return (
                <Form.Tr>
                    <Form.Label>
                        <MultiClamp title={label}>{label}</MultiClamp>
                    </Form.Label>
                    <Form.Content>
                        <span className="textShow" title={title}>
                            {showResult}
                        </span>
                    </Form.Content>
                </Form.Tr>
            );
        });

        return (
            <div className={styles.detailBody}>
                <Form pageType="detailPage">
                    <Form.Table>
                        {detailCons}
                        {extendDetailCons && extendDetailCons()}
                    </Form.Table>
                </Form>
            </div>
        );
    }
}

export default Detail;
