import { portalFrontContextPath } from '@/components/common/common/ConfigConstant';
import notification from '@/components/ui/Notification/index';

const { NetError } = require('../../common/Errors');
const { Sender, CONTENT_TYPE } = require('./Sender');

class FetchSender extends Sender {
    constructor() {
        super();

        this.hide = null;
    }

    /**
     * 判断是否网络异常，response中的status值
     * @param response
     * @return {Promise.<*>}
     */
    assertNetWork(response) {
        this.hide();
        if (!response.ok) {
            return Promise.reject(new NetError(`[网络异常][${response.status}]`, response.status));
        }

        return response.text();
    }

    post(url, data, contentType) {
        if (!this.hide) {
            this.hide = notification.loading({
                content: '加载中，请稍候...',
                duration: 0,
            });
        }

        const staticUrl = Sender.getUrl(url);
        const body = contentType === CONTENT_TYPE.JSON ? Sender.getJsonData(data) : FetchSender.getUrlSearchData(data);

        const option = {
            method: 'POST',
            mode: 'cors',
            credentials: 'include',
            headers: {
                'Content-Type': `${contentType};charset=utf-8`,
                'Accept-Charset': 'utf-8',
            },
            body,
        };

        return window.fetch(staticUrl, option).then(this.assertNetWork.bind(this)).then(Sender.parseToJsonObj);
    }

    get(url, data) {
        if (!this.hide) {
            this.hide = notification.loading({
                content: '加载中，请稍候...',
                duration: 0,
            });
        }
        let staticUrl = Sender.getUrl(url);

        if (data && Object.keys(data).length > 0) {
            const paramStr = Object.entries(data)
                .map(([key, value]) => `${key}=${value}`)
                .join('&');

            staticUrl += `?${paramStr}`;
        }
        const option = {
            method: 'GET',
            mode: 'cors',
            credentials: 'include',
            referrer: portalFrontContextPath,
        };

        return window.fetch(staticUrl, option).then(this.assertNetWork.bind(this)).then(Sender.parseToJsonObj);
    }
}

FetchSender.getUrlSearchData = (data = {}) => {
    const requestData = new URLSearchParams();

    Object.entries(data).forEach(([key, value]) => {
        if (Array.isArray(value)) {
            value.forEach((item) => requestData.append(`${key}[]`, item));
        } else {
            requestData.append(key, value);
        }
    });

    return requestData;
};

module.exports = FetchSender;
