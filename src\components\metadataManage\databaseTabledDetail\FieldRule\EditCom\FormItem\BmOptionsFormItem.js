import React, { Component, Fragment } from 'react';
import * as formRule from '@/components/common/common/formRule';
import { getComponents } from '@/components/business/Form';

const { ArrayFormItem, Input } = getComponents();

class BmOptionsFormItem extends Component {
    render() {
        const { field, options, ...restProps } = this.props;

        return (
            <ArrayFormItem
                field={field}
                label="码表配置"
                rule={[
                    formRule.checkFunction((value) => Array.isArray(value) && value.length > 0, '不能为空'),
                    formRule.checkFunction(
                        (value) => value.every((item) => item.label.trim().length !== 0 && item.value.trim().length !== 0),
                        '不能存在空项'
                    ),
                ]}
                defaultChildrenData={{ label: '', value: '' }}
                {...restProps}
            >
                {(props) => {
                    const valueItem = props.value || {};

                    return (
                        <Fragment>
                            <Input.View
                                value={valueItem.label}
                                onChange={({ target: { value } }) => props.onChange({ target: { value: { ...valueItem, label: value } } })}
                                options={options}
                                placeholder="请输入显示名称"
                                className="g-6"
                            />
                            <Input.View
                                value={valueItem.value}
                                onChange={({ target: { value } }) => props.onChange({ target: { value: { ...valueItem, value } } })}
                                options={[
                                    { label: 'label1', value: '1' },
                                    { label: 'label2', value: '2' },
                                ]}
                                placeholder="请输入码表值"
                                className="g-6"
                            />
                        </Fragment>
                    );
                }}
            </ArrayFormItem>
        );
    }
}

export default BmOptionsFormItem;
