import React from 'react';

const getFocusBlurComponent = (Component, defaultProps) => {
    return class extends React.Component {
        static defaultProps = {
            ...defaultProps,
        };

        state = {
            focus: false,
            temValue: '',
        };

        render() {
            const { value, onChange, onFocus, onBlur, ...restProps } = this.props;
            const { focus, temValue } = this.state;

            return (
                <Component
                    {...restProps}
                    value={focus ? temValue : value}
                    onChange={(e) => {
                        this.setState({ temValue: e.target.value });
                    }}
                    onFocus={(e) => {
                        this.setState({ focus: true, temValue: value });
                        onFocus && onFocus(e);
                    }}
                    onBlur={(e) => {
                        onChange({ target: { value: temValue } });
                        onBlur && onBlur(e);
                    }}
                />
            );
        }
    };
};

export default getFocusBlurComponent;
