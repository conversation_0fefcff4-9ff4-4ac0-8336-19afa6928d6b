@charset "utf-8";

@mixin bsType($type, $color) {
    &.#{$type}{
        .left .dot{
            background: $color;
        }
        .right .contentBox .title{
            border-color: $color;
        }
    }
}
$typeList: (
    primary: #0ce,
    danger: #fe6654,
    success: #00bc89,
    warning: #fb8,
    disabled: #ebeced
);

.timeLineContainer{
    .timeLine{
        .left{
            position: relative;
            width: 16%;
            padding: 35px 24px 0 10px;
            border-right: 1px dashed #d0d1d4;
            text-align: right;
            div{
                margin-bottom: 0;
                line-height: 18px;
                font-size: 14px;
                color: #404348;
            }
            .dot{
                position: absolute;
                top: 35px;
                right: 0;
                transform: translateX(50%);
                width: 13px;
                height: 13px;
                border-radius: 50%;
                background: #0ce;
            }
        }
        .right{
            width: 84%;
            padding: 20px 40px 0 26px;
            margin-left: -1px;
            border-left: 1px dashed #d0d1d4;
           .contentBox{
               border: solid 1px #ebeced;
               .title{
                   margin: 0;
                   padding: 0 20px;
                   line-height: 32px;
                   border-top: 3px solid #00ccee;
                   background: #f7f8f9;
                   font-weight: bold;
                   font-size: 14px;
                   .titleText{
                       width: 100%;
                   }
                   .collapseBtn{
                       display: inline-block;
                       font-weight: normal;
                       font-size: 12px;
                       color: #09d;
                       cursor: pointer;
                   }
               }
               .alwaysDisplay,.collapseContent{
                   padding: 0 20px;
               }
           }
        }
        &:last-child{
            .right{
                padding-bottom: 40px;
            }
        }

        @each $key, $value in $typeList{
            @include bsType($key,$value)
        }
    }
    @each $key, $value in $typeList{
        @include bsType($key,$value)
    }

    &.withoutBorder,
    .timeLine.withoutBorder {
        .right {
            padding: 20px 12% 0 6px;
            .contentBox {
                border: none;
                .title {
                    padding-top: 5px;
                    border-top: none;
                    background: transparent;
                }
            }
        }
    }
}

