import React from 'react';
import { Button } from '@share/shareui';
import { Select } from '@share/shareui-form';
import { registerFormItem } from '@/utils/shareFormUtil';
import ConditionNode from './ConditionNode';
import style from './ConditionComposeNode.scss';

const composeLogicOptions = [
    { label: '同时满足(AND)', value: 'and' },
    { label: '满足其一(OR)', value: 'or' },
];

const ConditionComposeNode = (props) => {
    const { value, onChange, field, inventoryList, suffix } = props;
    const { composeNodes } = value || {};
    const addComposeNode = (node) => {
        const nodes = [...composeNodes, node];
        onChange({ target: { value: { ...value, composeNodes: nodes } } });
    };
    const deleteComposeNode = (i) => {
        const nodes = composeNodes.filter((item, index) => index !== i);
        onChange({ target: { value: { ...value, composeNodes: nodes } } });
    };

    return (
        <div className={style.body}>
            <div className={style.operate}>
                <Select
                    className={style.composeLogic}
                    field={`${field}.composeLogic`}
                    placeholder="组合逻辑"
                    rule="required"
                    noView
                    options={composeLogicOptions}
                    clearable={false}
                />
                <Button bsStyle="info" onClick={() => addComposeNode({ data: {}, matchSymbol: 'eq', matchValue: '' })}>
                    添加匹配节点
                </Button>
                <Button bsStyle="info" onClick={() => addComposeNode({ composeLogic: 'and', composeNodes: [] })}>
                    添加组合节点
                </Button>
                {suffix}
            </div>
            <div className={style.composeNodes}>
                {Array.isArray(value?.composeNodes) &&
                    value.composeNodes.map((item, index) => {
                        const paramField = `${field}.composeNodes.${index}`;

                        return (
                            <div key={paramField} className={style.composeNode}>
                                <ConditionNode
                                    field={paramField}
                                    placeholder="组合节点"
                                    noView
                                    inventoryList={inventoryList}
                                    suffix={
                                        <div>
                                            <Button bsStyle="info" onClick={() => deleteComposeNode(index)}>
                                                删除
                                            </Button>
                                        </div>
                                    }
                                />
                            </div>
                        );
                    })}
            </div>
        </div>
    );
};

export default registerFormItem(ConditionComposeNode);
