import React, { Component } from 'react';
import { Input } from 'antd';

class JumpInput extends Component {
    state = {
        input: 1,
    };

    handleInputChange = ({ target: { value } }) => {
        this.setState({ input: value });
    };

    render() {
        const { onPressEnter, ...restProps } = this.props;
        const { input } = this.state;

        return (
            <Input
                type="number"
                min={1}
                value={input}
                onChange={this.handleInputChange}
                onPressEnter={() => onPressEnter(input)}
                placeholder="请输入..."
                {...restProps}
            />
        );
    }
}

export default JumpInput;
