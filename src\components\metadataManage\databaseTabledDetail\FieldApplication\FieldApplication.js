/*
 * @(#) MenuConfig2.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-06-18 10:28:33
 */
import React, { Component, Fragment } from 'react';
// 样式
import classnames from 'classnames';
// 接口
import * as MetaFieldApi from '@/services/data/meta/MetaFieldApi';
// 工具类
import { CONFIG_PARAM_KEY, filterPrimaryKeyConfig } from '@/components/common/common/MetaConfigUtils';
// 组件
import { CommonAntdTree as Tree } from '@/components/ui/Tree';
import { Panel, Button, Icon } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';
import TableFieldInformationDetail from '../FieldInformation/TableFieldInformationDetail';
import DatabaseTableFieldInformationEdit from '../FieldInformation/TableFieldInformationEdit';
import styles from './FieldApplication.scss';

const { Form, Input, RadioGroup, CheckboxGroupCustom } = getComponents('div');

const defaultSearchForm = {
    keyword: '',
    fieldOrientedObjectTypes: [],
    openStyles: [],
    enableds: '',
    businessFields: [],
    configParamKey: CONFIG_PARAM_KEY.EDIT,
};

const applicationOptions = [
    { label: '查询条件', value: CONFIG_PARAM_KEY.QUERY },
    { label: '列表', value: CONFIG_PARAM_KEY.LIST },
    { label: '编辑', value: CONFIG_PARAM_KEY.EDIT },
    { label: '详情', value: CONFIG_PARAM_KEY.DETAIL },
];

class FieldApplication extends Component {
    state = {
        // 所有搜索条件
        searchForm: new FormState(defaultSearchForm, (editForm, callback) => this.setState({ editForm }, callback)),
        // 选中项
        leftCheckedKeys: [],
        rightCheckedKey: [],
        showDetailModal: false,
        showEditModal: false,
        operateData: {},
    };

    onExtend = (node) => {
        const { metaFieldList } = this.props;
        const buttons = [
            <Icon
                key="detail"
                className="si si-com_search"
                title="详情"
                onClick={() => {
                    const config = metaFieldList.find((item) => item.fieldCode === node.fieldCode);

                    this.setState({ showDetailModal: true, operateData: config });
                }}
            />,
            <Icon
                key="edit"
                className="text-primary si si-com_b6"
                title="编辑"
                onClick={() => {
                    const config = metaFieldList.find((item) => item.fieldCode === node.fieldCode);

                    this.setState({ showEditModal: true, operateData: config });
                }}
            />,
        ];

        if (node.primaryKey) {
            return buttons;
        }
        buttons.push(
            <Icon
                key="enabled"
                className={classnames({
                    'si si-com_lock text-danger': node.enabled,
                    'si si-com_lockopen text-success': !node.enabled,
                })}
                title={node.enabled ? '停用' : '启用'}
                onClick={() => this.batchUpdateEnabled([node.fieldId], !node.enabled)}
            />
        );

        return buttons;
    };

    onDrop = (info) => {
        const { metaFieldList } = this.props;
        const { searchForm } = this.state;
        const configParamKey = searchForm.getFieldValue('configParamKey');
        // 数据对象
        const dragObjFieldId = info.dragNode.props.fieldId; // 拖动对象主键
        const dragObjApplicationOrderValue = info.dragNode.props[configParamKey].order; // 拖动对象序号
        const dropObjApplicationOrderValue = info.node.props[configParamKey].order; // 容器对象序号
        const dropPos = info.node.props.pos.split('-'); // 目标位置数组
        const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]); // 目标位置相对容器对象位置（-1：上方，0：内部，1：下方）
        let targetApplicationOrderValue;
        let correctMinOrderValue;
        let correctMaxOrderValue;
        let correctParam; // 目标位置、修正最小范围数、修正最大范围数、修正参数

        if (dragObjApplicationOrderValue > dropObjApplicationOrderValue) {
            // 上移
            targetApplicationOrderValue = dropPosition === 1 ? dropObjApplicationOrderValue + 1 : dropObjApplicationOrderValue;
            correctMinOrderValue = targetApplicationOrderValue;
            correctMaxOrderValue = dragObjApplicationOrderValue;
            correctParam = 1;
        } else {
            // 下移
            targetApplicationOrderValue = dropPosition === 1 ? dropObjApplicationOrderValue : dropObjApplicationOrderValue - 1;
            correctMinOrderValue = dragObjApplicationOrderValue;
            correctMaxOrderValue = targetApplicationOrderValue;
            correctParam = -1;
        }
        // 无移动操作
        if (dragObjApplicationOrderValue === targetApplicationOrderValue) {
            return;
        }
        const newDataList = metaFieldList.map((item) => {
            const itemApplicationOrderValue = item[configParamKey].order;

            // 左栏和操作范围值返回原值
            if (
                !Number.isInteger(itemApplicationOrderValue) ||
                itemApplicationOrderValue < 0 ||
                itemApplicationOrderValue < correctMinOrderValue ||
                itemApplicationOrderValue > correctMaxOrderValue
            ) {
                return item;
            }
            // 拖动对象直接修改序号为目标位置序号
            if (item.fieldId === dragObjFieldId && itemApplicationOrderValue === dragObjApplicationOrderValue) {
                item[configParamKey].order = targetApplicationOrderValue;

                return item;
            }
            // 范围值修正序号
            item[configParamKey].order = itemApplicationOrderValue + correctParam;

            return item;
        });

        this.operateSubmit(newDataList);
    };

    restCheckedKeys = () => {
        this.setState({ leftCheckedKeys: [], rightCheckedKey: [] });
    };

    leftMoveRight = () => {
        const { metaFieldList } = this.props;
        const { searchForm, leftCheckedKeys } = this.state;

        if (leftCheckedKeys.length === 0) {
            return;
        }
        const configParamKey = searchForm.getFieldValue('configParamKey');
        const rightDataCount = metaFieldList.filter(
            (item) => item[configParamKey] && Number.isInteger(item[configParamKey].order) && item[configParamKey].order >= 0
        ).length;
        const newDataList = metaFieldList.map((item) => {
            const index = leftCheckedKeys.findIndex((leftCheckedKey) => leftCheckedKey === item.fieldId);

            if (index === -1) {
                return item;
            }

            return { ...item, [configParamKey]: { ...item[configParamKey], order: rightDataCount + (index + 1) } };
        });

        this.operateSubmit(newDataList);
        this.setState({ leftCheckedKeys: [] });
    };

    rightMoveLeft = () => {
        const { metaFieldList } = this.props;
        const { searchForm, rightCheckedKey } = this.state;

        if (rightCheckedKey.length === 0) {
            return;
        }
        const configParamKey = searchForm.getFieldValue('configParamKey');
        const newRightDataList = metaFieldList.filter(
            (item) =>
                item[configParamKey] &&
                Number.isInteger(item[configParamKey].order) &&
                item[configParamKey].order >= 0 &&
                !rightCheckedKey.includes(item.fieldId)
        );

        newRightDataList.sort((a, b) => a[configParamKey].order - b[configParamKey].order);
        const newRightDataKeys = newRightDataList.map((item) => item.fieldId);
        const newDataList = metaFieldList.map((item) => {
            // 左边数据
            if (!item[configParamKey] || !Number.isInteger(item[configParamKey].order) || item[configParamKey].order < 0) {
                return item;
            }
            // 右边被选中数据
            if (rightCheckedKey.includes(item.fieldId)) {
                return { ...item, [configParamKey]: { ...item[configParamKey], order: -1 } };
            }
            // 右边剩余数据
            const index = newRightDataKeys.findIndex((key) => key === item.fieldId);

            return { ...item, [configParamKey]: { ...item[configParamKey], order: index + 1 } };
        });

        this.operateSubmit(newDataList);
        this.setState({ rightCheckedKey: [] });
    };

    batchUpdateEnabled = (fieldIds, enabled) => {
        if (!Array.isArray(fieldIds) || fieldIds.length === 0) {
            return;
        }
        const { metaFieldList } = this.props;
        const newDataList = metaFieldList.map((item) => (fieldIds.includes(item.fieldId) ? { ...item, enabled } : item));

        this.operateSubmit(newDataList);
    };

    transToRcTreeData = (dataList, configParamKey, searchWord) => {
        const dealSearchWord = searchWord ? searchWord.trim() : '';

        return dataList.map((item) => {
            const title = `${item.fieldCode}（${item.showLabel}）（${item[configParamKey].order}）`;
            const fieldEfficacious = item.enabled && item[configParamKey] && item[configParamKey].componentId;
            const iconText = item.businessField ? '业' : '系';
            const icon = (
                <span
                    className={classnames({
                        tag: true,
                        antdTreeTagCircle: true,
                        success: fieldEfficacious && !item.businessField,
                        primary: fieldEfficacious && item.businessField,
                        danger: !fieldEfficacious,
                    })}
                >
                    {iconText}
                </span>
            );
            let showTitle = title;

            if (dealSearchWord && title.includes(dealSearchWord)) {
                const index = title.indexOf(dealSearchWord);
                const beforeStr = title.substr(0, index);
                const afterStr = title.substr(index + dealSearchWord.length);

                showTitle = (
                    <Fragment>
                        {beforeStr}
                        <span style={{ color: '#f50' }}>{dealSearchWord}</span>
                        {afterStr}
                    </Fragment>
                );
            } else {
                showTitle = <Fragment>{showTitle}</Fragment>;
            }
            const disableCheckbox = configParamKey === CONFIG_PARAM_KEY.EDIT && item.primaryKey;

            return { ...item, key: item.fieldId, title, showTitle, icon, disableCheckbox };
        });
    };

    buildRcTreeData = (configParamFilterFn, orderDeal) => {
        const { metaFieldList } = this.props;
        const { searchForm } = this.state;
        const { keyword, fieldOrientedObjectTypes, openStyles, enableds, businessFields, configParamKey } = searchForm.getFormData();

        let filterDataList = metaFieldList;

        if (typeof configParamFilterFn === 'function') {
            filterDataList = filterDataList.filter((item) => configParamFilterFn(item[configParamKey]));
        }
        if (keyword.trim().length > 0) {
            filterDataList = filterDataList.filter((item) =>
                `${item.fieldCode}（${item.showLabel}）（${item[configParamKey].order}）`
                    .toUpperCase()
                    .includes(keyword.trim().toUpperCase())
            );
        }
        if (fieldOrientedObjectTypes.length > 0) {
            filterDataList = filterDataList.filter(
                (item) => item.fieldOrientedObjectType === '2' || fieldOrientedObjectTypes.includes(item.fieldOrientedObjectType)
            );
        }
        if (openStyles.length > 0) {
            filterDataList = filterDataList.filter((item) => openStyles.includes(item.openStyle));
        }
        if (enableds.length > 0) {
            filterDataList = filterDataList.filter((item) => enableds.includes(item.enabled.toString()));
        }
        if (businessFields.length > 0) {
            filterDataList = filterDataList.filter((item) => businessFields.includes(item.businessField.toString()));
        }
        const reeData = this.transToRcTreeData(filterDataList, configParamKey, keyword);

        if (orderDeal) {
            reeData.sort((a, b) => a[configParamKey].order - b[configParamKey].order);
        }

        return reeData;
    };

    operateSubmit = async (configList) => {
        const { refreshDataFn } = this.props;

        await MetaFieldApi.saveTableMeta(configList);
        refreshDataFn && refreshDataFn();
    };

    render() {
        const { metaFieldList } = this.props;
        const { searchForm, leftCheckedKeys, rightCheckedKey, showDetailModal, showEditModal, operateData } = this.state;
        const leftTreeData = this.buildRcTreeData(
            (configParam) => !configParam || !Number.isInteger(configParam.order) || configParam.order < 0
        );
        const rightTreeData = this.buildRcTreeData(
            (configParam) => configParam && Number.isInteger(configParam.order) && configParam.order >= 0,
            true
        );
        const { configParamKey } = searchForm.getFormData();
        const applicationName = (applicationOptions.find((item) => item.value === configParamKey) || {}).label;
        const primaryKeyFieldId = filterPrimaryKeyConfig(metaFieldList).fieldId;

        return (
            <Panel>
                <Form pageType="queryPage" formState={searchForm}>
                    <Input label="关键字" field="keyword" labelCol={3} col={10} />
                    {/* <CheckboxGroupCustom
                        label="面向主体"
                        field="fieldOrientedObjectTypes"
                        bmName="DM_OBJECT_TYPE"
                        onChange={this.restCheckedKeys}
                        labelCol={3} col={10}
                     />
                     <CheckboxGroupCustom
                        label="公开类型"
                        field="openStyles"
                        bmName="BM_OPEN_STYLE"
                        onChange={this.restCheckedKeys}
                        labelCol={3} col={10}
                     /> */}
                    <CheckboxGroupCustom
                        label="停启状态"
                        field="enableds"
                        options={[
                            { label: '启用', value: 'true' },
                            { label: '停用', value: 'false' },
                        ]}
                        onChange={this.restCheckedKeys}
                        labelCol={3}
                        col={10}
                    />
                    <CheckboxGroupCustom
                        label="字段类型"
                        field="businessFields"
                        options={[
                            { label: '业务字段', value: 'true' },
                            { label: '系统字段', value: 'false' },
                        ]}
                        onChange={this.restCheckedKeys}
                        labelCol={3}
                        col={10}
                    />
                    <RadioGroup
                        label="配置应用"
                        field="configParamKey"
                        options={applicationOptions}
                        onChange={this.restCheckedKeys}
                        labelCol={3}
                        col={10}
                    />
                </Form>
                <div className={`${styles.tableField} clearfix`}>
                    <div className={styles.left}>
                        <Panel>
                            <Panel.Head title={`非${applicationName}字段`}>
                                <Panel.HeadRight>
                                    <ul className="ui-list-horizontal">
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                onClick={() => {
                                                    this.setState({
                                                        leftCheckedKeys: leftTreeData
                                                            .filter((item) => configParamKey !== CONFIG_PARAM_KEY.EDIT || !item.primaryKey)
                                                            .map((item) => item.fieldId),
                                                    });
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-com_appsquare" />
                                                    全选
                                                </a>
                                            </Button>
                                        </li>
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                onClick={() => {
                                                    this.setState({
                                                        leftCheckedKeys: leftTreeData
                                                            .filter(
                                                                (item) =>
                                                                    (configParamKey !== CONFIG_PARAM_KEY.EDIT || !item.primaryKey) &&
                                                                    !leftCheckedKeys.includes(item.fieldId)
                                                            )
                                                            .map((item) => item.fieldId),
                                                    });
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-jy_jfrx1" />
                                                    反选
                                                </a>
                                            </Button>
                                        </li>
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                onClick={() => {
                                                    this.setState({ leftCheckedKeys: [] });
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-com_appsquareo" />
                                                    清空
                                                </a>
                                            </Button>
                                        </li>
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                onClick={() => {
                                                    this.batchUpdateEnabled(leftCheckedKeys, true);
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-com_lockopen" />
                                                    启用
                                                </a>
                                            </Button>
                                        </li>
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                disabled={leftCheckedKeys.includes(primaryKeyFieldId)}
                                                onClick={() => {
                                                    this.batchUpdateEnabled(leftCheckedKeys, false);
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-com_lock" />
                                                    停用
                                                </a>
                                            </Button>
                                        </li>
                                    </ul>
                                </Panel.HeadRight>
                            </Panel.Head>
                            <Panel.Body full>
                                <Tree
                                    treeData={leftTreeData}
                                    showIcon
                                    checkable
                                    checkedKeys={leftCheckedKeys}
                                    onCheck={(checkedKeys) => this.setState({ leftCheckedKeys: checkedKeys })}
                                    extend={this.onExtend}
                                />
                            </Panel.Body>
                        </Panel>
                    </div>
                    <div className={styles.middle}>
                        <div>
                            <Button onClick={this.leftMoveRight}>
                                <span>右移 </span>
                                <Icon className="si si-com_sxy" />
                            </Button>
                        </div>
                        <div>
                            <Button onClick={this.rightMoveLeft}>
                                <Icon className="si si-com_sxz" />
                                <span>左移</span>
                            </Button>
                        </div>
                    </div>
                    <div className={styles.right}>
                        <Panel>
                            <Panel.Head title={`${applicationName}字段`}>
                                <Panel.HeadRight>
                                    <ul className="ui-list-horizontal">
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                onClick={() => {
                                                    this.setState({
                                                        rightCheckedKey: rightTreeData.map((item) => item.fieldId),
                                                    });
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-com_appsquare" />
                                                    全选
                                                </a>
                                            </Button>
                                        </li>
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                onClick={() => {
                                                    this.setState({
                                                        rightCheckedKey: rightTreeData
                                                            .map((item) => item.fieldId)
                                                            .filter((fieldId) => !rightCheckedKey.includes(fieldId)),
                                                    });
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-jy_jfrx1" />
                                                    反选
                                                </a>
                                            </Button>
                                        </li>
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                onClick={() => {
                                                    this.setState({ rightCheckedKey: [] });
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-com_appsquareo" />
                                                    清空
                                                </a>
                                            </Button>
                                        </li>
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                onClick={() => {
                                                    this.batchUpdateEnabled(rightCheckedKey, true);
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-com_lockopen" />
                                                    启用
                                                </a>
                                            </Button>
                                        </li>
                                        <li>
                                            <Button
                                                type="button"
                                                className="btn-xs"
                                                border={false}
                                                disabled={rightCheckedKey.includes(primaryKeyFieldId)}
                                                onClick={() => {
                                                    this.batchUpdateEnabled(rightCheckedKey, false);
                                                }}
                                            >
                                                <a href="javascript:void(0)">
                                                    <Icon className="si si-com_lock" />
                                                    停用
                                                </a>
                                            </Button>
                                        </li>
                                    </ul>
                                </Panel.HeadRight>
                            </Panel.Head>
                            <Panel.Body full>
                                <Tree
                                    treeData={rightTreeData}
                                    showIcon
                                    checkable
                                    checkedKeys={rightCheckedKey}
                                    onCheck={(checkedKeys) => this.setState({ rightCheckedKey: checkedKeys })}
                                    draggable
                                    onDrop={this.onDrop}
                                    extend={this.onExtend}
                                />
                            </Panel.Body>
                        </Panel>
                    </div>
                </div>
                <DatabaseTableFieldInformationEdit
                    show={showEditModal}
                    successFn={(data) => {
                        this.operateSubmit([data]);
                        this.setState({ showEditModal: false });
                    }}
                    cancelFn={() => this.setState({ showEditModal: false })}
                    data={{
                        detail: operateData,
                        metaFieldList,
                    }}
                />
                <TableFieldInformationDetail
                    show={showDetailModal}
                    cancelFn={() => this.setState({ showDetailModal: false })}
                    data={{
                        detail: operateData,
                        metaFieldList,
                    }}
                />
            </Panel>
        );
    }
}

export default FieldApplication;
