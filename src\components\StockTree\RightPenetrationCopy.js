import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

const StockTree = ({ originTreeData, nodeClickEvent }) => {
    const svgRef = useRef(null);
    const config = {
        dx: 200,
        dy: 170,
        width: 0,
        height: 0,
        rectWidth: 170,
        rectHeight: 70,
    };
    const renderGraph = () => {
        const host = d3.select(svgRef.current);
        const dom = host.node();
        const domRect = dom.getBoundingClientRect();
        config.width = domRect.width;
        config.height = domRect.height;

        const oldSvg = d3.select('svg');
        if (!oldSvg.empty()) {
            oldSvg.remove();
        }

        const svg = d3
            .create('svg')
            .attr('viewBox', () => {
                const parentsLength = originTreeData.parents ? originTreeData.parents.length : 0;

                return [-config.width / 2, parentsLength > 0 ? -config.height / 2 : -config.height / 3, config.width, config.height];
            })
            .style('user-select', 'none')
            .style('cursor', 'move');

        const gAll = svg.append('g').attr('id', 'all');
        svg.call(
            d3
                .zoom()
                .scaleExtent([0.2, 5])
                .on('zoom', (e) => {
                    gAll.attr('transform', () => {
                        return `translate(${e.transform.x},${e.transform.y}) scale(${e.transform.k})`;
                    });
                })
        ).on('dblclick.zoom', null);

        const gLinks = gAll.append('g').attr('id', 'linkGroup');
        const gNodes = gAll.append('g').attr('id', 'nodeGroup');
        const tree = d3.tree().nodeSize([config.dx, config.dy]);

        const rootOfDown = d3.hierarchy(originTreeData, (d) => d.children);
        const rootOfUp = d3.hierarchy(originTreeData, (d) => d.parents);
        tree(rootOfDown);

        [rootOfDown.descendants(), rootOfUp.descendants()].forEach((nodes) => {
            nodes.forEach((node) => {
                node._children = node.children || null;
                node.children = null;
            });
        });

        svg.append('marker')
            .attr('id', 'markerOfDown')
            .attr('markerUnits', 'userSpaceOnUse')
            .attr('viewBox', '0 -5 10 10')
            .attr('refX', 55)
            .attr('refY', 0)
            .attr('markerWidth', 10)
            .attr('markerHeight', 10)
            .attr('orient', '90')
            .attr('stroke-width', 2)
            .append('path')
            .attr('d', 'M0,-5L10,0L0,5')
            .attr('fill', '#215af3');

        svg.append('marker')
            .attr('id', 'markerOfUp')
            .attr('markerUnits', 'userSpaceOnUse')
            .attr('viewBox', '0 -5 10 10')
            .attr('refX', -50)
            .attr('refY', 0)
            .attr('markerWidth', 10)
            .attr('markerHeight', 10)
            .attr('orient', '90')
            .attr('stroke-width', 2)
            .append('path')
            .attr('d', 'M0,-5L10,0L0,5')
            .attr('fill', '#215af3');
        const drawLink = ({ source, target }) => {
            const halfDistance = (target.y - source.y) / 2;
            const halfY = source.y + halfDistance;

            return `M${source.x},${source.y} L${source.x},${halfY} ${target.x},${halfY} ${target.x},${target.y}`;
        };
        const update = (source) => {
            if (!source) {
                source = { x0: 0, y0: 0 };
                rootOfDown.x0 = 0;
                rootOfDown.y0 = 0;
                rootOfUp.x0 = 0;
                rootOfUp.y0 = 0;
            }

            const nodesOfDown = rootOfDown.descendants().reverse();
            const linksOfDown = rootOfDown.links();
            const nodesOfUp = rootOfUp.descendants().reverse();
            const linksOfUp = rootOfUp.links();

            tree(rootOfDown);
            tree(rootOfUp);

            const myTransition = svg.transition().duration(500);

            const node1 = gNodes.selectAll('g.nodeOfDownItemGroup').data(nodesOfDown, (d) => d.data.id);

            const node1Enter = node1
                .enter()
                .append('g')
                .attr('class', 'nodeOfDownItemGroup')
                .attr('transform', (d) => `translate(${source.x0},${source.y0})`)
                .attr('fill-opacity', 0)
                .attr('stroke-opacity', 0)
                .style('cursor', 'pointer');

            node1Enter
                .append('rect')
                .attr('width', (d) => (d.depth === 0 ? (d.data.name.length + 2) * 16 : config.rectWidth))
                .attr('height', (d) => (d.depth === 0 ? 30 : config.rectHeight))
                .attr('x', (d) => (d.depth === 0 ? (-(d.data.name.length + 2) * 16) / 2 : -config.rectWidth / 2))
                .attr('y', (d) => (d.depth === 0 ? -15 : -config.rectHeight / 2))
                .attr('rx', 5)
                .attr('stroke-width', 1)
                .attr('stroke', (d) => (d.depth === 0 ? '#5682ec' : '#7A9EFF'))
                .attr('fill', (d) => (d.depth === 0 ? '#7A9EFF' : '#FFFFFF'))
                .on('click', (e, d) => nodeClickEvent(e, d));

            node1Enter
                .append('text')
                .attr('class', 'main-title')
                .attr('x', 0)
                .attr('y', (d) => (d.depth === 0 ? 5 : -14))
                .attr('text-anchor', 'middle')
                .text((d) => (d.depth === 0 ? d.data.name : d.data.name.length > 11 ? d.data.name.substring(0, 11) : d.data.name))
                .attr('fill', (d) => (d.depth === 0 ? '#FFFFFF' : '#000000'))
                .style('font-size', (d) => (d.depth === 0 ? 16 : 14))
                .style('font-family', '黑体')
                .style('font-weight', 'bold');

            node1Enter
                .append('text')
                .attr('class', 'sub-title')
                .attr('x', 0)
                .attr('y', 5)
                .attr('text-anchor', 'middle')
                .text((d) =>
                    d.depth !== 0
                        ? d.data.name.substring(11).length > 10
                            ? `${d.data.name.substring(11, 21)}...`
                            : d.data.name.substring(11)
                        : ''
                )
                .style('font-size', 14)
                .style('font-family', '黑体')
                .style('font-weight', 'bold');

            node1Enter
                .append('text')
                .attr('class', 'percent')
                .attr('x', 12)
                .attr('y', -45)
                .text((d) => (d.depth !== 0 ? d.data.percent : ''))
                .attr('fill', '#000000')
                .style('font-family', '黑体')
                .style('font-size', 14);

            const expandBtnG = node1Enter
                .append('g')
                .attr('class', 'expandBtn')
                .attr('transform', `translate(0,${config.rectHeight / 2})`)
                .style('display', (d) => (d.depth === 0 || !d._children ? 'none' : ''))
                .on('click', (e, d) => {
                    d.children = d.children ? null : d._children;
                    update(d);
                });

            expandBtnG.append('circle').attr('r', 8).attr('fill', '#7A9EFF').attr('cy', 8);

            expandBtnG
                .append('text')
                .attr('text-anchor', 'middle')
                .attr('fill', '#ffffff')
                .attr('y', 13)
                .style('font-size', 16)
                .style('font-family', '微软雅黑')
                .text((d) => (d.children ? '-' : '+'));

            const link1 = gLinks.selectAll('path.linkOfDownItem').data(linksOfDown, (d) => d.target.data.id);

            const link1Enter = link1
                .enter()
                .append('path')
                .attr('class', 'linkOfDownItem')
                .attr('d', (d) => {
                    const o = { source: { x: source.x0, y: source.y0 }, target: { x: source.x0, y: source.y0 } };

                    return drawLink(o);
                })
                .attr('fill', 'none')
                .attr('stroke', '#7A9EFF')
                .attr('stroke-width', 1)
                .attr('marker-end', 'url(#markerOfDown)');

            node1
                .merge(node1Enter)
                .transition(myTransition)
                .attr('transform', (d) => `translate(${d.x},${d.y})`)
                .attr('fill-opacity', 1)
                .attr('stroke-opacity', 1);

            node1
                .exit()
                .transition(myTransition)
                .remove()
                .attr('transform', (d) => `translate(${source.x0},${source.y0})`)
                .attr('fill-opacity', 0)
                .attr('stroke-opacity', 0);

            link1.merge(link1Enter).transition(myTransition).attr('d', drawLink);

            link1
                .exit()
                .transition(myTransition)
                .remove()
                .attr('d', (d) => {
                    const o = { source: { x: source.x, y: source.y }, target: { x: source.x, y: source.y } };

                    return drawLink(o);
                });

            nodesOfUp.forEach((node) => {
                node.y = -node.y;
            });

            const node2 = gNodes.selectAll('g.nodeOfUpItemGroup').data(nodesOfUp, (d) => d.data.id);

            const node2Enter = node2
                .enter()
                .append('g')
                .attr('class', 'nodeOfUpItemGroup')
                .attr('transform', (d) => `translate(${source.x0},${source.y0})`)
                .attr('fill-opacity', 0)
                .attr('stroke-opacity', 0)
                .style('cursor', 'pointer');

            node2Enter
                .append('rect')
                .attr('width', (d) => (d.depth === 0 ? (d.data.name.length + 2) * 16 : config.rectWidth))
                .attr('height', (d) => (d.depth === 0 ? 30 : config.rectHeight))
                .attr('x', (d) => (d.depth === 0 ? (-(d.data.name.length + 2) * 16) / 2 : -config.rectWidth / 2))
                .attr('y', (d) => (d.depth === 0 ? -15 : -config.rectHeight / 2))
                .attr('rx', 5)
                .attr('stroke-width', 1)
                .attr('stroke', (d) => (d.depth === 0 ? '#5682ec' : '#7A9EFF'))
                .attr('fill', (d) => (d.depth === 0 ? '#7A9EFF' : '#FFFFFF'))
                .on('click', (e, d) => nodeClickEvent(e, d));

            node2Enter
                .append('text')
                .attr('class', 'main-title')
                .attr('x', 0)
                .attr('y', (d) => (d.depth === 0 ? 5 : -14))
                .attr('text-anchor', 'middle')
                .text((d) => (d.depth === 0 ? d.data.name : d.data.name.length > 11 ? d.data.name.substring(0, 11) : d.data.name))
                .attr('fill', (d) => (d.depth === 0 ? '#FFFFFF' : '#000000'))
                .style('font-size', (d) => (d.depth === 0 ? 16 : 14))
                .style('font-family', '黑体')
                .style('font-weight', 'bold');

            node2Enter
                .append('text')
                .attr('class', 'sub-title')
                .attr('x', 0)
                .attr('y', 5)
                .attr('text-anchor', 'middle')
                .text((d) =>
                    d.depth !== 0
                        ? d.data.name.substring(11).length > 10
                            ? `${d.data.name.substring(11, 21)}...`
                            : d.data.name.substring(11)
                        : ''
                )
                .style('font-size', 14)
                .style('font-family', '黑体')
                .style('font-weight', 'bold');

            node2Enter
                .append('text')
                .attr('class', 'percent')
                .attr('x', 12)
                .attr('y', 55)
                .text((d) => (d.depth !== 0 ? d.data.percent : ''))
                .attr('fill', '#000000')
                .style('font-family', '黑体')
                .style('font-size', 14);

            const expandBtnG2 = node2Enter
                .append('g')
                .attr('class', 'expandBtn')
                .attr('transform', `translate(0,${-config.rectHeight / 2})`)
                .style('display', (d) => (d.depth === 0 || !d._children ? 'none' : ''))
                .on('click', (e, d) => {
                    d.children = d.children ? null : d._children;
                    update(d);
                });

            expandBtnG2.append('circle').attr('r', 8).attr('fill', '#7A9EFF').attr('cy', -8);

            expandBtnG2
                .append('text')
                .attr('text-anchor', 'middle')
                .attr('fill', '#ffffff')
                .attr('y', -3)
                .style('font-size', 16)
                .style('font-family', '微软雅黑')
                .text((d) => (d.children ? '-' : '+'));

            const link2 = gLinks.selectAll('path.linkOfUpItem').data(linksOfUp, (d) => d.target.data.id);

            const link2Enter = link2
                .enter()
                .append('path')
                .attr('class', 'linkOfUpItem')
                .attr('d', (d) => {
                    const o = { source: { x: source.x0, y: source.y0 }, target: { x: source.x0, y: source.y0 } };

                    return drawLink(o);
                })
                .attr('fill', 'none')
                .attr('stroke', '#7A9EFF')
                .attr('stroke-width', 1)
                .attr('marker-end', 'url(#markerOfUp)');

            node2
                .merge(node2Enter)
                .transition(myTransition)
                .attr('transform', (d) => `translate(${d.x},${d.y})`)
                .attr('fill-opacity', 1)
                .attr('stroke-opacity', 1);

            node2
                .exit()
                .transition(myTransition)
                .remove()
                .attr('transform', (d) => `translate(${source.x0},${source.y0})`)
                .attr('fill-opacity', 0)
                .attr('stroke-opacity', 0);

            link2.merge(link2Enter).transition(myTransition).attr('d', drawLink);

            link2
                .exit()
                .transition(myTransition)
                .remove()
                .attr('d', (d) => {
                    const o = { source: { x: source.x, y: source.y }, target: { x: source.x, y: source.y } };

                    return drawLink(o);
                });

            d3.selectAll('g.expandBtn')
                .select('text')
                .transition()
                .text((d) => (d.children ? '-' : '+'));

            rootOfDown.eachBefore((d) => {
                d.x0 = d.x;
                d.y0 = d.y;
            });
            rootOfUp.eachBefore((d) => {
                d.x0 = d.x;
                d.y0 = d.y;
            });
        };

        update();

        host.append(() => svg.node());
    };
    useEffect(() => {
        renderGraph();
    }, [originTreeData, nodeClickEvent]);

    return <div ref={svgRef} style={{ width: '100%', height: '100%' }} />;
};

export default StockTree;
