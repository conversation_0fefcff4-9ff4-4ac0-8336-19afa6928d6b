require('fancybox/dist/css/jquery.fancybox.css');
const React = require('react');

const { Component } = React;
const uuid = require('uuid');
const { addFile, getDownLoadUrl } = require('./PluploadAdapt');

const EMPTY_PIC = require('../../source/images/image_sc.png');

const inputFaceStyle = {
    position: 'absolute',
    width: '100%',
    height: '100%',
    opacity: 0,
};

const blockCenter = {
    display: 'block',
    textAlign: 'center',
};

const docLabelStyle = { color: 'rgb(65,134,224)', ...blockCenter };

const buttonStyle = { right: '3px', top: '15px' };

class Image extends Component {
    constructor(props) {
        super(props);

        this.state = {
            message: '',
        };

        this.uuid = uuid();

        this.cleanPic = this.cleanPic.bind(this);
        this.fileChange = this.fileChange.bind(this);
        this.messageCallback = this.messageCallback.bind(this);
        this.hasPic = this.hasPic.bind(this);
        this.imageSrc = this.imageSrc.bind(this);
        this.initFancybox = this.initFancybox.bind(this);
    }

    componentDidMount() {
        this.initFancybox();
    }

    initFancybox() {
        $(`#${this.uuid}`)
            .find('.ss_showBigPic,.ss_slBigPic')
            .fancybox({
                helpers: {
                    overlay: {
                        css: {
                            background: 'rgba(0, 0, 0, 0.95)',
                        },
                    },
                },
                type: 'image',
                arrows: false,
            });
    }

    cleanPic() {
        if (this.props.value === '') {
            return;
        }
        this.props.onChange('');
    }

    fileChange(e) {
        addFile(e.target, this.messageCallback);
    }

    messageCallback(type, data) {
        if (type === 'success') {
            this.props.onChange(data);

            return;
        }
        this.setState({
            message: type,
        });
    }

    hasPic() {
        return this.props.value !== '';
    }

    imageSrc() {
        const { value } = this.props;

        return !value ? EMPTY_PIC : getDownLoadUrl(value);
    }

    render() {
        const { label, picExample, disabled, docExample, docLabel } = this.props;

        const { cleanPic, imageSrc, fileChange } = this;

        return (
            <div style={{ float: 'left' }} id={this.uuid}>
                {!disabled && (
                    <button className="btnPicClose" style={buttonStyle} type="button" onClick={cleanPic}>
                        &times;
                    </button>
                )}
                <div className="ss_jy_picDiv" style={{ cursor: 'pointer' }} title="点击上传照片">
                    <img src={imageSrc()} />
                    <span>{label}</span>
                    {!disabled && <input type="file" onChange={fileChange} style={inputFaceStyle} />}
                </div>
                <div style={{ display: 'block' }} className="cl pad-top-25">
                    <a className="ss_showBigPic" style={blockCenter} href={imageSrc()}>
                        点击查看大图
                    </a>
                    {picExample ? (
                        <a className="ss_slBigPic" style={blockCenter} title="照片示例" href={picExample}>
                            照片示例
                        </a>
                    ) : (
                        ''
                    )}
                    {docLabel !== '' && (
                        <a style={docLabelStyle} target="_blank" href={docExample} rel="noreferrer">
                            {docLabel}
                        </a>
                    )}
                </div>
            </div>
        );
    }
}
Image.defaultProps = {
    value: '',
    onChange: () => {},

    label: '',
    picExample: '',
    docLabel: '',
    docExample: '',
};

const EXAMPLE_PIC = {
    SFZ_ZM: require('../../source/images/example/sfz_facade.jpg'),
    SFZ_FM: require('../../source/images/example/sfz_reverse.jpg'),
    CSYXZM: require('../../source/images/example/doctor_proof.jpg'),
    JHR_JHZ: require('../../source/images/example/marriage.jpg'),
    HZ_HKY: require('../../source/images/example/resident_first_page.jpg'),
    FQ_HKY: require('../../source/images/example/resident_page.jpg'),
    MQ_HKY: require('../../source/images/example/resident_page.jpg'),
};

Image.EXAMPLE_PIC = EXAMPLE_PIC;

module.exports = Image;
