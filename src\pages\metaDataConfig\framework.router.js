/*
 * @(#) framework.router.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2021
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2021-03-16 10:35:22
 */
import { USER_PLUGIN_NAME, routesHelper } from '@share/framework';

/**
 * 路由配置
 */
export const routes = routesHelper([
    {
        path: '/databaseTableInfoDetail/:tableId/:tabActiveKey?',
        component: () => import('@/pages/metaDataConfig/routes/DatabaseTableDetail'),
        disabledLoader: [USER_PLUGIN_NAME],
    },
    {
        path: '/tagDefinition',
        component: () => import('@/pages/metaDataConfig/routes/TagDefinition'),
        title: '企业标签定义',
    },
    {
        path: '/PersonnelTagDefinition',
        component: () => import('@/pages/metaDataConfig/routes/PersonnelTagDefinition/'),
        title: '个人标签定义',
    },
    {
        path: '/tagRule',
        component: () => import('@/pages/metaDataConfig/routes/TagRule/List'),
        title: '企业标签定义',
    },
    {
        path: '/playingExecutionConfig',
        component: () => import('@/pages/metaDataConfig/routes/PlayingExecutionConfig'),
        title: '企业标签计算审计',
    },
    {
        path: '/PersonnelPlayingExecutionConfig',
        component: () => import('@/pages/metaDataConfig/routes/PersonnelPlayingExecutionConfig'),
        title: '个人标签计算审计',
    },
    {
        path: '/labelingManagement',
        component: () => import('@/pages/metaDataConfig/routes/LabelingManagement'),
        title: '企业标签标注管理',
    },
   {
       path: '/personnelLabelingManagement',
       component: () => import('@/pages/metaDataConfig/routes/PersonnelLabelingManagement'),
       title: '个人标签标注管理',
   },
]);
