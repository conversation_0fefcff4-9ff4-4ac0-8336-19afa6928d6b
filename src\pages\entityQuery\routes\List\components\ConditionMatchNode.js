import React, { useEffect, Fragment } from 'react';
import { Input, Select } from '@share/shareui-form';
import { addErrorTip, registerFormItem } from '@/utils/shareFormUtil';
import DataNode from './DataNode';
import style from './ConditionMatchNode.scss';

const InputCus = addErrorTip(Input);
const SelectCus = addErrorTip(Select);

const matchSymbolOptions = [
    { label: '为空(NULL)', value: 'isNull' },
    { label: '不为空(NULL)', value: 'isNotNull' },
    { label: '全模糊匹配(LIKE)', value: 'like' },
    { label: '左模糊匹配(LIKE)', value: 'likeLeft' },
    { label: '右模糊匹配(LIKE)', value: 'likeRight' },
    { label: '模糊不匹配(LIKE)', value: 'notLike' },
    { label: '等于(=)', value: 'eq' },
    { label: '不等于(!=)', value: 'ne' },
    { label: '大于(>)', value: 'gt' },
    { label: '大于等于(>=)', value: 'gte' },
    { label: '小于(<)', value: 'lt' },
    { label: '小于等于(<=)', value: 'lte' },
    { label: '在(IN)', value: 'in' },
    { label: '不在(IN)', value: 'notIn' },
    { label: '范围(BETWEEN AND)', value: 'between' },
];

const emptyValueSymbol = ['isNull', 'isNotNull'];
const manyValueSymbol = ['in', 'notIn'];

const ConditionMatchNode = (props) => {
    const { value, onChange, field, inventoryList, suffix } = props;
    const dict = inventoryList.find((item) => item.id === value?.data?.id)?.result?.dict?.alias || '';
    const multiple = Boolean(dict) && manyValueSymbol.includes(value?.matchSymbol);

    useEffect(() => {
        onChange({ target: { value: { ...value, matchSymbol: value?.data?.id ? 'eq' : '', matchValue: '' } } });
    }, [value?.data?.result?.id]);

    useEffect(() => {
        let matchValue = '';
        if (multiple) {
            matchValue = [];
        }
        if (emptyValueSymbol.includes(value?.matchSymbol)) {
            matchValue = null;
        }
        onChange({ target: { value: { ...value, matchValue } } });
    }, [value?.matchSymbol]);

    return (
        <div className={style.body}>
            <DataNode
                field={`${field}.data`}
                label="数据节点"
                noView
                inventoryList={inventoryList}
                suffix={
                    <div className={style.operate}>
                        {value?.data?.result?.id && (
                            <Fragment>
                                <SelectCus
                                    className={style.matchSymbol}
                                    field={`${field}.matchSymbol`}
                                    label="匹配符"
                                    rule="required"
                                    options={matchSymbolOptions}
                                    noView
                                    placeholder="匹配符"
                                />
                                {dict && (
                                    <SelectCus
                                        className={style.matchValue}
                                        field={`${field}.matchValue`}
                                        label="匹配值"
                                        rule="required"
                                        noView
                                        options={dict}
                                        multiple={multiple}
                                        placeholder="匹配值"
                                    />
                                )}
                                {!dict && !['isNull', 'isNotNull'].includes(value?.matchSymbol) && (
                                    <InputCus
                                        className={style.matchValue}
                                        field={`${field}.matchValue`}
                                        label="匹配值"
                                        rule="required"
                                        noView
                                        placeholder={value?.matchSymbol === 'between' ? '匹配值(开始,结束)' : '匹配值'}
                                    />
                                )}
                            </Fragment>
                        )}
                        <div>{suffix}</div>
                    </div>
                }
            />
        </div>
    );
};

export default registerFormItem(ConditionMatchNode);
