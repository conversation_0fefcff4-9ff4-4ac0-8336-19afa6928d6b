import * as XLSX from 'xlsx';
import * as TimeUtil from './TimeUtil';

/**
 * 导出Excel
 * @param workbookSheets Excel数据
 *   范例：
  [{
     sheetName: '工作簿名称',
     sheetData: [['1', 'a', 'aa'],['2', 'b', 'bb'],['3', 'c', 'cc']]
   },
    ...]
 * @param excelName 导出Excel名称
 */
export function exportExcel(workbookSheets = [], excelName = TimeUtil.getCurrentTimeStr()) {
    const wb = XLSX.utils.book_new();

    workbookSheets.forEach((sheet) => {
        const ws = XLSX.utils.aoa_to_sheet(sheet.sheetData);

        XLSX.utils.book_append_sheet(wb, ws, sheet.sheetName);
    });
    XLSX.writeFile(wb, `${excelName}.xlsx`);
}

/**
 * Excel读取
 * @param resource 数据源（字节数据，或者本地数据）
 * @param readerProps 读取数据配置（字节数据时参数为{ type: 'binary' }）
 * @param formatMap 参数过滤转换 如 { 信息项名称: 'infoItemName', 信息项英文名: 'infoItemCode' }
 * @param titleRowNum 标题行下标，默认为 0
 * @returns {Array}
 * 例：[
        {
          sheetName: "工作簿名称",
          sheetData: [{infoItemName:"名称1", infoItemCode:"888"},{infoItemName:"名称2", infoItemCode:"999"}
        },
        ...
       ]
 */
export function readerExcel(resource, readerProps = {}, formatMap, titleRowNum = 0) {
    // 以二进制流方式读取得到整份excel表格对象
    const workbook = XLSX.read(resource, readerProps);
    // 存储获取到的数据
    const workbookData = [];

    // 遍历每张工作表进行读取
    Object.entries(workbook.Sheets).forEach(([sheetName, sheetData]) => {
        let dataList = XLSX.utils.sheet_to_json(sheetData, { header: 1 });
        const startRowArray = (dataList[titleRowNum] || []).map((key) => key.replace(/^(.*)\*$/, '$1'));

        if (startRowArray.length === 0) {
            workbookData.push({ sheetName, sheetData: [] });

            return;
        }
        dataList = dataList.filter((data, index) => index > titleRowNum);
        dataList = dataList.map((dataArray) =>
            startRowArray.reduce((data, key, index) => ({ ...data, [key]: dataArray[index] || '' }), {})
        );
        // 参数转换
        if (formatMap) {
            const handleFormatMap = Object.keys(formatMap).reduce((data, key) => {
                data[key.replace(/^(.*)\*$/, '$1')] = formatMap[key];

                return data;
            }, {});
            const formatKeys = Object.keys(handleFormatMap);

            dataList = dataList
                .filter((data) => formatKeys.some((key) => Object.keys(data).includes(key)))
                .map((data) => {
                    const formatData = {};

                    formatKeys.forEach((formatKey) => {
                        formatData[handleFormatMap[formatKey]] = data[formatKey] ? data[formatKey].toString().trim() : '';
                    });

                    // formatData.rowNum = Reflect.get(data, '_rowNum_');
                    return formatData;
                });
        }
        workbookData.push({ sheetName, sheetData: dataList, sheetTitle: startRowArray });
    });

    return workbookData;
}
