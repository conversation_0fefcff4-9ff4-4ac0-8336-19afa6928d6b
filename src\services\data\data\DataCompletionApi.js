import { vformPost, postJson } from '@/components/common/network/Network';

// 单条主体补全(法人)
export const legalCompletion = (param) => {
    return postJson('/edp-front/data/completion/legal.do', param);
};

// 批量主体补全(法人)（目录）
export const legalCompletionBatch = (catalogId) => {
    return `/edp-front/data/completion/legal/catalog/${catalogId}.do`;
};

// 单条主体补全(自然人)
export const natureCompletion = (param) => {
    return postJson('/edp-front/data/completion/nature.do', param);
};

// 批量主体补全(自然人)（目录）
export const natureCompletionBatch = (catalogId) => {
    return `/edp-front/data/completion/nature/catalog/${catalogId}.do`;
};

// 补全结果下载
export const downloadCompletion = (param) => {
    return vformPost('/edp-front/data/completion/result/download.do', param);
};
