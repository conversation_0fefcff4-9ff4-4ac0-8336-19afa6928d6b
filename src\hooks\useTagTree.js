import { useService } from '@share/framework';
import MetaFieldApi from '@/services/MetaFieldApi';
import { arrayToTree2 } from '@/utils/TreeUtil';
import { filterChecked } from '@/utils';

const QYDA_KEY = '05'; // 企业档案表吗值
const useTagTree = () => {
    const services = useService(MetaFieldApi);
    const getIntersection = (res = [], accInfoList = []) => {
        // 将详情接口返回的accInfoList字段与整个产业数的数据进行匹配，只显示匹配中的数据
        const idSet = new Set(accInfoList.map((item) => item.tagId));
        const intersection = res.reduce((previous, current) => {
            if (idSet.has(current.id)) {
                return [...previous, current.id];
            }

            return previous;
        }, []);

        return intersection;
    };
    const isIdInIntersection = (intersection = [], id = '') => {
        const result = intersection.includes(id);

        return result;
    };
    const getDefaultExpandedKeys = (dataTree) => {
        // 直接遍历第一层数据，提取每个项的 id
        const firstLevelIds = dataTree.map((item) => item.id);

        return firstLevelIds;
    };
    const filterNoCheckedData = (res = [], accInfoList = []) => {
        const intersection = getIntersection(res, accInfoList);
        console.log('🚀 ~ filterNoCheckedData ~ intersection:', intersection);

        const dataTree = arrayToTree2(
            res,
            (item) => !item.parentId || res.every((one) => one.id !== item.parentId),
            (one, two) => one.id === two.parentId,
            (a, b) => (a.sort || 0) - (b.sort || 0),
            (item, children) => {
                return {
                    ...item,
                    key: item.id,
                    title: item.name,
                    name: item.name,
                    value: item.id,
                    isChecked: isIdInIntersection(intersection, item.id),
                    // disableCheckbox: item.nodeType === '2',
                    children: children.length > 0 ? children.map((i) => ({ ...i })) : null,
                };
            }
        );

        return dataTree;
    };

    const treeDataTranslate = (res = [], accInfoList = []) => {
        const intersection = getIntersection(res, accInfoList);

        const transTree = (noDisabled = false) => {
            return arrayToTree2(
                res,
                (item) => !item.parentId || res.every((one) => one.id !== item.parentId),
                (one, two) => one.id === two.parentId,
                (a, b) => (a.sort || 0) - (b.sort || 0),
                (item, children) => {
                    const disabledObj = noDisabled
                        ? { isChecked: isIdInIntersection(intersection, item.id) }
                        : { disabled: isIdInIntersection(intersection, item.id) };

                    return {
                        ...item,
                        key: item.id,
                        title: item.name,
                        name: item.name,
                        value: item.id,
                        ...disabledObj,
                        // disableCheckbox: item.nodeType === '2',
                        children: children.length > 0 ? children.map((i) => ({ ...i })) : null,
                    };
                }
            );
        };
        // 将数据转换成树结构的数据
        const dataTree = transTree();
        const dataTree2 = filterNoCheckedData(res, accInfoList);
        // 获取顶层标签
        const topTagList = filterChecked(dataTree2);
        console.log('🚀 ~ treeDataTranslate ~ topTagList:', topTagList);
        // 提取第一层的 children 并调用函数

        const extractedChildrenWithParents = topTagList
            .map((item) => {
                // 检查 parentId 是否为空，且 item 是否有 children
                if (item.parentId === '' && item?.children?.length > 0) {
                    // 如果有 children，为每个子项添加 parents 字段
                    return item.children.map((child) => ({
                        ...child,
                        parent: { ...item, key: item.key, title: item.title }, // 添加父级信息
                    }));
                }
                if (item.parentId === '') {
                    // 如果 parentId 为空但 item 没有 children，添加自身的信息
                    // 这里假设你想要以相同的格式添加单个项，没有 children 属性
                    return [{ ...item, parents: [] }];
                }

                // 如果 parentId 不为空，或者有 children，不进行处理
                return [];
            })
            .flat(); // 展平结果数组
        const extractIdsWithParents = getDefaultExpandedKeys(dataTree);

        return {
            extractIdsWithParents,
            tagList: extractedChildrenWithParents,
            treeDataSource: {
                treeData: dataTree,
                checkedKeys: res?.filter((item) => isIdInIntersection(intersection, item.id))?.map((item) => item.id) || [],
            },
        };
    };
    const getTreeData = async () => {
        return (await services.getTagList({ queryRange: QYDA_KEY })) || [];
    };

    return {
        getTreeData,
        getDefaultExpandedKeys,
        treeDataTranslate,
    };
};

export default useTagTree;
