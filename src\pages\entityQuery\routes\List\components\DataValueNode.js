import React, { Fragment } from 'react';
import { Input, Select } from '@share/shareui-form';
import { DatePicker } from '@/components/form';
import { addErrorTip, registerFormItem } from '@/utils/shareFormUtil';
import { Icon } from '@share/shareui';
import style from './DataValueNode.scss';

const SelectCus = addErrorTip(Select);

const valueAggTypeOptions = [
    { value: 'MAX', label: '最大' },
    { value: 'MAX', label: '最大' },
    { value: 'MIN', label: '最小' },
    { value: 'SUM', label: '求和' },
    { value: 'AVG', label: '平均' },
    { value: 'JOIN', label: '连接' },
    { value: 'DISTINCT_JOIN', label: '去重连接' },
    { value: 'COUNT', label: '计数' },
];

const DataValueNode = (props) => {
    const { value, onChange, field, inventoryList, prefix, suffix } = props;
    const idOptions = inventoryList.map((item) => ({ label: item.name, value: item.id }));
    const inventory = inventoryList.find((item) => item.id === value?.id) || {};
    const resultOptions = (inventory.result || []).map((item) => ({ label: item.name, value: item.id }));
    const paramConditions = inventory.param || [];
    const updateData = (data) => {
        onChange({ target: { value: data } });
    };
    const onInventoryChange = (e) => {
        updateData({ id: e.target.value, result: {}, condition: {} });
    };

    const resultAggChange = (e) => {
        updateData({ ...value, result: { id: value.result.id, agg: e.target.value || null } });
    };

    return (
        <div className={style.body}>
            <div className={style.operate}>
                <div className={style.switch}>
                    <Icon
                        className="si si-xy_ssjccgs"
                        title="切换类型"
                        onClick={() => updateData({ composeLogic: '', composeNodes: [] })}
                    />
                </div>
                {prefix}
                <div className={style.inventorySelect}>
                    <SelectCus
                        field={`${field}.id`}
                        label="数据清单"
                        rule="required"
                        options={idOptions}
                        noView
                        placeholder="数据清单"
                        onChange={onInventoryChange}
                    />
                    {value?.id && (
                        <Fragment>
                            <SelectCus
                                field={`${field}.result.id`}
                                label="结果项"
                                rule="required"
                                options={resultOptions}
                                noView
                                placeholder="结果项"
                            />
                            <SelectCus
                                field={`${field}.result.agg`}
                                label="聚合方式"
                                options={valueAggTypeOptions}
                                noView
                                placeholder="聚合方式"
                                onChange={resultAggChange}
                            />
                        </Fragment>
                    )}
                </div>
                {suffix}
            </div>
            {Array.isArray(paramConditions) && paramConditions.length > 0 && (
                <div className={style.paramCondition}>
                    {paramConditions.map((item) => {
                        const paramField = `${field}.condition.${item.id}`;
                        if (item.dict) {
                            return (
                                <Select
                                    key={paramField}
                                    field={paramField}
                                    label={item.name}
                                    options={item.dict.alias}
                                    multiple
                                    placeholder={`请选择${item.name}`}
                                    search-col={4}
                                />
                            );
                        }
                        if (item.id === 'YEAR') {
                            return (
                                <DatePicker
                                    key={paramField}
                                    field={paramField}
                                    label={item.name}
                                    format="YYYY"
                                    picker="year"
                                    search-col={4}
                                />
                            );
                        }
                        if (item.id === 'QUARTER') {
                            return (
                                <DatePicker
                                    key={paramField}
                                    field={paramField}
                                    label={item.name}
                                    format="YYYY\QQ"
                                    picker="quarter"
                                    search-col={4}
                                />
                            );
                        }
                        if (item.id === 'MONTH') {
                            return (
                                <DatePicker
                                    key={paramField}
                                    field={paramField}
                                    label={item.name}
                                    format="YYYYMM"
                                    picker="month"
                                    search-col={4}
                                />
                            );
                        }

                        return <Input key={paramField} field={paramField} label={item.name} search-col={4} />;
                    })}
                </div>
            )}
        </div>
    );
};

export default registerFormItem(DataValueNode);
