import React, { Fragment } from 'react';
// 样式
import classnames from 'classnames';
import styles from '@/pages/metaData/styles/index.scss';
// 服务接口
import * as DataQueryApi from '@/services/data/data/DataQueryApi';
import * as DataSaveApi from '@/services/data/data/DataSaveApi';
// 工具类
import { getQueryString } from '@share/utils';
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 组件
import DataFilling from '@/components/credit_data/DataFilling';
import RoleAuthControl from '@/components/business/auth/RoleAuthControl/RoleAuthControl';
import Edit from '@/components/business/metadata/Edit';
import { Panel, ButtonToolBar, Button } from '@share/shareui';

class DataManageEdit extends DataFilling {
    // 初始化数据
    initData = async () => {
        const { editForm } = this.state;
        const {
            match: {
                params: { categoryId, recordId },
            },
        } = this.props;

        if (categoryId && recordId) {
            const data = await DataQueryApi.editDetailByCategory(categoryId, '2', recordId);

            editForm.setFormData(data);
        }
    };

    // 提交Api
    submitApi = (ignoreSuspectedError) => {
        const { editForm } = this.state;
        const {
            match: {
                params: { categoryId },
            },
        } = this.props;
        const data = editForm.getFormData();

        data.ACTION_REASON = getQueryString(this.props.location, 'ACTION_REASON');
        data.ACTION_EXPLAIN = getQueryString(this.props.location, 'ACTION_EXPLAIN');

        return DataSaveApi.updateFromPage(categoryId, '2', data, ignoreSuspectedError);
    };

    // 提交成功
    afterSuccess = () => {
        this.cancel();
    };

    render() {
        const { editConfigList, editForm, checkErrorLevel, composeCheckErrorMsg } = this.state;
        const from = getQueryString(this.props.location, 'from');

        return (
            <Fragment>
                <Panel>
                    <Panel.Head title="数据修改" />
                    <Panel.Body full>
                        <div
                            className={classnames({
                                [styles.isError]: composeCheckErrorMsg.length !== 0,
                            })}
                        >
                            <Edit formState={editForm} editType="update" metadataConfigList={editConfigList} />
                        </div>
                        {composeCheckErrorMsg.length !== 0 && (
                            <div className={`${styles.pl20} input-hasTip has-error`}>
                                {composeCheckErrorMsg.map((item, index) => (
                                    <p className="text text-tip pull-left" key={index}>
                                        <i className="fa fa-times-circle" />
                                        {item}
                                    </p>
                                ))}
                            </div>
                        )}
                    </Panel.Body>
                </Panel>
                <ButtonToolBar>
                    {from !== 'model' && (
                        <Button type="button" bsSize="large" onClick={this.cancel}>
                            取消
                        </Button>
                    )}
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                        <RoleAuthControl buttonKey="meta-data-manage-suspected-confirm">
                            <Button type="button" bsSize="large" bsStyle="success" onClick={() => this.submit(true)}>
                                确认
                            </Button>
                        </RoleAuthControl>
                    )}
                    <Button type="button" bsStyle="primary" onClick={() => this.submit(false)} disabled={editConfigList.length === 0}>
                        提交
                    </Button>
                </ButtonToolBar>
            </Fragment>
        );
    }
}

export default DataManageEdit;
