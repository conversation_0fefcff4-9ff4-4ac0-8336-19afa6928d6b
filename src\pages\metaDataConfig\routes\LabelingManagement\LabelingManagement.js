import React, { useState } from 'react';

import NoData from '@/components/NoData/NoData';
import Highlighter from 'react-highlight-words';
import ShareList from '@share/list';

import { fieldTranslate, moneyFormat } from '@/utils/format';
import { useLogic } from './hooks';
import styles from './LabelingManagement.scss';
import Search from './Search';
import CompanyTag from './CompanyTag';

const TableComponent = (props) => {
    const { api, goDetail, bm, searchVal, handleRemoveTagEnterprise, refresh, tagList } = props;
    const { RYGMLXDM } = bm || {};

    if (api.list.length === 0) {
        return <NoData />;
    }

    return api.list.map((v, index) => {
        return (
            <div key={v.tyshxydm} className={styles.listItem} onClick={() => goDetail(v.tyshxydm)}>
                <div className={styles.listItemTitle}>
                    <Highlighter
                        key={v.tyshxydm}
                        className={styles.title}
                        highlightClassName={styles.titleHighLight}
                        searchWords={[searchVal]}
                        autoEscape
                        textToHighlight={`${v.zzmc} `}
                    />
                    ({v.tyshxydm})
                </div>
                <div className={styles.listItemInfo}>
                    <div>
                        <span>法人：</span>
                        {v.frdbr}
                    </div>
                    <div>
                        <span>注册资本：</span>
                        {moneyFormat(v.zczb)}万元
                    </div>
                    <div>{v.zs}</div>
                </div>

                <CompanyTag
                    rowIndex={index}
                    accInfoList={v.accInfoList}
                    tyshxydm={v.tyshxydm}
                    handleRemoveTagEnterprise={handleRemoveTagEnterprise}
                    refresh={refresh}
                    tagList={tagList}
                />
            </div>
        );
    });
};

const LabelingManagement = () => {
    const { listState, keyWordParam, handlerQuery, goDetail, handleRemoveTagEnterprise, refresh, tagList } = useLogic();

    const renderTableComponent = (props) => (
        <TableComponent
            {...props}
            goDetail={goDetail}
            searchVal={keyWordParam}
            refresh={refresh}
            handleRemoveTagEnterprise={handleRemoveTagEnterprise}
            tagList={tagList}
        />
    );

    return (
        <div className={styles.LabelingManagement}>
            <div className={styles['LabelingManagement-warp']}>
                <Search onSearch={handlerQuery} placeholder="输入企业名称/统一社会信用代码检索" />
                <div className={styles.listBox}>
                    <ShareList uniqKey="id" listState={listState} TableComponent={renderTableComponent} refresh={refresh} />
                </div>
            </div>
        </div>
    );
};
export default LabelingManagement;
