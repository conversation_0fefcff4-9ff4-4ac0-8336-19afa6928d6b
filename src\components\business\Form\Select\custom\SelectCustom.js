import React, { Component } from 'react';
// style
import classNames from 'classnames';
// 工具
import * as ArrayUtil from '@/components/common/common/ArrayUtil';
import styles from '../style/SelectCustom.scss';

function coverToArray(data) {
    if (Array.isArray(data)) {
        return data;
    }
    if (typeof data === 'string' && data !== '') {
        return data.split(',');
    }

    return [];
}

class SelectCustom extends Component {
    static defaultProps = {
        value: [], // 选中值
        options: [], // 选项
        // disabled: false,         // 是否禁用
        multi: false, // 是否多选
        acceptString: false, // 多选时，返回值是否为逗号隔开的字符串
        placeholder: '', // 空值提示语
        searchAble: true, // 是否可搜索
        controlAble: true, // 是否可控制
    };

    constructor(props) {
        super(props);
        this.state = {
            isDropDown: false, // 是否显示下拉框
            selectedFilter: false, // 已选过滤
            inputFilterValue: '', // 搜索输入框的值
        };
    }

    componentDidMount() {
        document.addEventListener('click', this.globalClick, true);
    }

    componentWillUnmount() {
        document.removeEventListener('click', this.globalClick);
    }

    // 值变更事件
    onChange = (valueArray) => {
        const { multi, acceptString, onChange } = this.props;
        let valueResult = valueArray;

        // 判断出参类型
        if (!multi || acceptString) {
            valueResult = valueResult.join(',');
        }
        onChange && onChange({ target: { value: valueResult } });
    };

    // 页面点击事件
    globalClick = (e) => {
        const { isDropDown } = this.state;

        if (isDropDown) {
            // 是否点击自身
            const clickSelf = $(e.target)  //eslint-disable-line
                    .parents('.multiSelectCustomDropDown').length > 0;

            if (clickSelf) {
                return;
            }
            this.setState({ isDropDown: false });
        }
    };

    // 改变下拉框状态
    changeDropDown = () => {
        const { isDropDown } = this.state;
        const { disabled } = this.props;

        if (disabled) {
            return;
        }
        this.setState({
            isDropDown: !isDropDown,
        });
    };

    // 清空下拉
    handleClearSelect = () => {
        const { disabled } = this.props;

        if (disabled) {
            return;
        }
        this.onChange([]);
    };

    // 操作栏全选、清空、反选、按钮点击事件
    handleControlTypeSelect = (options, type) => {
        const { value: keys } = this.props;
        let valueArray = coverToArray(keys);
        const operateValue = options.map((item) => item.value);
        const repeatValue = operateValue.filter((value) => valueArray.includes(value));

        switch (type) {
            case 'all':
                valueArray = [...valueArray, ...operateValue];
                break;
            case 'none':
                valueArray = valueArray.filter((value) => !operateValue.includes(value));
                break;
            case 'invert':
                valueArray = [...valueArray, ...operateValue].filter((value) => !repeatValue.includes(value));
                break;
            default:
                break;
        }
        this.onChange(ArrayUtil.distinct(valueArray));
    };

    // 单选下拉选择项被选中事件
    handleSingleOptionSelect = (item) => {
        const { value: keys } = this.props;
        let valueArray = coverToArray(keys);

        // 如果当前项已选择，就删除
        if (valueArray.includes(item.value)) {
            valueArray = [];
        } else {
            // 未选择，就添加
            valueArray = [item.value];
        }
        this.onChange(valueArray);
    };

    // 多选下拉选择项被选中事件
    handleMultiOptionSelect = (item) => {
        const { value: keys } = this.props;
        let valueArray = coverToArray(keys);

        // 如果当前项已选择，就删除
        if (valueArray.includes(item.value)) {
            valueArray = valueArray.filter((value) => value !== item.value);
        } else {
            // 未选择，就添加
            valueArray = [...valueArray, item.value];
        }
        this.onChange(valueArray);
    };

    lightByMatchStr = (nodeList, matchStr) => {
        return nodeList.map((item) => {
            const index = item.label.indexOf(matchStr);
            const beforeStr = item.label.substr(0, index);
            const afterStr = item.label.substr(index + matchStr.length);
            const showLabel =
                index > -1 ? (
                    <span title={item.title}>
                        {beforeStr}
                        <span style={{ color: '#f50' }}>{matchStr}</span>
                        {afterStr}
                    </span>
                ) : (
                    <span title={item.title}>{item.title}</span>
                );

            item.showLabel = showLabel;

            return item;
        });
    };

    render() {
        const {
            value: keys,
            options,
            onChange,
            disabled,
            multi,
            placeholder,
            searchAble,
            controlAble,
            className,
            filterFn,
            extendHead,
            extendTail,
            ...restProps
        } = this.props;
        const { isDropDown, selectedFilter, inputFilterValue } = this.state;
        // 处理被选中值成数组
        const valueArray = coverToArray(keys);
        // 获取被选中项
        const selectedOptions = options.filter((item) => valueArray.includes(item.value));
        const existSelectValue = ArrayUtil.notEmptyArr(selectedOptions);
        const selectLabel = existSelectValue ? selectedOptions.map((item) => item.label).join('、') : '';
        let filterOptions = options;

        // 获取被过滤后选择项
        if (selectedFilter) {
            filterOptions = selectedOptions;
        }
        if (searchAble && inputFilterValue) {
            filterOptions = filterOptions.filter((item) => item.label.includes(inputFilterValue));
        }
        if (filterFn) {
            filterOptions = filterOptions.filter((item) => filterFn(item));
        }

        this.lightByMatchStr(filterOptions, inputFilterValue);

        const selectedFilterOptions = filterOptions.filter((item) => valueArray.includes(item.value));

        return (
            <div
                className={classNames('Select Select--single w100 is-clearable', {
                    'is-focused': isDropDown,
                    'is-open': isDropDown,
                    'is-disabled': disabled,
                    'has-value': existSelectValue,
                    [className]: className,
                    [styles.selectBox]: true,
                })}
                {...restProps}
            >
                <div className="Select-control">
                    {existSelectValue ? (
                        <div className="Select-multi-value-wrapper" onClick={this.changeDropDown}>
                            <div className="Select-value">
                                <span className="Select-value-label" title={selectLabel} key={selectLabel}>
                                    {selectLabel}
                                </span>
                            </div>
                        </div>
                    ) : (
                        <div className="Select-placeholder" onClick={this.changeDropDown}>
                            <span>{placeholder}</span>
                        </div>
                    )}
                    {existSelectValue && (
                        <span aria-label="清空值" className="Select-clear-zone" title="清空值" onClick={this.handleClearSelect}>
                            <span className="Select-clear">×</span>
                        </span>
                    )}
                    <span className="Select-arrow-zone" onClick={this.changeDropDown}>
                        <span className="Select-arrow" />
                    </span>
                </div>
                {isDropDown && (
                    <div className={`multiSelectCustomDropDown ${styles.dorpDownArea}`}>
                        {extendHead}
                        {searchAble && (
                            <div className={styles.searchArea} style={{ padding: 0 }}>
                                <div className={styles.input} style={{ paddingRight: 0 }}>
                                    <div id="specialSelectInput">
                                        <input
                                            type="checkbox"
                                            checked={selectedFilter}
                                            onClick={() => this.setState({ selectedFilter: !selectedFilter })}
                                            className={styles.customSelectCheck}
                                            title="过滤已选"
                                        />
                                    </div>
                                    <div id="specialSelectInput2">
                                        <input
                                            type="text"
                                            value={inputFilterValue}
                                            onChange={(e) => this.setState({ inputFilterValue: e.target.value })}
                                            placeholder="请输入内容"
                                        />
                                    </div>
                                    <div className={styles.canleBtn2} onClick={() => this.setState({ inputFilterValue: '' })}>
                                        <i className="si si-com_closethin" />
                                    </div>
                                </div>
                            </div>
                        )}
                        {multi && controlAble && (
                            <div className={styles.controlArea}>
                                <label>
                                    <input
                                        type="radio"
                                        name="type"
                                        checked={selectedFilterOptions.length === filterOptions.length}
                                        disabled={selectedFilterOptions.length === filterOptions.length}
                                        onClick={() => this.handleControlTypeSelect(filterOptions, 'all')}
                                    />
                                    全选
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="type"
                                        checked={selectedFilterOptions.length === 0}
                                        disabled={selectedFilterOptions.length === 0}
                                        onClick={() => this.handleControlTypeSelect(filterOptions, 'none')}
                                    />
                                    清空
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="invert"
                                        checked={false}
                                        disabled={filterOptions.length === 0}
                                        onClick={() => this.handleControlTypeSelect(filterOptions, 'invert')}
                                    />
                                    反选
                                </label>
                            </div>
                        )}
                        <div className={styles.optionsArea} style={{ marginTop: searchAble || controlAble ? '8px' : '0px' }}>
                            {filterOptions && filterOptions.length !== 0 ? (
                                filterOptions.map((item, index) => (
                                    <div
                                        className={`${styles.option} clearfix`}
                                        key={index}
                                        onClick={() => (multi ? this.handleMultiOptionSelect(item) : this.handleSingleOptionSelect(item))}
                                        title={item.label}
                                    >
                                        {multi && (
                                            <input
                                                type="checkbox"
                                                className="pull-left"
                                                checked={valueArray && valueArray.includes(item.value)}
                                            />
                                        )}
                                        <span
                                            style={{
                                                whiteSpace: 'nowrap',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                display: 'block',
                                                marginLeft: multi ? '25px' : 0,
                                            }}
                                        >
                                            {item.showLabel}
                                        </span>
                                    </div>
                                ))
                            ) : (
                                <div className={styles.option}>查询不到结果</div>
                            )}
                        </div>
                        {extendTail}
                    </div>
                )}
            </div>
        );
    }
}

export default SelectCustom;
