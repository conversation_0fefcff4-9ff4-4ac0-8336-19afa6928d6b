import React, { Component } from 'react';
import CommonImageUpload from './com/CommonImageUpload';
import getSingleUploadComponent from './com/getSingleUploadComponent';

class ImageUpload extends Component {
    constructor(props) {
        super(props);
        const { singleValue } = props;
        let Com = CommonImageUpload;

        if (singleValue) {
            Com = getSingleUploadComponent(Com);
        }
        this.state = { Com };
    }

    render() {
        const { Com } = this.state;
        const { singleValue, ...restProps } = this.props;

        return <Com {...restProps} />;
    }
}

export default ImageUpload;
