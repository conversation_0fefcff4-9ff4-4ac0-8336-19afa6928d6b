.layout {
    position: relative;
    width: 100%;
    // min-height: 400px;
    height: 100%;
}

.sider {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
}

.content {
    padding-left: 2px;
}

.sider-resizer {
    position: absolute;
    width: 2px;
    top: 0;
    bottom: 0;
    cursor: col-resize;
    background-color: #ebeced;
}

.resize-mask {
    background: rgba(0, 0, 0, 0);
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    cursor: col-resize;
}
