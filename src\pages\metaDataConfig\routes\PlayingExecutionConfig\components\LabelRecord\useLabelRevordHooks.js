import { useForm } from '@share/shareui-form';
import { useService } from '@share/framework';

import { useList } from '@share/list';
import MetaFieldApi from '@/services/MetaFieldApi';
import { useSetState, useUpdateEffect } from 'ahooks';
import { message } from 'antd';

const useLabelRecordHooks = ({ selectNode }) => {
    const metaFieldApi = useService(MetaFieldApi);
    const [formData, form] = useForm({});

    const formValid = async () => {
        if (!(await form.validHelper())) {
            return;
        }
        const { title, id, key, ...other } = formData;
    };
    const listState = useList({
        dataSource: (condition) => {
            return metaFieldApi.tagRuleExecuteLogList({
                data: { tagId: selectNode.id },
                ...condition,
            });
        },
        moreMode: true,
        autoLoad: false,
    });

    // 执行规则
    const handleExecuteRule = async (id) => {
        const res = await metaFieldApi.tagRuleExecute(id);
        if (res) {
            message.success('执行成功', 1, () => {
                // getTagRuleExecuteLogList(selectNode.id);
                listState.refresh();
            });
        } else {
            message.error('执行失败');
        }
    };

    useUpdateEffect(() => {
        // getTagRuleExecuteLogList(selectNode.id);
        listState.query({ tagId: selectNode.id });
    }, [selectNode.id]);

    return {
        form,
        formValid,
        listState,
        handleExecuteRule,
    };
};

export default useLabelRecordHooks;
