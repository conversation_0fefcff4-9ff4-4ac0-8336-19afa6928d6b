import React, { useState } from 'react';
import { Alert } from '@share/shareui';
import { Collapse } from 'antd';
import styles from './CreditInfo.scss';

const { Panel } = Collapse;
const TabContent = ({ data, type, collapse = true }) => {
    const [collapseKey, setCollapseKey] = useState(['0']);
    const onChange = (key) => {
        // console.log(key);
        setCollapseKey(key);
    };
    if (data.length === 0) {
        return (
            <div className={styles.alert}>
                <span>
                    <i className="fa fa-info-circle" />
                    {type}：暂无数据
                </span>
            </div>
        );
    }

    return (
        <div className={styles.tabContent}>
            <Collapse activeKey={collapseKey} collapsible="header" onChange={onChange} expandIconPosition="end">
                {data.map((item, index) => {
                    return (
                        <Panel
                            header={
                                <div className={styles.alert}>
                                    <span>
                                        <i className="fa fa-info-circle" />
                                        {type}
                                        {/* ：来源：市国税局 */}
                                    </span>
                                    {collapse && <span>{collapseKey.includes(`${index}`) ? '收起' : '展开'}</span>}
                                </div>
                            }
                            key={`${index}`}
                        >
                            <div className={styles.infoTable}>
                                {item.map((i, idx) => {
                                    return (
                                        <div className={styles['infoTable-row']} key={idx}>
                                            <div className={styles['infoTable-row_label']}>{i.label}</div>
                                            <div className={styles['infoTable-row_value']}>{i.context}</div>
                                        </div>
                                    );
                                })}
                            </div>
                        </Panel>
                    );
                })}
            </Collapse>
        </div>
    );
};

export default TabContent;
