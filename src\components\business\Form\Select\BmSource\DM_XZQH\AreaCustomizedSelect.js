import React, { Component } from 'react';
// 被封装组件
import { getComponents } from '@share/shareui-form';

const { Select } = getComponents();
// 表码管理器
const bmManager = require('@/components/common/business/manager/BmManager');

class AreaCustomizedSelect extends Component {
    constructor(props) {
        super(props);
        const { value } = props;
        const data = bmManager.getBmList('DM_XZQH');

        this.state = { data };
        this.state = this.getNewState(value);
    }

    componentWillReceiveProps(nextProps) {
        const { value } = nextProps;

        if (value !== this.props.value) {
            const state = this.getNewState(value);

            this.setState(state);
        }
    }

    onProvinceChange = ({ target: { value } }) => {
        const { onChange } = this.props;

        onChange && onChange({ target: { value: value || '000000' } });
    };

    onCityChange = ({ target: { value } }) => {
        const { provinceValue } = this.state;
        const { onChange } = this.props;

        onChange && onChange({ target: { value: value || provinceValue || '000000' } });
    };

    onAreaChange = ({ target: { value } }) => {
        const { provinceValue, cityValue } = this.state;
        const { onChange } = this.props;

        onChange && onChange({ target: { value: value || cityValue || provinceValue || '000000' } });
    };

    getNewState = (value) => {
        const { data } = this.state;
        let provinceValue = '';
        let cityValue = '';
        let areaValue = '';

        if (value && value.length === 6 && value !== '000000') {
            if (new RegExp(/^\d{2}0{4}$/).exec(value)) {
                provinceValue = value;
            } else if (new RegExp(/^\d{4}0{2}$/).exec(value)) {
                cityValue = value;
                provinceValue = cityValue.replace(/^(\d{2})\d{4}$/, '$10000');
            } else {
                areaValue = value;
                cityValue = areaValue.replace(/^(\d{4})\d{2}$/, '$100');
                provinceValue = cityValue.replace(/^(\d{2})\d{4}$/, '$10000');
            }
        }
        const provinceLabel = provinceValue ? bmManager.getBmLabel('DM_XZQH', provinceValue) : '';
        const cityLabel = cityValue ? bmManager.getBmLabel('DM_XZQH', cityValue) : '';
        const provinceOptions = data
            .filter((item) => new RegExp('\\d{2}0000').exec(item.code))
            .map((item) => ({ label: item.label, value: item.code }));
        const cityOptions = data
            .filter(
                (item) =>
                    !new RegExp(/^\d{2}0{4}$/).exec(item.code) &&
                    new RegExp(/^\d{4}0{2}$/).exec(item.code) &&
                    (!provinceValue || new RegExp(`^${provinceValue.substring(0, 2)}\\d{4}$`).exec(item.code))
            )
            .map((item) => {
                const parentProvinceLabel = provinceLabel || bmManager.getBmLabel('DM_XZQH', item.code.replace(/^(\d{2})\d{4}$/, '$10000'));
                const label = item.label.replace(parentProvinceLabel, '');

                return { label, value: item.code };
            });
        const areaOptions = data
            .filter(
                (item) =>
                    !new RegExp(/^\d{4}0{2}$/).exec(item.code) &&
                    new RegExp(/^\d{6}$/).exec(item.code) &&
                    (!cityValue || new RegExp(`^${cityValue.substring(0, 4)}\\d{2}$`).exec(item.code))
            )
            .map((item) => {
                const parentCityLabel = cityLabel || bmManager.getBmLabel('DM_XZQH', item.code.replace(/^(\d{4})\d{2}$/, '$100'));
                const label = item.label.replace(parentCityLabel, '');

                return { label, value: item.code };
            });

        return { provinceValue, cityValue, areaValue, provinceOptions, cityOptions, areaOptions, data };
    };

    render() {
        const { provinceValue, provinceOptions, cityValue, cityOptions, areaValue, areaOptions } = this.state;
        const { value, onChange, addonAfter, ...restProps } = this.props;

        return (
            <span>
                <Select.View
                    {...restProps}
                    value={provinceValue}
                    options={provinceOptions}
                    onChange={this.onProvinceChange}
                    placeholder="-请选择省份-"
                    inline
                />
                <Select.View
                    {...restProps}
                    value={cityValue}
                    options={cityOptions}
                    onChange={this.onCityChange}
                    placeholder="-请选择城市-"
                    inline
                />
                <Select.View
                    {...restProps}
                    value={areaValue}
                    options={areaOptions}
                    onChange={this.onAreaChange}
                    placeholder="-请选择区县-"
                    inline
                />
                {addonAfter}
            </span>
        );
    }
}

export default AreaCustomizedSelect;
