const htmlStyleToReactStyle = (str) => {
    const baseStyle = { background: '#fff', lineHeight: '18px', marginBottom: '4px' };

    const excludeAttribute = ['background', 'color', 'border', 'backgroundColor'];

    if (!str) {
        return baseStyle;
    }

    try {
        const styleObj = {};

        str.split(';').map((v) => {
            const temp = v.split(':');
            const key = temp[0].replace(/-[^0-9]/g, (a) => a.charAt(1).toUpperCase()).trim();
            const value = String(temp[1]);

            // 过滤掉其他样式
            excludeAttribute.includes(key) && (styleObj[key] = value);

            return '';
        });

        return { ...baseStyle, ...styleObj };
    } catch (e) {
        console.error('有非标准的 css 样式');

        return baseStyle;
    }
};

module.exports = {
    htmlStyleToReactStyle,
};
