import { get, vformPost, postJson } from '@/components/common/network/Network';

// 数据列表（表）
export const listByTable = (tableId, objectType = '2', param) => {
    return postJson(`/edp-front/data/query/list/table/${tableId}.do?objectType=${objectType}`, param);
};

// 数据列表（类别）
export const listByCategory = (categoryId, objectType = '2', param) => {
    return postJson(`/edp-front/data/query/list/category/${categoryId}.do?objectType=${objectType}`, param);
};
export const listDataByCategory = (categoryId, objectType = '2', param) => {
    return postJson(`/edp-front/data/upload/log/listData/category/${categoryId}.do?objectType=${objectType}`, param);
};

// 数据列表（目录）
export const listByCatalog = (categoryId, param) => {
    return postJson(`/edp-front/data/query/list/catalog/${categoryId}.do`, param);
};

// 数据详情(表)
export const detailByTable = (tableId, objectType = '2', recordId) => {
    return get(`/edp-front/data/query/detail/table/${tableId}/${recordId}.do?objectType=${objectType}`);
};

// 数据详情(类别)
export const detailByCategory = (categoryId, objectType = '2', recordId) => {
    return get(`/edp-front/data/query/detail/category/${categoryId}/${recordId}.do?objectType=${objectType}`);
};

// 数据详情（目录）
export const detailByCatalog = (catalogId, recordId) => {
    return get(`/edp-front/data/query/detail/catalog/${catalogId}/${recordId}.do`);
};

// 数据编辑详情(表)
export const editDetailByTable = (tableId, objectType = '2', recordId) => {
    return get(`/edp-front/data/query/edit_detail/table/${tableId}/${recordId}.do?objectType=${objectType}`);
};

// 数据编辑详情(类别)
export const editDetailByCategory = (categoryId, objectType = '2', recordId) => {
    return get(`/edp-front/data/query/edit_detail/category/${categoryId}/${recordId}.do?objectType=${objectType}`);
};

// 数据编辑详情(类别)
export const editDetailByCategoryYwId = (categoryId, objectType = '2', ywId) => {
    return get(`/edp-front/data/query/edit_detail/category/${categoryId}/ywId/${ywId}.do?objectType=${objectType}`);
};

// 数据编辑详情（目录）
export const editDetailByCatalog = (catalogId, recordId) => {
    return get(`/edp-front/data/query/edit_detail/catalog/${catalogId}/${recordId}.do`);
};

// 数据excel(表)
export const exportByTable = (tableId, objectType = '2', param) => {
    return vformPost(`/edp-front/data/query/export/table/${tableId}.do`, { objectType, param });
};

// 数据excel(类别)
export const exportByCategory = (categoryId, objectType = '2', param) => {
    return vformPost(`/edp-front/data/query/export/category/${categoryId}.do`, { objectType, param });
};

// 数据excel（目录）
export const exportByCatalog = (catalogId, param) => {
    return vformPost(`/edp-front/data/query/export/catalog/${catalogId}.do`, { param });
};

// 数据excel（目录）
export const exportLedgerByCategory = (categoryId, objectType = '2', param) => {
    return vformPost(`/edp-front/data/query/export/ledger/category/${categoryId}.do`, { objectType, param });
};

// 数据ES类别
export const esCategory = () => {
    return get('/edp-front/data/query/es/category.do');
};

// 数据ES
export const listByEs = (param) => {
    return postJson('/edp-front/data/query/es/list.do', param);
};
