import React, { Component } from 'react';
// 工具类
import { isEqualObject } from '@/components/common/common/Entity';
import { getBmComponent } from '../../../Custom/index';
// 被封装组件
import Select from '../../Select';

const BmSelect = getBmComponent(Select);
// 表码管理器
const bmManager = require('@/components/common/business/manager/BmManager');

class AreaSelect extends Component {
    constructor(props) {
        super(props);
        const data = this.getCacheData(props);

        this.state = { data };
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.cityId !== this.props.cityId || !isEqualObject(nextProps.data, this.props.data)) {
            const cacheData = this.getCacheData(nextProps);

            this.setState({ cacheData });
        }
    }

    getCacheData = (props) => {
        const { cityId, data = bmManager.getBmList('DM_XZQH') } = props;
        const cityLabel = cityId ? bmManager.getBmLabel('DM_XZQH', cityId) : null;
        const cacheData = data
            .filter(
                (item) =>
                    (!cityId || new RegExp(`^${cityId.substring(0, 4)}\\d{2}$`).exec(item.code)) &&
                    !new RegExp(/^\d{4}0{2}$/).exec(item.code) &&
                    new RegExp(/^\d{6}$/).exec(item.code)
            )
            .map((i) => {
                const parentCityLabel = cityLabel || bmManager.getBmLabel('DM_XZQH', i.code.replace(/^(\d{4})\d{2}$/, '$100'));
                const label = i.label.replace(parentCityLabel, '');

                return { label, code: i.code };
            });

        return cacheData;
    };

    render() {
        const { data } = this.state;
        const { cityId, ...restProps } = this.props;

        return <BmSelect {...restProps} data={data} />;
    }
}

export default AreaSelect;
