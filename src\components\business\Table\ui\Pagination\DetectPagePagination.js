/*
 * @(#) DetectPagePagination.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2020
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2020-09-29 15:40:05
 */

import React, { Component } from 'react';
// styles
import './style/ulynlist4react.css';
import loading from '@/assets/images/icon-loading.gif';
import PopModal from '@/components/ui/PopModal';
import PageSizeSelect from '@/components/business/Table/ui/Pagination/PageSizeSelect/PageSizeSelect';
import JumpInput from '@/components/business/Table/ui/Pagination/JumpInput';

class DetectPagePagination extends Component {
    static defaultProps = {
        defaultCurrent: 1,
        defaultPageSize: 10,
        maxPageCount: null,
        current: 1,
        pageSize: 10,
        detectStep: 10,
        detectTotal: 0,
        showTotal: false,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 30],
        showJumper: false,
        onChange: (page, pageSize) => console.log(page, pageSize),
    };

    handleChange = ({ pageSize, current }) => {
        const { onChange } = this.props;

        onChange && onChange(current, pageSize);
    };

    calculationPages = () => {
        const {
            defaultCurrent,
            defaultPageSize,
            current = defaultCurrent,
            pageSize = defaultPageSize,
            maxPageCount,
            detectStep,
            detectTotal,
        } = this.props;
        const pageNumbers = [];
        let totalPageCount = Math.ceil(detectTotal / pageSize);
        let begin;
        let end;

        if (detectTotal === 0) {
            return [1];
        }
        // 判定最大页数限制
        if (maxPageCount && maxPageCount < totalPageCount) {
            totalPageCount = maxPageCount;
        }
        // 计算开头
        begin = current - Math.floor(detectStep / 2);
        if (begin <= 0) {
            begin = 1;
        }
        // 计算结尾
        end = begin + (detectStep - 1);
        if (end > totalPageCount) {
            end = totalPageCount;
            // 纠正开头
            begin = end - (detectStep - 1) >= 1 ? end - (detectStep - 1) : 1;
        }
        // 填充页数
        for (let i = begin; i <= end; i++) {
            pageNumbers.push(i);
        }

        return pageNumbers;
    };

    render() {
        const {
            defaultCurrent,
            defaultPageSize,
            current = defaultCurrent,
            pageSize = defaultPageSize,
            total,
            showTotal,
            pageSizeOptions,
            showSizeChanger,
            showJumper,
            maxPageCount,
        } = this.props;
        const pageNumbers = this.calculationPages();

        return (
            <div className="tablePageBar">
                <div className="totalPage share-pagination_item share-pagination_total__info share-pagination-item">
                    {showTotal && (
                        <p
                            className="share-pagination_total__item share-pagination_total"
                            style={{ backgroundColor: 'rgba(0, 0, 0, 0)', lineHeight: '27px', height: '26px' }}
                        >
                            共有{Number.isInteger(total) ? total : <img src={loading} alt="" />}条
                        </p>
                    )}
                    {showSizeChanger && (
                        <div className="share-pagination_total__item share-pagination_pagesize">
                            <PopModal
                                content={(open, close) => (
                                    <PageSizeSelect
                                        pageSizeOptions={pageSizeOptions}
                                        onChange={(v) => {
                                            this.handleChange({ pageSize: v, current: 1 });
                                            close();
                                        }}
                                    />
                                )}
                            >
                                <p className="share-select_value" style={{ backgroundColor: 'rgba(0, 0, 0, 0)' }}>{`每页${
                                    pageSize || defaultPageSize
                                }条`}</p>
                            </PopModal>
                        </div>
                    )}
                </div>
                <ul className="tablePageBar-pagination share-pagination clearfix">
                    <li
                        title="上一页"
                        className={`share-pagination-prev ${current === 1 ? 'share-pagination-disabled' : ''}`}
                        onClick={() => current > 1 && this.handleChange({ pageSize, current: current - 1 })}
                    >
                        &lt;
                    </li>
                    {pageNumbers[0] === 1 ? (
                        ''
                    ) : (
                        <li title="..." className="share-pagination-item share-pagination-item-more">
                            ...
                        </li>
                    )}
                    {pageNumbers.map((idx) => (
                        <li
                            title={idx}
                            className={`share-pagination-item ${idx === current ? 'share-pagination-item-active' : ''}`}
                            onClick={() => current !== idx && this.handleChange({ pageSize, current: idx })}
                        >
                            <a>{idx}</a>
                        </li>
                    ))}
                    <li
                        title="下一页"
                        className={`share-pagination-next ${
                            current === pageNumbers[pageNumbers.length - 1] ? 'share-pagination-disabled' : ''
                        }`}
                        onClick={() =>
                            current < pageNumbers[pageNumbers.length - 1] && this.handleChange({ pageSize, current: current + 1 })
                        }
                    >
                        &gt;
                    </li>
                </ul>
                {showJumper && (
                    <div className="jumpInput">
                        跳至
                        <JumpInput
                            onPressEnter={(p) =>
                                this.handleChange({
                                    pageSize,
                                    current: maxPageCount && p > maxPageCount ? maxPageCount : p,
                                })
                            }
                            placeholder="请输入..."
                        />
                        页
                    </div>
                )}
            </div>
        );
    }
}

export default DetectPagePagination;
