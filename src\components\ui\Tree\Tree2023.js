import React, { Component } from 'react';
import RC<PERSON>ree, { TreeNode as RCTreeNode } from 'rc-tree';
import 'rc-tree/assets/index.css';

// styles
import './style/index.scss';
import directoryImg from '../../../assets/icons/ico_directory.png';
import fileImg from '../../../assets/icons/ico_file.png';

class Tree extends Component {
    render() {
        const { treeData = [], onAdd, onEdit, onDel, extend, extendBqGroup, ...restProps } = this.props;
        const editGroup = (item) => {
            if (item.groupName) {
                return <ul className="title_operate clearfix">{extendBqGroup && extendBqGroup(item)}</ul>;
            }

            return (
                <ul className="title_operate clearfix" style={{ position: 'relative', zIndex: 9 }}>
                    {/* {item.root && onAdd && <li className="operate_icon operate_add" onClick={() => onAdd(item)} />} */}
                    {/* {onEdit && <li
                        className="operate_icon operate_edit" onClick={() => {
                            console.log('item', item);
                            alert(1);
                            onEdit(item);
                        }}
                    />}
                    {onDel && <li className="operate_icon operate_del" onClick={() => onDel(item.key)} />} */}
                    {extend && extend(item)}
                </ul>
            );
        };
        const loop = (data = []) => {
            return data.map((item) => {
                if (item.root) {
                    return (
                        <RCTreeNode
                            {...item}
                            icon={<img src={item.root ? directoryImg : fileImg} />}
                            key={item.key}
                            title={
                                <span className="title_pane">
                                    <span className="title_content">{item.title}</span>
                                    {editGroup(item)}
                                </span>
                            }
                        >
                            {loop(item.children)}
                        </RCTreeNode>
                    );
                }

                return (
                    <RCTreeNode
                        {...item}
                        key={item.key}
                        title={
                            <span className="title_pane">
                                <span className="title_content">{item.title}</span>
                                {editGroup(item)}
                            </span>
                        }
                    />
                );
            });
        };
        const convertTreeData = loop(treeData);

        return (
            <div className="share-tree_container">
                <RCTree {...restProps}>{convertTreeData}</RCTree>
            </div>
        );
    }
}

export default Tree;
