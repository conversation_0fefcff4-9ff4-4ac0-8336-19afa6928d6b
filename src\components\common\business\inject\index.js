const MeManager = require('../manager/MeManager');
const BmManager = require('../manager/BmManager');
const DeptListManager = require('../manager/DeptListManager');
const LocalManager = require('../manager/LocalManager');

const withInject = require('./withInject');

function withInjectDefLoader(Component, loadingComponent, errorComponent) {
    return withInject(
        Component,
        [
            MeManager.load,
            BmManager.load,
            // DeptListManager.load,
            // LocalManager.load,
        ],
        loadingComponent,
        errorComponent
    );
}

module.exports = {
    withInject,
    withInjectDefLoader,
};
