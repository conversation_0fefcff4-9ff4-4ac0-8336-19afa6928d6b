/*
 * @(#) ScurdUtil.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-07-12 17:16:03
 */
import React from 'react';

/**
 * 实体key转化为小驼峰
 * @param object
 * @returns {{}}
 */
function objectKeyToSmallHump(object) {
    const keys = Object.keys(object);
    const result = {};

    keys.forEach((oldKey) => {
        const arr = oldKey.toLowerCase().split('_');

        for (let i = 1; i < arr.length; i++) {
            arr[i] = arr[i].charAt(0).toUpperCase() + arr[i].substring(1);
        }
        const newKey = arr.join('');

        result[newKey] = object[oldKey];
    });

    return result;
}

/**
 * 获取在线表单列表页信息
 * @param state
 * @returns {Array}
 */
function getScurdListInfo(state) {
    if (!state.listResponseList) {
        return [];
    }

    return state.listResponseList.map((item) => objectKeyToSmallHump(item));
}

/**
 * 获取在线表单详情页信息参数
 * @param props
 */
function getScurdDetailInfo(props) {
    const detailInfo = {};

    props.schema.columns.forEach((item) => {
        detailInfo[item.column] = {
            name: item.column_name,
            label: item.detail_label,
            val: item.val,
            bm: item.detail_ext.data,
            groupName: item.groupName,
            key: item.column,
        };
    });

    return objectKeyToSmallHump(detailInfo);
}

/**
 * 获取在线表单详情页信息参数
 * @param props
 */
function getScurdSimpleDetailInfo(props) {
    const detailInfo = {};

    props.schema.columns.forEach((item) => {
        detailInfo[item.column] = item.val;
    });

    return objectKeyToSmallHump(detailInfo);
}

/**
 * 构建在线表单下拉选择组件
 * @param self 在线表单组件实例this
 * @param Component React目标组件
 * @param options 自定义选项，如果不传则使用在线表单控制下的选项 格式：[{ value: item.id, label: item.label }, ...]
 * @param restProps 额外的自定义属性
 * @param onChange 变更通知回调方法
 * @param originalDetail 详情页使用原样组件
 * @returns {*}
 */
function buildSelectComponent(self, Component, options, restProps, onChange, originalDetail = false) {
    const { itemData, stateChange } = self.props;
    let __options;

    // 如果没有自定义options，则获取在线表单传入的options
    if (!options || !Array.isArray(options)) {
        __options = itemData.con_ext.data.map((item) => ({
            value: item.id,
            label: item.label,
        }));
    } else {
        __options = options;
    }
    const stateVal = stateChange(itemData.column);
    const value = restProps.multi ? (stateVal ? stateVal.replace(/'/g, '').split(',') : []) : stateVal;

    // 详情页中使用纯展示
    if (self.parent().props.blockName === 'detail_item' && !originalDetail) {
        let labels;

        if (Array.isArray(value)) {
            labels = value.map((i) => {
                const opt = __options.find((v) => v.value === i);

                return opt ? opt.label : i;
            });
        } else {
            const opt = __options.find((v) => v.value === value);

            labels = [opt ? opt.label : value];
        }

        return labels ? (
            <div className="Select--multi">
                {labels.map((label) => (
                    <div className="Select-value">
                        <span className="Select-value-label" role="option" aria-selected="true">
                            {label}
                            <span className="Select-aria-only">&nbsp;</span>
                        </span>
                    </div>
                ))}
            </div>
        ) : (
            ''
        );
    }

    return <Component parent={self} {...self.props} {...restProps} options={__options} value={value} onChange={onChange} />;
}

/**
 * 构建在线表单多选下拉组件
 * @param self 在线表单组件实例this
 * @param Component React目标组件
 * @param options 自定义选项，如果不传则使用在线表单控制下的选项 格式：[{ value: item.id, label: item.label }, ...]
 * @param restProps 额外的自定义属性
 * @param isSearch 是否是搜索条件（搜索条件将对每个节点添加符号 '' ，以方便SQL拼接）
 * @param originalDetail 详情页使用原样组件
 * @returns {*}
 */
function buildMultipleSelectComponent(self, Component, options, restProps, isSearch = true, originalDetail = false) {
    const { itemData, stateChange } = self.props;

    return buildSelectComponent(
        self,
        Component,
        options,
        restProps,
        (e) => {
            const values = e.target.value;

            if (isSearch) {
                stateChange(itemData.column, values && Array.isArray(values) && values.length > 0 ? `'${values.join("','")}'` : '');
            } else {
                stateChange(itemData.column, values && Array.isArray(values) && values.length > 0 ? values.join(',') : '');
            }
            self.blurValidate();
        },
        originalDetail
    );
}

/**
 * 构建在线表单单选下拉组件
 * @param self 在线表单组件实例this
 * @param Component React目标组件
 * @param options 自定义选项，如果不传则使用在线表单控制下的选项 格式：[{ value: item.id, label: item.label }, ...]
 * @param restProps 额外的自定义属性
 * @returns {*}
 */
function buildRadioSelectComponent(self, Component, options, restProps) {
    const { itemData, stateChange } = self.props;

    return buildSelectComponent(self, Component, options, restProps, (e) => {
        const { eventBus } = stateChange();

        stateChange(itemData.column, e.target.value);
        if (
            eventBus &&
            eventBus.events &&
            eventBus.events[`${itemData.column}__change`] &&
            eventBus.events[`${itemData.column}__change`].length > 0
        ) {
            eventBus.events[`${itemData.column}__change`].forEach((event) => event && event(e));
        }
        self.blurValidate();
    });
}

module.exports = {
    objectKeyToSmallHump,
    getScurdListInfo,
    getScurdDetailInfo,
    getScurdSimpleDetailInfo,
    buildSelectComponent,
    buildRadioSelectComponent,
    buildMultipleSelectComponent,
};
