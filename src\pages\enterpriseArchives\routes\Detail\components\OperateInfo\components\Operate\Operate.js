/**  版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 *  @Copyright:  Copyright (c) 2020
 *  @Company:厦门畅享信息技术有限公司
 *  @Author: 李家其
 *  Date: 2023/11/24 17:01
 */
// 经营信息
import React from 'react';
import { Panel, Select } from '@share/shareui';
import { Row, TableForm, Text } from '@share/shareui-form';
import { emptyDefault, moneyFormatUnit } from '@/utils/format';
import { useDeepCompareEffect, useSetState } from 'ahooks';
import styles from './Operate.scss';
import { useFetchHooks } from '../../hook';

const Operate = ({ formState, options, tyshxydm }) => {
    const { getBusinessInfo } = useFetchHooks();
    const [state, setState] = useSetState({
        info: {},
        year: '',
    });

    const getBusinessInfoData = async (params) => {
        const res = await getBusinessInfo(params);
        setState({
                     info: res,
                     year: params.year,
                 });
    };

    useDeepCompareEffect(() => {
        if (options?.[0]?.value) {
            getBusinessInfoData({
                                    year: options?.[0]?.value,
                                    creditCode: tyshxydm,
                                });
        }
    }, [options]);

    return (
        <div className={styles.operate}>
            <Panel toggle>
                <Panel.Head
                    title="经营信息"
                    extra={
                        <div className={styles.selectWrap}>
                            <Select
                                options={options}
                                value={state.year}
                                onChange={({ value }) => {
                                    setState({ year: value });
                                    getBusinessInfoData({
                                        nf: value,
                                        tyshxydm,
                                    });
                                }}
                                resetValue=""
                                placeholder="选择年份"
                            />
                        </div>
                    }
                />
                <Panel.Body full>
                    <div className="operateInfoTableRow">
                        <div className="operateInfoDataFromText">
                            数据来源：<span>{emptyDefault(state?.info?._cn_dataSource)}</span>
                        </div>
                        <div className="formTableStyleCover">
                            <TableForm formState={formState}>
                                <Row>
                                    {/* <Text label="主营业务收入" value={`${moneyFormatUnit(info.zyysr)}万元`} /> */}

                                    <Text label="营业收入" value={`${moneyFormatUnit(state?.info?.yysr)}`}/>
                                    <Text label="加计扣除研发费用" value={`${moneyFormatUnit(state?.info?.jjkcfy)}`}/>
                                </Row>
                                <Row>
                                    <Text label="工业产值" value={`${moneyFormatUnit(state?.info?.gycz)}`}/>
                                    <Text label="固定投资" value={`${moneyFormatUnit(state?.info?.gdtz)}`}/>
                                </Row>
                            </TableForm>
                        </div>
                    </div>
                </Panel.Body>
            </Panel>
        </div>
    );
};

export default Operate;
