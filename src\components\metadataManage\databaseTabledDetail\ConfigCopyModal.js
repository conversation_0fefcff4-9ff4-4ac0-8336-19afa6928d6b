import React, { Component } from 'react';
import styles from '@/pages/metaData/styles/index.scss';
// 请求接口
import * as MetaTableApi from '@/services/data/meta/MetaTableApi';
import * as MetaFieldApi from '@/services/data/meta/MetaFieldApi';
// 工具
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
import * as formRule from '@/components/common/common/formRule';
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 列表组件
import { Button, Modal } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';

const { Form, Row, Select, CheckboxGroup, CheckboxGroupCustom } = getComponents();

const defaultBody = {
    type: ['commonConfig', 'esConfig', 'componentConfig', 'ruleConfig'],
    tableName: '',
    fieldCode: [],
};

class ConfigCopyModal extends Component {
    state = {
        editForm: new FormState({ ...defaultBody }, (editForm, callback) => this.setState({ editForm }, callback)),
        dataTableOptions: [],
        dataFieldOptions: [],
        dataMetaFieldList: [],
    };

    async componentDidMount() {
        const {
            data: { tableId },
        } = this.props;
        const dataTableList = await MetaTableApi.getDataTableList();
        const dataTableOptions = dataTableList
            .filter((item) => item.tableId !== tableId)
            .map((item) => ({ label: `${item.tableId}（${item.tableDescription}）`, value: item.tableId }));

        this.setState({ dataTableOptions });
    }

    onTableNameChange = async ({ target: { value } }) => {
        if (!value) {
            return;
        }
        const {
            data: { metaFieldList: currentMetaFieldList },
        } = this.props;
        const { editForm } = this.state;
        const dataMetaFieldList = await MetaFieldApi.tableMetaByTableName(value);
        const currentField = currentMetaFieldList.map((item) => item.fieldCode);
        const dataFieldOptions = dataMetaFieldList
            .filter((item) => currentField.includes(item.fieldCode))
            .map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));

        editForm.setFieldValue('fieldCode', []);
        this.setState({ dataMetaFieldList, dataFieldOptions });
    };

    submit = async () => {
        const {
            successFn,
            data: { metaFieldList: currentMetaFieldList },
        } = this.props;
        const { editForm, dataMetaFieldList } = this.state;
        const { type, fieldCode } = editForm.getFormData();
        const currentTableFields = currentMetaFieldList.map((item) => item.fieldCode);

        if (!FormVaildHelper.isValid(await editForm.valid())) {
            return;
        }

        if (type.includes('identityConfig')) {
            const currentPrimaryKeyMetaField = MetaConfigUtils.filterPrimaryKeyConfig(currentMetaFieldList);
            const sourcePrimaryKeyMetaField = MetaConfigUtils.filterPrimaryKeyConfig(dataMetaFieldList);

            if (sourcePrimaryKeyMetaField.identityConfig) {
                currentPrimaryKeyMetaField.identityConfig = {
                    ...sourcePrimaryKeyMetaField.identityConfig,
                    fieldCodes: Array.isArray(sourcePrimaryKeyMetaField.identityConfig.fieldCodes)
                        ? sourcePrimaryKeyMetaField.identityConfig.fieldCodes.filter((item) => currentTableFields.includes(item))
                        : [],
                };
            }
        }
        currentMetaFieldList
            .filter((currentMetaField) => fieldCode.includes(currentMetaField.fieldCode))
            .forEach((currentMetaField) => {
                const sourceMetaField = dataMetaFieldList.find((item) => item.fieldCode === currentMetaField.fieldCode) || {};

                if (type.includes('commonConfig')) {
                    currentMetaField.showLabel = sourceMetaField.showLabel;
                    currentMetaField.fieldAlias = sourceMetaField.fieldAlias;
                    currentMetaField.fieldOrientedObjectType = sourceMetaField.fieldOrientedObjectType;
                    currentMetaField.openStyle = sourceMetaField.openStyle;
                    currentMetaField.enabled = sourceMetaField.enabled;
                    currentMetaField.businessField = sourceMetaField.businessField;
                }
                if (type.includes('esConfig')) {
                    currentMetaField.esConfig = { ...sourceMetaField.esConfig };
                }
                if (type.includes('componentConfig')) {
                    currentMetaField.queryParam = { ...sourceMetaField.queryParam, order: currentMetaField.queryParam.order };
                    currentMetaField.listParam = { ...sourceMetaField.listParam, order: currentMetaField.listParam.order };
                    currentMetaField.editParam = { ...sourceMetaField.editParam, order: currentMetaField.editParam.order };
                    currentMetaField.detailParam = { ...sourceMetaField.detailParam, order: currentMetaField.detailParam.order };
                }
                if (type.includes('ruleConfig')) {
                    currentMetaField.bmRule = this.convertFieldRule(sourceMetaField.bmRule, currentTableFields);
                    currentMetaField.convertRule = this.convertFieldRule(sourceMetaField.convertRule, currentTableFields);
                    currentMetaField.checkRule = this.convertFieldRule(sourceMetaField.checkRule, currentTableFields);
                    currentMetaField.showRule = this.convertFieldRule(sourceMetaField.showRule, currentTableFields);
                }
                if (type.includes('applicationConfig')) {
                    currentMetaField.queryParam.order = sourceMetaField.queryParam.order;
                    currentMetaField.listParam.order = sourceMetaField.listParam.order;
                    currentMetaField.editParam.order = sourceMetaField.editParam.order;
                    currentMetaField.detailParam.order = sourceMetaField.detailParam.order;
                }
            });

        await MetaFieldApi.saveTableMeta(currentMetaFieldList);

        successFn && successFn(currentMetaFieldList);
        this.cancelFn();
    };

    cancelFn = () => {
        const { cancelFn } = this.props;
        const { editForm } = this.state;

        editForm.setFormData({ ...defaultBody });
        cancelFn && cancelFn();
    };

    convertFieldRule = (ruleList, currentTableFields) => {
        if (!Array.isArray(ruleList)) {
            return [];
        }

        return ruleList.map((fieldRule) => {
            const { checkField, combinationRule = [] } = fieldRule.ruleParam;

            if (checkField && !currentTableFields.includes(checkField)) {
                fieldRule.ruleParam.checkField = null;
            }
            if (Array.isArray(combinationRule) && combinationRule.length > 0) {
                fieldRule.ruleParam.combinationRule = this.convertFieldRule(combinationRule, currentTableFields);
            }

            return fieldRule;
        });
    };

    render() {
        const { show } = this.props;
        const { dataTableOptions, dataFieldOptions, editForm } = this.state;
        const { type, tableName } = editForm.getFormData();
        const fieldCodeRequired = type.length > 1 || (type.length === 1 && !type.includes('identityConfig'));

        return (
            <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={this.cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>拷贝配置</Modal.Header>
                <Modal.Body>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <CheckboxGroup
                                label="拷贝范围"
                                field="type"
                                options={[
                                    { label: '通用配置', value: 'commonConfig' },
                                    { label: 'ES配置', value: 'esConfig' },
                                    { label: '组件配置', value: 'componentConfig' },
                                    { label: '规则配置', value: 'ruleConfig' },
                                    { label: '重复配置', value: 'identityConfig' },
                                    { label: '展现配置', value: 'applicationConfig' },
                                ]}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                        <Row>
                            <Select
                                label="来源表名"
                                field="tableName"
                                options={dataTableOptions}
                                onChange={this.onTableNameChange}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                        {tableName && (
                            <Row>
                                <CheckboxGroupCustom
                                    label="拷贝字段"
                                    field="fieldCode"
                                    options={dataFieldOptions}
                                    startAllChecked
                                    startReverseChecked
                                    rule={fieldCodeRequired ? [formRule.checkRequiredNotBlank()] : []}
                                    required={fieldCodeRequired}
                                />
                            </Row>
                        )}
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        应用
                    </Button>
                    <Button onClick={this.cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default ConfigCopyModal;
