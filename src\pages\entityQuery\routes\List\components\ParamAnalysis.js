import React from 'react';
import { <PERSON><PERSON>, Panel } from '@share/shareui';
import { TableForm, Row, Text } from '@share/shareui-form';

const Detail = (props) => {
    return (
        <Panel>
            <Panel.Body full>
                <TableForm formState={props.detailForm}>
                    <Row>
                        <Text label="耗时(毫秒)" field="took" />
                    </Row>
                    <Row>
                        <Text label="SDK" field="sdk" format={(v) => JSON.stringify(v)} />
                    </Row>
                    <Row>
                        <Text label="SQL" field="sql" />
                    </Row>
                </TableForm>
            </Panel.Body>
        </Panel>
    );
};

export default Detail;
