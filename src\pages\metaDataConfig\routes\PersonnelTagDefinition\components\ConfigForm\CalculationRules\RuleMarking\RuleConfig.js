import React, { Fragment } from 'react';
import * as FormComponents from '@share/shareui-form';
import styles from './RuleMarking.scss';
import RuleItem from './RuleItem';

const { SearchForm } = FormComponents;

const RuleConfig = ({ form, state, event }) => {
    console.log('sss');

    return (
        <Fragment>
            <SearchForm autoLayout={false} formState={form} className={styles.RuleConfig}>
                <RuleItem form={form} state={state} formKey="rule" event={event} />
            </SearchForm>
            <div>说明：满足以上逻辑关系，则在对应的企业贴上标签，其中运算主体对象为企业中台所有的企业。</div>
        </Fragment>
    );
};
export default RuleConfig;
