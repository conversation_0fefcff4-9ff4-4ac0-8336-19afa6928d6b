import React, { Component } from 'react';
import MultiClamp from '@/components/ui/MultiClamp';
import { Popover } from 'antd';
import { FileUpload } from '@/components/business/Form/Upload';
import { CacheServicePageTable } from '../Table/index';

class QueryList extends Component {
    buildColumn = (config) => {
        const { componentParam } = config.listParam;
        // 扩展参数
        const extendProps = componentParam
            ? Object.entries(componentParam).reduce((obj, [key, value]) => {
                  if (typeof value !== 'undefined' && value !== null) {
                      obj[key] = value;
                  }

                  return obj;
              }, {})
            : {};
        const column = {
            title: <MultiClamp title={config.showLabel}>{config.showLabel}</MultiClamp>,
            key: config.fieldCode,
            dataIndex: ['data', config.fieldCode],
            render: (value, data) => this.renderColumnValue(value, config, extendProps, data),
            ...extendProps,
        };

        // 填充默认宽度
        if (column.width) {
            column.width = Number.parseInt(column.width) || 100;
        }

        return column;
    };

    renderColumnValue = (value, config, extendProps, data) => {
        if (!value || (Array.isArray(value) && value.length === 0)) {
            return '';
        }
        if (config.editParam.componentId.includes('FileUpload') || config.editParam.componentId.includes('FileUpload')) {
            return this.renderFileColumnValue(value, config, extendProps);
        }

        return this.renderStringColumnValue(value, config, extendProps);
    };

    renderFileColumnValue = (value) => {
        if (!Array.isArray(value) || value.length === 0) {
            return '';
        }
        const showValue = value.map((item) => item.name).join('；');

        return (
            <Popover
                placement="bottomLeft"
                trigger="click"
                content={
                    <div style={{ width: '196px' }}>
                        <FileUpload value={value} styleType="shareui" disabled />
                    </div>
                }
            >
                <a href="javascript:void(0)">
                    <MultiClamp>{showValue}</MultiClamp>
                </a>
            </Popover>
        );
    };

    renderStringColumnValue = (value, config, extendProps) => {
        // 解析颜色
        function calculateColor(showValue, conditionColor, defaultColor) {
            const valueArray = Array.isArray(showValue) ? showValue : [showValue];

            return conditionColor.reduce((result, item) => (valueArray.includes(item.value) ? item.color : result), defaultColor);
        }
        const { border, defaultColor = '#666666', conditionColor = [] } = extendProps;
        const color = calculateColor(value, conditionColor, defaultColor);
        const convertValue = Array.isArray(value) ? value.join(',') : value;

        // 是否外框
        if (border) {
            return (
                <span className="label label-default" style={{ backgroundColor: color }}>
                    <MultiClamp title={convertValue} style={{ height: '100%' }}>
                        {convertValue}
                    </MultiClamp>
                </span>
            );
        }

        return (
            <MultiClamp title={convertValue} style={{ color }}>
                {convertValue}
            </MultiClamp>
        );
    };

    render() {
        const {
            metadataConfigList = [],
            service,
            rowKey,
            extendHeadColumns = [],
            extendTailColumns = [],
            namespace = 'listConditionNamespace',
            ...restProps
        } = this.props;

        const fixLeftConfig = metadataConfigList.filter(
            (config) => config && config.listParam && config.listParam.componentParam && config.listParam.componentParam.fixed === 'left'
        );
        const noFixConfig = metadataConfigList.filter(
            (config) => config && config.listParam && config.listParam.componentParam && !config.listParam.componentParam.fixed
        );
        const fixRightConfig = metadataConfigList.filter(
            (config) => config && config.listParam && config.listParam.componentParam && config.listParam.componentParam.fixed === 'right'
        );
        const convertConfig = [...fixLeftConfig, ...noFixConfig, ...fixRightConfig];
        const columns = convertConfig.reduce((currentColumns, config) => {
            currentColumns.push(this.buildColumn(config));

            return currentColumns;
        }, []);
        const finallyColumns = [...extendHeadColumns, ...columns, ...extendTailColumns];
        const tableWidth = finallyColumns.reduce(
            (width, column) => width + (Number.parseInt(column.width) || 100),
            restProps.rowSelection ? 60 : 0
        );
        const panelWidth = document.querySelector('.ui-box')
            ? document.querySelector('.ui-box').offsetWidth - 24
            : document.body.clientWidth;

        if (tableWidth > panelWidth) {
            // 表宽度大于容器宽度，添加横向滚动条
            restProps.scroll = { x: 'max-content' };
        } else {
            // 表宽度小于容器宽度，添加尾列自适应 去除所有列固定
            finallyColumns[finallyColumns.length - 1].width = '';
            finallyColumns.forEach((column) => {
                column.fixed = false;
            });
        }

        return (
            <CacheServicePageTable
                namespace={namespace}
                rowKey={rowKey}
                service={service}
                columns={finallyColumns}
                // showEmptyLine
                {...restProps}
            />
        );
    }
}

export default QueryList;
