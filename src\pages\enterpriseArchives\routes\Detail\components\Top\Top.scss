.topInfo {
    display: flex;
    align-items: flex-start;
    flex: 1;
}

.topInfoLeft {
    flex: 1;

    .companyTitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .title {
            font-size: 20px;
            font-weight: bold;
            line-height: 32px;
            color: rgba(0, 0, 0, 0.85);
        }

        .titleLabel {
            background-color: #52c41a;
            color: #fff;
            margin-left: 16px;
            flex-shrink: 0;
        }
    }

    .tagWrap {
        display: flex;
        justify-content: space-between;
        position: relative;
        .companyTag {
            flex: 1; /* div1 自动铺满 */
            margin-bottom: 16px;
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;

            div {
                flex: 1;
            }

            span {
                color: #1677ff;
                background-color: #e4efff;
                cursor: pointer;
                margin-right: 8px;
                margin-bottom: 8px;
            }

            .more {
                background-color: transparent;
                flex-shrink: 0;
                margin-left: 16px;

                i {
                    line-height: 22px;
                }
            }
        }
        .addTag {
            flex: 0;
            align-items: flex-end; /* div2 水平居右 */
            margin-right: 0;
            background-color: transparent;
            margin-left: 16px;
            color: #1677ff;
            cursor: pointer;
            margin-bottom: 8px;
            position: relative;
            i {
                line-height: inherit;
                font-size: 16px;
            }
        }
    }

    .companyInfo {
        display: flex;
        flex-wrap: wrap;

        > div {
            width: 50%;
            padding-right: 20px;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            line-height: 18px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;

            > span {
                color: #8c8c8c;
                width: 80px;
                margin-right: 16px;
                flex-shrink: 0;
            }

            .linkText {
                margin-right: 0;
                width: auto;
            }
        }
    }
}

.topInfoRight {
    width: 35%;
    margin-left: 5%;
    flex-shrink: 0;

    .mapBox {
        height: 220px;
        margin-bottom: 8px;
    }

    .location {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        width: 100%;
        display: flex;
        align-items: center;
        i {
            color: #ff6655;
            margin-right: 4px;
            vertical-align: sub;
        }

        span {
            color: #8c8c8c;
        }
    }
}

.tagTree {
    width: 100%;
    &-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        i {
            cursor: pointer;
        }
    }
    &-footer {
        text-align: right;
        margin: 8px -16px 0;
        border-top: 1px solid #ddd;
        padding: 12px 16px 0;
    }
}
:global {
    .ant-popover-inner-content {
        width: auto !important;
    }
}
.companyTag {
    &-Popover {
        display: flex;
        align-items: stretch;
        &_parents,
        &_oneself {
            &--item {
                padding: 0 12px 0 12px;
            }
        }
        &_parents {
            padding-left: 0;
            &--item {
                position: relative;
                &::after {
                    content: '>';
                    position: absolute;
                    right: -5px;
                    color: rgba(0, 0, 0, 0.3);
                }
            }
        }
        &_children {
            position: relative;

            &::after {
                content: '>';
                position: absolute;
                left: 0;
                top: 0;
                color: rgba(0, 0, 0, 0.3);
            }
        }
    }
}
