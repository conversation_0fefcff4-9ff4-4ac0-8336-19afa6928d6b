import React, { Component } from 'react';
import styles from '@/pages/metaData/styles/index.scss';
// 工具类
import * as Entity from '@/components/common/common/Entity';
import * as formRule from '@/components/common/common/formRule';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
// 组件
import { Modal, Button } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';
import FieldRuleCom from './EditCom/FieldRuleCom';

const { Form, Row, Select, RadioGroup } = getComponents();

class FieldRuleEdit extends Component {
    state = {
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
    };

    componentWillReceiveProps(nextProps) {
        if (!this.props.show && nextProps.show) {
            const { rule } = nextProps.data;
            const { editForm } = this.state;

            editForm.setFormData(Entity.simpleDeepCopy(rule));
            editForm.cleanValidError();
        }
    }

    onRuleIdChange = (value, keyPrefix) => {
        const { ruleOptional } = this.props.data;
        const { editForm } = this.state;
        const { index, fieldCode, ruleType } = editForm.getFormData();
        let editBody;

        if (value) {
            const targetRule = ruleOptional.find((item) => item.ruleId === value);

            editBody = Entity.simpleDeepCopy(targetRule);
        } else {
            editBody = { ruleId: '' };
        }
        editForm.cleanValidError();
        if (keyPrefix) {
            editForm.setFieldValue(keyPrefix.substring(0, keyPrefix.length - 1), editBody);
        } else {
            editBody.index = index;
            editBody.fieldCode = fieldCode;
            editBody.ruleType = ruleType;
            editForm.setFormData(editBody);
        }
    };

    submit = async () => {
        const { successFn } = this.props;
        const { editForm } = this.state;

        if (FormVaildHelper.isValid(await editForm.valid())) {
            successFn && successFn(editForm.getFormData());
        }
    };

    render() {
        const {
            show,
            cancelFn,
            data: { ruleOptional, ruleTypeOptions, bmTableOption, metaFieldList },
        } = this.props;
        const { editForm } = this.state;
        const rule = editForm.getFormData();
        const fieldCodeOptions = metaFieldList.map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));
        const filterRuleOptional = ruleOptional.filter((item) => item.ruleType === rule.ruleType);

        return (
            <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>规则编辑</Modal.Header>
                <Modal.Body>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <Select
                                label="规则字段"
                                field="fieldCode"
                                rule={formRule.checkRequiredNotBlank()}
                                required
                                options={fieldCodeOptions}
                                disabled={rule.index !== -1}
                            />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="规则类型"
                                field="ruleType"
                                rule={formRule.checkRequiredNotBlank()}
                                required
                                options={ruleTypeOptions}
                                onChange={({ target: { value } }) => value && this.onRuleIdChange('')}
                                disabled={rule.index !== -1}
                            />
                        </Row>
                        <FieldRuleCom
                            editForm={editForm}
                            ruleOptional={filterRuleOptional}
                            bmTableOption={bmTableOption}
                            metaFieldList={metaFieldList}
                            onRuleIdChange={this.onRuleIdChange}
                        />
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        确认
                    </Button>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default FieldRuleEdit;
