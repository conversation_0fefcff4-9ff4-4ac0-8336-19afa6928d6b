function isValid(validArray) {
    return validArray.every((vaild) => vaild === true);
}

/* 若不符合配置的校验规则，则页面滚动到第一个校验失败的位置 */
function scrollToError(classNameSelector = '.text.text-tip.pull-left') {
    const firstError = document.querySelector(classNameSelector);

    if (!firstError) {
        return;
    }
    let actualTop = firstError.offsetTop;
    let actualLeft = firstError.offsetLeft;
    let current = firstError.offsetParent;

    while (current !== null) {
        actualTop += current.offsetTop;
        actualLeft += current.offsetLeft;
        current = current.offsetParent;
    }
    document.querySelector('#root').scrollTo(actualLeft, actualTop - 50);
}

module.exports = {
    isValid,
    scrollToError,
};
