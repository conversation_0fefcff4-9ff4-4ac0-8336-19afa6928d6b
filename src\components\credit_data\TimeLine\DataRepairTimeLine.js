/*
 *@(#) DataRepairTimeLine.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-26
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import React, { Component } from 'react';

class DataRepairTimeLine extends Component {
    state = {
        show: false,
    };

    render() {
        const {
            system: { USER_NAME, ERROR_TYPES, CREATE_TIME },
            data,
            preData,
        } = this.props;
        const { show } = this.state;

        return (
            <div style={{ padding: '20px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
                    <div>
                        <span style={{ color: '#aaa' }}>操作用户：</span>
                        <span>{USER_NAME}</span>
                    </div>
                    <div>
                        <span style={{ color: '#aaa' }}>操作时间：</span>
                        <span>{CREATE_TIME}</span>
                    </div>
                    <div>
                        <span style={{ color: '#aaa' }}>修正结果：</span>
                        <span>
                            {ERROR_TYPES ? <span style={{ color: 'red' }}>失败</span> : <span style={{ color: 'green' }}>成功</span>}
                        </span>
                    </div>
                </div>
                <div style={{ marginBottom: '12px' }}>
                    <div>
                        <span style={{ color: '#aaa' }}>修正问题：</span>
                        <span>{(preData && preData.system.ERROR_TYPES) || '无'}</span>
                    </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                    <a onClick={() => this.setState({ show: !show })}>{show ? '收起>>' : '查看修正详情>>'}</a>
                </div>
                {show && (
                    <div>
                        {preData && preData.data && (
                            <div
                                style={{
                                    display: 'flex',
                                    maxHeight: '300px',
                                    overflow: 'auto',
                                    marginTop: '12px',
                                    border: '1px solid #ccc',
                                    borderBottom: '1px solid #ccc',
                                }}
                            >
                                <div
                                    style={{
                                        flex: 1,
                                        padding: '6px',
                                        backgroundColor: '#f5f5f5',
                                        textAlign: 'center',
                                        fontWeight: 'bold',
                                        color: '#666',
                                    }}
                                >
                                    修正前
                                </div>
                                <div
                                    style={{
                                        flex: 1,
                                        padding: '6px',
                                        backgroundColor: '#f5f5f5',
                                        textAlign: 'center',
                                        fontWeight: 'bold',
                                        color: '#666',
                                    }}
                                >
                                    修正后
                                </div>
                            </div>
                        )}
                        <div
                            style={{
                                display: 'flex',
                                maxHeight: '300px',
                                border: '1px solid #ccc',
                                borderBottom: '1px solid #ccc',
                                overflow: 'auto',
                            }}
                        >
                            {preData && preData.data && (
                                <div style={{ width: '50%' }}>
                                    {preData.data.map((v, i) => {
                                        return (
                                            <div
                                                className="clearfix"
                                                style={{
                                                    borderBottom: '1px solid #ccc',
                                                    borderLeft: '1px solid #ccc',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                }}
                                            >
                                                <span
                                                    style={{
                                                        padding: '12px',
                                                        backgroundColor: '#f5f5f5',
                                                        width: '150px',
                                                        float: 'left',
                                                        textAlign: 'right',
                                                    }}
                                                    title={v.label}
                                                >
                                                    {v.label}
                                                </span>
                                                <span
                                                    style={{
                                                        padding: '12px',
                                                        display: 'block',
                                                        flex: 1,
                                                        overflow: 'hidden',
                                                        whiteSpace: 'nowrap',
                                                        textOverflow: 'ellipsis',
                                                        wordBreak: 'break-all',
                                                        color: data && data[i] && v.stringValue !== data[i].stringValue ? 'red' : '#74767A',
                                                    }}
                                                    title={v.stringValue}
                                                >
                                                    {v.stringValue}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                            {data && (
                                <div style={{ width: preData && preData.data ? '50%' : '100%' }}>
                                    {data.map((v, i) => {
                                        return (
                                            <div
                                                key={i}
                                                className="clearfix"
                                                style={{
                                                    borderBottom: '1px solid #ccc',
                                                    borderLeft: '1px solid #ccc',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                }}
                                            >
                                                <span
                                                    style={{
                                                        padding: '12px',
                                                        backgroundColor: '#f5f5f5',
                                                        width: '150px',
                                                        float: 'left',
                                                        textAlign: 'right',
                                                    }}
                                                    title={v.label}
                                                >
                                                    {v.label}
                                                </span>
                                                <span
                                                    style={{
                                                        flex: 1,
                                                        padding: '12px',
                                                        display: 'block',
                                                        overflow: 'hidden',
                                                        whiteSpace: 'nowrap',
                                                        textOverflow: 'ellipsis',
                                                        wordBreak: 'break-all',
                                                        color:
                                                            preData &&
                                                            preData.data &&
                                                            preData.data[i] &&
                                                            v.stringValue !== preData.data[i].stringValue
                                                                ? 'red'
                                                                : '#74767A',
                                                    }}
                                                    title={v.stringValue}
                                                >
                                                    {v.stringValue}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        );
    }
}

export default DataRepairTimeLine;
