.displayFlex {
    display: flex;
    align-items: center;
}
.arrayFormItem {
    flex: 1;
    .ruleList {
        width: 100%;
        position: relative;
        border: 1px solid #ccc;
        margin-bottom: 5px;
        // &:last-child {
        //     margin-bottom: 0;
        // }
        @extend .displayFlex;
        &-head-select {
            display: flex;
            align-items: center;
        }
        &-head {
            &.compose {
                background-color: #f2f8ff;
                border: solid 1px #c3d8e1;
                margin-left: 0;
            }
            flex: 1;
            margin-bottom: 8px;
            padding: 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
            border: solid 1px #d9dadc;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 64px;
        }
        &-head-select {
            flex: 1;
        }
        &-children {
            margin-left: 46px;
        }
        &-btns {
            width: 60px;
            height: 42px;
            // position: absolute;
            top: 4px;
            right: -2px;
            // transform: translateY(-50%);
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            z-index: 3;
            .add {
                color: #1677ff;
            }
            .remove {
                color: #f5222d;
            }
            .toggle {
                color: #8c8c8c;
            }
            i {
                margin-left: 8px;
                font-size: 16px;
                cursor: pointer;
                // margin-bottom: 12px;
            }
        }
    }
    &-group {
        @extend .displayFlex;
        width: 100%;
        &_button {
            margin-right: 12px;
        }
    }
}
