import React from 'react';
import { Input, Tree } from 'antd';
import useHooks from './hooks';
import styles from './Sidebar.scss';

const { Search } = Input;
const Sidebar = (props) => {
    const { onSelect, selectNode, isEdit = true } = props;
    const { treeData, onChange, onDrop, expandedKeys, autoExpandParent, addTreeNode, onExpand } = useHooks(props);
    console.log('selectNodeselectNode', selectNode);

    return (
        <div className={styles.Sidebar}>
            <Search allowClear placeholder="输入标签名称" onSearch={onChange} enterButton style={{ marginBottom: '12px' }} />
            {isEdit && (
                <div className={styles.addRootNode}>
                    <i
                        id="addTreeNode"
                        className="add si si-com_increase"
                        onClick={(event) => {
                            event.stopPropagation();
                            addTreeNode();
                        }}
                    />
                </div>
            )}

            <div className={styles['Sidebar-tree']}>
                <Tree
                    onExpand={onExpand}
                    expandedKeys={expandedKeys}
                    autoExpandParent={autoExpandParent}
                    // defaultExpandAll
                    treeData={treeData}
                    onSelect={onSelect}
                    onDrop={onDrop}
                    selectedKeys={selectNode?.id ? [selectNode?.id] : []}
                    blockNode
                    draggable={{
                        icon: false,
                    }}
                />
            </div>
        </div>
    );
};
export default React.memo(Sidebar);
