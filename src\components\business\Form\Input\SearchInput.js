import React from 'react';
import { Select } from 'antd';
import classnames from 'classnames';
import { get } from '@/components/common/network/Network'; // eslint-disable-line

// styles
import styles from '../style/index.scss';

const { Option } = Select;

function noop(value) {
    return `resourceName=${value}`;
}

function defaultRenderOptions(options) {
    return options.map((i) => ({
        label: i,
        value: i,
    }));
}

class SearchInput extends React.Component {
    state = {
        data: [],
    };

    handleChange = async (value) => {
        const { service, onChange } = this.props;
        const { url, query = noop } = service;

        onChange && onChange(value);

        const finalUrl = `${url}?${query(value)}`;

        const reqData = await get(finalUrl);
        // await sleep(1000);

        this.setState({
            data: reqData,
        });
        // this.setState({
        //     data: [{ lable: '全部', value: '' }, { label: '恩恩', value: '231' }]
        // });

        console.log('reqData==>', reqData);
    };

    render() {
        const { label = '', value, renderOptions = defaultRenderOptions, onChange, ...restProps } = this.props;
        const options = renderOptions(this.state.data);
        const classNames = classnames(styles['share-form__container'], styles['share-form__container--sm']);

        return (
            <div className={classNames}>
                <span className={styles['share-form_container__title']}>{label}：</span>
                <Select
                    mode="combobox"
                    value={value}
                    defaultActiveFirstOption={false}
                    showArrow={false}
                    filterOption={false}
                    onChange={this.handleChange}
                    {...restProps}
                >
                    {options.map((option) => {
                        return (
                            <Option key={option.value} value={option.value}>
                                {option.label}
                            </Option>
                        );
                    })}
                </Select>
            </div>
        );
    }
}

export default SearchInput;
