/**
 * 安全取值，防止嵌套取值报undefined错误
 * @param target 目标对象
 * @param attribute "a.b.c"
 * @param defValue 当为undefined时取到的默认值
 * @return {undefined}
 */
const NO_FOUND = '___NOFOUNd____';

function safeGet(target, attribute, defValue = undefined) {
    if (typeof attribute !== 'string') {
        return defValue;
    }

    const keyArr = attribute.split('.');

    const result = keyArr.reduce((preValue, key) => {
        const nextValue = preValue[key];

        if (typeof nextValue === 'undefined' || nextValue === null || nextValue === NO_FOUND) {
            return NO_FOUND;
        }

        return nextValue;
    }, target);

    return result === NO_FOUND ? defValue : result;
}

/**
 * 魔法set
 * @param target 目标对象
 * @param attribute  "a.b.d" || "a.b[0][c]"
 * @param value  要设置的值
 * @param willChangeOrigin 是否直接修改target
 * @return {{}}
 */
function magicSet(target = {}, attribute, value, willChangeOrigin = false) {
    const attrArray = attribute.split(/[.\[\]]/).filter((key) => key !== '');
    const deptCount = attrArray.length;
    const targetCopy = willChangeOrigin ? target : simpleDeepCopy(target);
    let currentData = targetCopy;

    for (let i = 0, len = deptCount; i < len; i++) {
        const key = attrArray[i];
        const nextKey = attrArray[i + 1];

        if (i === deptCount - 1) {
            currentData[key] = value;
            continue;
        }

        if (typeof currentData[key] === 'undefined') {
            currentData[key] = assertArrayKey(nextKey) ? [] : {};
        }
        currentData = currentData[key];
    }

    return targetCopy;
}

function assertArrayKey(string) {
    return /\d/g.test(string);
}

function filterAttribute(obj, ...keyArr) {
    if (keyArr.length === 0) {
        return { ...obj };
    }
    const result = {};

    Object.keys(obj)
        .filter((key) => !keyArr.includes(key))
        .forEach((key) => {
            result[key] = obj[key];
        });

    return result;
}

/**
 * 将对象中的0，1转换为boolean
 * @param obj
 * @param keyArr
 * @returns {*}
 */
function transNumericToBoolean(entity, keyArr) {
    if (keyArr.length === 0) {
        return entity;
    }
    Object.keys(entity)
        .filter((key) => keyArr.includes(key))
        .forEach((key) => {
            if (entity[key] !== '' && entity[key] != null) {
                entity[key] = entity[key] !== '0';
            }
        });

    return entity;
}

function transBooleanToNumeric(entity, keyArr) {
    if (keyArr.length === 0) {
        return entity;
    }
    keyArr.forEach((key) => {
        if (entity[key] !== '' && entity[key] != null) {
            entity[key] = entity[key] === false ? '0' : '1';
        }
    });

    return entity;
}

/**
 * 普通对象深拷贝，isNoHandleType() === true的话保留原值
 * @param value  拷贝目标
 * @return {*}
 */
function simpleDeepCopy(value) {
    if (isNoHandleType(value)) {
        return value;
    }

    if (Array.isArray(value)) {
        return value.map((item) => simpleDeepCopy(item));
    }

    return Object.entries(value).reduce((preData, [key, value]) => {
        preData[key] = simpleDeepCopy(value);

        return preData;
    }, {});
}

function simpleCopy(value) {
    return { ...value };
}

/**
 * 只拷贝本对象拥有的属性
 * @param owners
 * @param copysource
 * @returns {*}
 */
function copyOwnersProperties(owners, source) {
    const copyOwners = simpleDeepCopy(owners);

    Object.keys(copyOwners).forEach((key) => {
        if (source[key] !== undefined && source[key] != null && source[key] !== '') {
            copyOwners[key] = source[key];
        }
    });

    return copyOwners;
}

/**
 * 拷贝对象，过滤null
 */
function copyNotNull(obj) {
    const param = {};

    if (obj === null || obj === '') {
        return param;
    }
    for (const key in obj) {
        if (obj[key] !== null && obj[key] !== '') {
            param[key] = obj[key];
        }
    }

    return param;
}

const noHandleType = ['string', 'number', 'undefined', 'boolean', 'function'];

function isNoHandleType(obj) {
    if (obj === null) {
        return true;
    }

    const type = typeof obj;

    return noHandleType.includes(type);
}

/**
 * 压扁对象
 * @param obj
 * @param parentKey
 * @param splitKey
 * @return {{}}
 */
function plain(obj, parentKey = '', splitKey = '.') {
    const result = {};

    Object.keys(obj).forEach((key) => {
        const value = obj[key];
        const pKey = parentKey === '' ? key : `${parentKey}${splitKey}${key}`;

        if (isNoHandleType(value)) {
            result[pKey] = value;
        } else {
            const collection = plain(value, pKey);

            Object.assign(result, collection);
        }
    });

    return result;
}

/**
 * 充气对象
 * @param plainObj
 * @param key
 * @return {undefined}
 */
function recoverPlain(plainObj, key = '') {
    const recoverObj = {};

    Object.keys(plainObj)
        .filter((k) => k.startsWith(key))
        .forEach((k) => magicSet(recoverObj, k, plainObj[k], true));

    return key === '' ? recoverObj : safeGet(recoverObj, key);
}

function isEqualObject(a, b) {
    // 去除null情况
    if (a === null && b === null) {
        return true;
    }
    if (a === null || b === null) {
        return false;
    }
    // 判断类型形同
    if (typeof a !== typeof b) {
        return false;
    }
    // 去除undefined情况
    if (typeof a === 'undefined') {
        return true;
    }
    // 如果是对象类型（包含数组）
    if (typeof a === 'object') {
        // 判断是否都是数组或者都不是数组
        if (Array.isArray(a) !== Array.isArray(b)) {
            return false;
        }
        // 获取键集合
        const aKeys = Object.keys(a);
        const bKeys = Object.keys(b);

        // 判断键个数是否相等
        if (aKeys.length !== bKeys.length) {
            return false;
        }
        // 判断键集合是否完全相同
        if (aKeys.some((key) => !bKeys.includes(key))) {
            return false;
        }

        // 判断每个键的值是否都相等
        return aKeys.every((key) => isEqualObject(a[key], b[key]));
    }

    // 剩下数字、布尔值、字符串直接比较是否相等
    return a === b;
}

/**
 * 移除对象中值为空的key
 * @param data
 */
function rmEmptyKey(data) {
    const rmEmptyKeyData = {};

    Object.keys(data)
        .filter((k) => data[k] === 0 || data[k])
        .forEach((key) => (rmEmptyKeyData[key] = data[key]));

    return rmEmptyKeyData;
}

/**
 * 覆盖属性
 * @param original
 * @param source
 * @returns {*}
 */
function cover(original, source) {
    Object.keys(source).forEach((key) => {
        if (typeof source[key] === 'object') {
            if (original[key]) {
                original[key] = cover(original[key], source[key]);
            } else {
                original[key] = source[key];
            }
        } else {
            original[key] = source[key];
        }
    });

    return original;
}

module.exports = {
    safeGet,
    filterAttribute,
    simpleDeepCopy,
    simpleCopy,
    magicSet,
    plain,
    recoverPlain,
    isEqualObject,
    copyOwnersProperties,
    transNumericToBoolean,
    transBooleanToNumeric,
    rmEmptyKey,
    copyNotNull,
    cover,
};
