@charset "utf-8";

.shareuiUpload{
    .uploadBtn{
        display: inline-block;
        width: 196px;
        padding: 8px;
        color: #a4a5a9;
        i{
            display: inline-block;
            width: 32px;
            height: 32px;
            margin-right: 8px;
            vertical-align: middle;
            text-align: center;
            line-height: 30px;
            border: 1px solid #d0d1d4;
            border-radius: 4px;
        }
        span{
            font-size: 12px;
        }
    }
    .uploadedFile{
        position: relative;
        display: inline-block;
        width: 196px;
        padding: 8px;
        margin: 0 8px 8px 0;
        vertical-align: middle;
        border: 1px solid transparent;
        cursor: default;
        font-size: 12px;
        .info{
            margin-left: 8px;
            text-align: left;
            .name{
                margin-bottom: 4px;
                //width: 115px;
                line-height: 16px;
                color: #323538;
            }
            .size{
                line-height: 1;
                color: #a4a5a9;
            }
        }
        &:hover{
            border-color: #c0ecff;
            background: #eaf9ff;
            .closeBtn{
                background: url('../images/icon-uploadSuccess.png') no-repeat center center;
            }
        }
        .closeBtn{
            position: absolute;
            top: 0;
            right: 0;
            width: 28px;
            height: 28px;
            cursor: pointer;
            &:hover{
                background: url('../images/icon-uploadCancel.png') no-repeat center center;
            }
        }
    }
}
