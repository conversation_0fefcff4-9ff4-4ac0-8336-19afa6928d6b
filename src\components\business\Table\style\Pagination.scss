:global {
    .share-pagination_ {
        &container {
            overflow: hidden;
        }

        &item,
        &total__item,
        &actions__action {
            float: left;
        }

        &item:first-child {
            margin-right: 10px;
        }

        &total__info {
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            box-sizing: border-box;
            height: 30px;
        }

        &total__item {
            border-right: 1px solid #ddd;

            &:last-child {
                border-right: none;
            }
        }

        &total {
            display: inline-block;
            height: 30px;
            line-height: 28px;
            padding: 0 10px;
            color: #666;
            font-family: "sans-serif";
            font-size: 12px;
            background-color: #fff;
        }

        &actions__action {
            display: inline-block;
            height: 30px;
            line-height: 28px;
            border: 1px solid #ddd;
            padding: 0 10px;
            color: #666;
            font-family: "sans-serif";
            font-size: 12px;
            background-color: #fff;
            border-right: none;
            margin-bottom: 0;

            &:first-child {
                border-top-left-radius: 5px;
                border-bottom-left-radius: 5px;
            }

            &:last-child {
                border-right: 1px solid #ddd;
                border-top-right-radius: 5px;
                border-bottom-right-radius: 5px;
            }

            &:not(:nth-child(3)) {
                cursor: pointer;

                &:hover {
                    background-color: #e0f6ff;
                }
            }
        }

        &actions__currentPage {
            text-align: center;
            min-width: 60px;
        }

        &pagesize{
            font-size: 12px;
        }
    }

    .ant-table-footer,
    .share-pagination_container,
    .share-pagination_item{
        overflow: visible!important;

        zoom: 1;
        &::before,&::after{
            display: table;
            content: '';
        }
        &::after {
            clear: both;
        }
        .popPromptModel{
            right: -12px;
            top: auto;
            bottom: 40px;
        }
    }
    .share-pagination_total {
        height: 28px;
        line-height:28px;
        border-radius: 5px;
        border-right:none;

    }
    .share-pagination_total__info{
        height: 30px;
        .share-select_value {
            height: 28px;
            border-radius: 5px;
        }
    }

    .cursorPointer {
        cursor: pointer;
    }
    .disabled{
        cursor: not-allowed !important;
        background: #f7f8f9!important;
    }

}
