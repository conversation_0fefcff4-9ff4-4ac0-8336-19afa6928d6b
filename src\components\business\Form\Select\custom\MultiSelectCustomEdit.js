/*
 * @(#) MultiSelectCustomEdit.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2020
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2020-04-01 11:23:39
 */

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import MultiClamp from '@/components/ui/MultiClamp/index';
// 工具
import * as ArrayUtil from '@/components/common/common/ArrayUtil';
// style
import classNames from 'classnames';
import styles from '../style/MultiSelectCustom.scss';

function coverToArray(data) {
    if (Array.isArray(data)) {
        return data;
    }
    if (typeof data === 'string' && data !== '') {
        return data.split(',');
    }

    return [];
}

// todo 解决多次绑定document点击事件

let multiSelectCustomCount = 0;

class MultiSelectCustom extends Component {
    constructor(props) {
        super(props);
        this.count = ++multiSelectCustomCount;
        props.parent && (props.parent.ready = this.authChangeCustomEditCss);
    }

    state = {
        isDropDown: false, // 是否显示下拉框
        searchInputValue: '', // 未确认搜索输入框的值
        searchValue: '', // 已确认搜索输入框的值
        controlType: '', // 全选、全不选、反选
    };

    componentDidMount() {
        document.addEventListener('click', this.handleHideOption, true);
    }

    componentDidUpdate() {
        this.authChangeCustomEditCss();
        setTimeout(this.authChangeCustomEditCss, 100);
    }

    componentWillUnmount() {
        document.removeEventListener('click', this.handleHideOption);
    }

    // 复选框的一些CSS样式，用于自适应的一些样式
    authChangeCustomEditCss = () => {
        const { field = '' } = this.props;
        const $multiSelectCustomEdit = $(`#multi-select-custom-edit-${field}`);
        const width = $multiSelectCustomEdit.width();
        const height = $multiSelectCustomEdit.height();
        const top = height / 2 - 15;

        // style={{ top: this.customEditCss().top, right: this.customEditCss().right }}
        const css = {
            width: `${width}px`,
            height: `${height}px`,
            top: `${top < 5 ? 0 : top}px`,
            right: height >= 150 ? '20px' : '0px',
        };

        $(`.auth-change-width-${field}`).css('width', css.width);
        $(`.auth-change-height-${field}`).css('height', css.height);
        $(`.auth-change-top-${field}`).css('top', css.top);
        $(`.auth-change-right-${field}`).css('right', css.right);
    };

    // 隐藏下拉框
    handleHideOption = (e) => {
        const { controlType, isDropDown } = this.state;

        if (isDropDown) {
            const isHide = !$(e.target)  //eslint-disable-line
                .parentsUntil('.form-search-wrap', `.multiSelectCustom${this.count}`)
                .hasClass(`multiSelectCustom${this.count}`);

            if (!isHide) {
                return;
            }

            this.setState({
                isDropDown: false,
                searchValue: '',
                searchInputValue: '',
                controlType: controlType === 'invert' ? '' : controlType,
            });
        }
    };

    // 全选、全不选、反选控制按钮
    changeControlType = (type) => {
        const { value: keys, onChange, acceptString, options } = this.props;
        let valueArray = coverToArray(keys);

        switch (type) {
            case 'all':
                valueArray = options.map((item) => item.value);
                break;
            case 'none':
                valueArray = [];
                break;
            case 'invert':
                valueArray = options.map((item) => item.value).filter((value) => !valueArray.includes(value));
                break;
            default:
                break;
        }
        // 判断出参类型
        if (acceptString) {
            valueArray = valueArray.join(',');
        }
        this.setState({ controlType: type }, () => onChange && onChange({ target: { value: valueArray } }));
    };

    // 下拉选择项
    handleSelect = (item) => {
        const { value: keys, onChange, acceptString } = this.props;
        let valueArray = coverToArray(keys);

        // 如果当前项已选择，就删除
        if (valueArray.includes(item.value)) {
            valueArray = valueArray.filter((value) => value !== item.value);
        } else {
            // 未选择，就添加
            valueArray = [...valueArray, item.value];
        }
        // 判断出参类型
        if (acceptString) {
            valueArray = valueArray.join(',');
        }
        onChange && onChange({ target: { value: valueArray } });
    };

    // 改变下拉框状态
    changeDropDown = () => {
        if (this.props.disabled) {
            return;
        }
        this.setState({
            isDropDown: !this.state.isDropDown,
        });
    };

    // 删除全部事件
    handleRemoveAllSelect = (e) => {
        e.stopPropagation();
        const { onChange } = this.props;

        this.setState({ controlType: 'none' }, () => onChange && onChange({ target: { value: '' } }));
    };

    render() {
        const {
            value: keys,
            options,
            disabled,
            isEdit = false,
            placeholder,
            searchAble,
            controlAble,
            className,
            field = '',
            ...restProps
        } = this.props;
        const { isDropDown, searchInputValue, searchValue, controlType } = this.state;

        // 去除onChange事件，防止冒泡的时候触发导致数据被异常修改
        restProps.onChange = undefined;  //eslint-disable-line
        // 处理被选中值成数组
        const valueArray = coverToArray(keys);
        // 获取被选中项
        const selectedOptions = options.filter((item) => valueArray.includes(item.value));
        // 获取被输入框过滤后选择项
        const filterOptions = searchValue.toString() ? options.filter((item) => item.label.includes(searchValue)) : options;

        return (
            <div
                className={`${styles.selectBox} ${className} ${isEdit ? styles.editStyle : ''} multiSelectCustom${
                    this.count
                } Select--multi`}
                id={`multi-select-custom-edit-${field}`}
                {...restProps}
            >
                <div
                    className={classNames({
                        [styles.selectArea]: true,
                        [styles.disabled]: disabled,
                    })}
                    onClick={this.changeDropDown}
                >
                    {ArrayUtil.notEmptyArr(selectedOptions) ? (
                        <MultiClamp className={styles.value}>
                            {/* {selectedOptions.map(item => item.label).join('、')} */}
                            {selectedOptions.map((item) => (
                                <div className="Select-value">
                                    <span
                                        className="Select-value-icon"
                                        aria-hidden="true"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            this.handleSelect(item);
                                        }}
                                    >
                                        ×
                                    </span>
                                    <span className="Select-value-label" role="option" aria-selected="true">
                                        {item.label}
                                        <span className="Select-aria-only">&nbsp;</span>
                                    </span>
                                </div>
                            ))}
                        </MultiClamp>
                    ) : (
                        <span className={styles.placeHolder}>{placeholder}</span>
                    )}
                    <div className={`${styles.dropDownBtnGroup} auth-change-top-${field} auth-change-right-${field}`}>
                        <span aria-label="清除所有值" className="Select-clear-zone" title="清除所有值" onClick={this.handleRemoveAllSelect}>
                            <span className="Select-clear">×</span>
                        </span>
                        <span className="Select-arrow-zone">
                            <span
                                className={classNames({
                                    'si si-com_lower-64': !isDropDown,
                                    'si si-com_up': isDropDown,
                                })}
                            />
                        </span>
                    </div>
                </div>
                {isDropDown && (
                    <div className={`${styles.dorpDownArea} auth-change-width-${field}`}>
                        {searchAble && (
                            <div className={styles.searchArea}>
                                <div className={styles.input}>
                                    <input
                                        type="text"
                                        value={searchInputValue}
                                        onChange={(e) => this.setState({ searchInputValue: e.target.value })}
                                        placeholder="请输入内容"
                                    />
                                    <div className={styles.canleBtn} onClick={() => this.setState({ searchInputValue: '' })}>
                                        <i className="si si-com_closethin" />
                                    </div>
                                </div>
                                <button
                                    className={`${styles.searchBtn} btn`}
                                    onClick={() =>
                                        this.setState({
                                            searchValue: searchInputValue,
                                        })
                                    }
                                >
                                    搜索
                                </button>
                            </div>
                        )}
                        {controlAble && (
                            <div className={styles.controlArea}>
                                <label>
                                    <input
                                        type="radio"
                                        name="type"
                                        checked={valueArray.length === options.length}
                                        onClick={() => this.changeControlType('all')}
                                    />
                                    全选
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="type"
                                        checked={valueArray.length === 0}
                                        onClick={() => this.changeControlType('none')}
                                    />
                                    全不选
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="invert"
                                        checked={controlType === 'invert'}
                                        onClick={() => this.changeControlType('invert')}
                                    />
                                    反选
                                </label>
                            </div>
                        )}
                        <div className={styles.optionsArea} style={{ marginTop: searchAble || controlAble ? '8px' : '0px' }}>
                            {filterOptions && filterOptions.length !== 0 ? (
                                filterOptions.map((item, index) => (
                                    <div
                                        className={`${styles.option} clearfix`}
                                        key={index}
                                        onClick={() => this.handleSelect(item)}
                                        title={item.label}
                                    >
                                        <input
                                            type="checkbox"
                                            className="pull-left"
                                            checked={valueArray && valueArray.includes(item.value)}
                                        />
                                        <MultiClamp className="pull-left">{item.label}</MultiClamp>
                                    </div>
                                ))
                            ) : (
                                <div className={styles.option}>查询不到结果</div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        );
    }
}

export default MultiSelectCustom;

MultiSelectCustom.defaultProps = {
    placeholder: '',
    searchAble: true,
    controlAble: true,
};

MultiSelectCustom.propTypes = {
    options: PropTypes.array.isRequired,
    placeholder: PropTypes.string,
    searchAble: PropTypes.bool,
    controlAble: PropTypes.bool,
    className: PropTypes.string,
    acceptString: PropTypes.bool,
    disabled: PropTypes.bool,
};
