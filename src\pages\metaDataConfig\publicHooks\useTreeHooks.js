import { useState, useRef } from 'react';
import { useMount, useUpdateEffect } from 'ahooks';
import { useQuery, useService } from '@share/framework';
import MetaFieldApi from '@/services/MetaFieldApi';
import { arrayToTree2 } from '@/utils/TreeUtil';

const useTreeHooks = () => {
    const { id } = useQuery();
    const [treeDataSource, setTreeDataSource] = useState({
        treeData: [],
        dataSource: [],
        expandedKeys: [],
    });
    const [selectNode, setSelectNode] = useState({});
    const services = useService(MetaFieldApi);

    const onSelect = (value, item) => {
        console.log('onCheck', value, item);
        const { node, nativeEvent } = item;
        console.log('event', nativeEvent);
        if (nativeEvent?.target.id === 'addTreeNode') {
            return;
        }
        setSelectNode(node);
    };

    const getTreeData = async () => {
        const res = (await services.getAllTagList()) || [];

        const initSelectNode = res.find((item) => item.id === id);
        console.log('🚀 ~ getTreeData ~ initSelectNode:', initSelectNode);

        // 将数据转换成树结构的数据
        const dataTree = arrayToTree2(
            res,
            (item) => !item.parentId || res.every((one) => one.id !== item.parentId),
            (one, two) => one.id === two.parentId,
            (a, b) => (a.sort || 0) - (b.sort || 0),
            (item, children) => ({
                ...item,
                key: item.id,
                title: item.name,
                value: item.id,
                children: children.length > 0 ? children.map((i) => ({ ...i })) : null,
            })
        );
        console.log('dataTree', dataTree);
        setSelectNode(initSelectNode || dataTree?.[0] || {});
        setTreeDataSource({
            treeData: dataTree,
            dataSource: res,
            expandedKeys: res?.map((item) => item.id) || [],
        });
    };
    const refresh = () => {
        getTreeData();
    };
    useMount(() => {
        getTreeData();
    });
    useUpdateEffect(() => {
        getTreeData();
    }, [id]);

    return { selectNode, onSelect, treeDataSource, refresh };
};

export default useTreeHooks;
