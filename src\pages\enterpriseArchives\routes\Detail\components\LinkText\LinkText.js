/*
 *@(#) LinkText.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-10
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React from 'react';
import { openTab } from '@/utils';
import { useHistory } from 'react-router';
import s from './LinkText.scss';

const LinkText = ({
    children,
    url,
    onClick,
    style = {},
    type = 'push', // <push | tab | go>
    tabOption = {}, // {key, label} type === tab required
}) => {
    const history = useHistory();

    return (
        <span
            className={s.linkText}
            style={style}
            onClick={() => {
                if (onClick) {
                    return onClick();
                }
                // console.log(url);
                if (url) {
                    if (type === 'push') {
                        history.push(url);
                    }

                    if (type === 'tab') {
                        const { key, label } = tabOption;

                        openTab({
                            key,
                            appId: key,
                            label,
                            url,
                        });
                    }

                    if (type === 'go') {
                        window.location.href = url;
                    }
                }
            }}
        >
            {children}
        </span>
    );
};

export default LinkText;
