import React, { Component } from 'react';
import { Upload, Button } from 'antd';
import { Spin } from '@share/shareui';
import MyAlert from '@/components/ui/MyAlert/MyAlert';
import { assertServerException } from '@/components/common/network/AnalysisServerData';

class FileUploadButton extends Component {
    static defaultProps = {
        buttonText: '上传', // 上传按钮显示内容
        url: '/edp-front/upload/any?type=PUBLIC_RESOURCE', // 服务端文件上传地址
        fileType: '', // 允许上传文件类型（空值不做限制，多值逗号隔开）
        limitSize: Infinity, // 允许上传单文件大小（单位M，允许小数）
        fileNameSize: 50, // 允许上传单文件名称最大长度
        fileMaxNum: 1, // 允许上传文件最大个数
        extendCheckFn: null, // 扩展上传服务器前校验函数(入参：文件体，出参：错误信息，无错误返回空串)
        extendCheckFailFn: null, // 扩展上传服务器前校验失败函数(入参：文件体)
        loadingMaskPage: false, // 上传过程是否页面loading遮罩
        loadingMaskText: '上传中，请等待', // 上传过程中提示内容
        successFn: (data) => console.log('上传成功', data), // 上传成功回调函数
        failFn: () => console.log('上传失败'), // 上传失败回调函数
    };

    state = { fileList: [], loading: false };

    // 检验是否为符合要求的上传文件
    beforeUpload = (file) => {
        const { fileType, limitSize, fileNameSize, extendCheckFn, extendCheckFailFn, loadingMaskPage, loadingMaskText } = this.props;
        const uploadFileType = /^.*\.(.*)$/.test(file.name) ? /^.*\.(.*)$/.exec(file.name)[1].toLocaleLowerCase() : '';
        // 校验
        const checkFileType = !fileType || (uploadFileType && fileType.split(',').some((type) => type.includes(uploadFileType)));
        const checkFileSize = file.size / 1024 / 1024 <= limitSize;
        const checkFileName = file.name.length <= fileNameSize;
        const extendValidErrorMsg = typeof extendCheckFn === 'function' ? extendCheckFn(file) : '';

        if (!checkFileType) {
            MyAlert.fail(`上传文件类型不符合要求（${fileType}）`);
        }
        if (!checkFileSize) {
            MyAlert.fail(`上传文件大小禁止超过${limitSize}M`);
        }
        if (!checkFileName) {
            MyAlert.fail(`上传文件名禁止超过${fileNameSize}个字`);
        }
        if (extendValidErrorMsg) {
            MyAlert.fail(extendValidErrorMsg);
        }
        const checkResult = checkFileType && checkFileSize && checkFileName && !extendValidErrorMsg;

        checkResult && this.setState({ loading: true });
        checkResult && loadingMaskPage && Spin.show(loadingMaskText);
        !checkResult && typeof extendCheckFailFn === 'function' && extendCheckFailFn(file);

        return checkResult;
    };

    // 上传数据发生改变
    handleChange = ({ fileList }) => {
        const { successFn, failFn } = this.props;

        // 服务器异常时中断传输，关闭加载状态
        if (fileList.find((file) => file.status === 'error')) {
            this.setState({ fileList: [], loading: false });
            Spin.hide();
            failFn && failFn();

            return;
        }
        // 处理传输成功文件
        fileList
            .filter((file) => file.status === 'done' && file.response)
            .forEach((file) => {
                try {
                    // 检查请求状态
                    assertServerException(file.response);
                    // 调用上传成功函数
                    successFn && successFn(file.response.data, file.originFileObj);
                } finally {
                    // 请求异常处理
                    this.setState({ loading: false, fileList: [] });
                    Spin.hide();
                }
            });
        // 保留传输中文件
        const finallyFileList = fileList.filter((file) => file.status === 'uploading');

        this.setState({ fileList: finallyFileList });
    };

    render() {
        const { url, buttonText, children, ...restProps } = this.props;
        const { fileList, loading } = this.state;
        const uploadProps = {
            action: `${CONTEXT_PATH}${url}`,  //eslint-disable-line
            fileList,
            beforeUpload: this.beforeUpload,
            onChange: this.handleChange,
            multiple: false,
            showUploadList: false,
            disabled: loading,
            ...restProps,
        };
        const button = children ? (
            React.Children.map(children, (child) =>
                typeof child === 'object' ? React.cloneElement(child, { disabled: uploadProps.disabled }) : child
            )
        ) : (
            <Button disabled={uploadProps.disabled}>{buttonText}</Button>
        );

        return <Upload {...uploadProps}>{button}</Upload>;
    }
}

export default FileUploadButton;
