import React from 'react';
import { FormItem, FieldItem, ErrorTip, Timing } from '@share/shareui-form';

export const registerFormItem = (Com) => {
    return (props) => <FormItem {...props}>{(p) => <Com {...p}>{props.children}</Com>}</FormItem>;
};

export const registerFieldItem = (Com) => {
    return (props) => <FieldItem {...props}>{(p) => <Com {...p}>{props.children}</Com>}</FieldItem>;
};

export const addErrorTip = (Com) => {
    return (props) => (
        <div className={`sub-item clearfix otherPart input-hasTip ${props.className || ''}`}>
            <Com {...props} />
            <ErrorTip field={props.field} />
        </div>
    );
};

export const validFunction = ({ fn, errMsg, errMsgCus, timing: validTiming = Timing.submit }) => {
    return (value, formData, field, timing, otherProps) => {
        if (timing === validTiming || timing === Timing.submit) {
            if (!fn(value, formData, field, timing, otherProps)) {
                if (errMsgCus) {
                    return new Error(`${errMsgCus}`);
                }

                return new Error(`${otherProps.label}${errMsg}`);
            }

            return true;
        }

        return true;
    };
};
