import React, { Component } from 'react';
// 样式
import '../style/Table.scss';
// 子组件
import Table from '../ui/CommonUiTable';

class EnhanceTable extends Component {
    // 创建定制化标题
    createTitleCustom = () => {
        const { title } = this.props;

        return (data) => {
            if (typeof title === 'function') {
                return title(data);
            }

            return <p className="share-table_title">{title}</p>;
        };
    };

    render() {
        const { title, ...restProps } = this.props;
        // 定制化参数
        const customProps = { pagination: false };

        // 定制化标题
        if (title) {
            customProps.title = this.createTitleCustom();
        }

        return <Table locale={{ emptyText: '暂无数据' }} {...restProps} {...customProps} />;
    }
}

export default EnhanceTable;
