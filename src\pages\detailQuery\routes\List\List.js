import React, { Fragment } from 'react';
import { SearchForm, Select, RadioGroup } from '@share/shareui-form';
import { ShareList, Toolbar } from '@share/list';
import { useModel } from './hooks';
import QueryNodes from './components/QueryNodes';
import ConditionNode from './components/ConditionNode';

const typeOptions = [
    { value: 'ent', label: '企业' },
    { value: 'person', label: '个人' },
];

const List = () => {
    const $model = useModel();

    return (
        <Fragment>
            <SearchForm formState={$model.form} query={$model.query} advanceRows={10}>
                <RadioGroup field="type" label="查询类型" rule="required" options={typeOptions} col={12} />
                <Select field="id" label="数据清单" rule="required" options={$model.inventoryOptions} col={12} />
                {$model.paramOptions.length > 0 && (
                    <Fragment>
                        <QueryNodes field="result" label="查询节点" rule="required" options={$model.resultOptions} col={12} />
                        <Select field="group" label="分 组 节 点" options={$model.resultOptions} multiple col={12} />
                        <ConditionNode field="condition" label="条 件 节 点" options={$model.paramOptions} col={12} />
                    </Fragment>
                )}
            </SearchForm>
            <Toolbar title="查询结果">
                <Toolbar.Desc>
                    <Toolbar.DescItem label="耗时(毫秒)" value={$model.detailForm.getFieldValue('took')} bsStyle="primary" />
                </Toolbar.Desc>
            </Toolbar>
            <ShareList listState={$model.list} columns={$model.columns} />
        </Fragment>
    );
};

export default List;
