import React from 'react';
import { Form } from '@share/shareui';
import { FormItem } from '@share/shareui-form';
import styles from './ArrayFormItem.scss';

const ArrayFormItem = (props) => {
    return (
        <FormItem {...props}>
            {(innerProps) => {
                const { value = [], onChange, defaultChildrenData, children, ...restProps } = innerProps;

                return (
                    <Form pageType="addPage" className={styles.arrayFormItem} {...restProps}>
                        <Form.Table>
                            {Array.isArray(value) &&
                                value.map((item, index) => (
                                    // eslint-disable-next-line react/no-array-index-key
                                    <Form.Tr key={index}>
                                        <Form.Content>
                                            <div className={styles['arrayFormItem-content']}>
                                                <div className={styles['arrayFormItem-content_children']}>
                                                    {children &&
                                                        children({
                                                            ...restProps,
                                                            index,
                                                            value: item,
                                                            onChange: ({ target: { value: itemValue } }) => {
                                                                onChange({
                                                                    target: {
                                                                        value: value.map((one, i) => (i !== index ? one : itemValue)),
                                                                    },
                                                                });
                                                            },
                                                        })}
                                                </div>
                                                <div className={styles['arrayFormItem-content_btns']}>
                                                    {/* 新增 */}
                                                    <i
                                                        className={`si si-com_increase ${styles.add}`}
                                                        onClick={() =>
                                                            onChange({
                                                                target: {
                                                                    value: [
                                                                        ...(value.slice(0, index + 1) || []),
                                                                        defaultChildrenData ? { ...defaultChildrenData } : '',
                                                                        ...(value.slice([index + 1]) || []),
                                                                    ],
                                                                },
                                                            })
                                                        }
                                                    />
                                                    {/* 删除 */}
                                                    <i
                                                        className={`si si-com_minus-circle ${styles.delete}`}
                                                        onClick={() =>
                                                            onChange({ target: { value: value.filter((one, i) => i !== index) } })
                                                        }
                                                    />
                                                    {/* 下移 */}
                                                    {index !== value.length - 1 && (
                                                        <i
                                                            className={`si si-app_xy ${styles.down}`}
                                                            onClick={() =>
                                                                onChange({
                                                                    target: {
                                                                        value: [
                                                                            ...(value.slice(0, index) || []),
                                                                            value[index + 1],
                                                                            value[index],
                                                                            ...(value.slice([index + 2]) || []),
                                                                        ],
                                                                    },
                                                                })
                                                            }
                                                        />
                                                    )}
                                                    {/* 上移 */}
                                                    {index !== 0 && (
                                                        <i
                                                            className={`si si-app_sy ${styles.up}`}
                                                            onClick={() =>
                                                                onChange({
                                                                    target: {
                                                                        value: [
                                                                            ...(value.slice(0, index - 1) || []),
                                                                            value[index],
                                                                            value[index - 1],
                                                                            ...(value.slice([index + 1]) || []),
                                                                        ],
                                                                    },
                                                                })
                                                            }
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        </Form.Content>
                                    </Form.Tr>
                                ))}
                            {(!Array.isArray(value) || value.length === 0) && (
                                <Form.Tr key="add">
                                    <Form.Content>
                                        <i
                                            className={`si si-com_increase ${styles['arrayFormItem-addBtn']}`}
                                            onClick={() =>
                                                onChange({
                                                    target: {
                                                        value: [...value, defaultChildrenData ? { ...defaultChildrenData } : ''],
                                                    },
                                                })
                                            }
                                        />
                                    </Form.Content>
                                </Form.Tr>
                            )}
                        </Form.Table>
                    </Form>
                );
            }}
        </FormItem>
    );
};

export default ArrayFormItem;
