/*
 *@(#) DataAuditTimeLine.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-29
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import React, { Component } from 'react';
import * as DataTrcApi from '@/services/data/data/DataTrcApi';
import { Button } from '@share/shareui';
import RoleAuthControl from '@/components/business/auth/RoleAuthControl/RoleAuthControl';

const actionMap = {
    1: '新增',
    2: '审核',
    3: '更新',
    4: '上线',
    5: '下线',
    6: '上传',
    7: '归档',
    8: '错误数据标记',
    9: '删除',
};
const statusMap = {
    0: '待审核',
    1: '公示中',
    2: '已下线',
    3: '已归档',
    4: '已删除',
    5: '待修正',
};

class DataAuditTimeLine extends Component {
    state = {
        show: false,
        preRecordDetail: null,
        recordDetail: null,
    };

    handleShowDetail = () => {
        const {
            system: { YW_ID, VERSION_ID, ACTION },
            preData,
        } = this.props;

        this.setState({ show: !this.state.show }, async () => {
            const { show, recordDetail: currentRecordDetail } = this.state;

            if (show && currentRecordDetail === null) {
                const recordDetail = await this.requestDetail(YW_ID, VERSION_ID);

                this.setState({ recordDetail });

                if (ACTION === '3' && preData && preData.system.VERSION_ID) {
                    const preRecordDetail = await this.requestDetail(preData.system.YW_ID, preData.system.VERSION_ID);

                    this.setState({ preRecordDetail });
                }
            }
        });
    };

    requestDetail = async (YW_ID, VERSION_ID) => {
        const { categoryCode, objectType } = this.props;
        const result = await DataTrcApi.detailsByCategory(categoryCode, objectType, { data: { YW_ID, VERSION_ID }, page: null });

        if (Array.isArray(result.list) && result.list.length > 0) {
            return result.list[0];
        }

        return [];
    };

    render() {
        const {
            recovery,
            system: { ACTION_EXPLAIN, USER_NAME, CREATE_TIME, STATUS, ACTION },
            preData,
        } = this.props;
        const { show, preRecordDetail, recordDetail } = this.state;

        return (
            <div style={{ padding: '20px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
                    <div>
                        <span style={{ color: '#aaa' }}>操作类型：</span>
                        <span>{actionMap[ACTION]}</span>
                    </div>
                    <div>
                        <span style={{ color: '#aaa' }}>操作用户：</span>
                        <span>{USER_NAME}</span>
                    </div>
                    <div>
                        <span style={{ color: '#aaa' }}>操作时间：</span>
                        <span>{CREATE_TIME}</span>
                    </div>
                </div>
                <div style={{ marginBottom: '12px' }}>
                    <div>
                        <span style={{ color: '#aaa' }}>状态变化：</span>
                        <span>
                            {`${preData ? `${statusMap[preData.system.STATUS]} -> ` : ''}${statusMap[STATUS]}
                                ${ACTION === '3' ? '(数据内容变更)' : ''}`}
                        </span>
                    </div>
                </div>
                <div style={{ marginBottom: '12px' }}>
                    <div>
                        <span style={{ color: '#aaa' }}>操作理由：</span>
                        <span>{ACTION_EXPLAIN}</span>
                    </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                    <a onClick={this.handleShowDetail}>{show ? '收起>>' : '查看数据明细>>'}</a>
                </div>

                {show && (
                    <div>
                        {preRecordDetail && preRecordDetail.data && (
                            <div
                                style={{
                                    display: 'flex',
                                    maxHeight: '300px',
                                    overflow: 'auto',
                                    marginTop: '12px',
                                    border: '1px solid #ccc',
                                    borderBottom: '1px solid #ccc',
                                }}
                            >
                                <div
                                    style={{
                                        flex: 1,
                                        padding: '6px',
                                        backgroundColor: '#f5f5f5',
                                        textAlign: 'center',
                                        fontWeight: 'bold',
                                        color: '#666',
                                    }}
                                >
                                    修改前
                                </div>
                                <div
                                    style={{
                                        flex: 1,
                                        padding: '6px',
                                        backgroundColor: '#f5f5f5',
                                        textAlign: 'center',
                                        fontWeight: 'bold',
                                        color: '#666',
                                    }}
                                >
                                    修改后
                                </div>
                            </div>
                        )}
                        <div
                            style={{
                                display: 'flex',
                                maxHeight: '300px',
                                border: '1px solid #ccc',
                                borderBottom: '1px solid #ccc',
                                overflow: 'auto',
                            }}
                        >
                            {preRecordDetail && preRecordDetail.data && (
                                <div style={{ width: '50%' }}>
                                    {preRecordDetail.data.map((v, i) => {
                                        return (
                                            <div
                                                className="clearfix"
                                                style={{
                                                    borderBottom: '1px solid #ccc',
                                                    borderLeft: '1px solid #ccc',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                }}
                                            >
                                                <span
                                                    style={{
                                                        padding: '12px',
                                                        backgroundColor: '#f5f5f5',
                                                        width: '150px',
                                                        float: 'left',
                                                        textAlign: 'right',
                                                    }}
                                                    title={v.label}
                                                >
                                                    {v.label}
                                                </span>
                                                <span
                                                    style={{
                                                        padding: '12px',
                                                        display: 'block',
                                                        flex: 1,
                                                        overflow: 'hidden',
                                                        whiteSpace: 'nowrap',
                                                        textOverflow: 'ellipsis',
                                                        wordBreak: 'break-all',
                                                        color:
                                                            recordDetail &&
                                                            recordDetail.data &&
                                                            recordDetail.data[i] &&
                                                            v.stringValue !== recordDetail.data[i].stringValue
                                                                ? 'red'
                                                                : '#74767A',
                                                    }}
                                                    title={v.stringValue}
                                                >
                                                    {v.stringValue}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                            {recordDetail && recordDetail.data && (
                                <div style={{ width: preRecordDetail && preRecordDetail.data ? '50%' : '100%' }}>
                                    {recordDetail.data.map((v, i) => {
                                        return (
                                            <div
                                                key={i}
                                                className="clearfix"
                                                style={{
                                                    borderBottom: '1px solid #ccc',
                                                    borderLeft: '1px solid #ccc',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                }}
                                            >
                                                <span
                                                    style={{
                                                        padding: '12px',
                                                        backgroundColor: '#f5f5f5',
                                                        width: '150px',
                                                        textAlign: 'right',
                                                    }}
                                                    title={v.label}
                                                >
                                                    {v.label}
                                                </span>
                                                <span
                                                    style={{
                                                        flex: 1,
                                                        padding: '12px',
                                                        display: 'block',
                                                        overflow: 'hidden',
                                                        whiteSpace: 'nowrap',
                                                        textOverflow: 'ellipsis',
                                                        wordBreak: 'break-all',
                                                        color:
                                                            preRecordDetail &&
                                                            preRecordDetail.data &&
                                                            preRecordDetail.data[i] &&
                                                            v.stringValue !== preRecordDetail.data[i].stringValue
                                                                ? 'red'
                                                                : '#74767A',
                                                    }}
                                                    title={v.stringValue}
                                                >
                                                    {v.stringValue}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                        {/* { */}
                        {/*     recordDetail && */}
                        {/*     recordDetail.data && */}
                        {/*     recovery && */}
                        {/*     <div style={{ textAlign: 'right', padding: '12px' }}> */}
                        {/*         <RoleAuthControl buttonKey="meta-data-filling-recovery"> */}
                        {/*             <Button */}
                        {/*                 type="button" */}
                        {/*                 bsStyle="primary" */}
                        {/*                 onClick={() => recovery(recordDetail.system.ID)} */}
                        {/*             > */}
                        {/*                 恢复该版本 */}
                        {/*             </Button> */}
                        {/*         </RoleAuthControl> */}
                        {/*     </div> */}
                        {/* } */}
                    </div>
                )}
            </div>
        );
    }
}

export default DataAuditTimeLine;
