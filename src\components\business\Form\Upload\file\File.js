import React, { Component } from 'react';
import { message } from 'antd';
import styles from '../style/ShareuiUpload.scss';

import bmp from '../images/icon-bmp.png';
import gif from '../images/icon-gif.png';
import png from '../images/icon-png.png';
import jpg from '../images/icon-jpg.png';
import doc from '../images/icon-doc.png';
import xls from '../images/icon-xls.png';
import ppt from '../images/icon-ppt.png';
import pdf from '../images/icon-pdf.png';
import txt from '../images/icon-txt.png';
import rar from '../images/icon-rar.png';
import zip from '../images/icon-zip.png';
import z7 from '../images/icon-7z.png';
import defaultType from '../images/icon-default.png';
import loadingImg from '../images/icon-loading.gif';

import MultiClamp from '@/components/ui/MultiClamp';

const fileTypeImg = {
    bmp,
    gif,
    png,
    jpg,
    jpeg: jpg,
    doc,
    docx: doc,
    xls,
    xlsx: xls,
    ppt,
    pptx: ppt,
    pdf,
    txt,
    rar,
    zip,
    '7z': z7,
};

class File extends Component {
    render() {
        const { file, handleDelete, disabled } = this.props;
        const fileExt = file.name.substring(file.name.lastIndexOf('.') + 1);
        let fileImg;

        if (file.status === 'uploading') {
            fileImg = loadingImg;
        } else {
            const fileType = /^.*\.(.*)$/.test(file.url) ? /^.*\.(.*)$/.exec(file.url)[1] : '';

            fileImg = fileTypeImg[fileType] || defaultType;
        }

        return (
            <div className={`${styles.uploadedFile} clearfix`} onClick={(e) => e.stopPropagation()}>
                {/* gif, png, jpg, jpeg, pdf */}
                {fileExt === 'gif' || fileExt === 'png' || fileExt === 'jpg' || fileExt === 'jpeg' || fileExt === 'pdf' ?
                    <a href={`${file.url}`} target="_blank" title="文件预览"> {/*eslint-disable-line*/}
                        <img className="pull-left" src={fileImg} alt="file type" />
                    </a> :
                    <div onClick={() => message.error('该文件类型不支持预览')}> {/*eslint-disable-line*/}
                        <img className="pull-left" src={fileImg} alt="file type" />
                    </div>
                }
                {/* ng  */}
                <a href={`${file.url}?name=${file.name}`} title="文件下载"> {/*eslint-disable-line*/}
                    <div className={`${styles.info} pull-left`}>
                        <MultiClamp className={styles.name} style={{ width: disabled ? 135 : 115 }} title={file.name}>
                            {file.name}
                        </MultiClamp>
                        <p className={styles.size}>{file.size ? `${(file.size / 1024 / 1024).toFixed(3)}M` : ''}</p>
                    </div>
                </a>
                {!disabled && <span className={styles.closeBtn} onClick={() => handleDelete(file)} />}
            </div>
        );
    }
}

export default File;
