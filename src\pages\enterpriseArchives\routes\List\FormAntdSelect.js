import React from 'react';

import { FormItem } from '@share/shareui-form';

import { Select } from 'antd';

const FormAntdSelect = ({ options, field, label, noView = false, onChange, multiple = false, ...otherProps }) => {
    return (
        <FormItem field={field} label={label} noView={noView} {...otherProps}>
            {(fieldProps) => {
                return (
                    <Select
                        {...otherProps}
                        {...fieldProps}
                        mode={multiple ? 'multiple' : ''}
                        allowClear
                        notFoundContent="暂无数据"
                        placeholder={`请选择${label}`}
                        options={options}
                        onChange={(value) => {
                            fieldProps.onChange({ target: { value } });
                            if (onChange) {
                                onChange();
                            }
                        }}
                        maxTagCount="responsive"
                        maxTagPlaceholder={(omittedValues) => {
                            const labels = omittedValues.map((item) => item.label).join('、');

                            return labels;
                        }}
                    />
                );
            }}
        </FormItem>
    );
};

export default FormAntdSelect;
