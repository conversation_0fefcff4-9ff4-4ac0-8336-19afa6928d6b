import React, { Component } from 'react';
import styles from './style/TextareaSelect.scss';

class MultiInput extends Component {
    constructor(props) {
        super(props);
        this.state = {
            value: '',
            isInput: -1,
        };
        const { onRef } = this.props;

        onRef && onRef(this);
    }

    // 输入框onChange事件
    inputOnChange = (e, index) => {
        const { list, changeList, inputReg = /.*/ } = this.props;
        const listArr = [...list];
        const { value } = e.target;

        // 不为空又不符合校验规则跳过
        if (value !== '' && !inputReg.test(value)) {
            return;
        }
        // 判断为下方输入框，否则为上方输入框
        if (index === -1) {
            this.setState({ value });
        } else {
            listArr[index] = value;
            changeList(listArr);
        }
    };

    // 输入框用户键入enter键，自动保存内容
    saveOnEnterClick = (e, index) => {
        const { list, changeList, submitReg = /.*/ } = this.props;
        const listArr = [...list];
        const enterKeyCode = 13;
        const { value } = e.target;

        // 如果为空或不符合校验规则跳过
        if (value === '' || !submitReg.test(value)) {
            return;
        }
        // 判断回车事件
        if (e.keyCode === enterKeyCode) {
            // 判断下方输入框保存内容
            if (index === -1) {
                listArr.push(value.trim());
                this.setState({ value: '' });
                changeList(listArr);
            }
            // 将焦点调整为下方输入框
            this.setState({ isInput: -1 });
        }
    };

    saveInputValue = async () => {
        const { list, changeList, submitReg = /.*/ } = this.props;
        const listArr = [...list];
        const { value } = this.state;

        if (value !== '' && submitReg.test(value)) {
            listArr.push(value.trim());
            await this.setState({ value: '' });
            changeList(listArr);
        }
    };

    // 上方输入框失去焦点事件
    saveOnBlur = (e, index) => {
        const { list, changeList, submitReg = /.*/ } = this.props;
        const listArr = [...list];
        const { value } = e.target;

        // 如果不符合校验规则跳过，焦点不变
        if (!submitReg.test(value)) {
            return;
        }
        // 如果输入内容为空自动清除上方输入框
        if (value === '') {
            listArr.splice(index, 1);
            changeList(listArr);
        }
        // 将焦点调整为下方输入框
        this.setState({ isInput: -1 });
    };

    // 上方输入框点击删除按钮事件
    deleteLabel = (index) => {
        const { list, changeList } = this.props;
        const listArr = [...list];

        listArr.splice(index, 1);
        changeList(listArr);
    };

    render() {
        const { value, isInput } = this.state;
        const { list, placeholder } = this.props;

        return (
            <div className={`dsgcp ${styles.multiInput}`}>
                {list && list.length !== 0 && (
                    <ul className="dsgcpList">
                        {list.map((item, index) =>
                            isInput === index ? (
                                <li key={index} className="changeValueArea">
                                    <span
                                        id="inputLen"
                                        style={{
                                            display: 'block',
                                            height: '0',
                                            visibility: 'hidden',
                                        }}
                                    >
                                        {item}
                                    </span>
                                    <input
                                        type="text"
                                        value={item}
                                        style={{ width: '100%' }}
                                        onChange={(e) => this.inputOnChange(e, index)}
                                        onKeyUp={(e) => this.saveOnEnterClick(e, index)}
                                        onBlur={(e) => this.saveOnBlur(e, index)}
                                    />
                                </li>
                            ) : (
                                <li key={index}>
                                    <span
                                        title="单击编辑"
                                        onClick={() => {
                                            this.setState({
                                                isInput: index,
                                            });
                                        }}
                                    >
                                        {item}
                                    </span>
                                    <span title="删除" onClick={() => this.deleteLabel(index)}>
                                        X
                                    </span>
                                </li>
                            )
                        )}
                    </ul>
                )}
                <input
                    className="inputArea"
                    type="text"
                    value={value}
                    placeholder={placeholder}
                    onChange={(e) => this.inputOnChange(e, -1)}
                    onKeyUp={(e) => this.saveOnEnterClick(e, -1)}
                />
            </div>
        );
    }
}

const MultiInputWrap = (props) => {
    const { value, onChange, ...restProps } = props;
    const changeList = (list) => {
        onChange({ target: { value: list } });
    };

    return <MultiInput list={value} changeList={changeList} {...restProps} />;
};

export default MultiInputWrap;
