import React, { Component } from 'react';
// 服务接口
import * as Meta<PERSON>ieldA<PERSON> from '@/services/data/meta/MetaFieldApi';
// 工具类
import * as StringUtils from '@/components/common/common/StringUtils';
// 列表组件
import FieldComponentDetail from './FieldComponentDetail';
import MultiClamp from '@/components/ui/MultiClamp';
import { FrontPageTable as Table } from '@/components/business/Table';
// 表单组件
import { Panel, FormItem, Button } from '@share/shareui';
import { ShareForm, getComponents } from '@/components/business/Form';
import FieldComponentEdit from './FieldComponentEdit';
const { Input, Select, CheckboxGroup } = getComponents('div');

const componentTypeOptions = [
    { label: '编辑组件', value: 'editParam' },
    { label: '查询组件', value: 'queryParam' },
    { label: '列表组件', value: 'listParam' },
    { label: '详情组件', value: 'detailParam' },
];

const defaultSearchBody = {
    componentId: '',
    componentTypes: [],
    fieldCode: '',
    defaultValue: '',
};

class FieldComponentList extends Component {
    state = {
        fieldComponentList: [],
        searchForm: { ...defaultSearchBody },
        searchBody: { ...defaultSearchBody },
        showEditModal: false,
        showDetailModal: false,
        operateData: {},
    };

    componentDidMount = async () => {
        // 获取组件
        const fieldComponentList = await MetaFieldApi.fieldComponents();

        this.setState({ fieldComponentList });
    };

    handleSearch = () => {
        const { searchForm } = this.state;
        const body = StringUtils.deleteSpace(searchForm);

        this.setState({ searchBody: { ...body } });
    };

    handleReset = () => {
        this.setState({ searchForm: { ...defaultSearchBody } });
    };

    convertRuleList = (dataList) => {
        return dataList.reduce((result, item) => {
            const editParam = {
                ...item.editParam,
                field: item,
                fieldCode: item.fieldCode,
                componentType: 'editParam',
                id: `${item.fieldCode}_editParam`,
            };
            const queryParam = {
                ...item.queryParam,
                field: item,
                fieldCode: item.fieldCode,
                componentType: 'queryParam',
                id: `${item.fieldCode}_queryParam`,
            };
            const listParam = {
                ...item.listParam,
                field: item,
                fieldCode: item.fieldCode,
                componentType: 'listParam',
                id: `${item.fieldCode}_listParam`,
            };
            const detailParam = {
                ...item.detailParam,
                field: item,
                fieldCode: item.fieldCode,
                componentType: 'detailParam',
                id: `${item.fieldCode}_detailParam`,
            };

            return [...result, editParam, queryParam, listParam, detailParam];
        }, []);
    };

    filterDataList = (dataList) => {
        const { componentId, componentTypes, fieldCode, defaultValue } = this.state.searchBody;
        let filterList = dataList;

        if (componentId) {
            filterList = filterList.filter((item) => item.componentId === componentId);
        }
        if (Array.isArray(componentTypes) && componentTypes.length > 0) {
            filterList = filterList.filter((item) => componentTypes.includes(item.componentType));
        }
        if (fieldCode) {
            filterList = filterList.filter((item) => item.field.fieldCode === fieldCode);
        }
        if (defaultValue) {
            filterList = filterList.filter((item) => item.defaultValue && item.defaultValue.includes(defaultValue));
        }

        return filterList;
    };

    saveComponent = async (component) => {
        const { metaFieldList, refreshDataFn } = this.props;
        const config = metaFieldList.find((item) => item.fieldCode === component.fieldCode);

        config[component.componentType] = component;
        await MetaFieldApi.saveTableMeta([config]);
        refreshDataFn && refreshDataFn();
        this.setState({ showEditModal: false });
    };

    render() {
        const { metaFieldList } = this.props;
        const { fieldComponentList, searchForm, showEditModal, showDetailModal, operateData } = this.state;
        const componentOptions = fieldComponentList.map((item) => ({ label: item.componentName, value: item.componentId }));
        const fieldCodeOptions = metaFieldList.map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));
        const filterList = this.filterDataList(this.convertRuleList(metaFieldList));

        // 列表展示内容
        const columns = [
            {
                title: '组件名称',
                key: 'componentId',
                dataIndex: 'componentId',
                width: '20%',
                render: (rowData) => {
                    const value = (componentOptions.find((item) => item.value === rowData) || {}).label;

                    return <MultiClamp title={value}>{value}</MultiClamp>;
                },
            },
            {
                title: '组件类型',
                key: 'componentType',
                dataIndex: 'componentType',
                width: '20%',
                render: (rowData) => componentTypeOptions.find((item) => item.value === rowData).label,
            },
            {
                title: '组件字段',
                key: 'fieldCode',
                width: '30%',
                render: (rowData) => {
                    const value = `${rowData.field.fieldCode}（${rowData.field.showLabel}）`;

                    return <MultiClamp title={value}>{value}</MultiClamp>;
                },
            },
            {
                title: '默认值',
                key: 'defaultValue',
                dataIndex: 'defaultValue',
                render: (rowData) => <MultiClamp title={rowData}>{rowData}</MultiClamp>,
            },
            {
                title: '操作',
                key: 'operate',
                width: '8%',
                render: (rowData) => {
                    return (
                        <div className="tableBtn">
                            <a
                                href="javascript:void(0)"
                                onClick={() => {
                                    const { id, field, ...component } = rowData;

                                    this.setState({ operateData: component, showDetailModal: true });
                                }}
                            >
                                查看
                            </a>
                            <a
                                href="javascript:void(0)"
                                onClick={() => {
                                    const { id, field, ...component } = rowData;

                                    this.setState({ operateData: component, showEditModal: true });
                                }}
                            >
                                修改
                            </a>
                        </div>
                    );
                },
            },
        ];

        return (
            <div>
                <Panel>
                    <Panel.Body full>
                        <ShareForm formData={searchForm} onChange={(data, callback) => this.setState({ searchForm: data }, callback)}>
                            <Select label="组件名称" field="componentId" options={componentOptions} col={10} labelCol={3} />
                            <Select label="组件字段" field="fieldCode" options={fieldCodeOptions} col={10} labelCol={3} />
                            <Input label="默认值" field="defaultValue" col={10} labelCol={3} />
                            <CheckboxGroup label="组件类型" field="componentTypes" options={componentTypeOptions} col={15} labelCol={3} />
                            <FormItem className="btn-item clearfix pull-right">
                                <Button type="submit" bsStyle="primary" onClick={this.handleSearch}>
                                    查询
                                </Button>
                                <Button type="reset" onClick={this.handleReset}>
                                    重置
                                </Button>
                            </FormItem>
                        </ShareForm>
                    </Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Head title="组件信息" />
                    <Panel.Body full>
                        <Table rowKey="id" columns={columns} dataSource={filterList} />
                    </Panel.Body>
                </Panel>
                {/* 规则编辑 */}
                <FieldComponentEdit
                    data={{
                        detail: operateData,
                        fieldComponentList,
                        componentTypeOptions,
                        metaFieldList,
                    }}
                    show={showEditModal}
                    successFn={this.saveComponent}
                    cancelFn={() => this.setState({ showEditModal: false })}
                />
                {/* /!* 规则详情*!/ */}
                <FieldComponentDetail
                    data={{
                        detail: operateData,
                        fieldComponentList,
                    }}
                    show={showDetailModal}
                    cancelFn={() => this.setState({ showDetailModal: false })}
                />
            </div>
        );
    }
}

export default FieldComponentList;
