const provinces = {
    11: '北京',
    12: '天津',
    13: '河北',
    14: '山西',
    15: '内蒙古',
    21: '辽宁',
    22: '吉林',
    23: '黑龙江',
    31: '上海',
    32: '江苏',
    33: '浙江',
    34: '安徽',
    35: '福建',
    36: '江西',
    37: '山东',
    41: '河南',
    42: '湖北',
    43: '湖南',
    44: '广东',
    45: '广西',
    46: '海南',
    50: '重庆',
    51: '四川',
    52: '贵州',
    53: '云南',
    54: '西藏',
    61: '陕西',
    62: '甘肃',
    63: '青海',
    64: '宁夏',
    65: '新疆',
    71: '台湾',
    81: '香港',
    82: '澳门',
    91: '国外',
};

// 检查号码是否符合规范，包括长度，类型
// 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
// todo：此正则表达式的问题是：1.省份可能会有问题 2.日期会有问题 3.需要对校验位进行校验
const isCardNo = (card) => {
    if (card.length !== 18 && card.length !== 15) {
        return false;
    }

    return /(^\d{6}(19|20)?\d{2}(0[1-9]|1[0-2])([0][1-9]|[1-2][0-9]|3[0-1])\d{3}(\d|X|x)?$)/.test(card);
};
// 只检验15位和18位身份证,其他不校验
const cardNo = (card) => {
    if (card.length === 18 || card.length === 15) {
        return /(^\d{6}(19|20)?\d{2}(0[1-9]|1[0-2])([0][1-9]|[1-2][0-9]|3[0-1])\d{3}(\d|X|x)?$)/.test(card);
    }

    return false;
};
// 取身份证前两位,校验省份
const checkProvince = (card) => typeof provinces[card.substr(0, 2)] !== 'undefined';

// 校验位的检测
const checkParityBit = function (card) {
    // 15位转18位
    card = changeFifteenToEighteen(card);
    // 最后一位的x转成X
    card = card.replace('x', 'X');
    if (card.length == 18) {
        const arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
        const arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
        let cardTemp = 0;

        for (let i = 0; i < 17; i++) {
            cardTemp += card.substr(i, 1) * arrInt[i];
        }
        const valNum = arrCh[cardTemp % 11];

        if (valNum == card.substr(17, 1)) {
            return true;
        }
    }

    return false;
};

// 15位转18位身份证号
const changeFifteenToEighteen = (idcard) => {
    let card = idcard;

    if (card.length == 15) {
        const arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
        const arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
        let cardTemp = 0;

        card = `${card.substr(0, 6)}19${card.substr(6, card.length - 6)}`;
        for (let i = 0; i < 17; i++) {
            cardTemp += card.substr(i, 1) * arrInt[i];
        }
        card += arrCh[cardTemp % 11];

        return card;
    }

    return card;
};

const getSexFromIdCard = (card) => {
    if (!card) {
        return '';
    }
    const xbBit = card.length === 18 ? parseInt(card.substring(16, 17)) : parseInt(card.substring(14, 15));

    return xbBit % 2 === 0 ? '2' : '1';
};

const getBirthDateFromIdCard = (card) => {
    if (card && card.length === 18) {
        return `${card.substring(6, 10)}-${card.substring(10, 12)}-${card.substring(12, 14)}`;
    }
    if (card && card.length === 15) {
        return `19${card.substring(6, 8)}-${card.substring(8, 10)}-${card.substring(10, 12)}`;
    }

    return '';
};

/**
 * 身份证校验
 * @param card
 * @returns {boolean}
 */
const checkCard = (card) => {
    // undefined||null||是否为空||校验长度，类型||检查省份||校验生日||检验位的检测
    if (typeof card === 'undefined' || card == null || card === '' || !isCardNo(card) || !checkProvince(card) || !checkParityBit(card)) {
        return false;
    }

    return true;
};

// 如果身份证号码为15或者18位  要对身份证号码进行校验，其他不校验

const checkIdCard = (card) => {
    if (typeof card === 'undefined' || card == null || card === '' || !cardNo(card)) {
        return false;
    }

    return true;
};

// 身份证掩码
const idCardMask = (idCard) => {
    if (!idCard) {
        return idCard;
    }
    if (idCard.length > 6) {
        if (idCard.length === 18) {
            return idCard.replace(/(\d{6})\d{8}(\w{4})/, '$1********$2');
        }
    } else {
        return idCard.length === 15 ? idCard.replace(/(\d{6})\d{5}(\w{4})/, '$1*****$2') : `${idCard.substr(0, 6)}*******`;
    }
};

// 姓名掩码
const nameMask = (name) => {
    if (name !== '') {
        if (name.length === 2) {
            return `${name.substring(0, 1)}*`;
        }

        return name.replace(/^(.).+(.)$/g, '$1*$2');
    }
};

module.exports = {
    checkCard,
    checkIdCard,
    changeFifteenToEighteen,
    getBirthDateFromIdCard,
    getSexFromIdCard,
    nameMask,
    idCardMask,
};
