import React, { Component } from 'react';
// 服务接口
import * as MetaTableApi from '@/services/data/meta/MetaTableApi';
// 列表组件
import MultiClamp from '@/components/ui/MultiClamp';
import { FrontPageTable as Table } from '@/components/business/Table';
// 表单组件
import { Panel, Button, Icon } from '@share/shareui';
import DatabaseTableGenEdit from './DatabaseTableGenEdit';
import DatabaseTableGenCopy from './DatabaseTableGenCopy';

const columnTypeOptions = [
    { label: '文本', value: 'text' },
    { label: '整数', value: 'integer' },
    { label: '小数', value: 'decimal' },
    { label: '日期', value: 'date' },
    { label: '时间', value: 'dateTime' },
];

class DatabaseTableGenList extends Component {
    state = {
        showEditModal: false,
        operateData: null,
        showCopyModal: false,
    };

    copy = async (data) => {
        const { onChange, systemFields } = this.props;
        const fieldList = await MetaTableApi.getDatabaseField(data);
        const list = fieldList
            .filter((item) => !systemFields.includes(item.columnName))
            .map((item, index) => {
                const field = {
                    id: index + 1,
                    columnName: item.columnName,
                    columnComment: item.columnComment,
                };

                if (item.integer) {
                    field.columnType = 'integer';
                    field.columnSize = [item.columnSize];
                } else if (item.decimal) {
                    field.columnType = 'decimal';
                    field.columnSize = [item.columnSize, item.decimalSize];
                } else if (item.date) {
                    field.columnType = 'date';
                    field.columnSize = [];
                } else if (item.dateTime) {
                    field.columnType = 'dateTime';
                    field.columnSize = [];
                } else {
                    field.columnType = 'text';
                    field.columnSize = [item.columnSize];
                }

                return field;
            });

        onChange && onChange({ target: { value: list } });
        this.setState({ showCopyModal: false });
    };

    edit = (data) => {
        const { value, onChange } = this.props;
        let list = value;

        if (data.id === -1) {
            data.id = list.length + 1;
            list.push(data);
        } else {
            list = list.map((item) => (item.id === data.id ? data : item));
        }
        onChange && onChange({ target: { value: list } });
        this.setState({ showEditModal: false, operateData: null });
    };

    render() {
        const { value: list, onChange, existTables, systemFields } = this.props;
        const { showEditModal, operateData, showCopyModal } = this.state;

        // 列表展示内容
        const columns = [
            {
                title: '字段代码',
                dataIndex: 'columnName',
                width: 200,
                render: (value) => {
                    return <MultiClamp title={value}>{value}</MultiClamp>;
                },
            },
            {
                title: '字段类型',
                dataIndex: 'columnType',
                width: 80,
                render: (rowData) => columnTypeOptions.find((item) => item.value === rowData).label,
            },
            {
                title: '字段长度',
                dataIndex: 'columnSize',
                width: 100,
                render: (value) => {
                    const show = value.toString();

                    return <MultiClamp title={show}>{show}</MultiClamp>;
                },
            },
            {
                title: '说明描述',
                key: 'columnComment',
                dataIndex: 'columnComment',
                render: (value) => <MultiClamp title={value}>{value}</MultiClamp>,
            },
            {
                title: '操作',
                key: 'operate',
                width: 100,
                render: (rowData) => {
                    return (
                        <div className="tableBtn">
                            <a
                                href="javascript:void(0)"
                                onClick={() => {
                                    this.setState({ operateData: rowData, showEditModal: true });
                                }}
                            >
                                修改
                            </a>
                            <a
                                href="javascript:void(0)"
                                onClick={() => {
                                    const newList = list.filter((item) => item.id !== rowData.id);

                                    onChange && onChange({ target: { value: newList } });
                                }}
                            >
                                删除
                            </a>
                        </div>
                    );
                },
            },
        ];

        return (
            <div>
                <Panel>
                    <Panel.Head>
                        <Panel.HeadRight>
                            <ul className="ui-list-horizontal">
                                <li>
                                    <Button
                                        type="button"
                                        className="btn-xs"
                                        border={false}
                                        onClick={() => {
                                            this.setState({
                                                showCopyModal: true,
                                            });
                                        }}
                                    >
                                        <Icon className="si si-com_copy2" on="true" />
                                        拷贝
                                    </Button>
                                </li>
                                <li>
                                    <Button
                                        type="button"
                                        className="btn-xs"
                                        border={false}
                                        onClick={() => {
                                            this.setState({
                                                operateData: {
                                                    id: -1,
                                                },
                                                showEditModal: true,
                                            });
                                        }}
                                    >
                                        <Icon className="si si si-com_plus" on="true" />
                                        新增
                                    </Button>
                                </li>
                            </ul>
                        </Panel.HeadRight>
                    </Panel.Head>
                    <Panel.Body full>
                        <Table rowKey="id" columns={columns} dataSource={list} />
                    </Panel.Body>
                </Panel>
                {/* 拷贝 */}
                <DatabaseTableGenCopy
                    data={{
                        existTables,
                    }}
                    show={showCopyModal}
                    successFn={this.copy}
                    cancelFn={() => this.setState({ showCopyModal: false })}
                />
                {/* 编辑 */}
                <DatabaseTableGenEdit
                    data={{
                        columnTypeOptions,
                        column: operateData,
                        list,
                        systemFields,
                    }}
                    show={showEditModal}
                    successFn={this.edit}
                    cancelFn={() => this.setState({ showEditModal: false })}
                />
            </div>
        );
    }
}

export default DatabaseTableGenList;
