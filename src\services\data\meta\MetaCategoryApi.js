import { get, postJson } from '@/components/common/network/Network';

// 获取列表
export const allList = () => {
    return get('/edp-front/meta/category/list/all.do');
};

// 重建节点
export const rebuild = (body) => {
    return postJson('/edp-front/meta/category/rebuild.do', body);
};

// 保存
export const add = (params) => {
    return postJson('/edp-front/meta/category/add.do', params);
};

// 保存
export const update = (params) => {
    return postJson('/edp-front/meta/category/update.do', params);
};

// 重建节点
export const sort = (body) => {
    return postJson('/edp-front/meta/category/sort.do', body);
};

// 状态切换
export const switchStatus = (id) => {
    return get(`/edp-front/meta/category/status/switch/${id}.do`);
};

// 获取元数据类别配置
export const metaConfig = (id, objectType = '2', type = 'DATA') => {
    return get(`/edp-front/meta/category/meta/config/${id}/${objectType}.do`, { type });
};

// 获取列表
export const useList = () => {
    return get('/edp-front/meta/category/list/use.do');
};

// 二级分类树形结构接口
export const metaCategoryTreeCatalogTotal = (objectType) => {
    return get(`/edp-front/meta/catalog/category/tree/catalog/total/${objectType}.do`);
};
