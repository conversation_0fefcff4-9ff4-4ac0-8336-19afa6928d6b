import React, { Component } from 'react';
// 接口
import * as DataAuditApi from '@/services/data/data/DataAuditApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 组件
import QueryList from '@/components/business/metadata/QueryList';
import { Panel } from '@share/shareui';

class DataAuditTable extends Component {
    // 数据列表请求接口
    buildDataListApi = (searchBody) => {
        const { categoryCode } = this.props;

        return DataAuditApi.listByCategory(categoryCode, '2', searchBody);
    };

    // 列表展示列
    extendTailColumns = () => {
        const { history, categoryCode } = this.props;

        return [
            {
                title: '操作',
                width: 70,
                fixed: 'right',
                key: 'operate',
                dataIndex: 'system',
                render: (rowData) => (
                    <div className="tableBtn">
                        <a
                            onClick={() =>
                                history.push(
                                    `/DataAuditDetail/${categoryCode}/${rowData.__XDR_LB || rowData.XDR_LB || '2'}/${
                                        rowData.YW_ID
                                    }?ACTION_TIME=${rowData.CREATE_TIME}`
                                )
                            }
                        >
                            详情
                        </a>
                    </div>
                ),
            },
        ];
    };

    render() {
        const { categoryCode, categoryCodeList, metaConfigList, body, ...otherProps } = this.props;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);
        const listConfig = MetaConfigUtils.filterListConfig(metaConfigList);

        return (
            <Panel>
                <Panel.Head title="数据审计" />
                <Panel.Body full>
                    <QueryList
                        namespace="DataAuditTable"
                        service={{
                            api: this.buildDataListApi,
                            body,
                        }}
                        rowKey={(data) => data.system[primaryKey]}
                        metadataConfigList={listConfig}
                        extendTailColumns={this.extendTailColumns()}
                        pagination={{ detectPage: true, showTotal: true }}
                        {...otherProps}
                    />
                </Panel.Body>
            </Panel>
        );
    }
}

export default DataAuditTable;
