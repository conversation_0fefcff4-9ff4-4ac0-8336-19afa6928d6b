import React, { Component } from 'react';

import RCTree, { TreeNode as RCTreeNode } from 'rc-tree';
import 'rc-tree/assets/index.css';

// styles
import './style/index.scss';

class TagTree extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        const { dataSource = [], onAdd, onEdit, onDel, needEdit = false } = this.props;

        const editGroup = (item) => {
            if (needEdit) {
                return (
                    <ul className="title_operate clearfix">
                        {item.root && <li className="operate_icon operate_add" onClick={() => onAdd(item)} />}
                        <li className="operate_icon operate_edit" onClick={() => onEdit(item)} />
                        <li className="operate_icon operate_del" onClick={() => onDel(item.key)} />
                    </ul>
                );
            }

            return false;
        };

        const loop = (data = []) => {
            return data.map((item) => {
                if (item.root) {
                    return (
                        <RCTreeNode
                            key={item.key}
                            title={
                                <span className="title_pane">
                                    <span className="title_content">{item.title}</span>

                                    {editGroup(item)}
                                </span>
                            }
                        >
                            {loop(item.children)}
                        </RCTreeNode>
                    );
                }

                return (
                    <RCTreeNode
                        key={item.key}
                        title={
                            <span className="title_pane">
                                <span className="title_content">{item.title}</span>
                                {editGroup(item)}
                            </span>
                        }
                    />
                );
            });
        };
        const treeNodes = loop(dataSource);

        return (
            <div className="share-tree_container">
                <RCTree {...this.props}>{treeNodes}</RCTree>
            </div>
        );
    }
}

export default TagTree;
