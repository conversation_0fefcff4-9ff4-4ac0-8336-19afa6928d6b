import { postJson, get, vformGet, vformPost } from '@/components/common/network/Network';

// 数据列表(类别)
export const listByCategory = (categoryId, objectType = '2', param) => {
    return postJson(`/edp-front/data/trc/list/category/${categoryId}.do?objectType=${objectType}`, param);
};

// 详情列表(类别)
export const detailsByCategory = (categoryId, objectType = '2', param) => {
    return postJson(`/edp-front/data/trc/detail/category/${categoryId}.do?objectType=${objectType}`, param);
};

// 数据详情(类别)
export const detailByCategory = (categoryId, objectType = '2', recordId) => {
    return get(`/edp-front/data/trc/detail/category/${categoryId}/${recordId}.do?objectType=${objectType}`);
};

// 数据编辑详情(类别)
export const editDetailByCategory = (categoryId, objectType = '2', recordId) => {
    return get(`/edp-front/data/trc/edit_detail/category/${categoryId}/${recordId}.do?objectType=${objectType}`);
};

// 数据excel(类别)
export const exportByCategory = (categoryId, objectType = '2', param) => {
    return vformPost(`/edp-front/data/trc/export/category/${categoryId}.do`, { objectType, param });
};

// 批量变更状态（类别）
export const statusUpdateFromPage = (categoryId, objectType = '2', param) => {
    return postJson(`/edp-front/data/trc/status/category/${categoryId}.do?objectType=${objectType}`, param);
};

// 溯源文件
export const exportTrcFile = (traceId) => {
    return vformGet('/edp-front/data/trc/file/download.do', { traceId });
};

// 溯源数据库
export const sourceDetail = (tableName, recordId) => {
    return get(`/edp-front/data/trc/database/${tableName}/${recordId}.do`);
};
