import React from 'react';
import { Button } from 'antd';
import { FormItem, Select } from '@share/shareui-form';
import { Icon } from '@share/shareui';
import classNames from 'classnames';
import styles from './ArrayFormItem.scss';

const ChildrenItem = ({ index, children, onChange, value, restProps = {}, item, defaultChildrenData }) => {
    return (
        <div className={styles.ruleList} data-index={index}>
            {children &&
                children({
                    ...restProps,
                    index,
                })}
            <div className={styles['ruleList-btns']}>
                {/* 新增 */}
                <Icon
                    className={`si si-com_increase ${styles.add}`}
                    onClick={() =>
                        onChange({
                            target: {
                                value: [
                                    ...(value.slice(0, index + 1) || []),
                                    defaultChildrenData ? { ...defaultChildrenData } : '',
                                    ...(value.slice([index + 1]) || []),
                                ],
                            },
                        })
                    }
                />
                {/* 删除 */}
                <Icon
                    className={`si si-com_minus-circle ${styles.remove}`}
                    onClick={() => onChange({ target: { value: value.filter((one, i) => i !== index) } })}
                />
                {/* 下移 */}
                {index !== value.length - 1 && (
                    <Icon
                        className={`si si-app_xy ${styles.down}`}
                        onClick={() =>
                            onChange({
                                target: {
                                    value: [
                                        ...(value.slice(0, index) || []),
                                        value[index + 1],
                                        value[index],
                                        ...(value.slice([index + 2]) || []),
                                    ],
                                },
                            })
                        }
                    />
                )}
                {/* 上移 */}
                {index !== 0 && (
                    <Icon
                        className={`si si-app_sy ${styles.up}`}
                        onClick={() =>
                            onChange({
                                target: {
                                    value: [
                                        ...(value.slice(0, index - 1) || []),
                                        value[index],
                                        value[index - 1],
                                        ...(value.slice([index + 1]) || []),
                                    ],
                                },
                            })
                        }
                    />
                )}
            </div>
        </div>
    );
};
const ArrayFormItem = (props) => {
    const { value = [], onChange, defaultChildrenData } = props;

    return (
        <div className={styles.arrayFormItem}>
            {/* <div className={styles['arrayFormItem-group']}>
                <Select
                    field={formKey ? `${formKey}.combinationLogic` : 'combinationLogic'}
                    noView
                    rule="required"
                    className={styles['arrayFormItem-group_select']}
                    options={[
                        {
                            label: '满足其一',
                            value: 'or',
                        },
                        {
                            label: '同时满足',
                            value: 'and',
                        },
                    ]}
                />
                <Button
                    type="primary"
                    className={styles['arrayFormItem-group_button']}
                    onClick={() => {
                        const $value = defaultChildrenData ? [...value, { ...defaultChildrenData }] : [...value];
                        onChange({
                            target: {
                                // value: [...value, defaultChildrenData ? { ...defaultChildrenData } : ''],
                                value: $value,
                            },
                        });
                    }}
                >
                    <Icon className="si si-com_plusthin" /> 添加规则
                </Button>
                <Button
                    type="primary"
                    className={styles['arrayFormItem-group_button']}
                    onClick={() => {
                        // event.handleAddCombination(formKey ? `${formKey}.combination` : 'combination');
                        onChange({
                            target: {
                                // value: [...value, defaultChildrenData ? { ...defaultChildrenData } : ''],
                                value: $value,
                            },
                        });
                    }}
                >
                    <Icon className="si si-com_plusthin" />
                    添加组合
                </Button>
            </div> */}
            {Array.isArray(value) &&
                value.map((item, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <ChildrenItem {...props} key={index} index={index} item={item} />
                ))}
            {(!Array.isArray(value) || value.length === 0) && (
                <div key="add" style={{ marginBottom: '8px' }}>
                    <Button
                        type="primary"
                        onClick={() =>
                            onChange({
                                target: {
                                    value: [...value, defaultChildrenData ? { ...defaultChildrenData } : ''],
                                },
                            })
                        }
                    >
                        <Icon className={`si si-com_increase `} />
                    </Button>
                </div>
            )}
        </div>
    );
};
const ArrayFormItemWrap = (props) => {
    return (
        <FormItem {...props}>
            {(innerProps) => {
                return <ArrayFormItem {...props} {...innerProps} />;
            }}
        </FormItem>
    );
};
export default ArrayFormItemWrap;
