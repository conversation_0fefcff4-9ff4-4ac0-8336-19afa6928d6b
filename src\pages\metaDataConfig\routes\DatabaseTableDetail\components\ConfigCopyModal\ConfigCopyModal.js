import React, { Component } from 'react';
import { But<PERSON>, Modal } from '@share/shareui';
import { TableForm, Row, Select, CheckboxGroup, FormItem } from '@share/shareui-form';
import * as formRule from '@/utils/formRule';
import CheckboxGroupCustom from '../CheckboxGroupCustom';
import useLogic from './logic';
import styles from './ConfigCopyModal.scss';

const ConfigCopyModal = (props) => {
    const { show } = props;
    const { state, form, handleCancel, onTableNameChange, formData, submit } = useLogic(props);
    const { dataTableOptions, dataFieldOptions } = state;
    const { type, tableName } = formData;
    const fieldCodeRequired = type.length > 1 || (type.length === 1 && !type.includes('identityConfig'));

    return (
        <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={handleCancel} bsSize="large" backdrop="static">
            <Modal.Header closeButton>拷贝配置</Modal.Header>
            <Modal.Body>
                <TableForm pageType="addPage" formState={form}>
                    <Row>
                        <CheckboxGroup
                            label="拷贝范围"
                            field="type"
                            options={[
                                { label: '通用配置', value: 'commonConfig' },
                                { label: 'ES配置', value: 'esConfig' },
                                { label: '组件配置', value: 'componentConfig' },
                                { label: '规则配置', value: 'ruleConfig' },
                                { label: '重复配置', value: 'identityConfig' },
                                { label: '展现配置', value: 'applicationConfig' },
                            ]}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                    <Row>
                        <Select
                            label="来源表名"
                            field="tableName"
                            options={dataTableOptions}
                            onChange={onTableNameChange}
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                    {tableName && (
                        <Row>
                            <FormItem label="拷贝字段" field="fieldCode">
                                {(fieldProps) => {
                                    return (
                                        <CheckboxGroupCustom
                                            {...fieldProps}
                                            label="拷贝字段"
                                            field="fieldCode"
                                            options={dataFieldOptions}
                                            startAllChecked
                                            startReverseChecked
                                            rule={fieldCodeRequired ? [formRule.checkRequiredNotBlank()] : []}
                                            required={fieldCodeRequired}
                                        />
                                    );
                                }}
                            </FormItem>
                        </Row>
                    )}
                </TableForm>
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={handleCancel}>关闭</Button>
                <Button bsStyle="primary" onClick={submit}>
                    应用
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default ConfigCopyModal;
