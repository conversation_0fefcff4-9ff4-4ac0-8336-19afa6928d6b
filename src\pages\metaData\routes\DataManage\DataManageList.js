/*
 *@(#) DataManageList.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-28
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React, { Fragment } from 'react';
// 样式
import styles from '@/pages/metaData/styles/creditData.scss';
import noData from '@/assets/images/image-noData.png';
// 服务接口
import * as MetaCategoryApi from '@/services/data/meta/MetaCategoryApi';
// 组件
import DataQuery from '@/components/credit_data/DataQuery';
import DataManageTable from '@/components/credit_data/List/DataManageTable';
import { Panel } from '@share/shareui';
import { getComponents } from '@/components/business/Form';

const { CheckboxGroup, RangeTime } = getComponents('div');

const statusOptions = [
    { label: '公示中', value: '1' },
    { label: '已下线', value: '2' },
    { label: '已归档', value: '3' },
    { label: '已删除', value: '4' },
    { label: '待修正', value: '5' },
];

class DataManageList extends DataQuery {
    // 初始化默认搜索条件
    initDefaultSearchBody() {
        return {
            CATEGORY_CODE: '',
            STATUS: [],
            UPDATE_TIME: { start: '', end: '' },
        };
    }

    // 初始化元数据配置Api
    initMetaConfigApi = (categoryCode, objectType) => {
        return MetaCategoryApi.metaConfig(categoryCode, objectType);
    };

    render() {
        const { history } = this.props;
        const { searchForm, listConfig, metaConfigList, selectedRecords, categoryCodeList } = this.state;
        const { CATEGORY_CODE: categoryCode } = searchForm;
        const searchBody = this.handleSearchBody();

        return (
            <div>
                <Panel>
                    <Panel.Body full>
                        {this.renderQueryCondition(
                            'CreditDataManageListForm',
                            <Fragment>
                                {/* <CheckboxGroup */}
                                {/*     label="数据状态" field="STATUS" */}
                                {/*     options={statusOptions} */}
                                {/*     col={13} labelCol={3} */}
                                {/* /> */}
                                <RangeTime label="更新时间" field="UPDATE_TIME" col={13} labelCol={3} />
                            </Fragment>
                        )}
                    </Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Body full>
                        {listConfig.length === 0 ? (
                            <div className={styles.noData}>
                                <img src={noData} alt="no data" />
                                <p>请选择信息类别</p>
                            </div>
                        ) : (
                            <Panel>
                                <Panel.Body full>
                                    <DataManageTable
                                        history={history}
                                        categoryCode={categoryCode}
                                        categoryCodeList={categoryCodeList}
                                        metaConfigList={metaConfigList}
                                        body={searchBody}
                                        selectedRecords={selectedRecords}
                                        onSelectedRecordsChange={(records) => this.setState({ selectedRecords: records })}
                                    />
                                    {/* <div style={{ fontSize: '12px', color: 'orange', display: 'flex', padding: '0 0 12px 12px' }}> */}
                                    {/*     <div>温馨提示：</div> */}
                                    {/*     <div style={{ color: '#666' }}> */}
                                    {/*         1、“公示中”状态，表示可在互联网及本平台中各子系统查询； */}
                                    {/*         <br /> */}
                                    {/*         2、“已下线”状态，表示无法在互联网查询，但平台中政务网系统可查询； */}
                                    {/*         <br /> */}
                                    {/*         3、“已归档”状态，表示无法在本平台中各子系统（含互联网）查询； */}
                                    {/*         <br /> */}
                                    {/*         4、“删除”功能，系统将不在本平台中留痕，仅供错误数据场景操作； */}
                                    {/*         <br /> */}
                                    {/*         5、“待修正”状态，表示上报至上一级平台后，被上级平台校验为不合格的数据，一般由上级平台临时调整校验规则导致，需修改后重新上报。 */}
                                    {/*     </div> */}
                                    {/* </div> */}
                                </Panel.Body>
                            </Panel>
                        )}
                    </Panel.Body>
                </Panel>
            </div>
        );
    }
}

export default DataManageList;
