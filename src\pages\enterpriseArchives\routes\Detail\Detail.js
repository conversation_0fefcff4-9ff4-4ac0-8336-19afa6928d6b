/*
 *@(#) Detail.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-09
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React from 'react';
import { Menu } from 'antd';
import { useHistory } from 'react-router';
import { timeToStr } from '@/utils/format';
import EnterpriseDevelopment from '@/pages/enterpriseArchives/routes/Detail/components/EnterpriseDevelopment';
import s from './Detail.scss';
import useLogic, { detailAuthExpressionEnum, DetailContext, filterMenus, useAuthExpressionHook } from './hooks';
import Top from './components/Top';
import BaseInfo from './components/BaseInfo';
import { PANELKEY, menus, basicMenu } from './config';
import BusinessInformation from './components/BusinessInformation';
import SupportingEnterprisesDetail from './components/SupportingEnterprisesDetail';
import TagTitle from './components/TagTitle';
import BusinessInfo from "@/pages/enterpriseArchives/routes/Detail/components/BusinessInformation/components/BusinessInfo";
import ListBlock from "@/pages/enterpriseArchives/routes/Detail/components/ListBlock";
import AnnualReport from "@/pages/enterpriseArchives/routes/Detail/components/AnnualReport";
import OperateInfo from "@/pages/enterpriseArchives/routes/Detail/components/OperateInfo";

const Detail = () => {
    const PANELKEYENUM = Object.keys(PANELKEY);
    const { formState, menuClick, detail, menuStatus, setMenuStatus, menuPanel, refresh } = useLogic();
    const {
        shareHolderInfoList = [],
        personnelInfoList = [],
        outwardInvestmentList = [],
        contactInfoList = [],
        branchOfficeList = [],
        blackListList = [], // 黑名单
        allowPunishList = [], // 行政处罚
        operateInfoList = [], // 经营信息
        creditList = [],
        operateInfo = {}, // 经营信息
        zzmc, // 企业名称
        tyshxydm, // 统一社会信用代码
        qyJj, // 企业简介
        mainProductsList = [], // 主要产品列表
    } = detail || {};
    const { authExpressionMap } = useAuthExpressionHook(
        {
            expressions: detailAuthExpressionEnum,
            isFocus: detail.isFocus,
            zcdzgsxzqhdm: detail.zcdzgsxzqhdm,
        },
        true
    );
    const newMenus = filterMenus(menus, authExpressionMap);

    return (
        // eslint-disable-next-line react/jsx-no-constructed-context-values
        <DetailContext.Provider value={{ refresh }}>
            <div className={s.detailPage}>
                <div className={s.detailPageTop}>
                    <Top detail={detail} authExpressionMap={authExpressionMap} />
                </div>
                <div className={s.division} />
                <div className={s.detailPageContent}>
                    <div className={s.detailPageLeft} style={{ left: menuStatus ? 0 : '-200px' }} id="menu">
                        <div className={s.menu}>
                            <Menu mode="inline" defaultOpenKeys={PANELKEYENUM} items={[...basicMenu, ...newMenus]} onClick={menuClick} />
                        </div>
                        <div className={s.trigger} onClick={() => setMenuStatus(!menuStatus)}>
                            {!menuStatus && <i className="si si-com_sortright" />}
                            {menuStatus && <i className="si si-com_sortleft" />}
                        </div>
                    </div>
                    <div className={s.detailPageRight} style={{ marginLeft: menuStatus ? '200px' : 0 }}>
                        {/* 基础信息 */}
                        <TagTitle id="contentTop" />
                        {menuPanel === PANELKEY.BASIC && (
                            <BaseInfo
                                formState={formState}
                                data={{
                                    shareHolderInfoList,
                                    personnelInfoList,
                                    outwardInvestmentList,
                                    contactInfoList,
                                    branchOfficeList,
                                    tyshxydm,
                                    zzmc,
                                }}
                            />
                        )}
                        {/* 企业经营 */}
                        {menuPanel === PANELKEY.BUSINESS_STAT &&
                            <OperateInfo formState={formState} tyshxydm={tyshxydm} />
                        }
                        {/* 企业年报 */}
                        {menuPanel === PANELKEY.ANNUALREPORT &&
                            <AnnualReport id={tyshxydm} />
                        }
                        {/* 商标信息 */}
                        {
                            menuPanel === PANELKEY.TM &&
                            <ListBlock
                                title="商标信息"
                                // 838
                                url="/enterprise/ipr/tm.do"
                                key="sub-sbxx"
                                params={{ creditCode: tyshxydm }}
                                columns={[
                                    { label: '商标', field: 'tmPic', render: (val) => <img src={val} alt="tmPic" style={{ maxWidth: '200px' }} /> },
                                    { label: '商标名称', field: 'tmName' },
                                    { label: '申请日期', field: 'appDate', render: (val) => timeToStr(val) },
                                    { label: '注册号', field: 'regNo' },
                                    { label: '国际分类', field: 'intCls' },
                                    { label: '商标状态', field: 'status' },
                                ]}
                            />
                        }
                        {/* 专利信息 */}
                        {menuPanel === PANELKEY.PATENTS &&
                             <ListBlock
                                 title="专利信息"
                                 key="sub-zlxx"
                                 // 1137
                                 url="/enterprise/ipr/patents.do"
                                 params={{ creditCode: tyshxydm }}
                                 columns={[
                                     { label: '专利名称', field: 'patentName' },
                                     { label: '申请号', field: 'appnumber', width: 200 },
                                     { label: '申请公布时间', field: 'applicationPublishTime', width: 120 },
                                     { label: '申请公布号', field: 'pubnumber', width: 130 },
                                     { label: '专利类型', field: 'patentType', width: 100 },
                                     { label: '首次发布日期', field: 'pubDate', width: 120 },
                                 ]}
                             />
                        }
                        {/* 软件著作权 */}
                        {menuPanel === PANELKEY.COPY_REG &&
                             <ListBlock
                                 title="软件著作权"
                                 key="sub-rjzzq"
                                 // 836
                                 url="/enterprise/ipr/copyReg.do"
                                 params={{ creditCode: tyshxydm }}
                                 columns={[
                                     { label: '登记批准号', field: 'regnum' },
                                     { label: '软件全称', field: 'fullname' },
                                     { label: '软件简称', field: 'simplename' },
                                     { label: '登记号', field: 'regnum' },
                                     { label: '分类号', field: 'catnum' },
                                     { label: '版本号', field: 'version' },
                                     { label: '首次发布日期', field: 'publishtime', render: (val) => timeToStr(val), width: 130 },
                                 ]}
                             />
                        }
                        {/* 作品著作权 */}
                        {menuPanel === PANELKEY.COPY_REG_WORKS &&
                             <ListBlock
                                 title="作品著作权"
                                 key="sub-zpzzq"
                                 // 833
                                 url="/enterprise/ipr/copyRegWorks.do"
                                 params={{ creditCode: tyshxydm }}
                                 columns={[
                                     { label: '作品名称', field: 'fullname' },
                                     { label: '登记号', field: 'regnum', width: 230 },
                                     { label: '作品类别', field: 'type' },
                                     { label: '创作完成日期', field: 'finishTime', render: (val) => timeToStr(val), width: 130 },
                                     { label: '登记日期', field: 'regtime', render: (val) => timeToStr(val), width: 130 },
                                     { label: '首次发布日期', field: 'publishtime', render: (val) => timeToStr(val), width: 130 },
                                 ]}
                             />
                        }
                        {/* 扶持信息 */}
                        {menuPanel === PANELKEY.FUCHI && <SupportingEnterprisesDetail noLeft id={tyshxydm} />}
                        {/* 扶持成效 */}
                        {menuPanel === PANELKEY.FUCHICX && <EnterpriseDevelopment data={{ operateInfoList }} />}
                        {/* 信用信息 */}
                        {menuPanel === PANELKEY.CREDIT && (
                            <BusinessInformation
                                data={{
                                    blackListList,
                                    allowPunishList,
                                    creditList,
                                }}
                            />
                        )}
                    </div>
                </div>
                {/* <ButtonToolBar sticky>
                <Button bsStyle="default" bsSize="large" onClick={() => history.go(-1)}>
                    返回
                </Button>
            </ButtonToolBar> */}
            </div>
        </DetailContext.Provider>
    );
};

export default Detail;
