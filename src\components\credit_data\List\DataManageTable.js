import React, { Component, Fragment } from 'react';
// 接口
import * as DataQueryApi from '@/services/data/data/DataQueryApi';
import * as DataSaveApi from '@/services/data/data/DataSaveApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
// 组件
import RoleAuthControl from '@/components/business/auth/RoleAuthControl';
import MultiClamp from '@/components/ui/MultiClamp';
import QueryList from '@/components/business/metadata/QueryList';
import MyAlert from '@/components/ui/MyAlert/MyAlert';
import Message from '@/components/ui/MyAlert/Message';
import { Button, Icon, Panel } from '@share/shareui';
import { FormState } from '@/components/business/Form';
import DataOperateModal from '../Modal/DataOperateModal';

const statusOptions = [
    { label: '公示中', value: '1' },
    { label: '已下线', value: '2' },
    { label: '已归档', value: '3' },
    { label: '已删除', value: '4' },
    { label: '待修正', value: '5' },
];

const defaultSubmitForm = {
    reason: '',
    explain: '',
};

class DataManageTable extends Component {
    state = {
        // 表数据量
        tableTotal: null,
        // 操作模态框
        showOperateModal: false,
        submitForm: new FormState({ ...defaultSubmitForm }, (submitForm, callback) => this.setState({ submitForm }, callback)),
    };

    // 数据列表请求接口
    buildDataListApi = (searchBody) => {
        const { categoryCode } = this.props;

        return DataQueryApi.listByCategory(categoryCode, '2', searchBody);
    };

    // 列表展示列
    extendHeadColumns = () => {
        return [
            {
                title: '信息类别',
                width: 200,
                // fixed: 'left',
                dataIndex: ['system', 'CATEGORY_NAME'],
                render: (value) => <MultiClamp title={value}>{value}</MultiClamp>,
            },
        ];
    };

    extendTailColumns = () => {
        const { history, categoryCode, metaConfigList } = this.props;
        const { submitForm } = this.state;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);

        return [
            {
                title: '数据状态',
                width: 80,
                key: 'status',
                fixed: 'right',
                dataIndex: 'system',
                render: (rowData) => {
                    const { STATUS, ISSUE_DATA_SOURCES, ISSUE_ERROR_MSG } = rowData;
                    const showValue = (statusOptions.find((item) => item.value === STATUS) || {}).label;
                    const title = STATUS === '5' && ISSUE_DATA_SOURCES !== 'B' && ISSUE_ERROR_MSG ? ISSUE_ERROR_MSG : '';

                    return (
                        <span title={showValue}>
                            {showValue}
                            {title && (
                                <span style={{ paddingLeft: '2px' }}>
                                    <Icon className="si si-com_problem" title={title} />
                                </span>
                            )}
                        </span>
                    );
                },
            },
            {
                title: '操作',
                width: 200,
                fixed: 'right',
                key: 'operate',
                dataIndex: 'system',
                render: (rowData) => (
                    <div className="tableBtn">
                        {/* <a */}
                        {/*     onClick={() => */}
                        {/*         history.push( */}
                        {/*             `/DataAuditDetail/${categoryCode}/${rowData.__XDR_LB || rowData.XDR_LB || '2'}/${rowData.YW_ID}` */}
                        {/*         ) */}
                        {/*     } */}
                        {/* >审计</a> */}
                        <a onClick={() => history.push(`/DataManageDetail/${rowData.CATEGORY_CODE}/${rowData[primaryKey]}`)}>详情</a>
                        {/* 上线、下线状态数据可编辑 */}
                        {(rowData.STATUS === '1' || rowData.STATUS === '2' || rowData.STATUS === '5') && (
                            <RoleAuthControl buttonKey="meta-data-filling-update">
                                <a
                                    onClick={() => {
                                        submitForm.setFormData({
                                            categoryId: rowData.CATEGORY_CODE,
                                            recordIds: [rowData[primaryKey]],
                                            status: rowData.STATUS,
                                            action: '3',
                                        });
                                        this.setState({ showOperateModal: true });
                                    }}
                                >
                                    修改
                                </a>
                            </RoleAuthControl>
                        )}
                        {/* 下线状态数据可上线 */}
                        {/* { */}
                        {/*     (rowData.STATUS === '2') && */}
                        {/*     <RoleAuthControl buttonKey="meta-data-manage-online"> */}
                        {/*         <a */}
                        {/*             onClick={() => { */}
                        {/*                 submitForm.setFormData({ */}
                        {/*                     categoryId: rowData.CATEGORY_CODE, */}
                        {/*                     recordIds: [rowData[primaryKey]], */}
                        {/*                     status: '1', */}
                        {/*                     action: '4', */}
                        {/*                 }); */}
                        {/*                 this.setState({ showOperateModal: true }); */}
                        {/*             }} */}
                        {/*         >上线</a> */}
                        {/*     </RoleAuthControl> */}
                        {/* } */}
                        {/* 上线状态数据可下线 */}
                        {/* { */}
                        {/*     (rowData.STATUS === '1') && */}
                        {/*     <RoleAuthControl buttonKey="meta-data-manage-offline"> */}
                        {/*         <a */}
                        {/*             onClick={() => { */}
                        {/*                 submitForm.setFormData({ */}
                        {/*                     categoryId: rowData.CATEGORY_CODE, */}
                        {/*                     recordIds: [rowData[primaryKey]], */}
                        {/*                     status: '2', */}
                        {/*                     action: '5', */}
                        {/*                 }); */}
                        {/*                 this.setState({ showOperateModal: true }); */}
                        {/*             }} */}
                        {/*         >下线</a> */}
                        {/*     </RoleAuthControl> */}
                        {/* } */}
                        {/* 上线、下线状态数据可归档 */}
                        {/* { */}
                        {/*     (rowData.STATUS === '1' || rowData.STATUS === '2') && */}
                        {/*     <RoleAuthControl buttonKey="meta-data-manage-archive"> */}
                        {/*         <a */}
                        {/*             onClick={() => { */}
                        {/*                 submitForm.setFormData({ */}
                        {/*                     categoryId: rowData.CATEGORY_CODE, */}
                        {/*                     recordIds: [rowData[primaryKey]], */}
                        {/*                     status: '3', */}
                        {/*                     action: '7', */}
                        {/*                 }); */}
                        {/*                 this.setState({ showOperateModal: true }); */}
                        {/*             }} */}
                        {/*         >归档</a> */}
                        {/*     </RoleAuthControl> */}
                        {/* } */}
                        {/* 上线、下线、归档状态数据可删除 */}
                        {/* { */}
                        {/*     (rowData.STATUS === '1' || rowData.STATUS === '2' || rowData.STATUS === '3') && */}
                        {/*     <RoleAuthControl buttonKey="meta-data-manage-delete"> */}
                        {/*         <a */}
                        {/*             onClick={() => { */}
                        {/*                 submitForm.setFormData({ */}
                        {/*                     categoryId: rowData.CATEGORY_CODE, */}
                        {/*                     recordIds: [rowData[primaryKey]], */}
                        {/*                     status: '4', */}
                        {/*                     action: '9', */}
                        {/*                 }); */}
                        {/*                 this.setState({ showOperateModal: true }); */}
                        {/*             }} */}
                        {/*         >删除</a> */}
                        {/*     </RoleAuthControl> */}
                        {/* } */}
                    </div>
                ),
            },
        ];
    };

    // 提交模态框
    handleModalSubmit = async () => {
        const { submitForm } = this.state;
        const { history, categoryCode } = this.props;
        const valids = await submitForm.valid();
        const data = submitForm.getFormData();
        const { categoryId, recordIds, action, reason, explain } = data;

        if (!FormVaildHelper.isValid(valids)) {
            return;
        }

        this.setState({ showOperateModal: false });
        if (action === '3') {
            history.push(`/DataManageEdit/${categoryId}/${recordIds[0]}?ACTION_REASON=${reason}&ACTION_EXPLAIN=${explain}`);
        } else {
            await DataSaveApi.statusUpdateFromPage(categoryCode, '2', data);
            MyAlert.ok('操作成功');
            this.table.refreshTable();
        }
    };

    // 导出Excel
    excelExport = (recordIds) => {
        const { categoryCode, categoryCodeList, body } = this.props;
        const category = categoryCodeList.find((v) => v.id === categoryCode) || {};
        const param = JSON.stringify({
            fileName: `${category.name}-上报数据`,
            recordIds,
            modeConditionMap: body,
        });

        Message.success('导出中...');
        DataQueryApi.exportByCategory(categoryCode, '2', param);
    };

    render() {
        const { categoryCode, categoryCodeList, metaConfigList, body, selectedRecords, onSelectedRecordsChange, ...otherProps } =
            this.props;
        const { tableTotal, showOperateModal, submitForm } = this.state;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);
        const listConfig = MetaConfigUtils.filterListConfig(metaConfigList);
        const selectedRowKeys = selectedRecords.map((item) => item.system[primaryKey]);

        return (
            <Fragment>
                <Panel>
                    <Panel.Head title="数据查询">
                        <Panel.HeadRight>
                            <ul className="ui-list-horizontal">
                                <RoleAuthControl buttonKey="meta-data-manage-batch-export">
                                    <li>
                                        <Button
                                            type="button"
                                            className="btn-xs"
                                            border={false}
                                            onClick={() => this.excelExport(selectedRowKeys)}
                                            disabled={selectedRowKeys.length === 0}
                                        >
                                            <Icon className="si si-com_export" />
                                            选择导出
                                        </Button>
                                    </li>
                                </RoleAuthControl>
                                <RoleAuthControl buttonKey="meta-data-manage-query-export">
                                    <li>
                                        <Button
                                            type="button"
                                            className="btn-xs"
                                            border={false}
                                            disabled={!tableTotal}
                                            onClick={() => {
                                                if (tableTotal > 5000) {
                                                    Message.warn('系统每次导出最多支持5000条，建议分批导出~');

                                                    return;
                                                }
                                                this.excelExport([]);
                                            }}
                                        >
                                            <Icon className="si si-app_xy" />
                                            导出查询结果
                                        </Button>
                                    </li>
                                </RoleAuthControl>
                            </ul>
                        </Panel.HeadRight>
                    </Panel.Head>
                    <Panel.Body full>
                        <QueryList
                            namespace="DataManageTable"
                            service={{
                                api: this.buildDataListApi,
                                body,
                            }}
                            rowKey={(data) => data.system[primaryKey]}
                            metadataConfigList={listConfig}
                            extendHeadColumns={this.extendHeadColumns()}
                            extendTailColumns={this.extendTailColumns()}
                            rowSelection={{
                                fixed: true,
                                selectedRowKeys,
                                onChange: (_selectedRowKeys, selectedRows) => onSelectedRecordsChange(selectedRows),
                            }}
                            onRef={(tableRef) => {
                                this.table = tableRef;
                            }}
                            onTableDataChange={(data) =>
                                this.setState({
                                    tableTotal: data.page.total,
                                })
                            }
                            pagination={{ detectPage: true, showTotal: true }}
                            {...otherProps}
                        />
                    </Panel.Body>
                </Panel>
                <DataOperateModal
                    formState={submitForm}
                    show={showOperateModal}
                    submitFn={this.handleModalSubmit}
                    cancelFn={() => {
                        this.setState({ showOperateModal: false });
                        submitForm.setFormData(defaultSubmitForm);
                        submitForm.cleanValidError();
                    }}
                />
            </Fragment>
        );
    }
}

export default DataManageTable;
