/*
 *@(#) useEnterpriseJumpUrl.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2024
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2024-01-24
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import Service from '@/services/Service';
import { useService } from '@share/framework';
import { useMount } from 'ahooks';
import { useState } from 'react';

const useEnterpriseJumpUrl = () => {
    const services = useService(Service);
    const [url, setUrl] = useState({});

    const getUrl = async () => {
        const res = await services.getEnterpriseJumpUrl();

        setUrl(res);
    };

    useMount(() => {
        getUrl();
    });

    return {
        ...url,
    };
};

export default useEnterpriseJumpUrl;
