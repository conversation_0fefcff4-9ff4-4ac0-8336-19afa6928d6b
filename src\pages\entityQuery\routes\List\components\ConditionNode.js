import React, { Fragment } from 'react';
import { registerFormItem } from '@/utils/shareFormUtil';
import { Button } from '@share/shareui';
import ConditionMatchNode from './ConditionMatchNode';
import ConditionComposeNode from './ConditionComposeNode';
import style from './ConditionNode.scss';

const ConditionNode = (props) => {
    const { value, onChange, field, inventoryList, suffix } = props;
    const nodeType = Object.keys(value || {}).includes('composeLogic') ? 'compose' : 'match';
    const NodeCom = nodeType === 'compose' ? ConditionComposeNode : ConditionMatchNode;

    const updateData = (data) => {
        onChange({ target: { value: data } });
    };

    return (
        <Fragment>
            {!value && (
                <div className={style.button}>
                    <Button bsStyle="info" onClick={() => updateData({ data: {}, matchSymbol: 'eq', matchValue: '' })}>
                        添加匹配节点
                    </Button>
                    <Button bsStyle="info" onClick={() => updateData({ composeLogic: 'and', composeNodes: [] })}>
                        添加组合节点
                    </Button>
                </div>
            )}
            {value && (
                <NodeCom
                    field={field}
                    inventoryList={inventoryList}
                    noView
                    suffix={
                        suffix || (
                            <div>
                                <Button bsStyle="info" onClick={() => updateData(null)}>
                                    删除
                                </Button>
                            </div>
                        )
                    }
                />
            )}
        </Fragment>
    );
};

export default registerFormItem(ConditionNode);
