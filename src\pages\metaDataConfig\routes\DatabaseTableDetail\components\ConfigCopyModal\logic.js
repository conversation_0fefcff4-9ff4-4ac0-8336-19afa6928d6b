/* eslint-disable no-param-reassign */
import { useForm } from '@share/shareui-form';
import { useSetState, useMount } from 'ahooks';
import { useServiceMap } from '../../hooks';

/**
 * 数据配置过滤
 */
const filterPrimaryKeyConfig = (metadataConfigList) => {
    return metadataConfigList.find((item) => item.primaryKey);
};

const defaultBody = {
    type: ['commonConfig', 'esConfig', 'componentConfig', 'ruleConfig'],
    tableName: '',
    fieldCode: [],
};

const useLogic = ({ data, successFn, cancelFn }) => {
    const { metaTableApi, metaFieldApi } = useServiceMap();
    const [formData, form] = useForm({ ...defaultBody });
    const [state, setState] = useSetState({
        dataTableOptions: [],
        dataFieldOptions: [],
        dataMetaFieldList: [],
    });
    const getDataTableOptions = async () => {
        const { tableId } = data;
        const dataTableList = await metaTableApi.getDataTableList();
        const dataTableOptions = dataTableList
            .filter((item) => item.tableId !== tableId)
            .map((item) => ({ label: `${item.tableId}（${item.tableDescription}）`, value: item.tableId }));

        setState({ dataTableOptions });
    };
    const onTableNameChange = async ({ target: { value } }) => {
        if (!value) {
            return;
        }
        const { metaFieldList: currentMetaFieldList } = data;
        const dataMetaFieldList = await metaFieldApi.tableMetaByTableName(value);
        const currentField = currentMetaFieldList.map((item) => item.fieldCode);
        const dataFieldOptions = dataMetaFieldList
            .filter((item) => currentField.includes(item.fieldCode))
            .map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));

        form.setFieldValue('fieldCode', []);
        setState({ dataMetaFieldList, dataFieldOptions });
    };
    const handleCancel = () => {
        form.setFormData({ ...defaultBody });
        // eslint-disable-next-line no-unused-expressions
        cancelFn && cancelFn();
    };

    const convertFieldRule = (ruleList, currentTableFields) => {
        if (!Array.isArray(ruleList)) {
            return [];
        }

        return ruleList.map((fr) => {
            const fieldRule = fr;
            const { checkField, combinationRule = [] } = fieldRule.ruleParam;

            if (checkField && !currentTableFields.includes(checkField)) {
                fieldRule.ruleParam.checkField = null;
            }
            if (Array.isArray(combinationRule) && combinationRule.length > 0) {
                fieldRule.ruleParam.combinationRule = convertFieldRule(combinationRule, currentTableFields);
            }

            return fieldRule;
        });
    };
    const submit = async () => {
        const { metaFieldList } = data;
        const { dataMetaFieldList } = state;
        const { type, fieldCode } = form.getFormData();
        const currentTableFields = metaFieldList.map((item) => item.fieldCode);
        if (!(await form.validHelper())) {
            return;
        }

        if (type.includes('identityConfig')) {
            const currentPrimaryKeyMetaField = filterPrimaryKeyConfig(metaFieldList);
            const sourcePrimaryKeyMetaField = filterPrimaryKeyConfig(dataMetaFieldList);

            if (sourcePrimaryKeyMetaField.identityConfig) {
                currentPrimaryKeyMetaField.identityConfig = {
                    ...sourcePrimaryKeyMetaField.identityConfig,
                    fieldCodes: Array.isArray(sourcePrimaryKeyMetaField.identityConfig.fieldCodes)
                        ? sourcePrimaryKeyMetaField.identityConfig.fieldCodes.filter((item) => currentTableFields.includes(item))
                        : [],
                };
            }
        }
        metaFieldList
            .filter((currentMetaField) => fieldCode.includes(currentMetaField.fieldCode))
            .forEach((currentMetaField) => {
                const sourceMetaField = dataMetaFieldList.find((item) => item.fieldCode === currentMetaField.fieldCode) || {};

                if (type.includes('commonConfig')) {
                    currentMetaField.showLabel = sourceMetaField.showLabel;
                    currentMetaField.fieldAlias = sourceMetaField.fieldAlias;
                    currentMetaField.fieldOrientedObjectType = sourceMetaField.fieldOrientedObjectType;
                    currentMetaField.openStyle = sourceMetaField.openStyle;
                    currentMetaField.enabled = sourceMetaField.enabled;
                    currentMetaField.businessField = sourceMetaField.businessField;
                }
                if (type.includes('esConfig')) {
                    currentMetaField.esConfig = { ...sourceMetaField.esConfig };
                }
                if (type.includes('componentConfig')) {
                    currentMetaField.queryParam = { ...sourceMetaField.queryParam, order: currentMetaField.queryParam.order };
                    currentMetaField.listParam = { ...sourceMetaField.listParam, order: currentMetaField.listParam.order };
                    currentMetaField.editParam = { ...sourceMetaField.editParam, order: currentMetaField.editParam.order };
                    currentMetaField.detailParam = { ...sourceMetaField.detailParam, order: currentMetaField.detailParam.order };
                }
                if (type.includes('ruleConfig')) {
                    currentMetaField.bmRule = convertFieldRule(sourceMetaField.bmRule, currentTableFields);
                    currentMetaField.convertRule = convertFieldRule(sourceMetaField.convertRule, currentTableFields);
                    currentMetaField.checkRule = convertFieldRule(sourceMetaField.checkRule, currentTableFields);
                    currentMetaField.showRule = convertFieldRule(sourceMetaField.showRule, currentTableFields);
                }
                if (type.includes('applicationConfig')) {
                    currentMetaField.queryParam.order = sourceMetaField.queryParam.order;
                    currentMetaField.listParam.order = sourceMetaField.listParam.order;
                    currentMetaField.editParam.order = sourceMetaField.editParam.order;
                    currentMetaField.detailParam.order = sourceMetaField.detailParam.order;
                }
            });

        await metaFieldApi.saveTableMeta(metaFieldList);

        // eslint-disable-next-line no-unused-expressions
        successFn && successFn(metaFieldList);
        handleCancel();
    };

    useMount(() => {
        getDataTableOptions();
    });

    return {
        state,
        form,
        handleCancel,
        onTableNameChange,
        formData,
        submit,
    };
};

export default useLogic;
