/*
 * @(#)  ManuallyLabel.js ----手动打标
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 * Copyright: Copyright (c) 2024
 * Company: 厦门畅享信息技术有限公司
 * Author: yujy (<EMAIL>)
 * File Created: 2024-05-08 15:32:12
 */
import React, { Fragment, useState } from 'react';
import { ShareList, Column, Toolbar, useList } from '@share/list';
import { Input, Modal, message } from 'antd';
import { useService } from '@share/framework';
import { ShareFileUpload, Label } from '@share/shareui';
import { Selector } from '@share/shareui/upload';
import cl from 'classnames';
import { InfoDataSource } from '@/utils/FileUploadDataSourceConfig';
import MetaFieldApi from '@/services/MetaFieldApi';
import { useUpdateEffect } from 'ahooks';
import styles from './CalculationRules.scss';

const { Search } = Input;

const tabsData = [
    {
        label: '打标列表',
        value: 'dblb',
    },
    {
        label: '人员列表',
        value: 'qylb',
    },
];
const ManuallyLabel = ({ selectNode }) => {
    const services = useService(MetaFieldApi);
    const [fileList, setFileList] = useState([]);
    const [fileListDataSource, setFileListDataSource] = useState([]);
    const [activeKey, setActiveKey] = useState('dblb');
    const listState = useList({
        dataSource: (condition) => {
            return services.getTagPersonList(condition);
        },
        autoLoad: selectNode?.id
            ? {
                  requestData: {
                      tagId: selectNode?.id,
                      xm: '',
                  },
              }
            : false,
    });
    const handleClickTab = (key) => {
        setActiveKey(key);
    };

    const onSearch = (value) => {
        console.log('value', value);
        if (!value) {
            listState.query({
                xm: '',
                tagId: selectNode?.id,
            });
            setFileList(fileListDataSource);

            return;
        }
        const newFileList = fileListDataSource.filter((item) => item.xm.includes(value));
        listState.query({
            xm: value,
            tagId: selectNode?.id,
        });
        setFileList(newFileList);
    };
    const markTagDownload = () => {
        const rees = services.personMarkTagDownload();
    };
    const handleRemoveTagEnterprise = (id) => {
        Modal.confirm({
            content: '是否确认删除该打标人员？',
            okText: '确认',
            cancelText: '取消',
            onOk: async () => {
                console.log('删除了');
                const res = await services.removeTagPerson(id);
                if (res) {
                    message.success('删除成功', 1.5, listState.refresh);
                }
            },
        });
    };

    useUpdateEffect(() => {
        setFileList(fileListDataSource);
    }, [fileListDataSource]);
    console.log('fileList', fileList);

    return (
        <Fragment>
            <Toolbar
                title={
                    <div className={styles['CalculationRules-tabs']}>
                        {tabsData.map((item) => {
                            return (
                                <div
                                    className={cl(styles['CalculationRules-tabs_item'], {
                                        [styles.active]: item.value === activeKey,
                                    })}
                                    onClick={() => handleClickTab(item.value)}
                                    key={item.value}
                                >
                                    {item.label}
                                </div>
                            );
                        })}
                    </div>
                }
            >
                <Toolbar.Extra>
                    <div className={styles['toolbar-extra']}>
                        <span onClick={markTagDownload}>
                            <i className="si si-com_arrowcircleodown" />
                            下载模板
                        </span>
                        <span>
                            <ShareFileUpload
                                value={fileList}
                                field="markTag"
                                dataSource={{
                                    ...InfoDataSource,
                                    upload: async (files, callback) => {
                                        const res = await services.personMarkTagUpload(selectNode.id, files, callback);
                                        const d = (res || []).reduce(
                                            (previous, current, currentIndex) => {
                                                // 创建一个新的对象来保存更新后的 success 和 fail 的值
                                                const updatedStats = {
                                                    ...previous,
                                                    success: current.result === false ? previous.success : previous.success + 1,
                                                    fail: current.result === false ? previous.fail + 1 : previous.fail,
                                                };

                                                // 创建一个新的对象来保存更新后的 fileList
                                                const updatedFileList = [
                                                    ...previous.fileList,
                                                    {
                                                        ...current,
                                                        id: new Date().getTime() * currentIndex,
                                                    },
                                                ];

                                                // 返回一个包含更新后统计信息和文件列表的新对象
                                                return {
                                                    ...updatedStats,
                                                    fileList: updatedFileList,
                                                };
                                            },
                                            {
                                                fileList: [],
                                                success: 0,
                                                fail: 0,
                                            }
                                        );
                                        Modal.info({
                                            content: (
                                                <div>
                                                    导入<span style={{ color: '#09d' }}>{d.fileList.length}</span>条，成功
                                                    <span style={{ color: '#0b8' }}>{d.success}</span>
                                                    条，导入失败<span style={{ color: 'red' }}>{d.fail}</span>条
                                                </div>
                                            ),
                                            onOk: () => {
                                                setFileListDataSource(d.fileList);
                                                listState.refresh();
                                            },
                                        });

                                        return res?.length > 0 ? res : [{}];
                                    },
                                }}
                            >
                                <Selector containerClassName={styles['ui-fileupload-add-btn']}>
                                    <i className="si si-com_arrowscircleoup" />
                                    导入人员
                                </Selector>
                            </ShareFileUpload>
                        </span>
                        <div className={styles.searchbar}>
                            <Search placeholder="请输入人员名称" allowClear enterButton onSearch={onSearch} />
                        </div>
                    </div>
                </Toolbar.Extra>
            </Toolbar>
            {activeKey === 'dblb' ? (
                <ShareList dataSource={fileList} uniqKey="id">
                    <Column field="sort" label="序号" width={60} />
                    <Column field="xm" label="姓名" />
                    <Column field="zjhm" label="身份证号" />
                    <Column
                        field="result"
                        label="打标状态"
                        render={(value) => {
                            if (value) {
                                return <Label bsStyle="success">成功</Label>;
                            }

                            return <Label bsStyle="danger">失败</Label>;
                        }}
                    />
                    {/* <Column
                    field="cretaTime"
                    label="打标时间"
                    render={() => {
                        return dayjs().format('YYYY-MM-DD');
                    }}
                /> */}
                </ShareList>
            ) : (
                <ShareList listState={listState}>
                    <Column field="xm" label="姓名" />
                    <Column field="zjhm" label="身份证号" />
                    <Column field="linkTime" label="打标时间" />
                    <Column
                        field="sourceId"
                        label="打标方式"
                        render={(sourceId) => {
                            return sourceId ? '规则打标' : '导入打标';
                        }}
                    />
                    <Column
                        field="cz"
                        label="操作"
                        width={60}
                        render={(value, row) => {
                            return (
                                <span
                                    className={styles.remove}
                                    onClick={() => {
                                        handleRemoveTagEnterprise(row.id);
                                    }}
                                >
                                    移除
                                </span>
                            );
                        }}
                    />
                </ShareList>
            )}
        </Fragment>
    );
};
export default ManuallyLabel;
