// 请求发送者;
import { instance } from '@share/network';
import { Spin } from '@share/shareui';
import VirtualFormSender from './VirtualFormSender';
// 环境配置
import { __MOCK__, COMMON_API_PREFIX, MOCK_API_PREFIX } from './NetworkConfig';
// 服务器数据处理
import AnalysisServerData from './AnalysisServerData';
// 引入 加载状态 组件

class NetworkSecond {
    constructor() {
        // 遮罩层数量，为 0 的时候消失
        this.maskNum = 0;
        this.noMaskNum = 0;
        // 模拟表单Sender
        this.vFormSender = new VirtualFormSender();
        // 使用公司@share/network实现（ajax底层具体实现，如FetchSender,XmlHttpRequestSender，底层根据fetch是否存在自动选择具体Sender）
        this.sender = instance();
        // 设置统一请求前缀
        this.sender.setContextPath(__MOCK__ ? MOCK_API_PREFIX : COMMON_API_PREFIX);
        // 设置统一请求参数处理
        this.sender.setTransRequest((requestData) => {
            this.addMask(requestData && requestData.noMask ? requestData.noMask : false);

            return requestData;
        });
        // 设置统一异常处理
        this.sender.setExceptionHandle((respnseObject, abort) => {
            this.maskNum = 0;
            this.noMaskNum = 0;
            Spin.hide();
            AnalysisServerData.assertServerException(respnseObject, abort);
        });
        // 设置统一返回参数处理
        this.sender.setResolver((respnseObject, abort) => {
            this.removeMask();

            return AnalysisServerData.resolverOldServerException(respnseObject, abort);
        });
    }

    addMask = (noMask) => {
        if (!noMask) {
            if (this.maskNum === 0) {
                Spin.show('加载中，请等待');
            }
            this.maskNum += 1;
        } else {
            this.noMaskNum += 1;
        }
    };

    removeMask = () => {
        this.noMaskNum > 0 ? (this.noMaskNum -= 1) : (this.maskNum -= 1);
        if (this.maskNum === 0) {
            Spin.hide();
        }
    };

    /**
     * 设置sender
     * @param sender
     * @return {Network}
     */
    setSender = (sender) => {
        this.sender = sender;

        return this;
    };

    /**
     * 通用请求发送
     */
    get = (url, data = {}) => {
        // get参数添加当前时间uuidTime参数用于解决IE浏览器缓存get请求
        return this.sender.formGet(url, { ...data, uuidTime: new Date().getTime() });
    };

    /**
     * Form头 请求发送
     */
    postForm = (url, data = {}) => {
        return this.sender.formPost(url, data);
    };

    /**
     * JSON头 请求发送
     */
    postJson = (url, data = {}) => {
        return this.sender.json(url, data);
    };

    // /**
    //  * ulynlist请求适配
    //  */
    // ulynlistAdapt = (url, data) => {
    //     return this.sender.formPost(url, data);
    // };

    /**
     * 表单发送Get请求
     */
    vformGet = (url, data = {}) => {
        this.vFormSender.send(url, data, 'get');
    };

    /**
     * 表单发送Post请求
     */
    vformPost = (url, data = {}) => {
        this.vFormSender.send(url, data, 'post');
    };

    /**
     * a标签发送文件下载请求
     */
    downFile = (url, fieldName) => {
        const finalUrl = __MOCK__ ? MOCK_API_PREFIX : COMMON_API_PREFIX + url;
        let finalFieldName;

        if (fieldName === null || typeof fieldName === 'undefined') {
            const split = url.includes('/') ? '/' : '\\';
            const arr = url.split(split);

            finalFieldName = arr[arr.length - 1];
        } else {
            finalFieldName = fieldName;
        }
        const a = document.createElement('a');

        a.setAttribute('style', 'display:none');
        a.setAttribute('href', finalUrl);
        a.setAttribute('download', finalFieldName);
        document.body.appendChild(a);
        a.click();
        a.parentNode.removeChild(a);
    };
}

module.exports = new NetworkSecond();
