import React, { Component, Fragment } from 'react';
// 子组件
import Table from './CommonUiTable';
import Pagination from './Pagination/Pagination';
import DetectPagePagination from './Pagination/DetectPagePagination';

// 获取数据主键
function getRecordRowKey(record, rowKey) {
    if (typeof rowKey === 'string') {
        return record[rowKey];
    }
    if (typeof rowKey === 'function') {
        return rowKey(record);
    }

    return null;
}

class CommonPageTable extends Component {
    static defaultProps = {
        // 标题
        title: '', // 列表标题
        // 数据
        dataSource: [], // 数据数组
        dataSourceHandleFn: (data) => data, // 数据处理函数
        showEmptyLine: false, // 是否自动填充空数据行
        footer: () => '', // 表格尾部扩展展示函数
        rowSelection: null, // 前置可选栏参数
        // 分页条
        pagination: {
            defaultCurrent: 1, // 默认的当前页数
            defaultPageSize: 10, // 默认的每页条数
            maxPageCount: null, // 最大页面数
            current: 1, // 当前页数
            pageSize: 10, // 每页条数
            total: 0, // 数据总量
            // showTotal: true,                                           // 展示总数
            showSizeChanger: true, // 展示每页条数选项
            pageSizeOptions: [10, 20, 30], // 每页条数选项
            showFirstJumper: true, // 展示首页跳转
            showLastJumper: true, // 展示尾页跳转
            onChange: (page, pageSize) => console.log(page, pageSize), // 分页条变更钩子函数
            // 渐布式分页
            detectPage: false, // 开启渐步分页
            detectStep: 10, // 渐步步长
            detectTotal: 0, // 渐步总数
        },
    };

    state = {
        selectData: [],
    };

    // 创建定制化标题
    createTitleCustom = () => {
        const { title } = this.props;

        return (data) => {
            if (typeof title === 'function') {
                return title(data);
            }

            return <p className="share-table_title">{title}</p>;
        };
    };

    // 创建定制化选中栏
    createRowSelectionCustom = () => {
        const { rowKey, rowSelection } = this.props;
        const { selectData } = this.state;

        const { selectedRowKeys: parentSelectedRowKeys, onChange, onSelect, onSelectAll, ...restRowSelection } = rowSelection;
        const rowSelectionCustom = { ...restRowSelection };
        let currentSelectedRow;

        if (parentSelectedRowKeys) {
            // 外部维护被选中字段
            rowSelectionCustom.selectedRowKeys = parentSelectedRowKeys;
            currentSelectedRow = selectData.filter((row) => parentSelectedRowKeys.includes(getRecordRowKey(row, rowKey)));
        } else {
            // 内部维护被选中字段
            rowSelectionCustom.selectedRowKeys = selectData.map((row) => getRecordRowKey(row, rowKey));
            currentSelectedRow = [...selectData];
        }

        rowSelectionCustom.onChange = (selectedRowKeys, selectedRows) => {
            const oldSelectedRowsKeys = currentSelectedRow.map((row) => getRecordRowKey(row, rowKey));
            const newAddSelectedRows = selectedRows.filter((row) => !oldSelectedRowsKeys.includes(getRecordRowKey(row, rowKey)));
            const newSelectedRows = [...currentSelectedRow, ...newAddSelectedRows].filter((row) =>
                selectedRowKeys.includes(getRecordRowKey(row, rowKey))
            );

            this.setState({ selectData: newSelectedRows }, () => onChange && onChange(selectedRowKeys, newSelectedRows));
        };

        rowSelectionCustom.onSelect = (record, selected, selectedRows, nativeEvent) => {
            let newSelectedRows;

            if (selected) {
                newSelectedRows = rowSelectionCustom.type === 'radio' ? [record] : [...currentSelectedRow, record];
            } else {
                newSelectedRows =
                    rowSelectionCustom.type === 'radio'
                        ? []
                        : currentSelectedRow.filter((row) => getRecordRowKey(row, rowKey) !== getRecordRowKey(record, rowKey));
            }
            this.setState({ selectData: newSelectedRows }, () => onSelect && onSelect(record, selected, newSelectedRows, nativeEvent));
        };

        rowSelectionCustom.onSelectAll = (selected, selectedRows, changeRows) => {
            let newSelectedRows;

            if (selected) {
                newSelectedRows = [...currentSelectedRow, ...changeRows];
            } else {
                const cancelSelectedRowKeys = changeRows.map((row) => getRecordRowKey(row, rowKey));

                newSelectedRows = currentSelectedRow.filter((row) => !cancelSelectedRowKeys.includes(getRecordRowKey(row, rowKey)));
            }
            this.setState({ selectData: newSelectedRows }, () => onSelectAll && onSelectAll(selected, newSelectedRows, changeRows));
        };

        return rowSelectionCustom;
    };

    // 创建定制化表格尾部扩展
    createFooterCustom = (currentPageData) => {
        const { footer, pagination } = this.props;
        const paginationProps = { ...CommonPageTable.defaultProps.pagination, ...pagination };
        const PaginationComponent = paginationProps.detectPage ? DetectPagePagination : Pagination;

        return (
            <Fragment>
                {footer && footer(currentPageData)}
                <PaginationComponent {...paginationProps} style={{ float: 'right' }} />
            </Fragment>
        );
    };

    render() {
        const { title, dataSource, dataSourceHandleFn, showEmptyLine, emptyText, rowSelection, ...restProps } = this.props;
        // 定制化参数
        const customProps = {};

        // 填充空缺行
        if (showEmptyLine && dataSource.length > 0) {
            while (restProps.pagination.pageSize - dataSource.length > 0) {
                dataSource.push({});
            }
        }
        // 填充数据
        customProps.dataSource = dataSourceHandleFn(dataSource);
        if (customProps.dataSource.length === 0 && emptyText) {
            return emptyText;
        }
        // 定制化标题
        if (title) {
            customProps.title = this.createTitleCustom();
        }
        // 定制化选中栏
        if (rowSelection) {
            customProps.rowSelection = this.createRowSelectionCustom();
        }
        // 填充分页参数
        customProps.footer = this.createFooterCustom;

        return <Table locale={{ emptyText: '暂无数据' }} {...restProps} {...customProps} pagination={false} />;
    }
}

export default CommonPageTable;
