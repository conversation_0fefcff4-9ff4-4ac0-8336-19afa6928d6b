/*
 * @(#)  AddAddressModal.js ---- 添加实际经营地址莫太快
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 * Copyright: Copyright (c) 2024
 * Company: 厦门畅享信息技术有限公司
 * Author: yujy (<EMAIL>)
 * File Created: 2024-04-18 10:44:03
 */
import React, { Fragment, useState, useCallback, useContext } from 'react';
import { Modal, Button, message, FilterInput, delHtmlTag } from '@share/shareui';
import { Row, TableForm, Text, FormItem, useForm } from '@share/shareui-form';
import { useUpdateEffect } from 'ahooks';
import { useService } from '@share/framework';
import DebounceSelect from '@/components/DebounceSelect';
import EnterpriseService from '@/services/EnterpriseService';
import classNames from 'classnames';
import { DetailContext } from '../../../../hooks';
import styles from './Business.scss';

const AddAddressModal = ({ jycsdz, tyshxydm, qymc }) => {
    const { refresh } = useContext(DetailContext);
    const service = useService(EnterpriseService);
    const [show, setShow] = useState(false);
    const [formData, form] = useForm(
        {
            jycsdz: '',
            tyshxydm: '',
            qymc: '',
            addressList: [],
        },
        {
            willClearEmptyField: true,
        }
    );
    const handleShow = () => setShow(true);
    const handleClose = () => setShow(false);

    // 新增实际经营地址
    const addEmpty = useCallback(() => {
        form.setFieldValue('addressList', formData.addressList.concat({}));
    }, [form, formData]);
    // 删除实际经营地址
    const remove = useCallback(
        async (index) => {
            if (!(await message.confirm({ message: '确认删除？' }))) {
                return;
            }
            form.setFieldValue(
                'addressList',
                formData.addressList.filter((item, idx) => idx !== index)
            );
        },
        [formData, form]
    );
    // 获取已经有的实际经营地址列表
    const getRealityVenue = async (val) => {
        const res = await service.business_address(val);
        form.setFieldValues({
            jycsdz,
            tyshxydm,
            qymc,
            addressList: res || [],
        });
    };
    const submit = useCallback(async () => {
        // 校验，并定位到第一个错误
        if (!(await form.validHelper())) {
            return;
        }
        const noEmptyObjects = (formData.addressList || []).every((item) => Object.keys(item).length !== 0);
        if (!noEmptyObjects) {
            message.error('请填写实际经营地址！');

            return;
        }
        // 判断是否有重复的jydz
        const labelMap = (formData.addressList || []).reduce((acc, obj) => {
            if (obj.jydz) {
                if (!acc[obj.jydz]) {
                    acc[obj.jydz] = 1; // 第一次遇到这个label，设置计数为1
                } else {
                    acc[obj.jydz] += 1; // 已经遇到这个label，计数加1
                }
            }

            return acc;
        }, {});
        const hasDuplicates = Object.values(labelMap).some((count) => count > 1);
        if (hasDuplicates) {
            message.error('请勿填写相同的实际经营地址！');

            return;
        }
        const res = await service.business_address_save(formData);
        // 调用后端接口
        if (res) {
            message.success('保存成功！', 1, () => {
                refresh();
                setShow(false);
            });
        } else {
            message.error('出错了，请稍后再试！');
        }
    }, [form, service, formData, refresh]);

    const renderJycsdz = (val) => {
        return (
            <div className={styles.addVenue}>
                <div>{val || '--'}</div>
                <span onClick={addEmpty}>新增</span>
            </div>
        );
    };
    useUpdateEffect(() => {
        getRealityVenue(tyshxydm);
    }, [show]);

    return (
        <Fragment>
            <span className={styles.addAddress} onClick={handleShow}>
                <i className="si si-com_addto" />
            </span>

            <Modal show={show} onHide={handleClose}>
                <Modal.Header closeButton>
                    <Modal.Title>经营地址</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="formTableStyleCover">
                        <TableForm formState={form}>
                            <Row>
                                <Text label="经营场所" field="jycsdz" format={renderJycsdz} />
                            </Row>
                            {formData.addressList.map((item, index) => {
                                return (
                                    // eslint-disable-next-line react/no-array-index-key
                                    <Row key={index}>
                                        <FormItem label={`实际经营场所${index + 1}`} field={`addressList[${index}]`} rule="required">
                                            {(fieldProps) => {
                                                const { value, onChange, ...otherProps } = fieldProps;

                                                return (
                                                    <div className={styles.addRealityVenue}>
                                                        <DebounceSelect
                                                            {...otherProps}
                                                            isCreated
                                                            showSearch
                                                            value={{
                                                                ...value,
                                                                value: value.dzbh,
                                                                label: value.jydz,
                                                            }}
                                                            placeholder="请选择走访地点"
                                                            onChange={(e, opt) => {
                                                                console.log('e', e, opt);
                                                                onChange({
                                                                    target: {
                                                                        value: {
                                                                            ...e,
                                                                            ...(value || {}),
                                                                            jydz: e.label,
                                                                            dzbh: e.label === e.value ? '' : e.value,
                                                                            jydzX: e.addressX,
                                                                            jydzY: e.addressY,
                                                                        },
                                                                    },
                                                                });
                                                            }}
                                                            fetchOptions={(e) => {
                                                                // return interviewService.getSiteOption(e);
                                                                // todo
                                                                return [];
                                                            }}
                                                            suffixIcon={
                                                                <i
                                                                    className={classNames('si si-com_position', {
                                                                        [styles.red]: value.jydzX && value.jydzY,
                                                                    })}
                                                                />
                                                            }
                                                        />
                                                        <span
                                                            onClick={() => {
                                                                remove(index);
                                                            }}
                                                        >
                                                            删除
                                                        </span>
                                                    </div>
                                                );
                                            }}
                                        </FormItem>
                                    </Row>
                                );
                            })}
                        </TableForm>
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={handleClose}>关闭</Button>
                    <Button bsStyle="primary" onClick={submit} preventDuplicateClick>
                        确定
                    </Button>
                </Modal.Footer>
            </Modal>
        </Fragment>
    );
};

export default AddAddressModal;
