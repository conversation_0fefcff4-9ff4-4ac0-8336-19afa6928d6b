/*
 *@(#) ListBlock.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2024
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2024-07-17
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import TagTitle from '@/pages/enterpriseArchives/routes/Detail/components/TagTitle';
import Service from '@/services/Service';
import { useService } from '@share/framework';
import ShareList, { Column, NumberColumn, useList } from '@share/list';
import React, { Fragment, useCallback, useState } from 'react';
import NoData from '@/components/NoData/NoData';
import classNames from 'classnames';
import { useMount } from 'ahooks';
import styles from './ListBlock.scss';

const ListBlock = ({
    title,
    showCount = true,
    columns,
    url,
    params,
    numberColumn = true,
    linesPerPage = 10,
    dataAdaptor,
    uniqKey = 'id',
    TableComponent,
    noPaging, // 参数是否要用分页格式
    usePageBar = true, // 是否使用分页条
    frontendPaging, // 是否前端分页
}) => {
    const services = useService(Service);
    const [listData, setListData] = useState([]);

    const getData = async ({ page, data }) => {
        let res = null;
        res = await services.getDetailCommonData(
            url,
            noPaging
                ? params
                : {
                      page: {
                          ...page,
                          linesPerPage,
                      },
                      data: {
                          ...data,
                          ...params,
                      },
                  }
        );

        if (dataAdaptor) {
            res = dataAdaptor(res);
        }

        return res;
    };

    const listState = useList({
        dataSource: frontendPaging ? listData : getData,
    });
    const { totalPage, totalNum, currentPage } = listState.page;

    const TableComponentRender = useCallback(({ api: { list } }) => {
        if (list.length === 0) {
            return <NoData />;
        }

        return list.map((v) => <TableComponent {...v} />);
    }, []);

    useMount(async () => {
        if (frontendPaging) {
            const res = await getData({});

            setListData(res);
        }
    });

    return (
        <div>
            <TagTitle
                title={
                    <Fragment>
                        {title}
                        {title && showCount && <span>{totalNum}</span>}
                    </Fragment>
                }
            />
            <div className="shareListStyleCover">
                {TableComponent ? (
                    <ShareList
                        listState={listState}
                        usePageBar={false}
                        emptyText={<NoData />}
                        uniqKey={uniqKey}
                        TableComponent={TableComponentRender}
                    />
                ) : (
                    <ShareList listState={listState} usePageBar={false} emptyText={<NoData />} uniqKey={uniqKey}>
                        {numberColumn && <NumberColumn />}
                        {columns.map((item) => {
                            return <Column render={(val) => val || '-'} align="center" {...item} />;
                        })}
                    </ShareList>
                )}
                {usePageBar && (
                    <div className={styles.pageBar}>
                        <span className={styles.pageBarCount}>
                            共{totalPage}页/{totalNum}条数据
                        </span>
                        <span className={styles.pageBarCurrent}>当前第{currentPage}页</span>
                        <span
                            className={classNames(styles.pageBarBtn, {
                                [styles.disabled]: currentPage === 1,
                            })}
                            onClick={() => {
                                if (currentPage !== 1) {
                                    listState.jump(currentPage - 1);
                                }
                            }}
                        >
                            &lt;
                        </span>
                        <span
                            className={classNames(styles.pageBarBtn, {
                                [styles.disabled]: totalPage === currentPage,
                            })}
                            onClick={() => {
                                if (currentPage !== totalPage) {
                                    listState.jump(currentPage + 1);
                                }
                            }}
                        >
                            &gt;
                        </span>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ListBlock;
