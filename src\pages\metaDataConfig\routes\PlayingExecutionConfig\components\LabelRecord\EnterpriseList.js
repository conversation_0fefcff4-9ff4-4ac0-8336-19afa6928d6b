import React, { useState } from 'react';
import { Panel, Icon, Button } from '@share/shareui';
import { ShareList, Column, CheckColumn, useList, ActionColumn } from '@share/list';
import MetaFieldApi from '@/services/MetaFieldApi';
import { useService } from '@share/framework';
import { Input, message } from 'antd';
import styles from './LabelRecord.scss';

const { Search } = Input;

const EnterpriseList = ({ handleToggleEnterpriseList, queryIds }) => {
    const services = useService(MetaFieldApi);
    const requestData = {
        requestData: {
            ...queryIds,
            qymc: '',
        },
    };
    const listState = useList({
        dataSource: (condition) => {
            return services.getTagEnterpriseList(condition);
        },
        uniqKey: 'id',
        autoLoad: queryIds?.sourceId ? requestData : false,
    });

    const onSearch = (value) => {
        console.log('value', value);
        if (!value) {
            listState.query({
                qymc: '',
                ...queryIds,
            });

            return;
        }
        listState.query({
            qymc: value,
            ...queryIds,
        });
    };

    const onRemove = async (row) => {
        await services.removeTagEnterprise(row.id);
        message.success('移除成功');
        listState.selected.clear();
        listState.refresh();
    };

    const onBatchRemove = async () => {
        await services.batchRemoveTagEnterprise(listState.selected.value);
        message.success('移除成功');
        listState.refresh();
    };

    return (
        <div className={styles.EnterpriseList}>
            <Panel>
                <Panel.Head
                    title="企业列表"
                    extra={
                        <div className={styles['EnterpriseList-right']}>
                            <Search
                                allowClear
                                placeholder="请输入"
                                onSearch={onSearch}
                                style={{
                                    width: 200,
                                }}
                            />
                            {listState.selected.value?.length > 0 && (
                                <span className={styles.zksq} onClick={onBatchRemove}>
                                    批量移除
                                </span>
                            )}
                            <span
                                className={styles.zksq}
                                onClick={() => {
                                    handleToggleEnterpriseList(false);
                                }}
                            >
                                收起
                            </span>
                        </div>
                    }
                />
                <Panel.Body>
                    <ShareList listState={listState}>
                        <CheckColumn canSelected={(row) => row.delInd === '0'} />
                        <Column field="qymc" label="企业名称" />
                        <Column field="tyshxydm" label="统一社会信用代码" />
                        <Column field="linkTime" label="打标时间" />
                        <Column field="delInd" label="本批标签状态" render={(v) => (v === '0' ? '生效' : '不生效')} />
                        <ActionColumn align="center" width={70}>
                            <ActionColumn.Action label="移除" use={(row) => row.delInd === '0'} onClick={onRemove} />
                        </ActionColumn>
                    </ShareList>
                </Panel.Body>
            </Panel>
        </div>
    );
};
export default EnterpriseList;
