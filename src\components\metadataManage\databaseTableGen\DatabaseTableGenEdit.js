import React, { Component } from 'react';
// 工具类
import * as formRule from '@/components/common/common/formRule';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
// 组件
import { Modal, Button } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';

const { Form, Row, Input, Select, Textarea } = getComponents();

const defaultBody = {
    id: '',
    columnName: '',
    columnType: '',
    columnSize: [],
    columnComment: '',
};

class DatabaseTableGenEdit extends Component {
    state = {
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
    };

    componentWillReceiveProps(nextProps) {
        if (!this.props.show && nextProps.show) {
            const { column } = nextProps.data;
            const { editForm } = this.state;

            editForm.setFormData({ ...defaultBody, ...column });
            editForm.cleanValidError();
        }
    }

    submit = async () => {
        const { successFn } = this.props;
        const { editForm } = this.state;

        if (FormVaildHelper.isValid(await editForm.valid())) {
            successFn && successFn(editForm.getFormData());
        }
    };

    render() {
        const {
            show,
            cancelFn,
            data: { columnTypeOptions, list, systemFields },
        } = this.props;
        const { editForm } = this.state;
        const data = editForm.getFormData();
        const existColumnName = list.filter((item) => item.id !== data.id).map((item) => item.columnName);

        return (
            <Modal show={show} onHide={cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>字段信息编辑</Modal.Header>
                <Modal.Body full>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <Input
                                label="字段代码"
                                field="columnName"
                                rule={[
                                    formRule.checkRequiredNotBlank(),
                                    formRule.checkLength(1, 64),
                                    formRule.checkRegex(/\w+/, '只能由“a-z”、”A-Z“、”0-9“、”_"组成'),
                                    formRule.checkFunctionByTiming((value) => !existColumnName.includes(value), '已存在，请勿重复定义', 1),
                                    formRule.checkFunctionByTiming((value) => !systemFields.includes(value), '为系统字段，禁止定义', 1),
                                ]}
                                required
                            />
                        </Row>
                        <Row>
                            <Select
                                label="字段类型"
                                field="columnType"
                                rule={formRule.checkRequiredNotBlank()}
                                required
                                options={columnTypeOptions}
                                onChange={() => {
                                    editForm.setFieldValue('columnSize', []);
                                }}
                            />
                        </Row>
                        {data.columnType === 'text' && (
                            <Row>
                                <Input
                                    label="字段长度"
                                    field="columnSize.0"
                                    type="number"
                                    rule={[formRule.checkRequiredNotBlank(), formRule.checkIsInteger(), formRule.checkNumRange(1)]}
                                    required
                                />
                            </Row>
                        )}
                        {/* { */}
                        {/*     data.columnType === 'integer' && */}
                        {/*     <Row> */}
                        {/*         <Input */}
                        {/*             label="整数长度" field="columnSize.0" type="number" */}
                        {/*             rule={[ */}
                        {/*                 formRule.checkRequiredNotBlank(), */}
                        {/*                 formRule.checkIsInteger(), */}
                        {/*                 formRule.checkNumRange(4, 20), */}
                        {/*             ]} required */}
                        {/*         /> */}
                        {/*     </Row> */}
                        {/* } */}
                        {data.columnType === 'decimal' && (
                            <Row>
                                <Input
                                    label="整数长度"
                                    field="columnSize.0"
                                    type="number"
                                    rule={[formRule.checkRequiredNotBlank(), formRule.checkIsInteger(), formRule.checkNumRange(1, 65)]}
                                    required
                                />
                            </Row>
                        )}
                        {data.columnType === 'decimal' && (
                            <Row>
                                <Input
                                    label="小数长度"
                                    field="columnSize.1"
                                    type="number"
                                    rule={[formRule.checkRequiredNotBlank(), formRule.checkIsInteger(), formRule.checkNumRange(1, 30)]}
                                    required
                                />
                            </Row>
                        )}
                        <Row>
                            <Textarea
                                label="说明描述"
                                field="columnComment"
                                rule={formRule.checkRequiredNotBlank()}
                                required
                                options={columnTypeOptions}
                            />
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        确认
                    </Button>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default DatabaseTableGenEdit;
