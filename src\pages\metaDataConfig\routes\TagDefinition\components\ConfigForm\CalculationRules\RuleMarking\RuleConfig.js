import React, { Fragment } from 'react';
import * as FormComponents from '@share/shareui-form';
import styles from './RuleMarking.scss';
import RuleItem from './RuleItem';

const { SearchForm, Row, Input, RadioGroup, Calendar, Select } = FormComponents;

const RuleConfig = ({ form, state, event }) => {
    return (
        <SearchForm autoLayout={false} formState={form}>
            <Row>
                <Input field="rule.taskName" label="任务名称" search-col={6} rule="required" />
                <Calendar
                    field="rule.executeTime"
                    label="执行时间"
                    search-col={6}
                    format={{
                        data: 'YYYY-MM-DD HH:mm:ss',
                        display: 'YYYY-MM-DD HH:mm:ss',
                    }}
                    rule="required"
                />
            </Row>
            <Row>
                <Select field="rule.executeFrequency" label="执行频率" search-col={6} rule="required" options="executeFrequencyEnum" />
                <RadioGroup
                    field="rule.status"
                    label="任务状态"
                    options={[
                        { label: '启用', value: '1' },
                        { label: '停用', value: '0' },
                    ]}
                    search-col={6}
                    rule="required"
                />
            </Row>
            <div className={styles.RuleConfig}>
                <RuleItem form={form} state={state} formKey="rule.rule" event={event} />
                <div>说明：满足以上逻辑关系，则在对应的企业贴上标签，其中运算主体对象为企业中台所有的企业。</div>
            </div>
        </SearchForm>
    );
};
export default RuleConfig;
