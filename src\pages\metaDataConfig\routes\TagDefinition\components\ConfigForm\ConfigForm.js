import React, { forwardRef, useImperativeHandle } from 'react';
import { Panel, ButtonToolBar, Button } from '@share/shareui';
import {
    TableForm,
    Row,
    FormItem,
    // 表单项组件
    Input,
    Textarea,
    Select,
    Selector,
} from '@share/shareui-form';
import useHooks, { useCodeMap } from './hooks';
import CalculationRules from './CalculationRules';
import styles from './ConfigForm.scss';

const ConfigForm = (props) => {
    const { DM_TAG_QUERY_SCOPE, DM_TAG_STYLE } = useCodeMap();
    const { form, formValid } = useHooks(props);
    const optionsWithCount = DM_TAG_STYLE.map((item) => {
        const [bgColor, color] = item.value ? item.value.split('-') : [];
        const styleObj = {
            backgroundColor: bgColor,
            color,
            border: `1px solid ${color}`,
            borderRadius: '4px',
            padding: '2px 8px',
            cursor: 'pointer',
            display: 'inline-block',
        };

        return {
            value: item.value,
            label: <span style={styleObj}>{item.label}</span>,
        };
    });

    return (
        <Panel className={styles.ConfigForm}>
            <Panel.Head title="标签编辑" />
            <Panel.Body>
                <TableForm formState={form}>
                    <Row>
                        <Input field="name" label="标签名称" rule="required" maxLength={50} />
                    </Row>
                    <Row>
                        <Select multiple options={DM_TAG_QUERY_SCOPE} field="queryRange" label="使用范围" rule="required" />
                    </Row>
                    <Row>
                        <Selector multiple={false} field="tagStyle" rule="required" label="标签样式" options={optionsWithCount} />
                    </Row>
                    <Row>
                        <Textarea field="description" label="标签描述" rows={5} maxLength={100} />
                    </Row>
                    {props?.selectNode?.id?.includes('新建标签') ? (
                        ''
                    ) : (
                        <Row>
                            <FormItem field="calculationRules" label="计算规则">
                                {(fieldProps) => {
                                    return <CalculationRules {...props} form={form} />;
                                }}
                            </FormItem>
                        </Row>
                    )}
                </TableForm>
                <ButtonToolBar>
                    <Button
                        type="button"
                        onClick={() => {
                            form.reset();
                        }}
                    >
                        取消
                    </Button>
                    <Button type="submit" bsStyle="primary" onClick={formValid}>
                        保存
                    </Button>
                </ButtonToolBar>
            </Panel.Body>
        </Panel>
    );
};
export default ConfigForm;
