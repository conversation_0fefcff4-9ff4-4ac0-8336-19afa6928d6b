import React from 'react';
// 表码管理器
const bmManager = require('@/components/common/business/manager/BmManager');

const getBmComponent = (Component, defaultProps) => {
    return class extends React.Component {
        static defaultProps = {
            labelKey: 'label', // label属性键
            valueKey: 'code', // value属性键
            handlerFn: (data) => data, // 数据处理函数
            ...defaultProps,
        };

        formatOptions = (data) => {
            const { labelKey, valueKey } = this.props;

            return data.map((i) => ({ label: i[labelKey], value: i[valueKey] || i.value || '' }));
        };

        render() {
            const { options, data, bmName, labelKey, valueKey, handlerFn, ...restProps } = this.props;
            const convertOptions = options || this.formatOptions(handlerFn(data || bmManager.getBmList(bmName)));

            return <Component options={convertOptions} {...restProps} />;
        }
    };
};

export default getBmComponent;
