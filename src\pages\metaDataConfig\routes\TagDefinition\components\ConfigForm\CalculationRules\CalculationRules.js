import React, { useState } from 'react';
import { Radio } from 'antd';
// import dayjs from 'dayjs';
import styles from './CalculationRules.scss';
import ManuallyLabel from './ManuallyLabel';
import RuleMarking from './RuleMarking';
import ScriptLabel from "@/pages/metaDataConfig/routes/TagDefinition/components/ConfigForm/CalculationRules/ScriptLabel";

const CalculationRules = ({ selectNode, form }) => {
    const [radioValue, setRadioValue] = useState('sddb');
    const handleChangeRadio = (e) => {
        console.log('radio checked', e.target.value);
        setRadioValue(e.target.value);
    };

    return (
        <div className={styles.CalculationRules}>
            <Radio.Group onChange={handleChangeRadio} value={radioValue} className={styles['CalculationRules-radioGroup']}>
                <Radio value="sddb">手动打标</Radio>
                <Radio value="gzdb">规则打标</Radio>
                <Radio value="jbdb">脚本打标</Radio>
            </Radio.Group>
            {
                radioValue === 'sddb' ? <ManuallyLabel selectNode={selectNode} /> :
                radioValue === 'gzdb' ? <RuleMarking form={form} selectNode={selectNode} /> :
                <ScriptLabel form={form} selectNode={selectNode} />
            }
        </div>
    );
};

export default CalculationRules;
