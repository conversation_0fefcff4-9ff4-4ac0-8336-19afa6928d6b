.addressList {
    &-item {
        display: flex;
        align-items: center;
        .addAddress {
            margin-right: 0;
            background-color: transparent;
            margin-left: 16px;
            color: #1677ff;
            cursor: pointer;
            i {
                line-height: inherit;
                font-size: 16px;
            }
        }
    }
}

.addRealityVenue,
.addVenue {
    display: flex;
    align-items: center;
    justify-content: space-between;
    > div {
        flex: 1;
    }
    > span {
        margin-right: 0;
        background-color: transparent;
        margin-left: 16px;
        color: #1677ff;
        cursor: pointer;
        width: 28px;
    }
}
.addRealityVenue {
    > div {
        width: calc(100% - 50px);
    }
    > span {
        width: 28px;
    }
}
.red {
    color: red;
}
