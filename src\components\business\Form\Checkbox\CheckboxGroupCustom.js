import React, { Component } from 'react';
import { CheckboxGroup } from '@share/shareui';

function coverToArray(data) {
    if (Array.isArray(data)) {
        return data;
    }
    if (typeof data === 'string' && data !== '') {
        return data.split(',');
    }

    return [];
}

class CheckboxGroupCustom extends Component {
    static defaultProps = {
        // 基础属性
        options: [],
        labelKey: 'label',
        valueKey: 'value',
        value: [],
        onChange: (value) => console.log(value),
        // 全选
        startAllChecked: false,
        allCheckedLabel: '全部',
        allCheckedValue: 'allCheckedValue',
        // 反选
        startReverseChecked: false,
        reverseCheckedLabel: '反选',
        reverseCheckedValue: 'reverseCheckedValue',
    };

    constructor(props) {
        super(props);
        if (!props.startAllChecked) {
            this.state = { allCheckedFlag: false, reverseCheckedFlag: false };

            return;
        }
        const valueArray = coverToArray(this.props.value);
        const options = this.props.options || [];

        this.state = {
            allCheckedFlag: this.isAllCheck(valueArray, options, this.props.valueKey),
            reverseCheckedFlag: false,
        };
    }

    componentWillReceiveProps(nextProps) {
        const { value, options, startAllChecked, startReverseChecked, valueKey } = nextProps;

        if (startAllChecked || startReverseChecked) {
            const valueArray = coverToArray(value);

            if (valueArray.length === 0) {
                this.setState({ allCheckedFlag: false, reverseCheckedFlag: false });
            } else {
                this.setState({ allCheckedFlag: this.isAllCheck(valueArray, options, valueKey) });
            }
        }
    }

    onChange = (dataArrChecked, operateOption) => {
        const { options, valueKey, onChange, startAllChecked, allCheckedValue, startReverseChecked, reverseCheckedValue } = this.props;
        const { allCheckedFlag, reverseCheckedFlag } = this.state;
        let keyArray;
        let newAllCheckedFlag;
        let newReverseCheckedFlag = reverseCheckedFlag;

        if (startAllChecked && operateOption[valueKey] === allCheckedValue) {
            // 选中项为全选且当前已全选，效果为全不选
            if (allCheckedFlag) {
                keyArray = [];
            } else {
                // 选中项为全选且当前未全选，效果为全选
                keyArray = options.map((item) => item[valueKey]);
            }
            newAllCheckedFlag = !allCheckedFlag;
        } else if (startReverseChecked && operateOption[valueKey] === reverseCheckedValue) {
            // 选中项为反选,则效果为取反
            keyArray = options
                .filter((item) => !dataArrChecked.some((checkOption) => item[valueKey] === checkOption[valueKey]))
                .map((item) => item[valueKey]);
            newAllCheckedFlag = this.isAllCheck(keyArray, options, valueKey);
            newReverseCheckedFlag = !reverseCheckedFlag;
        } else {
            // 选中项为普通项（既不是全选也不是反选）
            keyArray = dataArrChecked
                .map((item) => item[valueKey])
                .filter((item) => item !== allCheckedValue && item !== reverseCheckedValue);
            newAllCheckedFlag = this.isAllCheck(keyArray, options, valueKey);
            newReverseCheckedFlag = reverseCheckedFlag;
        }
        this.setState(
            { allCheckedFlag: newAllCheckedFlag, reverseCheckedFlag: newReverseCheckedFlag },
            () => onChange && onChange({ target: { value: keyArray } })
        );
    };

    isAllCheck = (keyArray, options, valueKey) => {
        return keyArray.length >= options.length && options.every((option) => keyArray.includes(option[valueKey]));
    };

    render() {
        const { labelKey, valueKey } = this.props;
        const {
            value: keys,
            options,
            startAllChecked,
            allCheckedLabel,
            allCheckedValue,
            startReverseChecked,
            reverseCheckedLabel,
            reverseCheckedValue,
            ...restProps
        } = this.props;
        const { allCheckedFlag, reverseCheckedFlag } = this.state;
        const newOptions = [...options];
        const keyArray = coverToArray(keys);
        const valueArray = options.filter((item) => keyArray.includes(item[valueKey]));

        // 处理选择项
        startReverseChecked && newOptions.unshift({ [labelKey]: reverseCheckedLabel, [valueKey]: reverseCheckedValue });
        startAllChecked && newOptions.unshift({ [labelKey]: allCheckedLabel, [valueKey]: allCheckedValue });
        // 处理选中项
        reverseCheckedFlag && valueArray.unshift({ [labelKey]: reverseCheckedLabel, [valueKey]: reverseCheckedValue });
        allCheckedFlag && valueArray.unshift({ [labelKey]: allCheckedLabel, [valueKey]: allCheckedValue });

        return <CheckboxGroup {...restProps} options={newOptions} value={valueArray} onChange={this.onChange} />;
    }
}

export default CheckboxGroupCustom;
