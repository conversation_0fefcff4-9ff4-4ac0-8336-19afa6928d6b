$timelineMarginB: 12px;

.container {
    :global {
        .ant-timeline-item-head-blue {
            background-color: #1890ff;
        }

        .ant-timeline-item-head {
            transform: scale(1.2, 1.2);
        }

        .ant-timeline-item-tail {
            top: 15px;
            border-left: 2px solid #1890ff;
            height: calc(100% - 20px)
        }

        .ant-timeline-item[data-isYear='true'] {
            .ant-timeline-item-head {
                transform: scale(1.5, 1.5);
            }
        }
    }

    @at-root .leftContent {
        padding: 0 24px;
        width: 290px;

        .name {
            width: 100%;
            margin-top: 28px;
            font-size: 20px;
            font-weight: bold;
            line-height: 1.3;
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            cursor: pointer;

            &:hover {
                color: #1677ff;
            }
        }

        .infoBox {
            margin-top: 32px;

            .infoItem {
                .label {
                    color: #8c8c8c;
                }

                .value {
                    margin: 12px 0 28px;
                    font-size: 16px;
                }
            }
        }
    }

    @at-root .rightContent {
        margin: 24px;

        @at-root .cardBox {
            display: flex;

            .cardLeft {
                padding: 20px 22px 22px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                width: 220px;
                height: 96px;
                background: url(@/assets/images/policySupport/supportNumBg.png) no-repeat center center;
                background-size: 100% 100%;
                border-radius: 4px;

                .value {
                    font-size: 24px;
                    color: #1677ff;
                }
            }

            .cardRight {
                display: flex;
                // flex-grow: 1; // 撑满整屏
                margin-left: 16px;
                border-radius: 4px;
                border: 1px solid #ffe4e4;
                background-color: #fffdf8;

                .supportMoneyCard {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    width: 220px;
                    height: 96px;
                    padding: 20px 22px 22px;
                    background: url(@/assets/images/policySupport/supportMoneyBg.png) no-repeat center center;
                    background-size: 100% 100%;

                    .value {
                        font-size: 24px;
                        color: #ff9c23;
                    }
                }

                .supportDetailCard {
                    min-width: 360px;
                    flex-grow: 1;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: space-between;
                    padding: 18px 24px;

                    .detailItem {
                        width: 50%;

                        .label {
                            color: #8c8c8c;
                        }
                    }
                }
            }
        }

        @at-root .supportTitleDesc {
            margin-left: 16px;
            font-weight: normal;
            font-size: 14px;
        }

        @at-root .timelineBox {
            margin-top: 12px;

            @at-root .yearBox {
                margin-bottom: $timelineMarginB;
                position: relative;
                top: -6px;
                padding: 0 16px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 36px;
                background-image: linear-gradient(270deg, rgba(22, 119, 255, 0.02) 0%, rgba(22, 119, 255, 0.1) 100%);
                border-radius: 2px;

                .yearLeft {
                    .yearTitle {
                        font-size: 16px;
                        font-weight: bold;
                    }

                    .yearDesc {
                        margin-left: 16px;
                    }
                }

                .yearRight {
                    user-select: none;
                    color: #1677ff;
                    cursor: pointer;
                }
            }

            @at-root .dayBox {
                .approvedContent {
                    margin-top: 8px;
                    margin-bottom: $timelineMarginB;
                    padding: 12px;
                    border: solid 1px #d4dff1;
                    border-radius: 2px;

                    .approvedTitleBox {
                        display: flex;
                        align-items: center;

                        .approvedTitle {
                            max-width: 700px;
                            font-weight: bold;
                        }
                    }

                    .policyBasis {
                        margin-top: 12px;
                        display: flex;

                        .label {
                            flex-shrink: 0;
                        }
                    }

                    .approvedDetail {
                        display: flex;
                        margin-top: 12px;

                        >div {
                            width: 33%;
                            margin-right: 12px;
                        }

                        .deptShow {
                            display: flex;

                            .deptContent {
                                max-width: 70%;
                            }
                        }
                    }
                }
            }
        }

        .more {
            display: flex;
            justify-content: center;
            color: #a1abb9;

            >span {
                padding: 2px;
                user-select: none;
                cursor: pointer;
            }
        }
    }
}

.blue {
    color: #1677ff;
}

.listBox {
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #F0F7FF;
    padding: 12px 16px;

    &+& {
        margin-top: 8px;
    }
}

.listItem {
    // width: 20%;
    padding-right: 8px;
    display: flex;
    align-items: center;
    flex: 1;

    span:first-child {
        color: rgba(0, 0, 0, 0.5);
        flex-shrink: 0;
    }

    span:last-child {
        cursor: pointer;
        font-weight: bold;
        word-break: break-all;

        &:hover,
        &.active {
            color: #1890ff;
        }
    }
}

.totalTrigger {
    cursor: pointer;

    &:hover {
        color: #1890ff!important;
    }
}