import React, { useState } from 'react';

import { useKeyPress } from 'ahooks';
import { Button } from '@share/shareui';
import styles from './LabelingManagement.scss';

const Search = ({
    onSearch = () => {
        console.log('onSearch');
    },
    placeholder = '输入个人关键词进行搜索',
}) => {
    const [searchVal, setSearchVal] = useState('');
    useKeyPress(
        'enter',
        () => {
            onSearch();
        },
        {
            target: document.getElementById('keyword'),
        }
    );

    return (
        <div className={styles.searchBar}>
            <span className={styles.inputBox}>
                <input id="keyword" value={searchVal} onChange={(e) => setSearchVal(e.target.value)} placeholder={placeholder} />
                <i className="si si-com_search" />
            </span>
            <Button
                bsSize="large"
                bsStyle="primary"
                onClick={() => {
                    if (onSearch) {
                        onSearch(searchVal);
                    }
                }}
            >
                查询
            </Button>
        </div>
    );
};
export default Search;
