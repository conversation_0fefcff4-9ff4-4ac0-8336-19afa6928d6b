// 工具类
import * as Entity from '@/components/common/common/Entity';
// 组件
import Table from './ServicePageTable';

const tableCacheData = {};

class CacheServicePageTable extends Table {
    static defaultProps = {
        ...Table.defaultProps,
        namespace: 'namespace', // 缓存key
    };

    // 初始化组件，从缓存中获取数据
    componentWillMount = () => {
        const {
            service: { body },
            onChange,
            namespace,
        } = this.props;
        const cacheData = this.getCacheData(namespace);
        const tableData = cacheData || { ...this.state, param: { ...body } };

        this.setState({ ...tableData, loading: true }, this.refreshTable);
        if (cacheData) {
            onChange && onChange(cacheData);
        }
    };

    // 搜索参数发生变化时（点击搜索按钮事件），当前页置为1
    componentWillReceiveProps = (nextProps) => {
        const { namespace, service: { api } = {} } = this.props;
        const {
            namespace: newNamespace,
            service: { api: newApi, body: newBody },
        } = nextProps;
        const {
            loading,
            param,
            page: { pageSize },
        } = this.state;

        if (loading) {
            return;
        }
        if (namespace !== newNamespace || api !== newApi || !Entity.isEqualObject(param, newBody)) {
            this.setState({ loading: true, param: newBody, page: { current: 1, pageSize } }, this.refreshTable);
        }
    };

    // 表格数据变化
    onTableDataChange = (data) => {
        const { namespace, onTableDataChange } = this.props;

        this.saveCacheData(namespace, data);
        onTableDataChange && onTableDataChange(data);
    };

    // 缓存数据
    saveCacheData = (namespace, data) => {
        tableCacheData[namespace] = data;
    };

    // 获取缓存数据
    getCacheData = (namespace) => {
        return tableCacheData[namespace];
    };
}

export default CacheServicePageTable;
