import { useForm } from '@share/shareui-form';
import { useSetState, useMount } from 'ahooks';
import { Modal } from 'antd';
import { deleteSpace } from '@/utils/index';
import { useList } from '@share/list';
import { useServiceMap } from '../../hooks';

const defaultSearchBody = {
    componentId: '',
    componentTypes: [],
    fieldCode: '',
    defaultValue: '',
};

const useLogic = (props) => {
    const { metaFieldList, refreshDataFn } = props;

    const { metaFieldApi } = useServiceMap();
    const [formData, form] = useForm({ ...defaultSearchBody });
    const [state, setState] = useSetState({
        fieldComponentList: [],
        showEditModal: false,
        showDetailModal: false,
        operateData: {},
    });
    const convertRuleList = (dataList) => {
        return dataList.reduce((result, item) => {
            const editParam = {
                ...item.editParam,
                field: item,
                fieldCode: item.fieldCode,
                componentType: 'editParam',
                id: `${item.fieldCode}_editParam`,
            };
            const queryParam = {
                ...item.queryParam,
                field: item,
                fieldCode: item.fieldCode,
                componentType: 'queryParam',
                id: `${item.fieldCode}_queryParam`,
            };
            const listParam = {
                ...item.listParam,
                field: item,
                fieldCode: item.fieldCode,
                componentType: 'listParam',
                id: `${item.fieldCode}_listParam`,
            };
            const detailParam = {
                ...item.detailParam,
                field: item,
                fieldCode: item.fieldCode,
                componentType: 'detailParam',
                id: `${item.fieldCode}_detailParam`,
            };

            return [...result, editParam, queryParam, listParam, detailParam];
        }, []);
    };
    const filterDataList = ({
        data,
        page,
        params = {
            componentId: '',
            componentTypes: [],
            fieldCode: '',
            defaultValue: '',
        },
    }) => {
        const { componentId, componentTypes, fieldCode, defaultValue } = params;
        // const pageSize =
        const { currentPage, linesPerPage } = page;
        let filterList = data;

        if (componentId) {
            filterList = filterList.filter((item) => item.componentId === componentId);
        }
        if (Array.isArray(componentTypes) && componentTypes.length > 0) {
            filterList = filterList.filter((item) => componentTypes.includes(item.componentType));
        }
        if (fieldCode) {
            filterList = filterList.filter((item) => item.field.fieldCode === fieldCode);
        }
        if (defaultValue) {
            filterList = filterList.filter((item) => item.defaultValue && item.defaultValue.includes(defaultValue));
        }
        const list = filterList.slice((currentPage - 1) * linesPerPage, currentPage * linesPerPage);

        return {
            list,
            page: {
                ...page,
                totalNum: filterList.length,
            },
        };
    };
    const listState = useList({
        uniqKey: 'id',
        dataSource: async (condition) => {
            console.log('condition', condition);
            const res = await filterDataList({
                params: condition.data,
                data: convertRuleList(metaFieldList),
                page: condition.page,
            });
            console.log('res', res);
            console.log('metaFieldList', metaFieldList);

            return res;
        },
        autoLoad: true,
    });
    // 获取组件
    const getFieldComponentList = async () => {
        const fieldComponentList = await metaFieldApi.fieldComponents();

        setState({ fieldComponentList });
    };

    const handleReset = () => {
        form.reset();
    };
    const handleSearch = (data) => {
        const body = deleteSpace(data);

        listState.query(body);
    };

    const saveComponent = async (component) => {
        const config = metaFieldList.find((item) => item.fieldCode === component.fieldCode);

        config[component.componentType] = component;
        await metaFieldApi.saveTableMeta([config]);
        if (refreshDataFn) {
            refreshDataFn();
        }
        setState({ showEditModal: false });
    };

    const handleShowEditModal = (bool, data) => {
        setState({ showEditModal: bool, operateData: data });
    };
    useMount(() => {
        getFieldComponentList();
    });

    return {
        state,
        form,
        handleSearch,
        handleReset,
        saveComponent,
        handleShowEditModal,
        listState,
    };
};

export default useLogic;
