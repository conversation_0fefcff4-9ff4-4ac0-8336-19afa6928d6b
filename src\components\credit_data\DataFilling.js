import React, { Component, Fragment } from 'react';
// 样式
import classnames from 'classnames';
import styles from '@/pages/metaData/styles/index.scss';
// 服务接口
import * as MetaCategoryApi from '@/services/data/meta/MetaCategoryApi';
import * as DataSaveApi from '@/services/data/data/DataSaveApi';
// 工具类
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 组件
import Edit from '@/components/business/metadata/Edit';
import { antdMessage } from '@/components/ui/MyAlert/MyAlert';
import { Panel, ButtonToolBar, Button } from '@share/shareui';
import { FormState } from '@/components/business/Form';

class DataFilling extends Component {
    state = {
        // 主键键名
        primaryKey: '',
        // 配置参数
        metaConfig: [],
        editConfigList: [],
        // 编辑参数
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm, checkErrorLevel: '' }, callback)),
        // 校验数据
        checkData: null,
        // 错误级别
        checkErrorLevel: '',
        // 联合校验错误信息
        composeCheckErrorMsg: [],
    };

    // 请求配置信息
    componentDidMount = async () => {
        const { categoryId } = this.props.match.params;
        // 获取目录上报表配置
        const metaConfig = await MetaCategoryApi.metaConfig(categoryId);
        // 获取主键
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfig);
        // 过滤获取编辑配置
        const editConfigList = MetaConfigUtils.filterEditConfig(metaConfig);

        this.setState({ primaryKey, editConfigList, metaConfig }, this.initData);
    };

    // 初始化数据
    initData = () => {
        const { editConfigList, editForm } = this.state;

        editForm.setFormData(MetaConfigUtils.getEditDefaultData(editConfigList));
    };

    // 重置数据
    resetData = () => {
        this.initData();
        this.setState({
            checkErrorLevel: '',
            composeCheckErrorMsg: [],
        });
    };

    // 提交Api
    submitApi = (ignoreSuspectedError, ignoreQuestionData) => {
        const { categoryId } = this.props.match.params;
        const { editForm } = this.state;
        const data = editForm.getFormData();

        return DataSaveApi.addFromPage(categoryId, data, ignoreSuspectedError, ignoreQuestionData);
    };

    // 提交成功
    afterSuccess = () => {
        this.cancel();
    };

    // 提交上报
    submit = async (ignoreSuspectedError, ignoreQuestionData = true) => {
        const { editForm, primaryKey } = this.state;

        // 表单校验
        if (!FormVaildHelper.isValid(await editForm.valid())) {
            FormVaildHelper.scrollToError();

            return;
        }
        // 上报
        const result = await this.submitApi(ignoreSuspectedError, ignoreQuestionData);
        const { checkData, checkSuccess, checkLevel, checkFailMsg = {} } = result;

        // 上报成功
        if (checkSuccess || !ignoreQuestionData) {
            antdMessage.success('操作成功');
            this.afterSuccess();

            return;
        }
        // 错误信息回显
        Object.entries(checkFailMsg)
            .filter(([key, value]) => key && value)
            .forEach(([key, value]) => {
                editForm.setValidError(key, Array.isArray(value) ? value.join('；') : value);
            });
        FormVaildHelper.scrollToError();
        this.setState({
            checkData,
            checkErrorLevel: checkLevel,
            composeCheckErrorMsg: Array.isArray(checkFailMsg[primaryKey]) ? checkFailMsg[primaryKey] : [],
        });
        // 仅存疑似错误
        if (checkLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspected) {
            antdMessage.warn('部分内容存在疑问，请检查后确认');

            return;
        }
        // 仅存暂缓错误
        if (checkLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspend) {
            antdMessage.warn('部分校验暂时无法通过，请检查后暂存或修改后重新提交');

            return;
        }
        // 其他错误
        antdMessage.error('数据校验不通过，请修改后重新提交');
    };

    // 取消
    cancel = () => {
        const { history } = this.props;

        history.go(-1);
    };

    render() {
        const { editConfigList, editForm, checkErrorLevel, composeCheckErrorMsg } = this.state;

        return (
            <Fragment>
                <Panel>
                    <Panel.Head title="数据填报" />
                    <Panel.Body full>
                        <div
                            className={classnames({
                                [styles.isError]: composeCheckErrorMsg.length !== 0,
                            })}
                        >
                            <Edit formState={editForm} editType="add" metadataConfigList={editConfigList} />
                        </div>
                        {composeCheckErrorMsg.length !== 0 && (
                            <div className={`${styles.pl20} input-hasTip has-error`}>
                                {composeCheckErrorMsg.map((item, index) => (
                                    <p className="text text-tip pull-left" key={index}>
                                        <i className="fa fa-times-circle" />
                                        {item}
                                    </p>
                                ))}
                            </div>
                        )}
                    </Panel.Body>
                </Panel>
                <ButtonToolBar>
                    <Button type="button" bsSize="large" onClick={this.cancel}>
                        取消
                    </Button>
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspend && (
                        <Button type="button" bsSize="large" bsStyle="warning" onClick={() => this.submit(true, false)}>
                            暂缓上报
                        </Button>
                    )}
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                        <Button type="button" bsSize="large" bsStyle="warning" onClick={() => this.submit(false, false)}>
                            疑问暂存
                        </Button>
                    )}
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                        <Button type="button" bsSize="large" bsStyle="success" onClick={() => this.submit(true, true)}>
                            确认
                        </Button>
                    )}
                    {checkErrorLevel !== MetaConfigUtils.RULE_CHECK_LEVEL.suspend &&
                        checkErrorLevel !== MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                            <Button
                                type="button"
                                bsSize="large"
                                bsStyle="primary"
                                onClick={() => this.submit(false, true)}
                                disabled={editConfigList.length === 0}
                            >
                                提交
                            </Button>
                        )}
                </ButtonToolBar>
            </Fragment>
        );
    }
}

export default DataFilling;
