import React, { Component } from 'react';
// styles
import '../../style/Pagination.scss';
import PopModal from '@/components/ui/PopModal';
import PageSizeSelect from './PageSizeSelect/PageSizeSelect';

class Pagination extends Component {
    static defaultProps = {
        defaultCurrent: 1,
        defaultPageSize: 10,
        maxPageCount: null,
        current: 1,
        pageSize: 10,
        showTotal: true,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 30],
        showFirstJumper: true,
        showLastJumper: true,
        onChange: (page, pageSize) => console.log(page, pageSize),
    };

    handleChange = ({ pageSize, current }) => {
        const { onChange } = this.props;

        onChange && onChange(current, pageSize);
    };

    render() {
        const {
            defaultCurrent,
            defaultPageSize,
            current = defaultCurrent,
            pageSize = defaultPageSize,
            total,
            showTotal,
            pageSizeOptions,
            showSizeChanger,
            showFirstJumper,
            showLastJumper,
            maxPageCount,
            style,
        } = this.props;
        const totalPageCount = Math.ceil(total / pageSize);
        const pageCount = maxPageCount ? (maxPageCount > totalPageCount ? totalPageCount : maxPageCount) : totalPageCount;

        return (
            <div className="share-pagination_container" style={style}>
                <div className="share-pagination_item share-pagination_total__info">
                    {showTotal && <div className="share-pagination_total__item share-pagination_total">共有{total}条</div>}
                    {showSizeChanger && (
                        <div className="share-pagination_total__item share-pagination_pagesize">
                            <PopModal
                                content={(open, close) => (
                                    <PageSizeSelect
                                        pageSizeOptions={pageSizeOptions}
                                        onChange={(v) => {
                                            this.handleChange({ pageSize: v, current: 1 });
                                            close();
                                        }}
                                    />
                                )}
                            >
                                <p className="share-select_value">{`每页${pageSize || defaultPageSize}条`}</p>
                            </PopModal>
                        </div>
                    )}
                </div>
                <div className="share-pagination_item share-pagination_actions">
                    {showFirstJumper && (
                        <p
                            className={`share-pagination_actions__action cursorPointer ${current !== 1 ? '' : 'disabled'}`}
                            onClick={() => current !== 1 && this.handleChange({ pageSize, current: 1 })}
                        >
                            首页
                        </p>
                    )}
                    <p
                        className={`share-pagination_actions__action cursorPointer ${current > 1 ? '' : 'disabled'}`}
                        onClick={() => current > 1 && this.handleChange({ pageSize, current: current - 1 })}
                    >
                        上一页
                    </p>
                    <p className="share-pagination_actions__action share-pagination_actions__currentPage" style={{ cursor: 'default' }}>
                        {`${current}/${pageCount}`}
                    </p>
                    <p
                        className={`share-pagination_actions__action cursorPointer ${current < pageCount ? '' : 'disabled'}`}
                        onClick={() => current < pageCount && this.handleChange({ pageSize, current: current + 1 })}
                    >
                        下一页
                    </p>
                    {showLastJumper && (
                        <p
                            className={`share-pagination_actions__action cursorPointer ${current !== pageCount ? '' : 'disabled'}`}
                            onClick={() => current !== pageCount && this.handleChange({ pageSize, current: pageCount })}
                        >
                            尾页
                        </p>
                    )}
                </div>
            </div>
        );
    }
}

export default Pagination;
