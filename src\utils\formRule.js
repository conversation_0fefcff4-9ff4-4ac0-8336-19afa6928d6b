import { Timing } from '@share/shareui-form';
import * as ValidUtils from './ValidUtils';
import * as IdCard from './IdCard';
import * as IdUtil from './IdUtil';

/**
 * 校验函数
 */
function checkFunctions(...checkParams) {
    return (value, formData, field, timing, otherProps) => {
        if (timing === Timing.submit) {
            for (const { fn, errMsg } of checkParams) {
                if (!fn(value, formData, field, timing, otherProps)) {
                    return new Error(`${otherProps.label}${errMsg}`);
                }
            }

            return true;
        }

        return true;
    };
}

function checkFunction(fn, errMsg, customErrorMessage) {
    return (value, formData, field, timing, otherProps) => {
        if (timing === Timing.submit) {
            if (!fn(value, formData, field, timing, otherProps)) {
                if (customErrorMessage) {
                    return new Error(`${errMsg}`);
                }

                return new Error(`${otherProps.label}${errMsg}`);
            }

            return true;
        }

        return true;
    };
}

function checkFunctionByTiming(fn, errMsg, customTiming, customErrorMessage) {
    return (value, formData, field, timing, otherProps) => {
        if (timing === customTiming) {
            if (!fn(value, formData, field, timing, otherProps)) {
                if (customErrorMessage) {
                    return new Error(`${errMsg}`);
                }

                return new Error(`${otherProps.label}${errMsg}`);
            }
        }

        return true;
    };
}

function checkFunctionsByTiming(customTiming, ...checkParams) {
    return (value, formData, field, timing, otherProps) => {
        if (timing === customTiming) {
            for (const { fn, errMsg } of checkParams) {
                if (!fn(value, formData, field, timing, otherProps)) {
                    return new Error(`${otherProps.label}${errMsg}`);
                }
            }

            return true;
        }

        return true;
    };
}

/**
 * 校验正则
 */
function checkRegex(regex, errMsg, customErrorMessage) {
    return checkFunction((value) => !value || new RegExp(regex).exec(value), errMsg, customErrorMessage);
}

/**
 * 校验正则（触发类型）
 */
function checkRegexByTiming(regex, errMsg, timing) {
    return checkFunctionByTiming((value) => !value || new RegExp(regex).exec(value), errMsg, timing);
}

/**
 * 校验长度
 */
function checkLength(min, max) {
    return checkFunctions(
        {
            fn: (value) => !value || !Number.isFinite(min) || value.toString().length >= min,
            errMsg: `长度不能小于${min}字`,
        },
        {
            fn: (value) => !value || !Number.isFinite(max) || value.toString().length <= max,
            errMsg: `长度不能超过${max}字`,
        }
    );
}
function checkLengthByTiming(customTiming, min, max) {
    return checkFunctionsByTiming(
        customTiming,
        {
            fn: (value) => !value || !Number.isFinite(min) || value.toString().length >= min,
            errMsg: `长度不能小于${min}字`,
        },
        {
            fn: (value) => !value || !Number.isFinite(max) || value.toString().length <= max,
            errMsg: `长度不能超过${max}字`,
        }
    );
}

/**
 * 校验数组
 */
function checkArray(min, max, isArray = true) {
    return checkFunctions(
        {
            fn: (value) => !isArray || Array.isArray(value),
            errMsg: '不能为空',
        },
        {
            fn: (value) => !value || !Number.isFinite(min) || value.length >= min,
            errMsg: `不能小于${min}条`,
        },
        {
            fn: (value) => !value || !Number.isFinite(max) || value.length <= max,
            errMsg: `不能超过${max}条`,
        }
    );
}

/**
 * 校验文件数组
 */
function checkFileArray(min, max, isArray = true) {
    return checkFunctions(
        {
            fn: (value) => !isArray || Array.isArray(value),
            errMsg: '不能为空',
        },
        {
            fn: (value) => !value || !Number.isFinite(min) || value.filter((file) => file.name && file.url).length >= min,
            errMsg: `不能小于${min}条`,
        },
        {
            fn: (value) => !value || !Number.isFinite(max) || value.filter((file) => file.name && file.url).length <= max,
            errMsg: `不能超过${max}条`,
        }
    );
}

/**
 * 校验范围时间
 */
function checkRangDate(isCheckBegin, isCheckEnd, isCheckBeginBeforeEnd) {
    return checkFunctions(
        {
            fn: (value) => !isCheckBegin || (value && value.start),
            errMsg: '起始时间不能为空',
        },
        {
            fn: (value) => !isCheckEnd || (value && value.end),
            errMsg: '结束时间不能为空',
        },
        {
            fn: (value) => !isCheckBeginBeforeEnd || !value || !value.start || !value.end || value.start <= value.end,
            errMsg: '起始时间不能晚于结束时间',
        }
    );
}

/**
 * 校验数字组成
 */
function checkNumString() {
    return checkRegex(/^(\d)+$/, '只支持0-9数字组成');
}

/**
 * 校验整数(正/负整数)
 */
function checkIsInteger() {
    return checkFunction((value) => !value || ValidUtils.checkIsInteger(value), '请输入正确的整数');
}

/**
 * 校验正整数
 */
function checkIsPositiveInteger() {
    return checkFunction((value) => !value || ValidUtils.checkIsPositiveInteger(value), '请输入正确的正整数');
}

/**
 * 校验数字(正/负 整数/小数)
 */
function checkIsNum() {
    return checkFunction((value) => !value || ValidUtils.checkIsNum(value), '请输入正确的数字格式（如：111、11.1）');
}

/**
 * 校验数字范围(正/负 整数/小数)
 */
function checkNumRange(min, max) {
    return checkFunctions(
        {
            fn: (value) => !value || /(?!((^-?0+(\d)+$)|(^-?0{2,}\..*$)))((^-?(\d)+$)|(^-?(\d)+\.(\d)+$))/.test(value || ''),
            errMsg: '请输入正确的数字格式（如：111、11.1）',
        },
        {
            fn: (value) => Number.isNaN(Number.parseFloat(value)) || !Number.isFinite(min) || Number.parseFloat(value) >= min,
            errMsg: `请输入不小于${min}的数字`,
        },
        {
            fn: (value) => Number.isNaN(Number.parseFloat(value)) || !Number.isFinite(max) || Number.parseFloat(value) <= max,
            errMsg: `请输入不大于${max}的数字`,
        }
    );
}

/**
 * 校验非空（去除前后空格）
 */
function checkRequiredNotBlank() {
    return checkFunction((value) => ValidUtils.checkNotBlank(value), '不能为空');
}

/**
 * 校验富文本内容非空
 */
function checkRichTextNotBlank() {
    return checkFunction((value) => ValidUtils.checkRichTextNotBlank(value), '不能为空');
}

/**
 * 校验联合非空
 */
function checkUnionNotBlank(keys, errMsg) {
    return checkFunction((value, formData) => keys.some((key) => ValidUtils.checkNotBlank(formData[key])), errMsg);
}

/**
 * 校验证件号码
 */
function checkIdCardNum() {
    return checkFunction((value) => !value || IdCard.checkIdCard(value), '格式错误');
}

/**
 * 校验身份证格式
 */
function checkIdNum() {
    return checkFunction((value) => !value || IdCard.checkCard(value), '格式错误');
}

/**
 * 校验统一社会信用代码
 */
function checkCreditCode() {
    return checkFunction((value) => !value || IdUtil.isCreditCodeExact(value), '格式错误');
}

/**
 * 校验简单统一社会信用代码
 */
function checkSimpleCreditCode() {
    return checkRegex(/^(?!0{18})(?=[^IOSVZ]{18})[0-9A-Y]{2}\d{6}[0-9A-Y]{8}[0-9X][0-9A-Y]$/, '格式错误');
}

/**
 * 校验组织机构代码
 */
function checkOrgCodeExact() {
    return checkFunction((value) => !value || IdUtil.isOrgCodeExact(value), '格式错误');
}

/**
 * 校验注册号
 */
function checkBusinessLicenseExact() {
    return checkFunction((value) => !value || IdUtil.isBusinessLicenseExact(value), '格式错误');
}

/**
 * 校验电话号码
 */
function checkTel() {
    return checkFunction(
        (value) => !value || ValidUtils.checkTel(value),
        '请输入正确的11位手机号码，或含区号的固定电话（要求格式为：区号-固定电话）'
    );
}

/**
 * 校验手机号码
 */
function checkMobile() {
    return checkFunction((value) => !value || ValidUtils.checkMobile(value), '请输入正确的11位手机号码');
}

/**
 * 校验邮箱
 */
function checkIsEmail() {
    return checkFunction((value) => !value || ValidUtils.checkIsEmail(value), '请输入正确的电子邮箱');
}

/**
 * 校验字符串由数组字母组成
 */
function checkEngString() {
    return checkRegex(/^[A-Za-z0-9_]+$/, '只支持英文和数字和_');
}

/**
 * 校验申请(复)函模板名称是否以.doc|.docx结尾
 * @param str
 * @returns {RegExpExecArray}
 */
function checkTemplateName() {
    return checkRegex(/^.*\.(doc|docx)$/, '模板名称必须以.doc或者.docx结尾');
}

/**
 * 校验函号
 * @returns {*}
 */
function checkLetterNo() {
    return checkFunctions(
        {
            fn: (val) => !val || /^.*〔.*〕.*/.test(val),
            errMsg: '需要包含“〔” 和 “〕” ，样例：大信干函〔2022〕24号',
        },
        {
            fn: (val) => {
                if (!val) {
                    return true;
                }
                const year = new Date().getFullYear();
                const reg = eval(`/^.*〔${year}〕.*/`);

                return reg.test(val);
            },
            errMsg: '未正确填写年度，样例：大信干函〔2022〕24号',
        },
        {
            fn: (val) => {
                if (!val) {
                    return true;
                }
                const year = new Date().getFullYear();
                const reg = eval(`/^[\u2E80-\u9FFF]{2,}〔${year}〕.*$/`);

                return reg.test(val);
            },
            errMsg: '未正确填写发函单位和文件类型，样例：大信干函〔2022〕24号',
        },
        {
            fn: (val) => {
                if (!val) {
                    return true;
                }
                const year = new Date().getFullYear();
                const reg = eval(`/^[\u2E80-\u9FFF]{2,}〔${year}〕\\d+[\u2E80-\u9FFF]+$/`);

                return reg.test(val);
            },
            errMsg: '未正确填写函件编码，样例：大信干函〔2022〕24号',
        }
    );
}

/**
 * 校验名称
 * （1）长度必须大于一个汉字或大于三个字符；校验不通过提示：认定部门名称必须大于一个汉字或大于三个字符。
 * （2）仅支持中文、英文、数字、“(”、“)”、“（”、“）”；校验不通过提示：联系人姓名仅支持中文、英文、数字、“(”、“)”、“（”、“）”。
 * （3）不得仅包含null、test、测试、无、没有、、“(”、“)”、“（”、“）”等词或特殊符号；校验不通过提示：联系人姓名不得仅包含null、test、测试、无、没有、暂无、“(”、“)”、“（”、“）”等词或特殊符号。
 * @returns {*}
 */
function checkName() {
    return checkFunctions(
        {
            fn: (val) => !val || /^(?=.*[\u4e00-\u9fa5]+.*)(^.{2,}$)|^(.{4,})$/.test(val),
            errMsg: '必须大于一个汉字或大于三个字符',
        },
        {
            fn: (val) => !val || /^[\u4e00-\u9fa5a-zA-Z0-9()（）·•]+$/.test(val),
            errMsg: '仅支持中文、英文、数字、“(”、“)”、“（”、“）“、“·“、“•“',
        },
        {
            // fn: val => !val || (/(?!^(null|test|测试|无|没有|暂无|\(|\)|（|）)+$)(^.*$)/).test(val.toLowerCase()),
            fn: (val) => !val || /(?!^(?:null|test){1,}|(?:测试|无|没有|暂无|\(|\)|(|)|（|）){1,}$)^(.*)/.test(val.toLowerCase()),
            errMsg: '不得仅包含null、test、测试、无、没有、暂无、“(”、“)”、“（”、“）”等词或特殊符号',
        }
    );
}
export {
    // 通用校验
    checkFunctions,
    checkFunction,
    checkFunctionByTiming,
    checkFunctionsByTiming,
    checkRegex,
    checkLength,
    checkLengthByTiming,
    checkArray,
    checkFileArray,
    checkRangDate,
    checkRegexByTiming,
    // 数字校验
    checkNumString,
    checkIsInteger,
    checkIsPositiveInteger,
    checkIsNum,
    checkNumRange,
    // 非空校验
    checkRequiredNotBlank,
    checkRichTextNotBlank,
    checkUnionNotBlank,
    // 证件号码校验
    checkIdCardNum,
    checkIdNum,
    checkCreditCode,
    checkSimpleCreditCode,
    checkOrgCodeExact,
    checkBusinessLicenseExact,
    // 电话校验
    checkTel,
    checkMobile,
    // 邮箱校验
    checkIsEmail,
    // 英文校验
    checkEngString,
    // 申请(复)函模板名称校验
    checkTemplateName,
    // 函号校验
    checkLetterNo,
    checkName,
    // 检验触发枚举
    Timing,
};
