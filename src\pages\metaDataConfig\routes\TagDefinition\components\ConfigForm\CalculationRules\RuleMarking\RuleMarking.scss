.displayFlex {
    display: flex;
    align-items: center;
}

.ruleList {
    width: 100%;
    position: relative;

    &-head-select {
        display: flex;
        align-items: center;
    }
    &-head {
        &.compose {
            background-color: #f2f8ff;
            border: solid 1px #c3d8e1;
            margin-left: 0;
        }
        flex: 1;
        margin-bottom: 8px;
        padding: 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
        border: solid 1px #d9dadc;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 64px;
    }
    &-head-select {
        flex: 1;
    }
    &-children {
        margin-left: 46px;
    }
    &-btns {
        width: 60px;
        height: 42px;
        position: absolute;
        top: 4px;
        right: -2px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        z-index: 3;
        .add {
            color: #1677ff;
        }
        .remove {
            color: #f5222d;
        }
        .toggle {
            color: #8c8c8c;
        }
        i {
            margin-left: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 12px;
        }
    }
}

.RuleConfig {
    padding: 0;
    .RuleItem {
        padding: 5px;
        &.displayFlex {
            flex: 1;
        }
        :global {
            .Select {
                width: 120px !important;
                margin-right: 12px;
                margin-left: 0;
            }
        }
        &-group {
            @extend .displayFlex;
            // height: 52px;
            padding: 5px;
            &.w100 {
                width: 100%;
            }
            &_button {
                margin-right: 12px;
            }
        }
        &-rules {
            @extend .displayFlex;
            margin-left: 16px;
            flex: 1;
            // margin-top: 12px;
            // margin-bottom: 12px;
            &_categoryItem {
                @extend .displayFlex;
                flex: 1;
                // padding-right: 35px;

                .value {
                    > div {
                        width: 100%;
                        padding-left: 0;
                        padding-right: 0;
                        :global {
                            .item .sub-item {
                                padding-right: 12px;
                            }
                        }
                    }
                }
            }
            .btns {
                i {
                    font-size: 18px;
                    margin-left: 12px;
                    cursor: pointer;
                    &.add {
                        color: #1677ff;
                    }
                    &.remove {
                        color: red;
                    }
                }
            }
        }
    }
}
