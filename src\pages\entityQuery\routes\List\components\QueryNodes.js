import React from 'react';
import { Button } from '@share/shareui';
import { registerFormItem } from '@/utils/shareFormUtil';
import QueryNode from './QueryNode';
import style from './QueryNodes.scss';

const QueryNodes = (props) => {
    const { value, onChange, field, inventoryList } = props;

    const addComposeNode = (node) => {
        const nodes = [...(value || []), node];
        onChange({ target: { value: nodes } });
    };
    const deleteComposeNode = (i) => {
        const nodes = (value || []).filter((item, index) => index !== i);
        onChange({ target: { value: nodes } });
    };

    return (
        <div className={style.body}>
            <Button className={style.button} bsStyle="info" onClick={() => addComposeNode({})}>
                添加查询节点
            </Button>
            <div>
                {Array.isArray(value) &&
                    value.map((item, index) => {
                        const paramField = `${field}.${index}`;

                        return (
                            <QueryNode
                                key={paramField}
                                field={paramField}
                                label="查询单节点"
                                inventoryList={inventoryList}
                                queryNodeList={value}
                                noView
                                suffix={
                                    <div>
                                        <Button bsStyle="info" onClick={() => deleteComposeNode(index)}>
                                            删除
                                        </Button>
                                    </div>
                                }
                            />
                        );
                    })}
            </div>
        </div>
    );
};

export default registerFormItem(QueryNodes);
