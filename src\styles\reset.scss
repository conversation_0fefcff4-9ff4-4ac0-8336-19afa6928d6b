:global {
    .btn-lg {
        min-width: 72px;
    }

    .btn-primary {
        border-color: #1677ff !important;
        background: #1677ff;
    }

    .btn-primary:hover,
    .btn-primary.active.focus,
    .btn-primary.active:focus,
    .btn-primary.active:hover,
    .btn-primary:active.focus,
    .btn-primary:active:focus,
    .btn-primary:active:hover,
    .open>.dropdown-toggle.btn-primary.focus,
    .open>.dropdown-toggle.btn-primary:focus,
    .open>.dropdown-toggle.btn-primary:hover,
    .btn-primary.active,
    .btn-primary:active,
    .open>.dropdown-toggle.btn-primary,
    .btn-primary.focus,
    .btn-primary:focus {
        background-color: #0061E8 !important;
    }

    .ui-body {
        background: #fff;
    }

    .share-list-share_table_nav {
        background-color: transparent !important;
    }

    // formTable 样式覆盖
    .formTableStyleCover {
        .form-wrap .form-table>tbody>tr>th {
            background-color: #f2f8ff;
        }

        .form-wrap .item .labelItem {
            text-align: left;
            color: rgba(0, 0, 0, 0.85);

            &::after {
                content: '';
            }
        }

        .form-wrap .form-table>tbody>tr>th,
        .form-wrap .form-table>tbody>tr>td {
            border-color: #d2e4ff;
        }

        .form-table {
            border: 1px solid #d2e4ff;
        }
    }

    // sharelist 样式覆盖
    .shareListStyleCover {

        .share-list-share-table td,
        .share-list-share-table th {
            height: 44px!important;
        }

        .share-list-share-table th {
            background-color: #f2f8ff!important;
        }

        .share-list-share-table tr {
            border-top: 1px solid #d2e4ff!important;
            border-bottom: 1px solid #d2e4ff!important;
        }
        
        .icon{
            display: none;
        }
    }

    //  
    .navStyleCover {
        .nav-tabs>li>a {
            font-size: 16px;
            line-height: 34px;
            height: 42px;
            color: rgba(0, 0, 0, 0.85) !important;
            padding: 4px 0;
        }

        .nav-tabs>li {
            padding: 0 20px;
        }

        .nav-tabs>li.active>a {
            color: #1677ff !important;
            border-bottom: 2px solid #1677ff !important;
        }

        .nav-tabs {
            border-bottom: 1px solid #ebeced;
        }
    }

    .calendarRangeFix{
        .datetime-box-multiple{
            display: block;
            margin-bottom: -10px;
        }
        .form_datetime{
            margin-bottom: 10px;
        }
        .link_line{
            display: none!important;
        }
    }

    // 标题命中高亮样式
    .titleHighLight {
        color: #1677ff;
        padding: 0;
        background-color: #fff;
    }

}
