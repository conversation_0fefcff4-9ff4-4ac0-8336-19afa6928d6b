import React, { Fragment, useState } from 'react';

import { FormItem, FieldItem } from '@share/shareui-form';
import { useMount, useUpdateEffect } from 'ahooks';
import classNames from 'classnames';
import styles from './FormSelectList.scss';

const SelectItem = ({ item, value, onChange }) => {
    return (
        <div
            className={classNames(styles.itemBox, {
                [styles.active]: (value || []).includes(item.value),
            })}
            key={item.value}
            onClick={() => {
                if (value.includes(item.value)) {
                    onChange({
                        target: {
                            value: value.filter((v) => v !== item.value),
                        },
                    });
                } else {
                    onChange({
                        target: {
                            value: [...value, item.value],
                        },
                    });
                }
            }}
        >
            {item.label}
            <svg width="16.057" height="13.14" viewBox="0 0 16.057 13.14">
                <g id="选中" transform="translate(0.001 -0.001)">
                    <path id="_3" d="M12.056,13.141H0L16.056,0V9.14A4,4,0,0,1,12.056,13.141Z" fill="#09d" />
                    <path
                        id="si_si-com_correct-73"
                        data-name="si si-com_correct-73"
                        d="M2.687,5.831.15,3.295a.239.239,0,0,1,0-.335l.479-.472a.214.214,0,0,1,.164-.075.236.236,0,0,1,.171.075L2.687,4.211a.239.239,0,0,0,.335,0L6.043,1.189a.224.224,0,0,1,.164-.068.247.247,0,0,1,.171.068l.472.479A.239.239,0,0,1,6.85,2L3.021,5.831a.239.239,0,0,1-.335,0Z"
                        transform="translate(7.207 6.14)"
                        fill="#fff"
                    />
                </g>
            </svg>
        </div>
    );
};

const FormSelectList = ({ servicesName, field, label, formData, ...otherProps }) => {
    const [parentList, setParentList] = useState([]);
    const [childList, setChildList] = useState([]);
    // const listMap = new Map();
    const optionsWithCount = (bm) => {
        return bm.map((item) => {
            const [bgColor, color] = item.tagStyle ? item.tagStyle.split('-') : [];
            const styleObj = {
                backgroundColor: bgColor,
                color,
                border: `1px solid ${color}`,
                borderRadius: '4px',
                padding: '2px 8px',
                cursor: 'pointer',
                display: 'inline-block',
            };

            return {
                value: item.value,
                label: <span style={styleObj}>{item.label}</span>,
            };
        });
    };
    const getList = async (id) => {
        try {
            const dataSource = await servicesName(id || '');
            const data = (dataSource || []).map((item) => {
                return {
                    ...item,
                    label: item.name,
                    value: item.id,
                };
            });
            // if (id) {
            //     listMap.set(id, data);
            // }

            return data;
        } catch (error) {
            throw Error(error);
        }
    };
    const getChildList = async (id) => {
        const res = await getList(id);
        const childOptions = optionsWithCount(res);
        setChildList(childOptions);
    };

    useMount(() => {
        const getData = async () => {
            const res = await getList();
            const parentOptions = optionsWithCount(res);
            setParentList(parentOptions);
        };
        getData();
    });
    useUpdateEffect(() => {
        const ids = formData?.tagIds?.parentId?.join(',');
        getChildList(ids);
    }, [formData?.tagIds?.parentId]);

    return (
        <FieldItem label={label}>
            {() => {
                return (
                    <div className={`form-item ${styles.formItem}`}>
                        <label className=" label-item">{label}</label>
                        <FormItem {...otherProps} field={`${field}.parentId`} noView>
                            {(fieldProps) => {
                                console.log('fieldProps', fieldProps);
                                const { value = [], onChange } = fieldProps;

                                return (
                                    <div className={styles.SelectList}>
                                        <div className={styles['SelectList-item']}>
                                            {parentList.map((item) => {
                                                return <SelectItem key={item.value} item={item} value={value} onChange={onChange} />;
                                            })}
                                        </div>
                                    </div>
                                );
                            }}
                        </FormItem>
                        {formData?.tagIds?.parentId?.length > 0 && (
                            <FormItem {...otherProps} field={`${field}.childId`} noView>
                                {(fieldProps) => {
                                    console.log('fieldProps', fieldProps);
                                    const { value = [], onChange } = fieldProps;

                                    return (
                                        <div className={styles.SelectList}>
                                            <div className={styles['SelectList-item']}>
                                                {childList.map((item) => {
                                                    return <SelectItem key={item.value} item={item} value={value} onChange={onChange} />;
                                                })}
                                            </div>
                                        </div>
                                    );
                                }}
                            </FormItem>
                        )}
                    </div>
                );
            }}
        </FieldItem>
    );
};

export default FormSelectList;
