import Network from '@share/network';

import { FileUploader } from '@share/shareui-form';

export const InfoDataSource = {
    mode: FileUploader.MODE.INFO,
    upload: (file, callback) => {
        return Network.upload(`${window.SHARE.CONTEXT_PATH}api/file/upload.do`, file, callback);
    },
    downloadPath: (fileInfo, extraParams) => {
        const paramsObject = { fileId: fileInfo.fileId, ...(extraParams ?? {}) };
        const queryString = Object.entries(paramsObject).reduce((acc, item) => {
            const [key, val] = item;
            const itemString = `${key}=${val}`;

            if (acc === '') {
                return `?${itemString}`;
            }

            return `${acc}&${itemString}`;
        }, '');

        return `${window.SHARE.CONTEXT_PATH}api/file/file.do${queryString}`;
    },
};
