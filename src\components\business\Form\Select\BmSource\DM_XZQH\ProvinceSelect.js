import React, { Component } from 'react';
// 增强工具类
import { getBmComponent } from '../../../Custom/index';
// 被封装组件
import Select from '../../Select';

const BmSelect = getBmComponent(Select);

// 表码管理器
const bmManager = require('@/components/common/business/manager/BmManager');

class ProvinceSelect extends Component {
    state = {
        data: this.data || bmManager.getBmList('DM_XZQH').filter((item) => new RegExp('\\d{2}0000').exec(item.code)),
    };

    render() {
        const { data } = this.state;

        return <BmSelect {...this.props} data={data} />;
    }
}

export default ProvinceSelect;
