import React from 'react';
// 样式
import '../style/Upload.scss';
import { Upload } from 'antd';
import styles from '../style/ShareuiUpload.scss';
// 组件
import File from '../file/File';
import CommonFileUpload from './CommonFileUpload';

class ShareUiFileUpload extends CommonFileUpload {
    static defaultProps = {
        ...CommonFileUpload.defaultProps,
    };

    // 自定义删除函数
    handleDelete = (deleteFile, fileList) => {
        const arr = fileList.filter((item) => item.uid !== deleteFile.uid);

        this.handleChange({ fileList: arr });
    };

    render() {
        const { fileMaxNum, uploadButton, disabled, value, onClick } = this.props;
        const fileMaxNumber = Number.parseFloat(fileMaxNum) || 5;
        // 拷贝数据并为文件添加文根
        const fileList = this.copyDataAndAddRoot(value);
        const uploadProps = this.uploadProps();
        const showUploadButton = uploadButton || (
            <div className={styles.uploadBtn}>
                <i className="si si-com_plus" />
                点击上传
            </div>
        );

        return (
            <div className={styles.shareuiUpload}>
                <Upload {...uploadProps} showUploadList={false} fileList={fileList}>
                    {fileList.map((file, index) => (
                        <File
                            key={`${file.name}${index}`}
                            disabled={disabled}
                            file={file}
                            handleDelete={(deleteFile) => this.handleDelete(deleteFile, fileList)}
                        />
                    ))}
                    {!disabled && fileList.length < fileMaxNumber && <span onClick={onClick}>{showUploadButton}</span>}
                </Upload>
            </div>
        );
    }
}

export default ShareUiFileUpload;
