import { useForm } from '@share/shareui-form';

import { getService, useService } from '@share/framework';
import DataCenterService from '@/services/DataCenterService';
import MetaFieldApi from '@/services/MetaFieldApi';
import { useMount, useSetState } from 'ahooks';
import { withCache } from '@/utils/Cache';

const dataCenterService = getService(DataCenterService);
const getTableDataCache = withCache(dataCenterService.getTableData);

const useRuleMarkingHooks = ({ form, selectNode }) => {
    const metaFieldApi = useService(MetaFieldApi);
    const [state, setState] = useSetState({
        categoryList: [],
        tableData: {},
    });

    // 获取规则详情
    const getTagRuleDetail = async (id) => {
        const res = await metaFieldApi.personTagRuleDetail(id);
        form.setFieldValue('rule', res.rule || {});
    };
    // 获取数据类别
    const getCategoryListAll = async () => {
        const res = await dataCenterService.getCategoryListAll();
        const filterList = res.filter((item) => item.dataTableId);
        setState({
            categoryList: [{ name: '组合', id: 'combination' }, ...filterList],
        });
    };

    useMount(() => {
        getCategoryListAll();
        getTagRuleDetail(selectNode.id);
    });

    return {
        state,
        event: {
            getTableData: getTableDataCache,
        },
    };
};

export default useRuleMarkingHooks;
