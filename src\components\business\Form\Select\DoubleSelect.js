import React, { Component } from 'react';
import { getComponents } from '@share/shareui-form';

const { Select } = getComponents();

function formatOptions(data, labelKey, valueKey) {
    const newData = Array.isArray(data) ? data : [];

    return newData
        .filter((i) => i[labelKey] && i[valueKey])
        .map((i) => ({
            label: i[labelKey],
            value: i[valueKey],
        }));
}

class DoubleSelect extends Component {
    static defaultProps = {
        controlOptions: [],
        controlLabelKey: 'label',
        controlValueKey: 'value',
        autoControlFilter: false,
        options: [],
        labelKey: 'label',
        valueKey: 'value',
    };

    constructor(props) {
        super(props);
        const { value = '', options = [], valueKey, filterAttributeKey } = props;
        let controlValue = '';
        // 当被控制选框初始值不为空时，设置控制选框值初始值为被控制选框初始值的被控制属性值

        if (value !== '') {
            const targetOption = options.find((option) => option[valueKey] === value);

            controlValue = targetOption && targetOption[filterAttributeKey] ? targetOption[filterAttributeKey] : '';
        }
        this.state = { controlValue };
    }

    componentWillReceiveProps(nextProps) {
        const { value, options, valueKey, filterAttributeKey } = nextProps;

        // 当被控制选框不为空且控制选框值为空时，设置控制选框值为被控制选框值的被控制属性值
        if (this.state.controlValue === '' && value && value !== '') {
            const targetOption = options.find((option) => option[valueKey] === value);
            const controlValue = targetOption && targetOption[filterAttributeKey] ? targetOption[filterAttributeKey] : '';

            this.setState({ controlValue });
        }
    }

    onControlSelectChange = (e) => {
        const controlValue = e.target.value;

        this.checkAndDealValue(controlValue);
        this.setState({ controlValue });
    };

    checkAndDealValue = (controlValue) => {
        const { value, options, valueKey, filterAttributeKey, onChange } = this.props;

        // 如果控制选框和被控制选框值都不为空，检查被控制选框当前值的被过滤属性值是否为控制值
        if (controlValue !== '' && value && value !== '') {
            const valueOption = options.find((option) => option[valueKey] === value);

            // 如果不是则当前值被过滤，通知父组件清空当前值
            if (valueOption[filterAttributeKey] !== controlValue) {
                onChange({ target: { value: '' } });
            }
        }
    };

    render() {
        const {
            controlOptions,
            controlLabelKey,
            controlValueKey,
            autoControlFilter,
            options,
            labelKey,
            valueKey,
            filterAttributeKey,
            ...restProps
        } = this.props;
        const { controlValue } = this.state;
        // 获取控制下拉选框数据
        const newControlOptions = !autoControlFilter
            ? controlOptions
            : controlOptions.filter((controlOption) =>
                  options.some((option) => option[filterAttributeKey] === controlOption[controlValueKey])
              );
        // 格式化控制下拉框数据
        const coverControlOptions = formatOptions(newControlOptions, controlLabelKey, controlValueKey);
        // 过滤被控制下拉框数据
        const filterOptions = controlValue === '' ? options : options.filter((option) => option[filterAttributeKey] === controlValue);
        // 格式化被控制下拉框数据
        const coverOptions = formatOptions(filterOptions, labelKey, valueKey);

        return (
            <span>
                <Select.View options={coverControlOptions} value={controlValue} onChange={this.onControlSelectChange} inline />
                <Select.View options={coverOptions} {...restProps} inline />
            </span>
        );
    }
}

export default DoubleSelect;
