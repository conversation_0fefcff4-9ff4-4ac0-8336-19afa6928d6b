import React from 'react';
import { registerFormItem } from '@/utils/shareFormUtil';
import { Input, Select } from '@share/shareui-form';
import { DatePicker } from '@/components/form';
import style from './ConditionNode.scss';

const ConditionNode = (props) => {
    const { field, options } = props;

    return (
        <div className={style.body}>
            {options.map((item) => {
                const paramField = `${field}.${item.value}`;
                if (item.options) {
                    return (
                        <Select
                            key={paramField}
                            field={paramField}
                            label={item.label}
                            options={item.options}
                            multiple
                            placeholder={`请选择${item.label}`}
                            search-col={4}
                        />
                    );
                }
                if (item.value === 'YEAR') {
                    return <DatePicker key={paramField} field={paramField} label={item.label} format="YYYY" picker="year" search-col={4} />;
                }
                if (item.value === 'QUARTER') {
                    return (
                        <DatePicker
                            key={paramField}
                            field={paramField}
                            label={item.label}
                            format="YYYY\QQ"
                            picker="quarter"
                            search-col={4}
                        />
                    );
                }
                if (item.value === 'MONTH') {
                    return (
                        <DatePicker key={paramField} field={paramField} label={item.label} format="YYYYMM" picker="month" search-col={4} />
                    );
                }

                return <Input key={paramField} field={paramField} label={item.label} search-col={4} />;
            })}
        </div>
    );
};

export default registerFormItem(ConditionNode);
