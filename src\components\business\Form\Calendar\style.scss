.box {
    width: 100%;
    position: relative;
}

.trigger {
    background-color: #fff;
    padding: 0 8px;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    font-family: "Microsoft YaHei";
    //border-radius: 4px;
    color: #404348;
    box-shadow: none;
    border: 1px solid #d0d1d4;
    position: relative;

    i{
        color: #999;
        position: absolute;
        right: 8px;
        top: 7px;
    }
    :global {
        i.si-com_closethin{
            right: 24px;
            top: 11px;
            font-size: 12px;
        }
    }
}

.content {
    position: absolute;
    top: 42px;
    left: 0;
    right: 0;
    background-color: #fff;
    border-radius: 5px;
    z-index: 10;
    box-shadow: 1px 1px 10px #ccc;

    .item {
        padding: 12px 15px 10px;
        font-size: 14px;
        line-height: 14px;
        cursor: pointer;

        &:hover,
        &.active {
            background-color: #E8F3FB;
        }
    }
}

.custom {
    padding: 0 15px 10px;

    .text {
        padding: 12px 0 10px;
        font-size: 12px;
        line-height: 12px;
    }

    :global {
        .datetime-box {
            display: flex;
            flex-direction: column;
            width: 100%;

            .link_line {
                display: none;
            }

            .form_datetime {
                margin: 5px 0;
                width: 100%!important;
            }
        }
    }

    .rangeBox {
        display: flex;

        .left {
            width: 1em;
            line-height: 42px;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .right {
            flex: 1;
        }
    }

    .customBtn {
        text-align: right;


        span {
            color: #09d;
            cursor: pointer;
        }
    }
}
