$leftWidth: 400px;

.enterpriseArchivesList {
    display: flex;
    // transition: width 1s ease-in-out;

    &-warp {
        flex: 1;
        margin-left: 0;

        // transition: margin-left 1s ease-in-out;
        &.show {
            margin-left: $leftWidth;
        }
    }

    .searchBar {
        padding: 32px 0;
        text-align: center;
        border-bottom: 1px solid #ebeced;
        display: flex;
        align-items: center;
        justify-content: center;

        .advancedQueryBtn {
            margin-left: 12px;
            color: #09d;
            cursor: pointer;
        }

        input {
            // width: 640px;
            width: 100%;
            height: 40px;
            background-color: #ffffff;
            border-radius: 4px;
            border: solid 1px #d0d1d4;
            padding: 10px 12px 10px 34px;
            font-size: 14px;
            // margin-right: 8px;
            display: inline-block;
            outline: 0;

            &:focus {
                box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
                border-color: #09d;
            }
        }

        .inputBox {
            position: relative;
            display: inline-block;
            margin-right: 8px;
            width: 48%;
            max-width: 640px;

            i {
                position: absolute;
                left: 12px;
                top: 12px;
                color: #a4a5a9;
            }
        }
    }

    .toolBar {
        height: 32px;
        background-image: linear-gradient(90deg, #ebf3ff 0%, transparent 100%);
        border-radius: 4px;
        margin: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 17px;
        font-size: 14px;
    }

    .toolBarLeft {
        color: rgba(0, 0, 0, 0.85);
        display: flex;
        align-items: center;

        span {
            margin-left: 11px;
            color: #8c8c8c;
            display: flex;
            align-items: center;

            i {
                color: #1677ff;
                font-style: normal;
                padding: 0 4px;
            }
        }
    }

    .toolBarRight {
        display: flex;
        align-items: center;
    }

    .listBox {
        margin: 20px;
    }

    .listItem {
        padding: 18px 16px 16px;
        cursor: pointer;

        >div {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }

        border-bottom: 1px solid #ebeced;

        &:hover {
            background-color: rgba(22, 119, 255, 0.06);

            .title {
                color: #1677ff;
            }
        }
    }

    .listItemTitle {
        margin-bottom: 17px;

        .title {
            font-size: 16px;
            font-weight: bold;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.85);
            margin-right: 16px;
        }

        .parkLabel {
            color: #1677ff;
            background-color: #e4efff;
        }
    }

    .listItemInfo {
        flex-wrap: wrap;

        >div {
            width: 33%;
            padding-right: 12px;
            display: flex;
            align-items: center;
            line-height: 20px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: 8px;

            span {
                color: #8c8c8c;
                width: 80px;
            }
        }
    }

    .listItemTag {
        margin-top: 8px;

        >span {
            color: #8c8c8c;
            background-color: #f3f3f3;
            margin-right: 8px;
            margin-bottom: 8px;
        }
    }

    .titleHighLight {
        color: #1677ff;
        padding: 0;
    }
}

.companyTag {
    &-Popover {
        display: flex;
        align-items: stretch;

        &_parents,
        &_oneself {
            &--item {
                padding: 0 12px 0 12px;
            }
        }

        &_parents {
            padding-left: 0;

            &--item {
                position: relative;

                &::after {
                    content: '>';
                    position: absolute;
                    right: -5px;
                    color: rgba(0, 0, 0, 0.3);
                }
            }
        }

        &_children {
            position: relative;

            &::after {
                content: '>';
                position: absolute;
                left: 0;
                top: 0;
                color: rgba(0, 0, 0, 0.3);
            }
        }
    }
}

.expBtn {
    //  position: absolute;
    //  top: 10px;
    //  right: 10px;
    //  z-index: 9;
    color: #09d !important;

    :global {
        button {
            background: transparent;
            border: none;
            color: #09d;
        }
    }
}