import { vformGet } from '../network/Network';
import * as Base64 from './Base64';

export const fileResourceRoot = '/no_auth_upload_resource';

export const getSuffix = (str) => {
    if (!str || !str.includes('.')) {
        return '';
    }

    return str.replace(/^.*\.(.*)$/, '$1');
};

export const download = ({ url, name, resourceRoot = fileResourceRoot }) => {
    if (url.startsWith('http')) {
        return vformGet(url);
    }
    return vformGet(`${CONTEXT_PATH}${resourceRoot || ''}${url}`,  //eslint-disable-line
        name ? { name } : {}
    );
};

export const preview = ({ url, name, resourceRoot = fileResourceRoot }) => {
    const origin = window.location.origin || '';
    const previewUrl = url.startsWith('http') ? url: `${origin}${CONTEXT_PATH}${resourceRoot || ''}${url}`; // eslint-disable-line
    const param = encodeURIComponent(
        Base64.b64EncodeUnicode(`${previewUrl}${name ? `${previewUrl.includes('?') ? '&' : '?'}fullfilename=${name}` : ''}`)
    );
    const requestUrl = `${origin}/file_preview/onlinePreview?url=${param}`;

    return window.open(requestUrl);
};

export const manyImagePreview = ({ urls, resourceRoot = fileResourceRoot }) => {
    const origin = window.location.origin || '';
    const previewUrl = urls.map(url => (url.startsWith('http') ? url : `${origin}${CONTEXT_PATH}${resourceRoot || ''}${url}`)) // eslint-disable-line
        .join('|');
    const requestUrl = `${origin}/file_preview/picturesPreview?urls=${encodeURIComponent(Base64.b64EncodeUnicode(previewUrl))}`;

    return window.open(requestUrl);
};
