import React, { Component, Fragment } from 'react';
// 样式
import styles from '@/pages/metaData/styles/index.scss';
import { Form, Modal, Button } from '@share/shareui';
import styles2 from './EditCom/FormItem/FieldRule.scss';
// 列表组件
// 表码管理器
const bmManager = require('@/components/common/business/manager/BmManager');

const compareSymbolMap = {
    lt: '<',
    eq: '=',
    gt: '>',
};

const unitMap = {
    number: '数字',
    year: '年',
    month: '月',
    day: '天',
};

const combinationLogicMap = {
    and: '同时满足',
    or: '单个满足',
};

const comparisonSourceMap = {
    countryShxymComparisonSource: '名称查询统一社会信用代码国家接口',
    xmIntranetShxydmComparisonSource: '厦门-内网名称、统一社会信用代码比对接口',
};

const checkLevelMap = {
    S: '严重错误',
    0: '明确错误',
    1: '疑似错误',
    2: '暂缓错误',
};

const specialFieldCode = {
    currentDate: '变量：填报当天',
    identityFieldCode: '变量：重复字段',
    editFieldCode: '变量：编辑字段',
};

const catalogUserFieldCode = {
    'catalog.id': '目录：id（主键）',
    'catalog.catalogName': '目录：catalogName（名称）',
    'catalog.categoryCode': '目录：categoryCode（信息类别）',
    'catalog.objectType': '目录：objectType（主体类型）',
    'catalog.qualityType': '目录：qualityType（性质类型）',
    'catalog.openStyle': '目录：openStyle（开放属性）',
    'catalog.publicityPeriod': '目录：publicityPeriod（公示期限）',
    'catalog.deptId': '目录：deptId（资源提供方）',
    'catalog.deptName': '目录：deptName（资源提供方名称）',
    'catalog.deptAreaId': '目录：deptAreaId（资源提供方区域）',
    'catalog.status': '目录：status（状态）',
    'catalog.type': '目录：type（类型）',
    'catalog.dataTableId': '目录：dataTableId（数据表ID）',
};

class FieldRuleDetail extends Component {
    renderCheckParam = (rule, parentRule, ruleOptional, metaFieldList) => {
        const {
            ruleName,
            ruleType,
            ruleParam: {
                describe,
                preConditions = [],
                bmName,
                bmOptions,
                checkField,
                checkFields,
                comparisonSource,
                databaseTable,
                matchRegex,
                convertRegex,
                ignoreCase,
                compareSymbols,
                number,
                unit,
                combinationLogic,
                combinationRule = [],
                checkReverse,
                checkLevel,
                checkFailType,
                checkFailMsg,
                useScenes = [],
                sort,
            } = {},
        } = rule;
        const targetRule = ruleOptional.find((item) => item.ruleId === rule.ruleId) || {};
        const ruleParamFillKeys = Array.isArray(targetRule.ruleParamFillKeys) ? targetRule.ruleParamFillKeys : [];
        // const useSceneBmName = ruleType === '3' ? 'DM_CREDIT_APPLICATION_SCENE' : 'BM_OBJECT_TYPE';
        const tableFieldCode = metaFieldList.reduce((r, i) => {
            r[i.fieldCode] = `字段：${i.fieldCode}（${i.showLabel}）`;

            return r;
        }, {});

        return (
            <Fragment>
                <Form.Tr>
                    <Form.Label>规则名称</Form.Label>
                    <Form.Content>
                        <span className="textShow">{ruleName}</span>
                    </Form.Content>
                </Form.Tr>
                {ruleParamFillKeys.includes('describe') && (
                    <Form.Tr>
                        <Form.Label>规则描述</Form.Label>
                        <Form.Content>
                            <span className="textShow">{describe}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('preConditions') && Array.isArray(preConditions) && preConditions.length > 0 && (
                    <Form.Tr>
                        <Form.Label>前置条件</Form.Label>
                        <Form.Content>
                            <span className="textShow">
                                {preConditions.map(({ conditionField, matchRegex: conditionMatchRegex }) => {
                                    const showLabel =
                                        tableFieldCode[conditionField] || catalogUserFieldCode[conditionField] || `变量：${conditionField}`;

                                    return <div>{`${showLabel} 匹配正则 ${conditionMatchRegex}`}</div>;
                                })}
                            </span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('bmName') && (
                    <Form.Tr>
                        <Form.Label>系统码表</Form.Label>
                        <Form.Content>
                            <span className="textShow">{bmName}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('bmOptions') && (
                    <Form.Tr>
                        <Form.Label>码表配置</Form.Label>
                        <Form.Content>
                            {Array.isArray(bmOptions) && (
                                <Form pageType="detailPage">
                                    <Form.Table>
                                        {bmOptions.map((item) => (
                                            <Form.Tr>
                                                <Form.Label>{item.label}</Form.Label>
                                                <Form.Content>
                                                    <span className="textShow">{item.value}</span>
                                                </Form.Content>
                                            </Form.Tr>
                                        ))}
                                    </Form.Table>
                                </Form>
                            )}
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('checkField') && (
                    <Form.Tr>
                        <Form.Label>检验字段</Form.Label>
                        <Form.Content>
                            <span className="textShow">{checkField || '当前字段'}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('checkFields') && (
                    <Form.Tr>
                        <Form.Label>检验字段</Form.Label>
                        <Form.Content>
                            <span className="textShow">
                                {Array.isArray(checkFields) &&
                                    checkFields
                                        .map((item) => tableFieldCode[item] || specialFieldCode[item] || `常量：${item}`)
                                        .map((item) => <div>{item}</div>)}
                            </span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('comparisonSource') && (
                    <Form.Tr>
                        <Form.Label>比对源头</Form.Label>
                        <Form.Content>
                            <span className="textShow">{comparisonSourceMap[comparisonSource]}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('databaseTable') && (
                    <Form.Tr>
                        <Form.Label>匹配表名</Form.Label>
                        <Form.Content>
                            <span className="textShow">{databaseTable}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('matchRegex') && (
                    <Form.Tr>
                        <Form.Label>匹配正则</Form.Label>
                        <Form.Content>
                            <span className="textShow">{matchRegex}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('convertRegex') && (
                    <Form.Tr>
                        <Form.Label>转换正则</Form.Label>
                        <Form.Content>
                            <span className="textShow">{convertRegex}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('ignoreCase') && (
                    <Form.Tr>
                        <Form.Label>匹配敏感</Form.Label>
                        <Form.Content>
                            <span className="textShow">{ignoreCase ? '忽略大小写' : '区分大小写'}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('compareSymbols') && (
                    <Form.Tr>
                        <Form.Label>匹配类型</Form.Label>
                        <Form.Content>
                            <span className="textShow">
                                {Array.isArray(compareSymbols) && compareSymbols.map((one) => compareSymbolMap[one]).join('')}
                            </span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('number') && (
                    <Form.Tr>
                        <Form.Label>匹配数值</Form.Label>
                        <Form.Content>
                            <span className="textShow">{number}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('unit') && (
                    <Form.Tr>
                        <Form.Label>匹配单位</Form.Label>
                        <Form.Content>
                            <span className="textShow">{unitMap[unit]}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('combinationLogic') && (
                    <Form.Tr>
                        <Form.Label>组合逻辑</Form.Label>
                        <Form.Content>
                            <span className="textShow">{combinationLogicMap[combinationLogic]}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('combinationRule') && (
                    <Form.Tr>
                        <Form.Label>组合规则</Form.Label>
                        <Form.Content className={styles2.topBottomPadding}>
                            <span className="textShow">
                                {Array.isArray(combinationRule) &&
                                    combinationRule.length > 0 &&
                                    combinationRule.map((item) => (
                                        <Form pageType="detailPage" style={{ paddingBottom: '12px' }}>
                                            <Form.Table>{this.renderCheckParam(item, rule, ruleOptional, metaFieldList)}</Form.Table>
                                        </Form>
                                    ))}
                            </span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('checkReverse') && (
                    <Form.Tr>
                        <Form.Label>错误判定</Form.Label>
                        <Form.Content>
                            <span className="textShow">{checkReverse ? '符合规则' : '违反规则'}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('checkLevel') && !parentRule && (
                    <Form.Tr>
                        <Form.Label>错误级别</Form.Label>
                        <Form.Content>
                            <span className="textShow">{checkLevelMap[checkLevel]}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
                {ruleParamFillKeys.includes('checkFailType') &&
                    combinationLogic !== 'and' &&
                    (!parentRule || parentRule.ruleParam.combinationLogic === 'and') && (
                        <Form.Tr>
                            <Form.Label>错误类型</Form.Label>
                            <Form.Content>
                                <span className="textShow">{bmManager.getBmLabel('DM_DATA_VALID_ERROR_TYPE', checkFailType)}</span>
                            </Form.Content>
                        </Form.Tr>
                    )}
                {ruleParamFillKeys.includes('checkFailMsg') &&
                    combinationLogic !== 'and' &&
                    (!parentRule || parentRule.ruleParam.combinationLogic === 'and') && (
                        <Form.Tr>
                            <Form.Label>错误提示</Form.Label>
                            <Form.Content>
                                <span className="textShow">{checkFailMsg}</span>
                            </Form.Content>
                        </Form.Tr>
                    )}
                {/* {
                    ruleParamFillKeys.includes('useScenes') &&
                    !parentRule &&
                    <Form.Tr>
                        <Form.Label>适用范围</Form.Label>
                        <Form.Content>
                            <span className="textShow">
                                {
                                    Array.isArray(useScenes) &&
                                    useScenes.map(useScene => bmManager.getBmLabel(useSceneBmName, useScene)).join('；')
                                }
                            </span>
                        </Form.Content>
                    </Form.Tr>
                } */}
                {ruleParamFillKeys.includes('sort') && !parentRule && (
                    <Form.Tr>
                        <Form.Label>规则排序</Form.Label>
                        <Form.Content>
                            <span className="textShow">{sort}</span>
                        </Form.Content>
                    </Form.Tr>
                )}
            </Fragment>
        );
    };

    render() {
        const {
            data: { rule, ruleOptional, ruleTypeOptions, metaFieldList },
            show,
            cancelFn,
        } = this.props;
        const { fieldCode, ruleType } = rule;

        return (
            <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={cancelFn} backdrop="static">
                <Modal.Header closeButton>规则详情</Modal.Header>
                <Modal.Body>
                    <Form pageType="detailPage">
                        <Form.Table>
                            <Form.Tr>
                                <Form.Label>规则字段</Form.Label>
                                <Form.Content>
                                    <span className="textShow">{fieldCode}</span>
                                </Form.Content>
                            </Form.Tr>
                            <Form.Tr>
                                <Form.Label>规则类型</Form.Label>
                                <Form.Content>
                                    <span className="textShow">
                                        {(ruleTypeOptions.find((item) => item.value === ruleType) || {}).label}
                                    </span>
                                </Form.Content>
                            </Form.Tr>
                            {this.renderCheckParam(rule, null, ruleOptional, metaFieldList)}
                        </Form.Table>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default FieldRuleDetail;
