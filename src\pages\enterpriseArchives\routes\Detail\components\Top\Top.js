/*
 *@(#) Top.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-10
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React, { useCallback, useEffect, useState } from 'react';
import classNames from 'classnames';
import { fieldTranslate, moneyFormat } from '@/utils/format';
import { useCode, useQuery, useService } from '@share/framework';
import EnterpriseService from '@/services/EnterpriseService';
import { Loading, message } from '@share/shareui';
import { useUpdateEffect } from 'ahooks';
import { useMapStore } from '@/store/mapStore';
import { maskPhoneNumber } from '@/utils';
import { Select } from 'antd';
import LinkText from '../LinkText';
import g from '../../../../style.scss';
import s from './Top.scss';
import CompanyTag from './CompanyTag';

const Top = ({ detail, authExpressionMap }) => {
    const {
        zzmc,
        scztlx,
        accInfoList,
        tyshxydm,
        frdbrlxfs,
        frdbr,
        hydm,
        hyml,
        zczb,
        clrq,
        zjycnbygrs,
        qylxdm,
        djzt,
        zcdzyqmc,
        zcdzldmc,
        zcdz,
        enterpriseAddressList = [],
        jycsdz,
        jycsdzx,
        jycsdzy,
        zcdzzbx,
        zcdzzby,
        isFocus,
        zcdzyqbh,
        zjycnbcbrs,
        parkJumpUrl,
    } = detail || {};
    const [focusStatus, setFocusStatus] = useState(false);
    const [loading, setLoading] = useState(false);
    const [addressSelected, setAddressSelected] = useState('注册地址');
    const canTagAdd = authExpressionMap['economicbrain:qyarchives:tag:add'];
    const canTagEdit = authExpressionMap['economicbrain:qyarchives:tag:edit'];

    const { id } = useQuery();
    const { focusEnterprise, unFocusEnterprise } = useService(EnterpriseService);
    const handlerFocus = async () => {
        if (!loading) {
            setLoading(true);
            if (focusStatus) {
                await unFocusEnterprise({ creditCode: id });
                message.success('已取消关注！');
            } else {
                await focusEnterprise({ creditCode: id });
                message.success('已关注成功！');
            }
            setLoading(false);
        }

        setFocusStatus(!focusStatus);
    };
    const { ECONOMY_TYPE, INDUSTRY_CATEGORIES, BUSINESS_STATUS } = useCode('ECONOMY_TYPE', 'INDUSTRY_CATEGORIES', 'BUSINESS_STATUS');
    const { mapFunc, mapProperty } = useMapStore();

    const $enterpriseAddressList = enterpriseAddressList.map((item, index) => {
        return {
            ...item,
            label: item.jydz,
            // value: item.jydz,
            value: `实际经营地址${index + 1}`,
            x: item.jydzX,
            y: item.jydzY,
        };
    });
    const addressOptions = [
        {
            label: zcdz,
            // value: zcdz,
            value: '注册地址',
            x: zcdzzbx,
            y: zcdzzby,
        },
        {
            label: jycsdz,
            // value: jycsdz,
            value: '经营地址',
            x: jycsdzx,
            y: jycsdzy,
        },
    ].concat($enterpriseAddressList);

    const handleChangeAddress = (e) => {
        const selectedOptionData = addressOptions.find((item) => item.value === e);
        const { map } = mapProperty;
        const { addFeatureToLayer } = mapFunc;
        const layer = map.findLayerById('companyPointLayer');
        layer.destroy();
        addFeatureToLayer([
            {
                // x: detail.zcdzzbx,
                // y: detail.zcdzzby,
                x: selectedOptionData.x,
                y: selectedOptionData.y,
            },
        ]);
        setAddressSelected(e);
    };
    useEffect(() => {
        setFocusStatus(isFocus);
    }, [isFocus]);

    // eslint-disable-next-line consistent-return
    useUpdateEffect(() => {
        if (mapFunc) {
            const { addFeatureToLayer } = mapFunc;
            const layer = addFeatureToLayer([
                {
                    // x: detail.zcdzzbx,
                    // y: detail.zcdzzby,
                    x: detail.zcdzzbx,
                    y: detail.zcdzzby,
                },
            ]);

            return () => {
                console.log('sss');
                layer.destroy();
            };
        }
    }, [detail, mapFunc]);

    const newParkJumpUrl = useCallback(() => {
        if (!parkJumpUrl) {
            return `${window.SHARE.CONTEXT_PATH}parkManage.html#/detail?id=${zcdzyqbh}`;
        }
        const url = parkJumpUrl.replace(/{parkId}/g, zcdzyqbh);

        return url;
    }, [parkJumpUrl, zcdzyqbh]);

    return (
        <div className={s.topInfo}>
            <div className={s.topInfoLeft}>
                <div className={s.companyTitle}>
                    <div>
                        <span className={s.title}>{zzmc}</span>
                        <span className={`${g.baseLabel} ${s.titleLabel}`}>{fieldTranslate(djzt, BUSINESS_STATUS)}</span>
                    </div>
                    <div style={{ textAlign: 'center', flexShrink: 0 }}>
                        {loading ? (
                            <Loading style={{ marginRight: '35px' }} />
                        ) : (
                            <div />
                            // <div
                            //     className={classNames({
                            //         [g.active]: true,
                            //         [g.baseButton]: true,
                            //         [g.reverse]: focusStatus,
                            //     })}
                            //     onClick={handlerFocus}
                            // >
                            //     <i className={`si si-com_hearto ${g.heart}`} />
                            //     <span>{focusStatus ? '已关注' : '关注'}</span>
                            // </div>
                        )}
                    </div>
                </div>
                <CompanyTag accInfoList={accInfoList} />

                <div className={s.companyInfo}>
                    <div>
                        <span>统一社会信用代码</span>
                        {tyshxydm}
                    </div>
                    <div>
                        <span>联系方式</span>
                        {maskPhoneNumber(frdbrlxfs)}
                    </div>
                    <div>
                        <span>法定代表人</span>
                        {frdbr}
                    </div>
                    <div>
                        <span>行业门类</span>
                        {fieldTranslate(hyml, INDUSTRY_CATEGORIES)}
                    </div>
                    <div>
                        <span>注册资本</span>
                        {moneyFormat(zczb)}万元
                    </div>
                    <div>
                        <span>成立日期</span>
                        {clrq}
                    </div>
                    <div>
                        <span>就业/参保</span>
                        {!zjycnbygrs && !zjycnbcbrs
                            ? '--'
                            : `${zjycnbygrs ? Number(zjycnbygrs).toLocaleString() : '--'} / ${ 
                                  zjycnbcbrs ? Number(zjycnbcbrs).toLocaleString() : '--' 
                              }`}
                    </div>
                    <div>
                        <span>企业类型</span>
                        {fieldTranslate(scztlx, ECONOMY_TYPE)}
                    </div>
                    {/* <div> */}
                    {/*    <span>所在园区</span> */}
                    {/*    <LinkText */}
                    {/*        style={{ width: 'auto' }} */}
                    {/*        url={newParkJumpUrl()} */}
                    {/*        type="tab" */}
                    {/*        tabOption={{ key: 'zcdzyqbh', label: '园区详情' }} */}
                    {/*    > */}
                    {/*        {zcdzyqmc} */}
                    {/*    </LinkText> */}
                    {/* </div> */}
                    {/* <div> */}
                    {/*    <span>所在楼栋</span> */}
                    {/*    /!* <LinkText style={{ width: 'auto' }}> *!/ */}
                    {/*    {zcdzldmc} */}
                    {/*    /!* </LinkText> *!/ */}
                    {/*    /!* （{zcdz}） *!/ */}
                    {/* </div> */}
                </div>
            </div>
            <div className={s.topInfoRight}>
                {/* <div className={s.location}> */}
                {/*    <span> */}
                {/*        <i className="si si-com_position" /> */}
                {/*        {addressSelected}： */}
                {/*    </span> */}
                {/*    /!* {zcdz} *!/ */}
                {/*    <Select */}
                {/*        showSearch */}
                {/*        style={{ */}
                {/*            width: '100%', */}
                {/*        }} */}
                {/*        bordered={false} */}
                {/*        onChange={handleChangeAddress} */}
                {/*        filterOption={(input, option) => { */}
                {/*            return (option?.children ?? '').includes(input); */}
                {/*        }} */}
                {/*        defaultValue={addressOptions?.[0]?.value} */}
                {/*    > */}
                {/*        {addressOptions.map((d) => ( */}
                {/*            <Select.Option key={d.value}>{d.label}</Select.Option> */}
                {/*        ))} */}
                {/*    </Select> */}
                {/* </div> */}
            </div>
        </div>
    );
};

export default Top;
