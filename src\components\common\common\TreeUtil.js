/*
 * @(#) TreeUtil.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-12-23 16:41:37
 */
import React from 'react';

/**
 * 数组数据转成树状数据
 * @param array 资源列表数组
 * @param parentArray 目标列表数组
 * @param childrenPredicateFn 子元素判定（入参：父元素、子元素，出参：是否为父子关系）
 * @param childrenSortFn 子元数数组排序函数
 * @param treeDataGenFn 数状数据创建函数（入参：单节点、子节点数组，出参：树状节点）
 * @returns {Array} 树状数据
 */
export function arrayToTree1(array, parentArray, childrenPredicateFn, childrenSortFn, treeDataGenFn) {
    if (!Array.isArray(array) || array.length === 0) {
        return [];
    }
    if (childrenSortFn) {
        parentArray.sort(childrenSortFn);
    }

    return parentArray.map((item) => {
        const childrenArray = array.filter((one) => childrenPredicateFn(item, one));
        const childrenTree = arrayToTree1(array, childrenArray, childrenPredicateFn, childrenSortFn, treeDataGenFn);

        return treeDataGenFn(item, childrenTree);
    });
}

/**
 * 数组数据转成树状数据
 * @param array 资源列表数组
 * @param topPredicateFn 顶层元数判定函数（入参：元素，出参：是否顶层元数）
 * @param childrenPredicateFn 子元素判定（入参：父元素、子元素，出参：是否为父子关系）
 * @param childrenSortFn 子元数数组排序函数
 * @param treeDataGenFn 数状数据创建函数（入参：单节点、子节点数组，出参：树状节点）
 * @returns {Array} 树状数据
 */
export function arrayToTree2(array, topPredicateFn, childrenPredicateFn, childrenSortFn, treeDataGenFn) {
    if (!Array.isArray(array) || array.length === 0) {
        return [];
    }
    const topArray = array.filter((item) => topPredicateFn(item));

    return arrayToTree1(array, topArray, childrenPredicateFn, childrenSortFn, treeDataGenFn);
}

/**
 * 将树状数据摊平成数组
 * @param tree 树
 * @param childrenKey 子元素属性key
 * @returns {Array}
 */
export function treeToArray(tree, childrenKey = 'children') {
    if (!Array.isArray(tree) || tree.length === 0) {
        return [];
    }

    return tree
        .map((item) => [item, ...treeToArray(item[childrenKey], childrenKey)])
        .reduce((resultArr, one) => resultArr.concat(one), []);
}

/**
 * 将树状数据摊平成路径数组
 * @param tree 树
 * @param childrenKey 子元素属性key
 */
export function treeToPathArray(tree, childrenKey = 'children') {
    if (!Array.isArray(tree) || tree.length === 0) {
        return [];
    }

    return tree
        .map((item) =>
            !Array.isArray(item[childrenKey]) || item[childrenKey].length === 0
                ? [[item]]
                : treeToPathArray(item[childrenKey], childrenKey).map((one) => [item, ...one])
        )
        .reduce((resultArr, one) => resultArr.concat(one), []);
}

/**
 * 递归获取符合要求元数
 * @param tree 树
 * @param predicateFn 判定函数（返回boolean）
 * @param childrenKey 子节点键名
 * @returns [] 符合条件节点
 */
export function findOne(tree, predicateFn, childrenKey = 'children') {
    if (!Array.isArray(tree) || tree.length === 0) {
        return null;
    }
    for (const node of tree) {
        if (predicateFn(node)) {
            return node;
        }
        const childrenFindResult = findOne(node[childrenKey], predicateFn, childrenKey);

        if (childrenFindResult) {
            return childrenFindResult;
        }
    }

    return null;
}

/**
 * 递归获取符合要求元数
 * @param tree 树
 * @param predicateFn 判定函数（返回boolean）
 * @param childrenKey 子节点键名
 * @returns [] 符合条件节点组成的新数组
 */
export function findNode(tree, predicateFn, childrenKey = 'children') {
    if (!Array.isArray(tree) || tree.length === 0) {
        return [];
    }

    return tree
        .map((item) =>
            predicateFn(item)
                ? [item, ...findNode(item[childrenKey], predicateFn, childrenKey)]
                : findNode(item[childrenKey], predicateFn, childrenKey)
        )
        .reduce((result, one) => result.concat(one), []);
}

/**
 * 数组类型转化为树形结构
 * @param list 标签列表
 * @param idKey Key字段名称
 * @param parentIdKey 父标签Key字段名称
 * @param childNodeKey  子节点数组字段名
 * @param rootMarkKey 父节点标识字段名称
 * @param sortedKey 排序字段名称
 */
export function arrayToTree(list, idKey, parentIdKey, childNodeKey, rootMarkKey, sortedKey) {
    // 为每个节点寻找位置
    for (let i = 0; i < list.length; i++) {
        const indexItem = list[i];

        for (let j = 0; j < list.length; j++) {
            const item = list[j];

            if (!item) {
                continue;
            }
            // 找到节点位置
            if (indexItem[parentIdKey] === item[idKey]) {
                if (!Array.isArray(item[childNodeKey])) {
                    item[childNodeKey] = [];
                }
                if (!sortedKey || !indexItem[sortedKey]) {
                    item[childNodeKey].push(indexItem);
                } else {
                    const targetIndex = item[childNodeKey].findIndex((o) => !o[sortedKey] || o[sortedKey] > indexItem[sortedKey]);

                    if (targetIndex < 0) {
                        item[childNodeKey].push(indexItem);
                    } else {
                        item[childNodeKey].splice(targetIndex, 0, indexItem);
                    }
                }
                if (rootMarkKey) {
                    item[rootMarkKey] = true;
                }
                // 标识已经移动，最后将其洗掉
                list[i].__move = true;
                break;
            }
        }
    }
    // 去除为空的节点
    const treeData = list.filter((item) => !item.__move);

    if (sortedKey) {
        treeData.sort((item1, item2) => (item1[sortedKey] > item2[sortedKey] ? 1 : -1));
    }

    return treeData;
}

/**
 * 获取KEY根节点的Key
 * @param tree 树
 * @param key key值
 * @param rootKey 父节点key
 * @returns {*}
 */
export function findRootKeyByKey(tree, key, rootKey) {
    for (let i = 0; i < tree.length; i++) {
        const item = tree[i];

        if (item.key === key) {
            return rootKey;
        }
        if (Array.isArray(item.children)) {
            const findResult = this.findRootKeyByKey(item.children, key, rootKey || item);

            if (findResult) {
                return findResult;
            }
        }
    }

    return null;
}

/**
 * 根据KEY获取父节点
 * @param tree 树
 * @param key key值
 * @param root 父节点
 * @returns {*}
 */
export function findParentNodeByKey(tree, key, root) {
    for (let i = 0; i < tree.length; i++) {
        const item = tree[i];

        if (item.key === key) {
            return root;
        }
        if (Array.isArray(item.children)) {
            const findResult = findParentNodeByKey(item.children, key, item);

            if (findResult) {
                return findResult;
            }
        }
    }

    return null;
}

/**
 * 根据KEY获取节点
 * @param tree 树
 * @param key key值
 * @returns {*}
 */
export function findNodeByKey(tree, key) {
    for (let i = 0; i < tree.length; i++) {
        const item = tree[i];

        if (item.key === key) {
            return item;
        }
        if (Array.isArray(item.children)) {
            const findResult = this.findNodeByKey(item.children, key);

            if (findResult) {
                return findResult;
            }
        }
    }

    return null;
}

/**
 * 获取节点的最末尾叶子节点
 * @param treeNode 节点
 * @returns {*}
 */
export function getTagEndNodeKeys(treeNode) {
    if (treeNode.root) {
        const keys = [];

        treeNode.children.forEach((node) => keys.push(...getTagEndNodeKeys(node)));

        return keys;
    }

    return [treeNode.key];
}

/**
 * 根据传入的节点计算应该选中的节点
 * @param tree 树形结构
 * @param keys 已选中的子节点
 */
export function getCheckedKeys(tree, keys) {
    const checkedKeys = [...keys];

    keys.forEach((key) => {
        let node = findParentNodeByKey(tree, key);

        while (node) {
            if (checkedKeys.indexOf(node.key) === -1) {
                checkedKeys.push(node.key);
            }
            node = findParentNodeByKey(tree, node.key);
        }
    });

    return checkedKeys;
}

/**
 * 检索名称添加颜色
 * @param treeData 树形结构数据
 * @param name 检索名称
 * @returns {*}
 */
export function addSearchColor(treeData, name) {
    return treeData.filter((data) => {
        let hit = false;

        if (!name) {
            hit = true;
            data.title = <span title={data.title}>{data.title}</span>;
        } else if (data.title && data.title.includes && data.title.includes(name)) {
            hit = true;
            const index = data.title.indexOf(name);
            const beforeStr = data.title.substr(0, index);
            const afterStr = data.title.substr(index + name.length);

            data.title =
                index > -1 ? (
                    <span title={data.title}>
                        {beforeStr}
                        <span style={{ color: '#F50' }}>{name}</span>
                        {afterStr}
                    </span>
                ) : (
                    <span title={data.title}>{data.title}</span>
                );
        }
        if (Array.isArray(data.children)) {
            const filter = this.addSearchColor(data.children, name);

            // 如果目录命中，则之下所有子节点全部显示
            if (!hit) {
                data.children = filter;
            }
            if (data.children && data.children.length > 0) {
                hit = true;
            }
        }

        return hit;
    });
}
