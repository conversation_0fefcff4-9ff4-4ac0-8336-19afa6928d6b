/*
 *@(#) DataRepairAudit.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-26
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import React, { Component } from 'react';
// 服务接口
import * as DataAuditApi from '@/services/data/data/DataAuditApi';
import * as DataSaveApi from '@/services/data/data/DataSaveApi';
// 工具类
import * as StringUtils from '@/components/common/common/StringUtils';
import { getUrlString } from '@share/utils';
// 组件
import { Panel, FormItem, ButtonToolBar, Button } from '@share/shareui';
import TimeLine from '@/components/business/TimeLine';
import DataAuditTimeLine from '@/components/credit_data/TimeLine/DataAuditTimeLine';
import { ShareForm, getComponents } from '@/components/business/Form';

const { Select, RangeTime } = getComponents('div');

const actionOptions = [
    { label: '新增', value: '1' },
    // { label: '审核', value: '2' },
    { label: '更新', value: '3' },
    // { label: '上线', value: '4' },
    // { label: '下线', value: '5' },
    // { label: '上传', value: '6' },
    // { label: '归档', value: '7' },
    // { label: '错误数据标记', value: '8' },
    { label: '删除', value: '9' },
];

const defaultForm = {
    ACTION: '',
    USER_NAME: '',
    ACTION_TIME: { start: '', end: '' },
};

class DataAuditDetail extends Component {
    state = {
        searchForm: { ...defaultForm }, // 搜索条件
        actionUserNameOptions: [], // 操作用户对下拉列表数据
        dataList: [],
        showDataList: [],
    };

    componentDidMount() {
        const { searchForm } = this.state;
        // 路径传参过滤条件
        const ACTION_TIME = getUrlString(window.location.href, 'ACTION_TIME');

        if (ACTION_TIME) {
            this.setState({
                searchForm: {
                    ...searchForm,
                    ACTION_TIME: { start: ACTION_TIME, end: ACTION_TIME },
                },
            });
        }
        this.requestData();
    }

    requestData = async () => {
        const {
            match: {
                params: { categoryId, objectType, ywid },
            },
        } = this.props;
        // 获取数据
        const { list } = await DataAuditApi.listByCategory(categoryId, objectType, { data: { YW_ID: ywid }, page: null });

        list.forEach((item, index) => {
            // 填充前一条数据
            item.preData = list[index + 1] || null;
            // 填充时间戳
            item.date = item.system.CREATE_TIME ? item.system.CREATE_TIME.substr(0, 10) : '无';
            item.categoryCode = categoryId;
            item.objectType = objectType;
            // 填充路由
            item.key = item.system.ID;
            item.recovery = item.system.VERSION_ID !== list[0].system.VERSION_ID ? this.recovery : null;
        });
        // 计算操作用户名称下拉
        const actionUserNameOptions = [...new Set(list.map((item) => item.system.USER_NAME).filter((item) => item))].map((item) => ({
            label: item,
            value: item,
        }));

        this.setState({ actionUserNameOptions, dataList: list }, this.handleSearch);
    };

    // 查询
    handleSearch = () => {
        const { searchForm, dataList } = this.state;
        const body = StringUtils.deleteSpace(searchForm);
        let showDataList = dataList;

        if (body.ACTION) {
            showDataList = showDataList.filter((item) => item.system.ACTION === body.ACTION);
        }
        if (body.USER_NAME) {
            showDataList = showDataList.filter((item) => item.system.USER_NAME === body.USER_NAME);
        }
        if (body.ACTION_TIME.start) {
            showDataList = showDataList.filter((item) => item.system.CREATE_TIME >= body.ACTION_TIME.start);
        }
        if (body.ACTION_TIME.end) {
            showDataList = showDataList.filter((item) => item.system.CREATE_TIME <= body.ACTION_TIME.end);
        }
        this.setState({ searchForm: { ...body }, showDataList });
    };

    // 重置
    handleReset = () => {
        this.setState({ searchForm: { ...defaultForm } });
    };

    recovery = async (trcId) => {
        const {
            match: {
                params: { categoryId, objectType },
            },
        } = this.props;

        await DataSaveApi.updateFromTrc(categoryId, objectType, trcId);
        this.requestData();
    };

    // 取消
    cancel = () => {
        const { history } = this.props;

        history.go(-1);
    };

    render() {
        const { searchForm, actionUserNameOptions, showDataList } = this.state;

        return (
            <div>
                <Panel>
                    <Panel.Head title="审计详情" />
                    <Panel.Body full>
                        <ShareForm formData={searchForm} onChange={(data, callback) => this.setState({ searchForm: data }, callback)}>
                            <Select
                                label="操作类型"
                                field="ACTION"
                                placeholder="请选择操作类型"
                                options={actionOptions}
                                labelCol={3}
                                col={8}
                            />
                            <Select
                                label="操作用户"
                                field="USER_NAME"
                                placeholder="请选择操作用户"
                                options={actionUserNameOptions}
                                col={8}
                                labelCol={3}
                            />
                            <RangeTime label="操作时间" field="ACTION_TIME" col={13} labelCol={3} />
                            <FormItem className="btn-item clearfix pull-right">
                                <Button type="submit" bsStyle="primary" onClick={this.handleSearch}>
                                    查询
                                </Button>
                                <Button type="reset" onClick={this.handleReset}>
                                    重置
                                </Button>
                            </FormItem>
                        </ShareForm>
                        {showDataList.length === 0 ? (
                            <div style={{ textAlign: 'center', padding: '30px' }}>无审计记录</div>
                        ) : (
                            <TimeLine data={showDataList} component={DataAuditTimeLine} />
                        )}
                    </Panel.Body>
                </Panel>
                <ButtonToolBar>
                    <Button type="button" bsSize="large" onClick={this.cancel}>
                        返回
                    </Button>
                </ButtonToolBar>
            </div>
        );
    }
}

export default DataAuditDetail;
