/*
 *@(#) useEnterpriseEntity.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2024
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2024-04-18
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import EnterpriseService from '@/services/EnterpriseService';
import { useService } from '@share/framework';
import { useState } from 'react';

const useEnterpriseEntity = () => {
    const services = useService(EnterpriseService);
    const [employedNumberList, setEmployedNumberList] = useState([]); // 企业就业人数列表

    // 获取企业就业人数列表
    const getEmployedNumberList = async (tyshxydm) => {
        const res = services.employedNumberList({ tyshxydm });

        setEmployedNumberList(res);
    };

    const employedNumberListService = (tyshxydm) => {
        return ({ page, data }) => services.employedNumberList({ page, data: { ...data, tyshxydm } });
    };

    return {
        employedNumberList,
        getEmployedNumberList,
        employedNumberListService,
    };
};

export default useEnterpriseEntity;
