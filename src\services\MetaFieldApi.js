/* eslint-disable max-params */
import { RemoteService } from '@share/framework';

class MetaFieldApi extends RemoteService {
    /**
     * 可配组件
     * @returns Promise 组件列表
     */
    fieldComponents = () => {
        return this.network.formGet('/data-plus/meta/field/component/all');
    };

    /**
     * 可配规则
     * @returns Promise 规则列表
     */
    fieldRules = () => {
        return this.network.formGet('/data-plus/meta/field/rule/all');
    };

    /**
     * 基础配置刷新（所有）
     * @returns Promise 操作是否成功
     */
    refreshTableMetas = (param) => {
        return this.network.json('/data-plus/meta/field/config/refresh', param);
    };

    /**
     * 基础配置刷新
     * @param tableId 表名
     * @returns Promise 操作是否成功
     */
    refreshTableMeta = (tableId) => {
        return this.network.formGet(`/data-plus/meta/field/config/refresh/table_id/${tableId}`);
    };

    /**
     * 配置详情
     * @param tableId 表名
     * @returns Promise 操作是否成功
     */
    tableMetaByTableName = (tableId) => {
        return this.network.formGet(`/data-plus/meta/field/config/table_id/${tableId}`);
    };

    /**
     * 配置编辑
     * @param fieldConfigList 字段配置集合
     * @returns Promise 操作是否成功
     */
    saveTableMeta = (fieldConfigList) => {
        return this.network.json('/data-plus/meta/field/config/save', fieldConfigList);
    };

    /**
     * 配置启用/停用
     * @param fieldId 配置字段id
     * @param enabled 配置使用状态（true 启用，false 停用）
     * @returns Promise 操作是否成功
     */
    enabledTableMeta = (fieldId, enabled) => {
        return this.network.formGet(`/data-plus/meta/field/config/enabled/${fieldId}`, { enabled });
    };

    /**
     * 配置导出
     * @param tableId 表名
     * @param param 参数
     */
    exportTableMetaJson = (tableId, param) => {
        this.network.fakeFormPost(`/data-plus/meta/field/config/json/export/table_id/${tableId}`, param);
    };

    /**
     * 配置导入
     * @param tableId 表名
     */
    importTableMetaJson = (tableId) => {
        return this.network.formGet(`/data-plus/meta/field/config/json/import/table_id/${tableId}`);
    };

    /**
     * 配置导出
     * @param param 参数
     */
    exportTableMetaZip = (param) => {
        this.network.fakeFormPost('/data-plus/meta/field/config/zip/export', param);
    };

    /**
     * 配置导入
     */
    importTableMetaZip = () => {
        return this.network.formGet('/data-plus/meta/field/config/zip/import');
    };

    /**
     * ES索引导出
     * @param param 参数
     */
    exportEsFile = (param) => {
        this.network.fakeFormPost('/data-plus/meta/field/es/zip/export', param);
    };

    /**
     * 测试列表
     * @param metaFieldList 配置列表
     * @param orientedObjectType 面向主体类型（0,1,2）
     * @param scene 使用场景
     * @param simulationListData 模拟数据列表
     * @returns Promise
     */
    testList = (metaFieldList, orientedObjectType, scene, simulationListData) => {
        return this.network.json('/data-plus/meta/field/test/list', {
            metaFieldList,
            orientedObjectType,
            scene,
            simulationListData,
        });
    };

    /**
     * 测试详情
     * @param metaFieldList 配置列表
     * @param orientedObjectType 面向主体类型（0,1,2）
     * @param scene 使用场景
     * @param simulationData 模拟数据
     * @returns Promise [{type:'',}]
     */
    testDetail = (metaFieldList, orientedObjectType, scene, simulationData) => {
        return this.network.json('/data-plus/meta/field/test/detail', {
            metaFieldList,
            orientedObjectType,
            scene,
            simulationData,
        });
    };

    /**
     * 测试页面上报
     * @param metaFieldList 配置列表
     * @param orientedObjectType 面向主体类型（0,1,2）
     * @param scene 使用场景
     * @param simulationData 模拟数据
     * @returns Promise 上报结果
     */
    testAddFromPage = (metaFieldList, orientedObjectType, scene, simulationData) => {
        return this.network.json('/data-plus/meta/field/test/add/from_page', {
            metaFieldList,
            orientedObjectType,
            scene,
            simulationData,
        });
    };

    /**
     * 测试模板下载
     * @param metaFieldList 配置列表
     * @param orientedObjectType 面向主体类型（0,1,2）
     * @param scene 使用场景
     */
    testTemplateDownload = (metaFieldList, orientedObjectType, scene) => {
        const simulationCollParamJson = JSON.stringify({ metaFieldList, orientedObjectType, scene });

        this.network.fakeFormPost('/data-plus/meta/field/test/template/download.do', { simulationCollParamJson });
    };

    getAllTagList = () => {
        return this.network.formGet('/tagInfo/list/all.do');
    };

    tagEntPreview = (params) => {
        return this.network.json('/tagInfo/ent/preview.do', params);
    };

    tagEntConfirm = (params) => {
        return this.network.json('/tagInfo/ent/confirm.do', params);
    };

    getPersonAllTagList = () => {
        return this.network.formGet('/person/tagInfo/list/all.do');
    };

    getTagList = (params) => {
        return this.network.formGet('/tagInfo/list.do', params);
    };

    getPersonTagList = (params) => {
        return this.network.formGet('/person/tagInfo/list.do', params);
    };

    markTag = (params) => {
        return this.network.json('/tagInfo/markTag.do', params);
    };

    personMarkTag = (params) => {
        return this.network.json('/person/tagInfo/markTag.do', params);
    };

    updateAllTagList = (params) => {
        return this.network.json('/tagInfo/update/batch.do', params);
    };

    personUpdateAllTagList = (params) => {
        return this.network.json('/person/tagInfo/update/batch.do', params);
    };

    tagInfoAdd = (params) => {
        return this.network.json('/tagInfo/add.do', params);
    };

    personTagInfoAdd = (params) => {
        return this.network.json('/person/tagInfo/add.do', params);
    };

    tagInfoUpdate = (params) => {
        return this.network.json('/tagInfo/update.do', params);
    };

    personTagInfoUpdate = (params) => {
        return this.network.json('/person/tagInfo/update.do', params);
    };

    tagInfoRemove = (id) => {
        return this.network.formGet(`/tagInfo/remove/${id}.do`);
    };

    personTagInfoRemove = (id) => {
        return this.network.formGet(`/person/tagInfo/remove/${id}.do`);
    };

    /**
     * 打标文件下载
     * @param param 参数
     */
    markTagDownload = () => {
        this.network.fakeFormPost(`${window.SHARE.CONTEXT_PATH}/tagInfo/markTag/download.do`);
    };

    personMarkTagDownload = () => {
        this.network.fakeFormPost(`${window.SHARE.CONTEXT_PATH}/person/tagInfo/markTag/download.do`);
    };

    /**
     * 打标
     * @param param 参数
     */
    markTagUpload = (id, file, callback) => {
        return this.network.upload(`/tagInfo/markTag/${id}.do`, file, callback);
    };

    personMarkTagUpload = (id, file, callback) => {
        return this.network.upload(`/person/tagInfo/markTag/${id}.do`, file, callback);
    };

    /**
     * 打标中的企业列表
     * @param param 参数
     */
    getTagEnterpriseList = (params) => {
        return this.network.json(`/tagInfo/enterprise/list.do`, params);
    };

    getTagPersonList = (params) => {
        return this.network.json(`/person/tagInfo/person/list.do`, params);
    };

    /**
     * 删除打标企业
     * @param param 参数
     */
    removeTagEnterprise = (id) => {
        return this.network.formGet(`/tagInfo/markTag/remove/${id}.do`);
    };

    removeTagPerson = (id) => {
        return this.network.formGet(`/person/tagInfo/markTag/remove/${id}.do`);
    };

    batchRemoveTagEnterprise = (ids) => {
        return this.network.json(`/tagInfo/markTag/batchRemove.do`, ids);
    };

    /**
     * 执行规则
     * @param param 参数
     */
    tagRuleExecute = (id) => {
        return this.network.formGet(`/tagInfo/rule/execute/${id}.do`);
    };

    personTagRuleExecute = (id) => {
        return this.network.formGet(`/person/tagInfo/rule/execute/${id}.do`);
    };

    tagRuleList = (params) => {
        return this.network.json(`/tagInfo/rule/list.do`, params);
    };

    /**
     * 执行日志列表
     * @param param 参数
     */
    tagRuleExecuteLogList = (params) => {
        return this.network.json(`/tagInfo/rule/execute/log/list.do`, params);
    };

    personTagRuleExecuteLogList = (params) => {
        return this.network.json(`/person/tagInfo/rule/execute/log/list.do`, params);
    };

    /**
     * 更新/保存规则
     * @param param 参数
     */
    tagRuleSave = (params) => {
        return this.network.json(`/tagInfo/rule/save.do`, params);
    };

    personTagRuleSave = (params) => {
        return this.network.json(`/person/tagInfo/rule/save.do`, params);
    };

    tagRuleRemove = (id) => {
        return this.network.formGet(`/tagInfo/rule/remove/${id}.do`);
    };

    /**
     * 规则详情
     * @param param 参数
     */
    tagRuleDetail = (id) => {
        return this.network.formGet(`/tagInfo/rule/detail/${id}.do`);
    };

    personTagRuleDetail = (id) => {
        return this.network.formGet(`/person/tagInfo/rule/detail/${id}.do`);
    };

    // 调研模板列表
    templateList = (params) => {
        return this.network.json(`/interview/interview/template/list.do`, params);
    };

    // 调研模板列表
    templateSave = (params) => {
        return this.network.json(`/interview/interview/template/save.do`, params);
    };

    // 切换状态
    toggleStatus = (id) => {
        return this.network.formGet(`/interview/interview/template/toggle_status/${id}.do`);
    };

    // 删除走访类型
    templateDelete = (id) => {
        return this.network.formGet(`/interview/interview/template/deleted/${id}.do`);
    };
}

export default MetaFieldApi;
