.payTaxes {
    :global {
        .panel .panel-head {
            padding-left: 0;
        }
    }
    .selectWrap {
        width: 160px;
        :global {
            .Select-multi-value-wrapper {
                line-height: 22px;
            }
            .Select-arrow-zone {
                line-height: 22px;
            }
        }
    }
    .singleRowTable {
        border: 1px solid #d2e4ff;
        width: 100%;
        table-layout: fixed;
        thead th,
        tbody td {
            padding: 15px 16px 15px 16px;
            text-align: center;
            word-break: break-all;
        }
        thead th {
            background-color: #f2f8ff;
            border-bottom: 1px solid #d2e4ff;
        }
        .boldText {
            font-weight: bold;
            color: rgba(0, 0, 0, 0.85);
        }
    }
}
