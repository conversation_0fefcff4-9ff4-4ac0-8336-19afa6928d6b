/*
 * @(#) DataFillingList.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-08-05 17:54:58
 */
import React from 'react';
// 样式
import styles from '@/pages/metaData/styles/index.scss';
import noData from '@/assets/images/image-noData.png';
// 服务接口
import * as MetaCategoryApi from '@/services/data/meta/MetaCategoryApi.js';
// 组件
import DataQuery from '@/components/credit_data/DataQuery';
import DataAuditTable from '@/components/credit_data/List/DataAuditTable';
import { Panel } from '@share/shareui';

class DataAuditList extends DataQuery {
    // 初始化默认搜索条件
    initDefaultSearchBody() {
        return {
            CATEGORY_CODE: '',
            DEPT_ID: '',
        };
    }

    // 初始化元数据配置Api
    initMetaConfigApi = (categoryCode, objectType) => {
        return MetaCategoryApi.metaConfig(categoryCode, objectType, 'DATA_AUDIT');
    };

    render() {
        const { history } = this.props;
        const { searchForm, searchBody, listConfig, metaConfigList, categoryCodeList } = this.state;
        const { CATEGORY_CODE: categoryCode } = searchForm;

        return (
            <div>
                <Panel>
                    <Panel.Body full>{this.renderQueryCondition('CreditDataAuditListForm')}</Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Body full>
                        {listConfig.length === 0 ? (
                            <div className={styles.noData}>
                                <img src={noData} alt="no data" />
                                <p>请选择信息类别</p>
                            </div>
                        ) : (
                            <DataAuditTable
                                history={history}
                                categoryCode={categoryCode}
                                categoryCodeList={categoryCodeList}
                                metaConfigList={metaConfigList}
                                body={{ ...searchBody }}
                            />
                        )}
                    </Panel.Body>
                </Panel>
            </div>
        );
    }
}

export default DataAuditList;
