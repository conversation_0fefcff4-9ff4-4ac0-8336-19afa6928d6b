// 水印模块

const isBlank = (txt) => {
    if (!txt) {
        return false;
    }

    return txt === '' || txt.replace(/^\s+|\s+$/gm, '') === '';
};

const getElementsByClass = (searchClass, node, tag) => {
    const classElements = new Array();

    if (node == null) {
        node = document;
    }
    if (tag == null) {
        tag = '*';
    }
    const els = node.getElementsByTagName(tag);
    const elsLen = els.length;
    const pattern = new RegExp(`(^|\\s)${searchClass}(\\s|$)`);

    for (let i = 0, j = 0; i < elsLen; i++) {
        if (pattern.test(els[i].className)) {
            classElements[j] = els[i];
            j++;
        }
    }

    return classElements;
};

export const autoGenerateWatermark = (text) => {
    if (isBlank(text)) {
        return;
    }

    let parentHasWatermark = false;
    let { parent } = window;

    while (parent != null && typeof parent !== 'undefined') {
        if (getElementsByClass('global_watermark', parent.document, 'span').length > 0) {
            parentHasWatermark = true;
            break;
        }
        if (parent === parent.parent) {
            break;
        }
        parent = parent.parent;
    }

    if (getElementsByClass('global_watermark', document, 'span').length <= 0 && !parentHasWatermark) {
        let waterHtml =
            '<style>' +
            '.global_watermark {\n' +
            '    position: fixed;\n' +
            '    opacity: 0.2;\n' +
            '    transform: rotate(-33deg);\n' +
            '    font-size: 20px;\n' +
            '    color: #b7b7b7;\n' +
            '    font-family: arial;\n' +
            '    z-index: 999999;\n' +
            '    white-space: nowrap;\n' +
            '    pointer-events: none;\n' +
            '}' +
            '</style>';
        const w = text.length * 10;

        for (let y = 0; y < 50; y++) {
            for (let x = 0; x < 50; x++) {
                // let n = x % 2;
                const t = y * w * 3 - text.length * x * 10;
                const l = x * w * 3 - text.length * y * 10;

                waterHtml += `<span class="global_watermark global_watermark_${0}" style="top:${t}px;left:${l}px;">${text}</span>`;
            }
        }
        const div = document.createElement('div');

        div.innerHTML = waterHtml;
        document.body.appendChild(div);
    }
};
