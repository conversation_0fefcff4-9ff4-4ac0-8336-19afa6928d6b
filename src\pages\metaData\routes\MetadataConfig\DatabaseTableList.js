/*
 * @(#)  DatabaseTableList.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 * Copyright: Copyright (c) 2021
 * Company: 厦门畅享信息技术有限公司
 * Author: yujy (<EMAIL>)
 * File Created: 2021-08-23 11:06:32
 */

import React, { Component } from 'react';
// 服务接口
import * as MetaTable<PERSON><PERSON> from '@/services/data/meta/MetaTableApi';
import * as MetaField<PERSON><PERSON> from '@/services/data/meta/MetaFieldApi';
// 工具类
import * as StringUtils from '@/components/common/common/StringUtils';
// 组件
import { Link } from 'dva/router';
import MultiClamp from '@/components/ui/MultiClamp';
import MyAlert from '@/components/ui/MyAlert/MyAlert';
import DatabaseTableEdit from '@/components/metadataManage/DatabaseTableEdit';
import EsExportModal from '@/components/metadataManage/EsExportModal';
import DatabaseTableGenModal from '@/components/metadataManage/databaseTableGen/DatabaseTableGenModal';
import { FileUploadButton } from '@/components/business/Other';
import { FrontPageTable as Table } from '@/components/business/Table';
import { Panel, FormItem, Button, Icon } from '@share/shareui';
import { CacheShareForm, getComponents } from '@/components/business/Form';

const { Input, CheckboxGroup } = getComponents('div');

const defaultEditForm = {
    tableId: '',
    databaseDescription: '',
    manualFilled: '0',
};

const defaultSearchBody = {
    tableId: '',
    tableDescription: '',
    manualFilled: [],
};

class DatabaseTableList extends Component {
    state = {
        // 搜索条件
        searchForm: { ...defaultSearchBody },
        searchBody: { ...defaultSearchBody },
        // 列表
        dataTableList: [],
        selectedRowKeys: [],
        // 编辑模态框
        showEditModal: false,
        editBody: {},
        // ES导出模态框
        showExportModal: false,
        // 表生成模态框
        showTableGenModal: false,
    };

    componentDidMount() {
        this.getTableList();
    }

    getTableList = async () => {
        const dataTableList = await MetaTableApi.getDataTableList();

        this.setState({ dataTableList });
    };

    handleSearch = () => {
        const { searchForm } = this.state;
        const body = StringUtils.deleteSpace(searchForm);

        this.setState({ searchBody: { ...body }, selectedRowKeys: [] });
    };

    handleReset = () => {
        this.setState({ searchForm: { ...defaultSearchBody } });
    };

    // 过滤数据
    filterData = (list) => {
        const {
            searchBody: { tableId, tableDescription, manualFilled },
        } = this.state;
        let filterList = list;

        if (tableId) {
            filterList = list.filter((item) => item.tableId && item.tableId.toUpperCase().includes(tableId.toUpperCase()));
        }
        if (tableDescription) {
            filterList = list.filter(
                (item) => item.tableDescription && item.tableDescription.toUpperCase().includes(tableDescription.toUpperCase())
            );
        }
        if (manualFilled.length === 1) {
            filterList = list.filter((item) => item.manualFilled === manualFilled[0]);
        }

        return filterList;
    };

    // 打开编辑框
    handleEdit = (rowData) => {
        this.setState({ showEditModal: true, editBody: rowData });
    };

    // 取消编辑
    cancelEdit = () => {
        this.setState({ showEditModal: false, editBody: defaultEditForm });
    };

    // 删除数据表
    handleDelete = async (id) => {
        await MetaTableApi.deleteDataTable(id);

        MyAlert.ok('删除成功');
        this.flush();
    };

    // 刷新已有配置表元数据
    flushMetaConfig = async () => {
        const { selectedRowKeys } = this.state;

        await MetaFieldApi.refreshTableMetas(selectedRowKeys);
        MyAlert.ok('刷新成功');
    };

    // 刷新列表
    flush = () => {
        this.getTableList();
        this.setState({ selectedRowKeys: [] });
    };

    render() {
        const { searchForm, dataTableList, selectedRowKeys, showEditModal, editBody, showExportModal, showTableGenModal } = this.state;
        // 列表展示内容
        const columns = [
            {
                title: '数据表',
                key: 'tableId',
                dataIndex: 'tableId',
                width: '20%',
                render: (tableId) => <MultiClamp title={tableId}>{tableId}</MultiClamp>,
            },
            {
                title: '数据表说明',
                key: 'tableDescription',
                dataIndex: 'tableDescription',
                render: (tableDescription) => <MultiClamp title={tableDescription}>{tableDescription}</MultiClamp>,
            },
            {
                title: '是否支持人工填报',
                key: 'manualFilled',
                dataIndex: 'manualFilled',
                width: '10%',
                render: (rowData) => {
                    return <span>{rowData === '1' ? '支持' : '不支持'}</span>;
                },
            },
            {
                title: '操作',
                width: 200,
                align: 'center',
                render: (rowData) => {
                    return (
                        <div className="tableBtn">
                            <a href="javascript:void(0)" onClick={() => this.handleEdit({ ...rowData, isModify: true })}>
                                修改
                            </a>
                            <a
                                href="javascript:void(0)"
                                onClick={() => {
                                    MyAlert.delConfirm(() => {
                                        this.handleDelete(rowData.tableId);
                                    });
                                }}
                            >
                                删除
                            </a>
                            <Link to={`/DatabaseTableDetail/${rowData.tableId}/1`}>配置维护</Link>
                            <Link to={`/DatabaseTableDetail/${rowData.tableId}/6`}>数据查询</Link>
                        </div>
                    );
                },
            },
        ];
        const filterDataTableList = this.filterData(dataTableList);

        return (
            <div>
                <Panel>
                    <Panel.Body full>
                        <CacheShareForm
                            formData={searchForm}
                            onChange={(data, callback) => this.setState({ searchForm: data }, callback)}
                            searchFn={this.handleSearch}
                            namespace="databaseTableInfoForm"
                        >
                            <Input label="数据表" field="tableId" placeholder="请输入" col={10} labelCol={3} />
                            <Input label="数据表说明" field="tableDescription" placeholder="请输入" col={10} labelCol={3} />
                            <CheckboxGroup
                                label="是否支持人工填报"
                                field="manualFilled"
                                options={[
                                    { label: '支持', value: '1' },
                                    { label: '不支持', value: '0' },
                                ]}
                                col={10}
                                labelCol={3}
                            />
                            <FormItem className="btn-item clearfix pull-right">
                                <Button type="submit" bsStyle="primary" onClick={this.handleSearch}>
                                    查询
                                </Button>
                                <Button type="reset" onClick={this.handleReset}>
                                    重置
                                </Button>
                            </FormItem>
                        </CacheShareForm>
                    </Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Head title="整合数据表">
                        <Panel.HeadRight>
                            <ul className="ui-list-horizontal">
                                <li>
                                    <Button
                                        type="button"
                                        className="btn-xs"
                                        border={false}
                                        onClick={() => {
                                            this.setState({ showTableGenModal: true });
                                        }}
                                    >
                                        <Icon className="si si-app_qkje" />
                                        添加物理表
                                    </Button>
                                </li>
                                <li>
                                    <Button
                                        type="button"
                                        className="btn-xs"
                                        border={false}
                                        onClick={() => {
                                            this.handleEdit({ ...defaultEditForm });
                                        }}
                                    >
                                        <Icon className="fa-plus" />
                                        新增库表
                                    </Button>
                                </li>
                                <li>
                                    <Button
                                        type="button"
                                        className="btn-xs"
                                        border={false}
                                        onClick={this.flushMetaConfig}
                                        disabled={selectedRowKeys.length === 0}
                                    >
                                        <Icon className="si si-com_rotate" />
                                        刷新字段
                                    </Button>
                                </li>
                                <li>
                                    <Button
                                        type="button"
                                        className="btn-xs"
                                        border={false}
                                        onClick={() => MetaFieldApi.exportTableMetaZip({ data: selectedRowKeys })}
                                        disabled={selectedRowKeys.length === 0}
                                    >
                                        <Icon className="si si-com_dc" />
                                        导出配置
                                    </Button>
                                </li>
                                <li>
                                    <FileUploadButton url={MetaFieldApi.importTableMetaZip()} successFn={() => MyAlert.ok('导入成功')}>
                                        <Button type="button" className="btn-xs" border={false}>
                                            <Icon className="si si-com_dr" />
                                            导入配置
                                        </Button>
                                    </FileUploadButton>
                                </li>
                            </ul>
                        </Panel.HeadRight>
                    </Panel.Head>
                    <Table
                        dataSource={filterDataTableList}
                        rowKey="tableId"
                        columns={columns}
                        rowSelection={{
                            selectedRowKeys,
                            onChange: (keys) => this.setState({ selectedRowKeys: keys }),
                        }}
                    />
                </Panel>
                <DatabaseTableEdit
                    editBody={editBody}
                    tableIdList={dataTableList.map((item) => item.tableId)}
                    showModal={showEditModal}
                    onSuccessFn={this.flush}
                    onHide={this.cancelEdit}
                />
                <EsExportModal
                    show={showExportModal}
                    cancelFn={() => this.setState({ showExportModal: false })}
                    data={{
                        tableIds: selectedRowKeys,
                        esAlias: '表名转小写',
                    }}
                />
                <DatabaseTableGenModal
                    show={showTableGenModal}
                    successFn={() => this.setState({ showTableGenModal: false })}
                    cancelFn={() => this.setState({ showTableGenModal: false })}
                />
            </div>
        );
    }
}
export default DatabaseTableList;
