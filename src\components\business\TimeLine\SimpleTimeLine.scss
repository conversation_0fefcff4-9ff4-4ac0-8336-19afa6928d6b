/*!
 * @(#) SimpleTimeLine.scss
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2023
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2023-08-18 15:30:58
 */
.panel {
    margin: 20px 0;

    .left {
        position: relative;
        width: 5%;
        padding: 35px 24px 0 10px;
        text-align: right;
        display: inline;

        div {
            margin-bottom: 0;
            line-height: 18px;
            font-size: 14px;
            color: #404348;
        }

        .dot {
            position: absolute;
            right: -1px;
            transform: translateX(50%);
            width: 13px;
            height: 13px;
            border-radius: 50%;
            background: #09d;
        }
    }

    .right {
        width: 95%;
        padding: 0 0 20px 25px;
        border-left: 2px dashed #d0d1d4;
        display: inline-block;

        .time {
            position: relative;
            top: -4px;
        }

    }

    &:last-child{
        padding-bottom: 20px;
    }
}




