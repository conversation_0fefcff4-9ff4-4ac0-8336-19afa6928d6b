/*
 * @(#) Steps.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2020
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2020-04-15 18:12:41
 */
import './ShareSteps.scss';
import React, { Component } from 'react';
import { Panel } from '@share/shareui';
import { Steps } from 'antd';

const { Step } = Steps;

class ShareSteps extends Component {
    render() {
        const { steps = [], current, title, stepClickHandle, stepsProps = {}, stepProps = {}, minHeight } = this.props;

        return (
            <Panel bsStyle="primary">
                {title && <Panel.Head title={title} />}
                <Panel.Body full>
                    <Steps current={current} type="navigation" {...stepsProps}>
                        {steps.map((item, index) => (
                            <Step
                                key={item.title}
                                title={item.title}
                                onClick={() => stepClickHandle && stepClickHandle(item, index)}
                                {...stepProps}
                            />
                        ))}
                    </Steps>
                    <div className="steps-content" style={{ minHeight: minHeight || '260px' }}>
                        {steps[current].content}
                    </div>
                </Panel.Body>
            </Panel>
        );
    }
}

export default ShareSteps;
