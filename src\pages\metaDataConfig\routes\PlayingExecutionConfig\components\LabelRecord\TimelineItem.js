import React, { Fragment, useState } from 'react';
import { Label } from '@share/shareui';
import classNames from 'classnames';
import styles from './LabelRecord.scss';
import EnterpriseList from './EnterpriseList';

const TimelineItem = ({ data }) => {
    const [enterpriseListShow, setEnterpriseListShow] = useState(false);
    const statusMap = {
        0: (
            <Label background={false} bsStyle="primary">
                任务执行中
            </Label>
        ),
        1: (
            <Label background={false} bsStyle="success">
                任务执行成功
            </Label>
        ),
        2: (
            <Label background={false} bsStyle="danger">
                任务执行失败
            </Label>
        ),
    };
    // 展开企业列表
    const handleToggleEnterpriseList = (bool) => {
        setEnterpriseListShow(bool);
    };

    return (
        <Fragment>
            <div className={styles.TimelineItem}>
                <div className={styles['TimelineItem-statusTime']}>
                    <span className={styles['TimelineItem-statusTime_time']}>{data.createTime}</span>
                    {statusMap[data.status]}
                </div>
                <div className={styles['TimelineItem-entNum']}>
                    本次执行 <span className={styles['TimelineItem-entNum_label']}>{data.tagName}</span> 成功标注了
                    <span
                        className={classNames(styles.num, {
                            [styles.isZero]: Number(data.markCount) === 0,
                        })}
                        onClick={() => {
                            if (Number(data.markCount) === 0) {
                                return;
                            }
                            handleToggleEnterpriseList(true, data);
                        }}
                    >
                        {data.markCount || 0}
                    </span>
                    家企业
                </div>
            </div>
            {enterpriseListShow && (
                <EnterpriseList
                    queryIds={{
                        sourceId: data.id,
                        tagId: data.tagId,
                    }}
                    handleToggleEnterpriseList={handleToggleEnterpriseList}
                />
            )}
        </Fragment>
    );
};
export default TimelineItem;
