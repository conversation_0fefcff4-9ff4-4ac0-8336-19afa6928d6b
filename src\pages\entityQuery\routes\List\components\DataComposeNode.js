import React from 'react';
import { Icon } from '@share/shareui';
import { CreatableSelect } from '@/components/form';
import { addErrorTip, registerFormItem, validFunction } from '@/utils/shareFormUtil';
import DataNode from './DataNode';
import style from './DataComposeNode.scss';

const CreatableSelectCus = addErrorTip(CreatableSelect);
const DataComposeNode = (props) => {
    const { value, onChange, field, inventoryList, prefix, suffix } = props;
    const { composeNodes = [] } = value || {};
    const updateData = (data) => {
        onChange({ target: { value: data } });
    };
    const addComposeNode = (node) => {
        const nodes = [...composeNodes, node];
        updateData({ ...value, composeNodes: nodes });
    };
    const deleteComposeNode = (i) => {
        const nodes = composeNodes.filter((item, index) => index !== i);
        updateData({ ...value, composeNodes: nodes });
    };

    return (
        <div className={style.body}>
            <div className={style.operate}>
                <div className={style.switch}>
                    <Icon className="si si-xy_ssjccgs" title="切换类型" onClick={() => updateData({ id: '', condition: {} })} />
                </div>
                <div className={style.addButton}>
                    <Icon className="si si-com_plusthin" title="添加子节点" onClick={() => addComposeNode({ id: '', condition: {} })} />
                </div>
                {prefix}
                <div className={style.composeLogic}>
                    <CreatableSelectCus
                        field={`${field}.composeLogic`}
                        label="组合逻辑"
                        rule={[
                            'required',
                            validFunction({
                                fn: () => Array.isArray(value?.composeNodes) && value.composeNodes.length > 0,
                                errMsgCus: '组合节点不能为空',
                            }),
                        ]}
                        options={[
                            { label: '增量【$1-$2】', value: '$1-$2' },
                            { label: '增速【($1-$2)*100/$2】', value: '($1-$2)*100/$2' },
                            { label: '占比【($1/$2)*100】', value: '($1/$2)*100' },
                        ]}
                        placeholder="组合逻辑"
                        noView
                    />
                </div>
                {suffix}
            </div>
            {Array.isArray(value?.composeNodes) && value.composeNodes.length > 0 && (
                <div className={style.composeNodes}>
                    {value.composeNodes.map((item, index) => {
                        const paramField = `${field}.composeNodes.${index}`;

                        return (
                            <div key={paramField} className={style.composeNode}>
                                <DataNode
                                    field={paramField}
                                    label="组合节点"
                                    inventoryList={inventoryList}
                                    noView
                                    prefix={
                                        <div className={style.deleteButton}>
                                            <Icon
                                                className="si si-com_minusthin"
                                                title="删除本节点"
                                                onClick={() => deleteComposeNode(index)}
                                            />
                                        </div>
                                    }
                                />
                            </div>
                        );
                    })}
                </div>
            )}
        </div>
    );
};

export default registerFormItem(DataComposeNode);
