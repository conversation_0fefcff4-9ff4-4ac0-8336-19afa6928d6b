import React, { Fragment } from 'react';
import { Popover } from 'antd';
import { stringLengthFormat } from '@/utils/format';
import { useDeepCompareEffect, useSetState, useUpdateEffect } from 'ahooks';
import { arrayToTree2 } from '@/utils/TreeUtil';
import { findParentsWithDisabledChildren } from '@/utils';
import { Icon } from '@share/shareui';
import classNames from 'classnames';
import s from './LabelingManagement.scss';
import TagTree from './TagTree';

const PersonTag = ({ accInfoList = [], handleRemoveTagPerson, refresh, idNum, tagList }) => {
    const [state, setState] = useSetState({
        needMore: false,
        moreStatus: false,
        clicked: false,
        treeDataSource: {
            treeData: [],
            checkedKeys: [],
        },
    });

    const getTreeData = async (res) => {
        // 将详情接口返回的accInfoList字段与整个产业数的数据进行匹配，只显示匹配中的数据
        const idSet = new Set(accInfoList.map((item) => item.tagId));
        const intersection = res.reduce((previous, current) => {
            if (idSet.has(current.id)) {
                return [...previous, current];
            }

            return previous;
        }, []);
        console.log('intersection', intersection);
        const isIdInIntersection = (id) => {
            return intersection.some((item) => item.id === id);
        };
        // 将数据转换成树结构的数据
        const dataTree = arrayToTree2(
            res,
            (item) => !item.parentId || res.every((one) => one.id !== item.parentId),
            (one, two) => one.id === two.parentId,
            (a, b) => (a.sort || 0) - (b.sort || 0),
            (item, children) => ({
                ...item,
                key: item.id,
                title: item.name,
                name: item.name,
                value: item.id,
                disabled: isIdInIntersection(item.id),
                // disableCheckbox: item.nodeType === '2',
                children: children.length > 0 ? children.map((i) => ({ ...i })) : null,
            })
        );
        console.log('res', res, findParentsWithDisabledChildren(dataTree));
        setState({
            treeDataSource: {
                treeData: dataTree,
                checkedKeys: res?.filter((item) => isIdInIntersection(item.id))?.map((item) => item.id) || [],
            },
        });
    };
    useUpdateEffect(() => {
        if (state.clicked) {
            getTreeData(tagList);
        }
    }, [state.clicked, tagList]);

    useDeepCompareEffect(() => {
        const $dom = document.getElementById('labelBox');
        setState({
            needMore: $dom?.clientHeight > 60,
        });
    }, [accInfoList, setState]);

    const labelBoxStyle = state.needMore ? { overflow: 'hidden', height: state.moreStatus ? 'auto' : '32px' } : {};

    return (
        // eslint-disable-next-line react/jsx-no-useless-fragment
        <Fragment>
            <div className={s.tagWrap}>
                {Array.isArray(accInfoList) && accInfoList.length > 0 ? (
                    <div className={s.companyTag}>
                        <div id="labelBox" style={labelBoxStyle}>
                            {accInfoList.map((v) => {
                                const [bgColor, color] = v.tagStyle ? v.tagStyle.split('-') : [];

                                return (
                                    <div
                                        style={{
                                            backgroundColor: bgColor,
                                            border: `1px solid ${color}`,
                                            color,
                                        }}
                                        className={s.baseLabel}
                                    >
                                        <span key={v.id} title={v.tagName} className={s.tagBox}>
                                            <span>{stringLengthFormat(v.tagName, 8)}</span>

                                            <Icon
                                                className={classNames('si si-com_closethin', s.removeTag)}
                                                onClick={() => {
                                                    handleRemoveTagPerson(v.id);
                                                }}
                                            />
                                        </span>
                                    </div>
                                );
                            })}
                        </div>

                        {state.needMore && (
                            <span
                                className={s.more}
                                // onMouseMove={() => setState({ moreStatus: true })}
                                // onMouseOut={() => setState({ moreStatus: false })}
                                onClick={() => setState({ moreStatus: !state.moreStatus })}
                            >
                                {!state.moreStatus && (
                                    <Fragment>
                                        更多 <i className="si si-com_angledownthin" />
                                    </Fragment>
                                )}
                                {state.moreStatus && (
                                    <Fragment>
                                        收起 <i className="si si-com_angleupthin" />
                                    </Fragment>
                                )}
                            </span>
                        )}
                    </div>
                ) : (
                    <div className={s.companyTag} />
                )}

                <Popover
                    destroyTooltipOnHide
                    className="ss"
                    getPopupContainer={(trigger) => trigger.parentNode}
                    content={
                        state.treeDataSource.treeData.length > 0 ? (
                            <TagTree
                                treeDataSource={state.treeDataSource}
                                onHide={() => {
                                    setState({ clicked: false });
                                }}
                                idNum={idNum}
                                refresh={refresh}
                            />
                        ) : null
                    }
                    open={state.clicked}
                    trigger="click"
                    title={
                        <div className={s['tagTree-title']}>
                            <span>人员标签</span>
                            <i
                                className="si si-com_closethin"
                                onClick={() => {
                                    setState({ clicked: false });
                                }}
                            />
                        </div>
                    }
                    onOpenChange={() => {
                        setState({ clicked: true });
                    }}
                >
                    <span className={s.addTag}>
                        <i className="si si-com_addto" />
                    </span>
                </Popover>
            </div>
        </Fragment>
    );
};

export default PersonTag;
