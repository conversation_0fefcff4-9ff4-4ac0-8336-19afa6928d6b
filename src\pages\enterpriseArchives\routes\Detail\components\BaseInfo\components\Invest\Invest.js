/*
 *@(#) Invest.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-10
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import ShareList, { Column, NumberColumn, useList } from '@share/list';
import React from 'react';
import { timeFormat } from '@/utils/format';
import NoData from '@/components/NoData/NoData';
import LinkText from '../../../LinkText/LinkText';

const Invest = ({ data = [] }) => {
    const listState = useList({ dataSource: data });

    return (
        <div className="shareListStyleCover">
            <ShareList listState={listState} usePageBar={false} emptyText={<NoData />}>
                <NumberColumn />
                <Column
                    label="被投资企业名称"
                    field="szjgmc"
                    render={(value, { szjgtyshxydm, szjghjqybs }, rowIndex) => {
                        if (szjghjqybs === '1') {
                            return (
                                <LinkText
                                    type="tab"
                                    tabOption={{ label: '企业档案', key: `qyxq-${szjgtyshxydm}-${rowIndex}` }}
                                    url={`${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/detail?id=${szjgtyshxydm}`}
                                >
                                    {value}
                                </LinkText>
                            );
                        }

                        return value;
                    }}
                />
                <Column label="日期" field="cjrq" render={(val) => timeFormat(val)} />
            </ShareList>
        </div>
    );
};

export default Invest;
