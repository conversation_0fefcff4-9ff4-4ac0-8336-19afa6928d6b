const { getQueryString } = require('@share/utils');
const BaseLog = require('./BaseLog');
const { ConsoleAppender } = require('./Appender');

class Log extends BaseLog {
    constructor(level, appenders) {
        super();
        const levelStr = getQueryString(window.location, 'log');

        this.level = level || (levelStr && getLevelFromString(levelStr)) || BaseLog.Level.INFO;
        if (appenders) {
            this.appenders = appenders;
        } else {
            this.appenders = [new ConsoleAppender()];
        }
        this.setLevel = this.setLevel.bind(this);
        this.setAppenders = this.setAppenders.bind(this);
    }

    setLevel(level) {
        this.level = level;

        return this;
    }

    setAppenders(appenders) {
        this.appenders = Array.isArray(appenders) ? appenders : [appenders];

        return this;
    }

    addAppenders(appender) {
        this.appenders.push(appender);

        return this;
    }

    print(args, level) {
        const levelCode = level.code;

        if (levelCode > this.level.code) {
            return;
        }
        const funName = level.name;

        this.appenders.forEach((appender) => {
            appender[funName](...args);
        });
    }
}

function getLevelFromString(levelStr) {
    const levelItem = Object.keys(BaseLog.Level)
        .map((levelKey) => BaseLog.Level[levelKey])
        .find((level) => level.name === levelStr);

    if (levelItem == null) {
        console.warn(`未找到对应的日志Level str = ${levelStr}`);
    }

    return levelItem;
}

module.exports = new Log();
