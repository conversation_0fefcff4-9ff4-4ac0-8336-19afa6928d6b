/*
 * @(#)  industryPictureStore.js 产业一张图页面使用到的状态集合
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 * Copyright: Copyright (c) 2023
 * Company: 厦门畅享信息技术有限公司
 * Author: yujy (<EMAIL>)
 * File Created: 2023-05-08 09:59:42
 */
import create from 'zustand';

export const useStore = create((set) => ({
    // listState: {}, // 规模企业使用
    // regionalStatisticsData: {}, // 存放划片区域的数据
    // toolbarObj: {}, // 底部工具栏相关数据
    // rightToolBarObj: {}, // 右侧工具栏相关数据
    // setListState: listState => set({
    //     listState
    // }),
    mapObj: {}, // 存放地图相关的实例
    setMapObj: (mapObj) =>
        set({
            mapObj,
        }),
    // setRegionalStatisticsData: regionalStatisticsData => set(state => ({
    //     ...state.regionalStatisticsData,
    //     regionalStatisticsData
    // })),
    // setToolbarObj: toolbarObj => set({
    //     toolbarObj
    // }),
    // setRightToolBarObj: rightToolBarObj => set(state => ({
    //     ...state.rightToolBarObj,
    //     rightToolBarObj
    // })),
    selectedXzqh: '', // 存放选择的行政区划
    setSelectedXzqh: (selectedXzqh) =>
        set({
            selectedXzqh,
        }),
}));
