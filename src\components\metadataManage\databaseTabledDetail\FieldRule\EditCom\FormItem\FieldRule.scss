@charset "utf-8";

.fieldCheckRuleEdit{
    :global{
        .form-wrap .form-table>tbody>tr>td{
            padding: 5px 0;
            border: none;
        }
        .form-wrap .item{
            .sub-item{
                padding: 0 0 10px 0;
                border-bottom: 1px solid #ddd;
                >input{
                    width: 44em!important;
                }
                .dsgcp{
                    display: inline-block;
                    width: 44em;
                    margin: 10px 0 0 0;
                }
            }
            &:last-child .sub-item{
                border-bottom: none;
            }
        }


    }
};

:global{
    .databaseTableFieldEditModal{
        .item-sm.item .sub-item{
            padding: 10px 20px 10px 14px!important;
        }
    }
}

.nonBorder{
    :global{
        .form-wrap .form-table>tbody>tr>td{
            padding: 5px 0;
            border: none;
            .sub-item{
                padding: 0;
            }
        }
    }
}
.noPadding{
    :global{
        .form-wrap .form-table {
            border: 1px solid #ddd;
        }
        .form-wrap .form-table>tbody>tr>td{
           &:last-child {
               border-top: none;
           }

            .sub-item{ border-top: none;
                position: relative;
                padding: 14px;
            }
        }
    }
}

.del{
    position: absolute;
    right: 0;
    top: 0;
    color:#fff;
    //display: flex;
    //align-items: center;
    font-size: 22px;
    width: 0;
    height: 0;
border-color: #f65 #f65 transparent transparent;
    border-width: 25px 25px 25px 25px;
    border-style: solid ;
    cursor: pointer;
    i {
        color: #FFF;
        position: absolute;
        top: -20px;
    }
}
.customPosition{
    float: right;
    margin: -28px 10px 0 0;
}

.widthBtn{
    display: inline-block;
    vertical-align: middle;
    :global{
        .Select{
            width: 100%;
            margin: 0;
        }
    }
}

.topBottomPadding{
    padding: 10px 20px 10px 14px !important;
}
