import React, { Component, Fragment } from 'react';
// 接口
import * as DataQueryApi from '@/services/data/data/DataQueryApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 组件
import QueryList from '@/components/business/metadata/QueryList';
import RoleAuthControl from '@/components/business/auth/RoleAuthControl';
import Message from '@/components/ui/MyAlert/Message';
import { Button, Icon, Panel } from '@share/shareui';
import MultiClamp from '@/components/ui/MultiClamp';

class DataCoverTable extends Component {
    // 数据列表请求接口
    buildDataListApi = (searchBody) => {
        const { categoryCode } = this.props;

        return DataQueryApi.listByCategory(categoryCode, '2', searchBody);
    };

    // 列表展示列
    extendHeadColumns = () => {
        return [
            {
                title: '目录名称',
                width: 200,
                // fixed: 'left',
                dataIndex: ['system', 'CATALOG_NAME'],
                render: (value) => <MultiClamp title={value}>{value}</MultiClamp>,
            },
        ];
    };

    extendTailColumns = () => {
        const { history, categoryCode, metaConfigList } = this.props;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);

        return [
            // {
            //     title: '处理状态',
            //     width: 80,
            //     fixed: 'right',
            //     dataIndex: 'system.YW_TYPE',
            //     render: value => {
            //         const showValue = value === '4' ? '已处理' : '未处理';
            //         const color = value === '4' ? '#01ba88' : '#ff6655';
            //
            //         return (
            //             <span title={showValue} style={{ color }}>
            //                 {showValue}
            //             </span>
            //         );
            //     }
            // },
            {
                title: '操作',
                width: 70,
                fixed: 'right',
                key: 'operate',
                dataIndex: 'system',
                render: (rowData) => (
                    <div className="tableBtn">
                        <a onClick={() => history.push(`/creditDataManageDetail/${rowData.CATALOG_ID}/${rowData[primaryKey]}`)}>详情</a>
                        {/* { */}
                        {/*    rowData.YW_TYPE === '1' && */}
                        {/*    <RoleAuthControl buttonKey="meta-data-repair-correct"> */}
                        {/*        <a */}
                        {/*            onClick={() => */}
                        {/*                history.push( */}
                        {/*                    `/creditDataAuditDetail/${categoryCode}/${rowData.__XDR_LB || rowData.XDR_LB || '2'}/${rowData.YW_ID}` */}
                        {/*                ) */}
                        {/*            } */}
                        {/*        >修正</a> */}
                        {/*    </RoleAuthControl> */}
                        {/* } */}
                    </div>
                ),
            },
        ];
    };

    // 导出Excel
    excelExport = (recordIds) => {
        const { categoryCode, categoryCodeList } = this.props;
        const category = categoryCodeList.find((v) => v.id === categoryCode) || {};
        const param = JSON.stringify({
            fileName: `${category.name}-已覆盖`,
            recordIds,
        });

        Message.success('导出中...');
        DataQueryApi.exportByCategory(categoryCode, '2', param);
    };

    render() {
        const { selectedRecords, onSelectedRecordsChange } = this.props;
        const { categoryCode, metaConfigList, body, ...otherProps } = this.props;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);
        const listConfig = MetaConfigUtils.filterListConfig(metaConfigList);
        const selectedRowKeys = selectedRecords.map((item) => item.system[primaryKey]);

        return (
            <Fragment>
                <ul className="ui-list-horizontal" style={{ position: 'absolute', zIndex: 2, top: '-30px', right: 0 }}>
                    <RoleAuthControl buttonKey="meta-data-repair-repeat-batch-export">
                        <li>
                            <Button
                                type="button"
                                className="btn-xs"
                                border={false}
                                onClick={() => this.excelExport(selectedRowKeys)}
                                disabled={selectedRowKeys.length === 0}
                            >
                                <Icon className="si si-com_export" />
                                批量导出
                            </Button>
                        </li>
                    </RoleAuthControl>
                </ul>
                <Panel>
                    <Panel.Body full>
                        <QueryList
                            namespace="DataCoverTable"
                            service={{
                                api: this.buildDataListApi,
                                body,
                            }}
                            rowKey={(data) => data.system[primaryKey]}
                            metadataConfigList={listConfig}
                            extendHeadColumns={this.extendHeadColumns()}
                            extendTailColumns={this.extendTailColumns()}
                            rowSelection={{
                                fixed: true,
                                selectedRowKeys,
                                onChange: (_selectedRowKeys, selectedRows) => onSelectedRecordsChange(selectedRows),
                            }}
                            pagination={{ detectPage: true, showTotal: true }}
                            {...otherProps}
                        />
                    </Panel.Body>
                </Panel>
            </Fragment>
        );
    }
}

export default DataCoverTable;
