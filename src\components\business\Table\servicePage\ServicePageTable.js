import React, { Component } from 'react';
// 工具类
import * as Entity from '@/components/common/common/Entity';
// 子组件
import Table from '../ui/CommonPageTable';

class ServicePageTable extends Component {
    static defaultProps = {
        ...Table.defaultProps,
        pagination: {
            ...Table.defaultProps.pagination,
            detectPageRequest: true,
        },
        service: {
            api: () => ({}), // 服务接口promise获取函数
            body: {}, // 服务接口参数
        },
    };

    state = {
        loading: false, // 表格加载状态
        param: {}, // 表格搜索参数
        dataSource: [], // 表格渲染数据
        page: {
            current: this.props.pagination.defaultCurrent || 1, // 表格当前页
            pageSize: this.props.pagination.defaultPageSize || 10, // 表格单页显示数
            total: this.props.pagination.defaultCurrent || 0, // 表格数据总数
            detectTotal: 0, // 渐步总数
        },
        sort: {
            field: '', // 排序字段
            order: '', // 排序方式（ascend|descend）
        },
    };

    // 初始化组件
    componentWillMount() {
        const {
            service: { body },
        } = this.props;

        this.setState({ loading: true, param: { ...body } }, this.refreshTable);
    }

    // 自身引用传递给父组件
    componentDidMount() {
        const { onRef } = this.props;

        onRef && onRef(this);
    }

    // 搜索参数发生变化时（点击搜索按钮事件），当前页置为1
    componentWillReceiveProps(nextProps) {
        const { service: { api } = {} } = this.props;
        const {
            service: { api: newApi, body: newBody },
        } = nextProps;
        const {
            loading,
            param,
            page: { pageSize },
        } = this.state;

        if (loading) {
            return;
        }
        if (api !== newApi || !Entity.isEqualObject(param, newBody)) {
            this.setState({ loading: true, param: newBody, page: { current: 1, pageSize } }, this.refreshTable);
        }
    }

    // 列表数据变更事件
    onTableDataChange = (data) => {
        const { onDataChange } = this.props;

        onDataChange && onDataChange(data);
    };

    // antd分页条、过滤、排序发生变化
    onChange = (pagination, filters, sorter) => {
        const { onChange } = this.props;

        this.handleSort(sorter.columnKey, sorter.order);
        onChange && onChange(pagination, filters, sorter);
    };

    // 页数参数发生变化时（分页条点击事件）调用函数
    handlePagination = (current, pageSize) => {
        const { page } = this.state;

        if (current !== page.current || pageSize !== page.pageSize) {
            this.setState({ loading: true, page: { ...page, current, pageSize } }, this.refreshTable);
        }
    };

    // 排序发生变化
    handleSort = (field, order) => {
        const { sort } = this.state;

        if (field !== sort.field || order !== sort.order) {
            this.setState({ loading: true, sort: { ...sort, field, order } }, this.refreshTable);
        }
    };

    // 刷新数据
    refreshTable = (extendParam) => {
        const {
            pagination: { detectPage, detectPageRequest },
        } = this.props;
        const {
            param,
            page: { current, pageSize },
            sort: { field, order },
        } = this.state;
        const reqParam = {
            // shareList1.0参数
            currentPage: current,
            linesPerPage: pageSize,
            condition: { ...param, ...extendParam },
            // shareList2.0参数
            page: { currentPage: current, linesPerPage: pageSize },
            data: { ...param, ...extendParam },
        };

        // 处理排序参数
        if (field && order) {
            // shareList1.0参数
            reqParam.sortField = field;
            reqParam.sortType = order.replace('end', '');
            // shareList2.0参数
            reqParam.page.orderBys = [{ sortField: reqParam.sortField, sortType: reqParam.sortType }];
        }
        if (detectPage && detectPageRequest) {
            this.refreshDetectPageTable(reqParam);
        } else {
            this.requestTable(reqParam);
        }
    };

    requestTable = async (reqParam) => {
        const {
            service: { api },
        } = this.props;
        const { param, page } = this.state;
        let reqResult;

        try {
            reqResult = await api(reqParam);
        } catch (e) {
            reqResult = {
                list: [],
                page: {
                    totalNum: 0,
                    currentPage: page.current,
                    linesPerPage: page.pageSize,
                },
            };
        }
        const { list, page: { totalNum: total, currentPage: current, linesPerPage: pageSize } = {} } = reqResult;

        if (list.length === 0 && total > 0 && current > 1) {
            const pageCount = Math.ceil(total / pageSize);

            this.setState(
                {
                    page: {
                        current: pageCount,
                        pageSize,
                        total,
                        detectTotal: total,
                    },
                },
                this.refreshTable
            );

            return;
        }
        const data = {
            loading: false,
            param,
            dataSource: list,
            page: {
                current,
                pageSize,
                total,
                detectTotal: total,
            },
        };

        this.setState({ ...this.state, ...data }, () => this.onTableDataChange(data));
    };

    refreshDetectPageTable = async (reqParam) => {
        const {
            detectStep = 10,
            showTotal,
            service: { api },
        } = this.props;
        const { param, page } = this.state;

        // 处理渐步式分页参数
        // shareList1.0参数
        reqParam.detectPage = true;
        reqParam.detectStep = detectStep;
        // shareList2.0参数
        reqParam.page.detectPage = true;
        reqParam.page.detectStep = detectStep;
        let reqResult;

        try {
            reqResult = await api(reqParam);
        } catch (e) {
            reqResult = {
                list: [],
                page: {
                    currentPage: page.current,
                    linesPerPage: page.pageSize,
                    totalNum: 0,
                },
            };
        }
        const { list, page: { totalNum, currentPage: current, linesPerPage: pageSize } = {} } = reqResult;
        const detectTotal = totalNum + (current - 1) * pageSize;

        if (list.length === 0 && detectTotal > 0 && current > 1) {
            const pageCount = Math.ceil(detectTotal / pageSize);

            this.setState(
                {
                    page: {
                        current: pageCount,
                        pageSize,
                        total: 0,
                    },
                },
                this.refreshTable
            );

            return;
        }
        const total = detectTotal < (current + Math.ceil(detectStep / 2)) * pageSize ? detectTotal : null;
        const data = {
            loading: false,
            param,
            dataSource: list,
            page: {
                current,
                pageSize,
                total,
                detectTotal,
            },
        };

        this.setState({ ...this.state, ...data }, () => {
            this.onTableDataChange(data);
            if (showTotal && data.page.total == null) {
                this.refreshDetectPageTotal(reqParam);
            }
        });
    };

    refreshDetectPageTotal = async (reqParam) => {
        const {
            service: { api },
        } = this.props;
        let reqResult;

        // shareList1.0参数
        reqParam.detectPage = false;
        reqParam.currentPage = 1;
        reqParam.linesPerPage = -1;
        // shareList2.0参数
        reqParam.page.detectPage = false;
        reqParam.page.currentPage = 1;
        reqParam.page.linesPerPage = -1;
        // 取消loading
        reqParam.noMask = true;
        try {
            reqResult = await api(reqParam);
        } catch (e) {
            reqResult = {
                list: [],
                page: {
                    totalNum: 0,
                },
            };
        }
        const { page: { totalNum } = {} } = reqResult;
        const data = {
            ...this.state,
            page: {
                ...this.state.page,
                total: totalNum,
            },
        };

        this.setState({ ...data }, () => this.onTableDataChange(data));
    };

    render() {
        const { pagination, ...restProps } = this.props;
        const { dataSource = [], page } = this.state;

        return (
            <Table
                {...restProps}
                onChange={this.onChange}
                dataSource={dataSource}
                pagination={{
                    ...pagination,
                    ...page,
                    onChange: this.handlePagination,
                }}
            />
        );
    }
}

export default ServicePageTable;
