import { useState } from 'react';
import { message } from 'antd';
import { useService, useCodeMapping, navigate } from '@share/framework';
import { useForm } from '@share/shareui-form';
import { useList } from '@share/list';
import MetaFieldApi from '@/services/MetaFieldApi';

export const useModel = () => {
    const [executeFrequencyOptions] = useCodeMapping('executeFrequencyEnum');
    const services = useService(MetaFieldApi);
    // 表单
    const [, formState] = useForm({});
    // 列表
    const listState = useList({
        dataSource: services.tagRuleList,
    });
    // 编辑
    const [showEditModal, setShowEditModal] = useState(false);
    const [, editFormState] = useForm({});
    // 事件
    const query = (condition) => {
        listState.query(condition);
    };
    const detail = (row) => {
        navigate(`PlayingExecutionConfig?id=${row.id}`);
    };
    const edit = async (row) => {
        const de = await services.tagRuleDetail(row.id);
        editFormState.setFormData(de);
        setShowEditModal(true);
    };
    const submitEdit = async () => {
        if (!(await editFormState.validHelper())) {
            return;
        }
        await services.tagRuleSave(editFormState.getFormData());
        message.success('操作成功');
        listState.refresh();
        setShowEditModal(false);
    };
    const switchStatus = async (row) => {
        const detail = await services.tagRuleDetail(row.id);
        detail.status = row.status === '1' ? '0' : '1';
        await services.tagRuleSave(detail);
        message.success('操作成功');
        listState.refresh();
    };
    const execute = async (row) => {
        await services.tagRuleExecute(row.id);
        message.success('执行成功');
        listState.refresh();
    };
    const remove = async (row) => {
        await services.tagRuleRemove(row.id);
        message.success('删除成功');
        listState.refresh();
    };

    return {
        executeFrequencyOptions,
        formState,
        listState,
        showEditModal,
        setShowEditModal,
        editFormState,
        query,
        detail,
        edit,
        submitEdit,
        switchStatus,
        execute,
        remove,
    };
};
