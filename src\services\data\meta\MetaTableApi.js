/*
 * @(#) dataBaseApi.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-07-30 11:55:16
 */
import { get, postJson } from '@/components/common/network/Network';

/**
 * 获取数据库表信息列表
 */
export const getDatabaseSource = () => {
    return postJson('/edp-front/meta/table/source.do');
};

/**
 * 获取数据库数据表字段
 */
export const getDatabaseField = (param) => {
    return postJson('/edp-front/meta/table/source/field.do', param);
};

/**
 * 获取系统字段
 */
export const getSystemField = () => {
    return get('/edp-front/meta/table/system/field.do');
};

/**
 * 获取数据表信息列表
 */
export const getDataTableList = () => {
    return postJson('/edp-front/meta/table/all.do', {});
};

/**
 * 获取数据表信息详情
 * @param tableId 数据表名
 * @returns Promise 表信息详情对象
 */
export const getDataTableDetail = (tableId) => {
    return get(`/edp-front/meta/table/detail/${tableId}.do`);
};

/**
 * 获取数据库表信息详情
 * @param tableId 数据表名
 * @param param 参数
 * @returns Promise 表信息详情对象
 */
export const getDataTableRecordCount = (tableId, param) => {
    return get(`/edp-front/meta/table/record_count/${tableId}.do`, param);
};

/**
 * 添加数据库表信息
 * @param submitBody 数据表信息对象
 * @returns Promise 是否操作成功
 */
export const addDataTable = (submitBody) => {
    return postJson('/edp-front/meta/table/add.do', submitBody);
};

/**
 * 修改数据库表信息
 * @param submitBody 数据表信息对象
 * @returns Promise 是否操作成功
 */
export const updateDataTable = (submitBody) => {
    return postJson('/edp-front/meta/table/update.do', submitBody);
};

/**
 * 删除数据库表信息
 * @param tableId 数据表名
 * @returns Promise 是否操作成功
 */
export const deleteDataTable = (tableId) => {
    return postJson(`/edp-front/meta/table/delete/${tableId}.do`);
};

/**
 * 删除数据库表信息
 * @param param 参数
 * @returns Promise 是否操作成功
 */
export const createDataTable = (param) => {
    return postJson('/edp-front/meta/table/create.do', param);
};
