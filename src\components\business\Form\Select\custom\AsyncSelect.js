import React, { Component } from 'react';
import { Select } from '@share/shareui';
import debounce from 'lodash/debounce';
import { getAdaptComponent } from '../../Custom';

const cacheMap = {};

class AsyncSelect extends Component {
    static defaultProps = {
        simpleValue: true, // 简单模式
        emptySearch: true, // 控制搜索
        service: Promise.resolve(), // 请求接口
        paramFn: () => ({}), // 参数函数
        resultFn: (data) => data, // 结果函数
        labelKey: 'label', // label属性键
        valueKey: 'code', // value属性键
        defaultMapping: {}, // 映射map
        namespace: 'namespace', // 缓存空间
    };

    onChange = (value) => {
        const { simpleValue, labelKey, valueKey, onChange } = this.props;
        let convertValue = value || {};

        if (simpleValue) {
            if (Array.isArray(convertValue)) {
                convertValue = convertValue.map((item) => {
                    this.mappingMap()[item[valueKey]] = item[labelKey];

                    return item[valueKey];
                });
            } else {
                this.mappingMap()[convertValue[valueKey]] = convertValue[labelKey];
                convertValue = convertValue[valueKey];
            }
        } else if (Array.isArray(convertValue)) {
            convertValue = convertValue.map((item) => ({ ...item }));
        } else {
            convertValue = { ...convertValue };
        }

        return onChange && onChange(convertValue, value);
    };

    mappingMap = () => {
        const { namespace } = this.props;

        if (!cacheMap[namespace]) {
            cacheMap[namespace] = {};
        }

        return cacheMap[namespace];
    };

    convertSingleValue = (value) => {
        const { labelKey, valueKey, defaultMapping } = this.props;
        const mappingMap = { ...defaultMapping, ...this.mappingMap() };

        if (typeof value === 'string') {
            return {
                [labelKey]: mappingMap[value] || value,
                [valueKey]: value,
            };
        }

        return value;
    };

    convertValue = (value) => {
        if (Array.isArray(value)) {
            return value.map((item) => this.convertSingleValue(item));
        }

        return this.convertSingleValue(value);
    };

    requestFn = async (value = '', callbackFn) => {
        const { service, paramFn, resultFn } = this.props;
        const requestParams = paramFn(value);
        const result = await service({ ...requestParams, noMask: true });
        const handleResult = resultFn(result);

        this.searchCache = this.searchCache || {};
        this.searchCache[value] = handleResult;

        return callbackFn(null, { options: handleResult });
    };

    loadOptions = (input, callback) => {
        const { emptySearch } = this.props;

        // 空值处理
        if (!input && !emptySearch) {
            callback(null, { options: [] });

            return;
        }
        // 缓存处理
        if (this.searchCache && this.searchCache[input]) {
            callback(null, { options: this.searchCache[input] });

            return;
        }
        // 防抖
        if (!this.debounceRequestFn) {
            this.debounceRequestFn = debounce(this.requestFn, 1000);
        }
        this.debounceRequestFn(input, callback);
    };

    render() {
        const { value, onChange, ...restProps } = this.props;

        return (
            <Select.Async
                loadOptions={this.loadOptions}
                {...restProps}
                onChange={this.onChange}
                value={this.convertValue(value)}
                simpleValue={false}
            />
        );
    }
}

export default getAdaptComponent(AsyncSelect, { loadingPlaceholder: '加载中...' });
