import { routesHelper, USER_PLUGIN_NAME, WATERMARK_PLUGIN_NAME } from '@share/framework';

/**
 * 路由配置
 */
export const routes = routesHelper([
    {
        path: '/query',
        component: () => import('@/pages/detailQuery/routes/List'), // 只支持动态import
        disabledLoader: [USER_PLUGIN_NAME], // 关闭用户信息加载
        disabledProvider: [WATERMARK_PLUGIN_NAME], // 关闭水印
        // ignoreUIBox: true, // 忽略 .ui-box 容器边距
        // authExpression:[], //权限表达式
        title: '明细查询', // 页面标题
        // exact: false, // 精确匹配
        // sensitive: false, // 精确大小写匹配 true => 区分大小写
        // strict: false // true => 匹配末尾斜杠
    },
]);
