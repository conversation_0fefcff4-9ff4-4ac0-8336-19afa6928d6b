/*
 *@(#) SearchTree.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-07-02
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React, { Component } from 'react';
import { Tree, Input } from 'antd';
import _ from 'lodash';
// import debounce from 'lodash.debounce';

const { TreeNode } = Tree;
const { Search } = Input;

class SearchTree extends Component {
    state = {
        expandedKeys: [],
        searchValue: '',
        autoExpandParent: true,
        filterTreeData: [...this.props.treeData],
    };

    componentWillReceiveProps(nextProp) {
        if (
            JSON.stringify(nextProp.targetKeys) !== JSON.stringify(this.props.targetKeys) ||
            JSON.stringify(nextProp.searchEvent) !== JSON.stringify(this.props.searchEvent)
        ) {
            const val = nextProp.searchEvent ? nextProp.searchEvent.value || '' : this.state.searchValue;

            this.onChange({ target: { value: val } });
        }
    }

    onExpand = (expandedKeys) => {
        this.setState({
            expandedKeys,
            autoExpandParent: false,
        });
    };

    onChange = (e) => {
        const { value } = e.target;
        const { treeData, filterMode } = this.props;
        const { filterTreeData } = this.state;

        if (!value) {
            return this.setState({
                filterTreeData: [...treeData],
                searchValue: value,
            });
        }

        if (filterMode === 'default') {
            const expandedKeys = this.generateList(filterTreeData)
                .map((item) => {
                    if (item.title.indexOf(value) > -1) {
                        return this.getParentKey(item.key, filterTreeData);
                    }

                    return null;
                })
                .filter((item, i, self) => item && self.indexOf(item) === i);

            this.setState({
                expandedKeys,
                searchValue: value,
                autoExpandParent: true,
            });
        } else if (filterMode === 'filterTree') {
            const tempData = this.convertMenuNames(_.cloneDeep(treeData), value);

            this.setState({
                filterTreeData: tempData,
                searchValue: value,
                expandedKeys: this.generateList(tempData).map((v) => v.key),
                autoExpandParent: false,
            });
        }
    };

    getParentKey = (key, tree) => {
        let parentKey;

        for (let i = 0; i < tree.length; i++) {
            const node = tree[i];

            if (node.children) {
                if (node.children.some((item) => item.key === key)) {
                    parentKey = node.key;
                } else if (this.getParentKey(key, node.children)) {
                    parentKey = this.getParentKey(key, node.children);
                }
            }
        }

        return parentKey;
    };

    generateList = (data) => {
        if (!data || data.length === 0) {
            return [];
        }
        const dataList = [];
        const iterator = (d) => {
            for (let i = 0; i < d.length; i++) {
                const node = d[i];
                const { key, title } = node;

                dataList.push({ key, title });
                if (node.children) {
                    iterator(node.children);
                }
            }
        };

        iterator(data);

        return dataList;
    };

    convertMenuNames = (sourceData, inputValue) => {
        if (!Array.isArray(sourceData) || sourceData.length === 0) {
            return [];
        }

        return sourceData.filter((menu) => {
            menu.children = menu.children ? this.convertMenuNames(menu.children, inputValue) : null;

            // Array.isArray(menu.children) && menu.children.length > 0 && this.convertMenuNames(menu.children, inputValue);
            // if (menu.title.includes(inputValue)) {
            //     menu.showTitle = this.convertShowName(menu.title, inputValue);
            // }
            return menu.title.includes(inputValue) || (Array.isArray(menu.children) && menu.children.length > 0);
        });
    };

    render() {
        const { searchValue, expandedKeys, autoExpandParent, filterTreeData } = this.state;
        const { treeData, className, filterMode, searchEvent, ...resProps } = this.props;
        const loop = (data) =>
            data.map((item) => {
                const index = item.title.indexOf(searchValue);
                const beforeStr = item.title.substr(0, index);
                const afterStr = item.title.substr(index + searchValue.length);
                const title =
                    index > -1 ? (
                        <span title={item.title}>
                            {beforeStr}
                            <span style={{ color: '#f50' }}>{searchValue}</span>
                            {afterStr}
                        </span>
                    ) : (
                        <span title={item.title}>{item.title}</span>
                    );

                if (item.children) {
                    return (
                        <TreeNode {...item} key={item.key} title={title}>
                            {loop(item.children)}
                        </TreeNode>
                    );
                }

                return <TreeNode {...item} key={item.key} title={title} />;
            });

        return (
            <div className={className}>
                {!searchEvent && <Search style={{ marginBottom: 8 }} placeholder="请输入搜索内容" onChange={this.onChange} />}
                {filterTreeData.length > 0 ? (
                    <Tree onExpand={this.onExpand} expandedKeys={expandedKeys} autoExpandParent={autoExpandParent} {...resProps}>
                        {loop(filterTreeData)}
                    </Tree>
                ) : (
                    <div style={{ padding: '3px' }}>无搜索项</div>
                )}
            </div>
        );
    }
}

SearchTree.defaultProps = {
    treeData: [
        {
            title: '0-0',
            key: '0-0',
            children: [
                {
                    title: '0-0-0',
                    key: '0-0-0',
                    children: [
                        {
                            title: '0-0-0-0',
                            key: '0-0-0-0',
                        },
                        {
                            title: '0-0-0-1',
                            key: '0-0-0-1',
                        },
                        {
                            title: '0-0-0-2',
                            key: '0-0-0-2',
                        },
                    ],
                },
                {
                    title: '0-0-1',
                    key: '0-0-1',
                    children: [
                        {
                            title: '0-0-1-0',
                            key: '0-0-1-0',
                        },
                        {
                            title: '0-0-1-1',
                            key: '0-0-1-1',
                        },
                        {
                            title: '0-0-1-2',
                            key: '0-0-1-2',
                        },
                    ],
                },
                {
                    title: '0-0-2',
                    key: '0-0-2',
                },
            ],
        },
    ],
    filterMode: 'default',
};

export default SearchTree;
