import { useEffect } from 'react';
import { useForm } from '@share/shareui-form';
import { useService, useCodeMapping } from '@share/framework';

import MetaFieldApi from '@/services/MetaFieldApi';
import { message } from 'antd';

export const mockTagsStyleOptions = [
    { label: '镂空蓝色', value: '#fff-rgb(29,139,243)' },
    { label: '镂空红色', value: '#fff-rgb(246,2,2)' },
    { label: '镂空黄色', value: '#fff-rgb(248,117,7)' },
    { label: '镂空绿色', value: '#fff-rgb(14,187,73)' },
    { label: '镂空黑色', value: '#fff-rgb(51,51,51)' },
    { label: '灰底白字', value: '#c9c5c5-#fff' },
    { label: '黑底白字', value: '#333-#fff' },
    { label: '蓝底白字', value: '#1d8bf3-#fff' },
    { label: '红底白字', value: '#f60000-#fff' },
    { label: '黄底白字', value: '#faac06-#fff' },
    { label: '绿底白字', value: '#0ebb49-#fff' },
    { label: '蓝底黑字', value: '#1d8bf3-#333' },
    { label: '黄底红字', value: '#faac06-#FA0303' },
];
export const useCodeMap = () => {
    const [DM_TAG_QUERY_SCOPE, DM_TAG_STYLE] = useCodeMapping('DM_TAG_QUERY_SCOPE', 'DM_TAG_STYLE');

    return { DM_TAG_QUERY_SCOPE, DM_TAG_STYLE };
};
const useHooks = ({ selectNode, refresh }) => {
    const [formData, form] = useForm(
        {
            parentId: '',
            tagStyle: [],
            description: '',
            nodeType: '',
            name: '',
            rule: {},
        },
        {
            willClearEmptyField: true,
        }
    );

    const services = useService(MetaFieldApi);
    const formValid = async () => {
        if (!(await form.validHelper())) {
            return;
        }
        const { title, id, key, rule, ...other } = formData;
        const noXjbq = {
            ...other,
            id,
            key,
            children: [],
            title: formData.name,
        };
        const params = id.includes('新建标签') ? other : noXjbq;
        const res = params.id ? await services.personTagInfoUpdate(params) : await services.personTagInfoAdd(params);
        if (res) {
            const saveRuleRes = await services.personTagRuleSave({
                id: res.id,
                rule,
            });
            message.success('保存成功', 1.5, () => {
                window.location.reload();
            });
        }
    };
    useEffect(() => {
        form.reset();
        form.setFieldValues(selectNode);
    }, [form, selectNode]);

    return { form, formValid };
};

export default useHooks;
