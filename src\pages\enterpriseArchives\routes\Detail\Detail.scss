.division {
    height: 12px;
    background-color: #EBECEE;
}

.detailPageTop {
    padding: 20px;
}

.detailPageContent {
    position: relative;
    padding-bottom: 40px;
}

.detailPageLeft {
    width: 200px;
    border-right: 1px solid #EBECED;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;

    .menu {
        overflow-y: auto;
        overflow-x: hidden;
        width: 100%;
        height: 100%;
    }

    .trigger {
        position: absolute;
        right: -10px;
        top: 0;
        width: 10px;
        height: 48px;
        background-color: #C2DCFF;
        border-radius: 0 10px 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 14px;
        color: #1677ff;
    }
}

.detailPageRight {
    margin-left: 200px;
    padding: 24px;
    min-height: 650px;
}

.tagTitle {
    font-size: 16px;
    font-weight: bold;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 16px;
    margin-top: 24px;

    span {
        color: #0061e8;
        margin-left: 8px;
    }
}

.btnGroup {
    text-align: right;
    margin-right: 24px;
}