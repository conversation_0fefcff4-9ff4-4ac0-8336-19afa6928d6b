// dva
import { connect } from 'dva';

const getDvaCacheTable = (CacheTable) => {
    class DvaPageTable extends CacheTable {
        static defaultProps = {
            ...CacheTable.defaultProps,
        };

        // 缓存数据
        saveCacheData = (namespace, data) => {
            const { dispatch } = this.props;

            dispatch({
                type: 'table/refreshTableData',
                namespace,
                ...data,
            });
        };

        // 获取缓存数据
        getCacheData = (namespace) => {
            const { table } = this.props;

            return table[namespace];
        };
    }

    return connect(({ table }) => ({ table }))(DvaPageTable);
};

export default getDvaCacheTable;
