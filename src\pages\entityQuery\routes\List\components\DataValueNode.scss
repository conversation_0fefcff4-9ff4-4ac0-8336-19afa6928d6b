.body {
    width: 100%;
    align-items: center;
}

.operate {
    display: flex;
    align-items: flex-start;
}

.switch {
    > i {
        margin-top: 8px;
        color: green;
    }
}

.inventorySelect {
    display: flex;
    align-items: flex-start;
    > div {
        width: 180px;
        display: inline-block !important;
    }
}

.paramCondition {
    margin: 10px 0 0 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    > div {
        padding-bottom: 10px !important;
    }
}
