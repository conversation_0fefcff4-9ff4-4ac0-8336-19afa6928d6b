/*
 *@(#) DataRepairAudit.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-26
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import React, { Component } from 'react';
// 服务接口
import * as DataTrcApi from '@/services/data/data/DataTrcApi';
// 工具类
import * as StringUtils from '@/components/common/common/StringUtils';
// 组件
import { Panel, Button, ButtonToolBar, FormItem } from '@share/shareui';
import TimeLine from '@/components/business/TimeLine';
import DataRepairTimeLine from '@/components/credit_data/TimeLine/DataRepairTimeLine';
import { ShareForm, getComponents } from '@/components/business/Form';

const { Select, RangeTime } = getComponents('div');

// 表码管理器
const bmManage = require('@/components/common/business/manager/BmManager');

const defaultForm = {
    USER_NAME: '',
    ACTION_TIME: { start: '', end: '' },
};

class DataRepairAudit extends Component {
    state = {
        searchForm: { ...defaultForm }, // 搜索条件
        actionUserNameOptions: [], // 操作用户对下拉列表数据
        dataList: [], // 数据列表
        showDataList: [],
    };

    async componentDidMount() {
        const {
            match: {
                params: { categoryId, ywid },
            },
        } = this.props;
        // 获取数据
        const { list } = await DataTrcApi.detailsByCategory(categoryId, '2', { data: { YW_ID: ywid }, page: null });
        const errorTypeBmList = bmManage.getBmList('DM_DATA_VALID_ERROR_TYPE');

        // 错误类型处理
        list.forEach((item) => {
            if (item.system.YW_RESULT && !Array.isArray(item.system.YW_RESULT)) {
                item.system.YW_RESULT = item.system.YW_RESULT.split(',');
            }
            if (item.system.ERROR_TYPES && !Array.isArray(item.system.ERROR_TYPES)) {
                item.system.ERROR_TYPES = item.system.ERROR_TYPES.split(',');
            }
            if (Array.isArray(item.system.ERROR_TYPES)) {
                item.system.ERROR_TYPES = item.system.ERROR_TYPES.map(
                    (a) => (errorTypeBmList.find((b) => b.code === a) || { label: a }).label
                ).join('，');
            }
        });
        // 修正列表（操作过问题表）
        const dataList = list.filter(
            (item) => item.system.YW_RESULT && (item.system.YW_RESULT.includes('1') || item.system.YW_RESULT.includes('2'))
        );

        dataList.forEach((item, index) => {
            // 填充前一条数据
            item.preData = dataList[index + 1] || null;
            // 填充时间戳
            item.date = item.system.CREATE_TIME ? item.system.CREATE_TIME.substr(0, 10) : '无';
            item.categoryCode = categoryId;
            item.objectType = '2';
        });
        // 计算操作用户名称下拉
        const actionUserNameOptions = [...new Set(dataList.map((item) => item.system.USER_NAME).filter((item) => item))].map((item) => ({
            label: item,
            value: item,
        }));

        this.setState({
            actionUserNameOptions,
            dataList,
            showDataList: dataList,
        });
    }

    // 查询
    handleSearch = () => {
        const { searchForm, dataList } = this.state;
        const body = StringUtils.deleteSpace(searchForm);
        let showDataList = dataList;

        if (body.USER_NAME) {
            showDataList = showDataList.filter((item) => item.system.USER_NAME === body.USER_NAME);
        }
        if (body.ACTION_TIME.start) {
            showDataList = showDataList.filter((item) => item.system.CREATE_TIME >= body.ACTION_TIME.start);
        }
        if (body.ACTION_TIME.end) {
            showDataList = showDataList.filter((item) => item.system.CREATE_TIME <= body.ACTION_TIME.end);
        }
        this.setState({ searchForm: { ...body }, showDataList });
    };

    // 重置
    handleReset = () => {
        const { dataList } = this.state;

        this.setState({ searchForm: { ...defaultForm }, showDataList: dataList });
    };

    // 取消
    cancel = () => {
        const { history } = this.props;

        history.go(-1);
    };

    render() {
        const { searchForm, actionUserNameOptions, showDataList } = this.state;

        return (
            <div>
                <Panel>
                    <Panel.Head title="数据修正审计" />
                    <Panel.Body full>
                        <ShareForm formData={searchForm} onChange={(data, callback) => this.setState({ searchForm: data }, callback)}>
                            <Select
                                label="操作用户"
                                field="USER_NAME"
                                placeholder="请选择操作用户"
                                options={actionUserNameOptions}
                                col={8}
                                labelCol={3}
                            />
                            <RangeTime label="操作时间" field="ACTION_TIME" col={13} labelCol={3} />
                            <FormItem className="btn-item clearfix pull-right">
                                <Button type="submit" bsStyle="primary" onClick={this.handleSearch}>
                                    查询
                                </Button>
                                <Button type="reset" onClick={this.handleReset}>
                                    重置
                                </Button>
                            </FormItem>
                        </ShareForm>
                        {showDataList.length === 0 ? (
                            <div style={{ textAlign: 'center', padding: '30px' }}>无审计记录</div>
                        ) : (
                            <TimeLine data={showDataList} component={DataRepairTimeLine} />
                        )}
                    </Panel.Body>
                </Panel>
                <ButtonToolBar>
                    <Button type="button" bsSize="large" onClick={this.cancel}>
                        返回
                    </Button>
                </ButtonToolBar>
            </div>
        );
    }
}

export default DataRepairAudit;
