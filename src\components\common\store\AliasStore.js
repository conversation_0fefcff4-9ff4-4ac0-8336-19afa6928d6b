const Saver = require('./Saver');
const Store = require('./Store');

class AliasStore extends Saver {
    constructor(alias) {
        super();
        this.alias = alias;
    }

    set(key, obj) {
        Store.set(`${this.alias}:${key}`, obj);
    }

    get(key) {
        return Store.get(`${this.alias}:${key}`);
    }

    del(key) {
        return Store.del(`${this.alias}:${key}`);
    }
}

module.exports = AliasStore;
