import React from 'react';
import { Input, Select, Timing } from '@share/shareui-form';
import { ArrayCom } from '@/components/form';
import { addErrorTip, validFunction } from '@/utils/shareFormUtil';
import style from './QueryNodes.scss';

const InputCus = addErrorTip(Input);
const SelectCus = addErrorTip(Select);
const valueAggTypeOptions = [
    { value: 'MAX', label: '最大' },
    { value: 'MAX', label: '最大' },
    { value: 'MIN', label: '最小' },
    { value: 'SUM', label: '求和' },
    { value: 'AVG', label: '平均' },
    { value: 'JOIN', label: '连接' },
    { value: 'DISTINCT_JOIN', label: '去重连接' },
    { value: 'COUNT', label: '计数' },
];

const QueryNodes = (props) => {
    const { field, label, rule, options, ...restProps } = props;

    return (
        <ArrayCom field={field} label={label} rule={rule} {...restProps} defaultChildrenData={{}}>
            {(pro) => (
                <div className={style.node}>
                    <SelectCus
                        field={`${field}.${pro.index}.data.id`}
                        label="结果项"
                        rule="required"
                        options={options}
                        noView
                        placeholder="结果项"
                        onChange={({ target: { value } }) =>
                            pro.onChange({
                                target: {
                                    value: {
                                        data: { id: value },
                                        alias: value,
                                    },
                                },
                            })
                        }
                    />
                    <SelectCus
                        field={`${field}.${pro.index}.data.agg`}
                        label="聚合方式"
                        options={valueAggTypeOptions}
                        noView
                        placeholder="聚合方式"
                        onChange={({ target: { value } }) =>
                            pro.onChange({
                                target: {
                                    value: {
                                        data: {
                                            id: pro.value.data.id,
                                            agg: value || null,
                                        },
                                        alias:
                                            pro.value.data.id && value
                                                ? `${pro.value.data.id}_${value}`
                                                : pro.value.data.id && !value
                                                ? pro.value.data.id
                                                : '',
                                    },
                                },
                            })
                        }
                    />
                    <InputCus
                        field={`${field}.${pro.index}.alias`}
                        label="查询别名"
                        noView
                        rule={[
                            'required',
                            validFunction({
                                fn: (v) => !v || pro.list.map((item) => item.alias).filter((item) => item === v).length === 1,
                                errMsg: '存在重复',
                                timing: Timing.blur,
                            }),
                            validFunction({
                                fn: (v) => !v || !/^.*\."+.*$/.test(v),
                                errMsg: '不允许存在点和双引号',
                                timing: Timing.blur,
                            }),
                        ]}
                        placeholder="查询别名"
                        inline
                    />
                </div>
            )}
        </ArrayCom>
    );
};

export default QueryNodes;
