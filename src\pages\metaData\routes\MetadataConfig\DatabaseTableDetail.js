import React, { Component, Fragment } from 'react';
// 接口
import * as Meta<PERSON><PERSON><PERSON><PERSON> from '@/services/data/meta/MetaTableApi';
import * as Meta<PERSON>ield<PERSON><PERSON> from '@/services/data/meta/MetaFieldApi';
// 组件
import DetailHeader from '@/components/metadataManage/databaseTabledDetail/DetailHeader/DetailHeader';
import ConfigCopyModal from '@/components/metadataManage/databaseTabledDetail/ConfigCopyModal';
import TableFieldList from '@/components/metadataManage/databaseTabledDetail/TableField/TableFieldList';
import FieldComponentList from '@/components/metadataManage/databaseTabledDetail/FieldComponent/FieldComponentList';
import FieldRuleList from '@/components/metadataManage/databaseTabledDetail/FieldRule/FieldRuleList';
import FieldApplication from '@/components/metadataManage/databaseTabledDetail/FieldApplication/FieldApplication';
import SimulationMetaResult from '@/components/metadataManage/databaseTabledDetail/SimulationResult/SimulationResult';
import TableDataList from '@/components/metadataManage/databaseTabledDetail/TableData/TableDataList';
import UpdateProtocol from '@/components/metadataManage/databaseTabledDetail/UpdateProtocol/UpdateProtocol';
import { Button, ButtonToolBar, Icon, Panel, Tab, Tabs } from '@share/shareui';
import { FileUploadButton } from '@/components/business/Other';
import MyAlert from '@/components/ui/MyAlert';
import ConfigExportModal from '@/components/metadataManage/databaseTabledDetail/ConfigExportModal';

class DatabaseTableDetail extends Component {
    state = {
        tabActiveKey: '1',
        renderTabMap: {},
        tableInfo: {},
        metaFieldList: [],
        showExportModal: false,
        showCopyModal: false,
    };

    componentDidMount() {
        const { tabActiveKey = '1' } = this.props.match.params;

        this.switchTab(tabActiveKey);
        this.requestDatabaseTableInfo();
        this.refreshTableFieldList();
    }

    requestDatabaseTableInfo = async () => {
        const { tableId } = this.props.match.params;
        const tableInfo = await MetaTableApi.getDataTableDetail(tableId);

        this.setState({ tableInfo: { ...tableInfo, recordCount: '--' } });
        // 异步计算表数据量
        setTimeout(async () => {
            const recordCount = await MetaTableApi.getDataTableRecordCount(tableId, { noMask: false });

            this.setState({ tableInfo: { ...tableInfo, recordCount } });
        }, 1);
    };

    refreshTabledMetaConfig = () => {
        const { tableId } = this.props.match.params;

        MyAlert.confirm('该操作会根据数据库表信息自动初始化或更正配置，请确认是否操作？', async () => {
            const success = await MetaFieldApi.refreshTableMeta(tableId);

            if (success) {
                MyAlert.ok('刷新成功');
                this.refreshTableFieldList();
            }
        });
    };

    importConfigSuccess = () => {
        MyAlert.ok('导入成功');
        this.refreshTableFieldList();
    };

    refreshTableFieldList = async () => {
        const { tableId } = this.props.match.params;
        const metaFieldList = await MetaFieldApi.tableMetaByTableName(tableId);

        if (metaFieldList.length === 0) {
            this.switchTab('1');
        }
        this.setState({ metaFieldList });
    };

    switchTab = (eventKey) => {
        const { renderTabMap } = this.state;

        this.setState({
            tabActiveKey: eventKey,
            renderTabMap: { ...renderTabMap, [eventKey]: true },
        });
    };

    render() {
        const {
            history,
            match: {
                params: { tableId },
            },
        } = this.props;
        const { tabActiveKey, renderTabMap, tableInfo, metaFieldList, showExportModal, showCopyModal } = this.state;

        return (
            <Fragment>
                <DetailHeader tableInfo={tableInfo} metaFieldList={metaFieldList} />
                <Panel>
                    <Tabs bsStyle="tabs" id="dataSearch" activeKey={tabActiveKey} onSelect={this.switchTab} className="tabs_full">
                        <ul className="ui-list-horizontal" style={{ position: 'absolute', zIndex: 2, top: '-72px', right: 0 }}>
                            <li>
                                <Button type="button" className="btn-xs" border={false} onClick={this.refreshTabledMetaConfig}>
                                    <Icon className="si si-com_rotate" />
                                    {metaFieldList.length === 0 ? '初始化字段' : '刷新字段'}
                                </Button>
                            </li>
                            {metaFieldList.length > 0 && (
                                <li>
                                    <Button
                                        type="button"
                                        className="btn-xs"
                                        border={false}
                                        onClick={() => this.setState({ showExportModal: true })}
                                    >
                                        <Icon className="si si-com_dc" />
                                        导出配置
                                    </Button>
                                </li>
                            )}
                            {metaFieldList.length > 0 && (
                                <li>
                                    <FileUploadButton url={MetaFieldApi.importTableMetaJson(tableId)} successFn={this.importConfigSuccess}>
                                        <Button type="button" className="btn-xs" border={false}>
                                            <Icon className="si si-com_dr" />
                                            导入配置
                                        </Button>
                                    </FileUploadButton>
                                </li>
                            )}
                            {metaFieldList.length > 0 && (
                                <li>
                                    <Button
                                        type="button"
                                        className="btn-xs"
                                        border={false}
                                        onClick={() => this.setState({ showCopyModal: true })}
                                    >
                                        <Icon className="si si-app_hdgz" />
                                        拷贝配置
                                    </Button>
                                </li>
                            )}
                        </ul>
                        <Tab eventKey="1" title="字段信息">
                            {renderTabMap['1'] && (
                                <TableFieldList
                                    tableId={tableId}
                                    metaFieldList={metaFieldList}
                                    refreshDataFn={this.refreshTableFieldList}
                                />
                            )}
                        </Tab>
                        {metaFieldList.length !== 0 && (
                            <Tab eventKey="2" title="数据规则">
                                {renderTabMap['2'] && (
                                    <FieldRuleList
                                        tableId={tableId}
                                        metaFieldList={metaFieldList}
                                        refreshDataFn={this.refreshTableFieldList}
                                    />
                                )}
                            </Tab>
                        )}
                        {metaFieldList.length !== 0 && (
                            <Tab eventKey="3" title="采集组件">
                                {renderTabMap['3'] && (
                                    <FieldComponentList
                                        tableId={tableId}
                                        metaFieldList={metaFieldList}
                                        refreshDataFn={this.refreshTableFieldList}
                                    />
                                )}
                            </Tab>
                        )}
                        {metaFieldList.length !== 0 && (
                            <Tab eventKey="4" title="采集页面">
                                {renderTabMap['4'] && (
                                    <FieldApplication
                                        tableId={tableId}
                                        metaFieldList={metaFieldList}
                                        refreshDataFn={this.refreshTableFieldList}
                                    />
                                )}
                            </Tab>
                        )}
                        {metaFieldList.length !== 0 && (
                            <Tab eventKey="5" title="页面试用">
                                {renderTabMap['5'] && (
                                    <SimulationMetaResult
                                        tableId={tableId}
                                        metaFieldList={metaFieldList}
                                        refreshDataFn={this.refreshTableFieldList}
                                    />
                                )}
                            </Tab>
                        )}
                        {metaFieldList.length !== 0 && (
                            <Tab eventKey="6" title="数据查询">
                                {renderTabMap['6'] && metaFieldList.length !== 0 && (
                                    <TableDataList
                                        tableId={tableId}
                                        metaFieldList={metaFieldList}
                                        refreshDataFn={this.refreshTableFieldList}
                                    />
                                )}
                            </Tab>
                        )}
                        {metaFieldList.length !== 0 && (
                            <Tab eventKey="7" title="更新协议">
                                {tabActiveKey === '7' && metaFieldList.length !== 0 && (
                                    <UpdateProtocol
                                        tableId={tableId}
                                        metaFieldList={metaFieldList}
                                        refreshDataFn={this.refreshTableFieldList}
                                    />
                                )}
                            </Tab>
                        )}
                    </Tabs>
                </Panel>
                <ButtonToolBar>
                    <Button type="button" onClick={() => history.go(-1)}>
                        返回
                    </Button>
                </ButtonToolBar>
                <ConfigExportModal
                    show={showExportModal}
                    cancelFn={() => this.setState({ showExportModal: false })}
                    data={{
                        tableId,
                        metaFieldList,
                    }}
                />
                <ConfigCopyModal
                    show={showCopyModal}
                    successFn={this.refreshTableFieldList}
                    cancelFn={() => this.setState({ showCopyModal: false })}
                    data={{
                        tableId,
                        metaFieldList,
                    }}
                />
            </Fragment>
        );
    }
}
export default DatabaseTableDetail;
