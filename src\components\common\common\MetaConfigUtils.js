/**
 * 配置枚举
 */
const CONFIG_PARAM_KEY = {
    EDIT: 'editParam',
    EXCEL: 'editParam',
    QUERY: 'queryParam',
    LIST: 'listParam',
    DETAIL: 'detailParam',
};

const RULE_CHECK_LEVEL = {
    /** 严重级别 */
    serious: 'S',

    /** 原则级别 */
    principle: '0',

    /** 疑似级别 */
    suspected: '1',

    /** 暂缓级别 */
    suspend: '2',
};

/**
 * 数据配置过滤
 */
function filterPrimaryKeyConfig(metadataConfigList) {
    return metadataConfigList.find((item) => item.primaryKey);
}

function getPrimaryKeyFieldCode(metadataConfigList) {
    const targetConfig = filterPrimaryKeyConfig(metadataConfigList);

    return targetConfig ? targetConfig.fieldCode : '';
}

function filterObjectTypeConfig(metadataConfigList, objectType = '2') {
    const filterList = metadataConfigList
        .filter((item) => item.enabled)
        .filter(
            (item) =>
                item.primaryKey || objectType === '2' || item.fieldOrientedObjectType === '2' || objectType === item.fieldOrientedObjectType
        );

    filterList.forEach((config) => {
        if (Array.isArray(config.bmRule) && config.bmRule.length > 0) {
            config.bmRule = config.bmRule.filter(
                (rule) => rule.ruleParam && rule.ruleParam.useScenes && rule.ruleParam.useScenes.includes(objectType)
            );
        }
        if (Array.isArray(config.convertRule) && config.convertRule.length > 0) {
            config.convertRule = config.convertRule.filter(
                (rule) => rule.ruleParam && rule.ruleParam.useScenes && rule.ruleParam.useScenes.includes(objectType)
            );
        }
        if (Array.isArray(config.checkRule) && config.checkRule.length > 0) {
            config.checkRule = config.checkRule.filter(
                (rule) => rule.ruleParam && rule.ruleParam.useScenes && rule.ruleParam.useScenes.includes(objectType)
            );
        }
        if (Array.isArray(config.showRule) && config.showRule.length > 0) {
            config.showRule = config.showRule.filter(
                (rule) => rule.ruleParam && rule.ruleParam.useScenes && rule.ruleParam.useScenes.includes(objectType)
            );
        }
    });

    return filterList;
}

function filterConfigList(metadataConfigList, configParamKey) {
    // 过滤可用和已配置组件
    const filterList = metadataConfigList.filter(
        (item) =>
            item.enabled &&
            item[configParamKey] &&
            Number.isInteger(item[configParamKey].order) &&
            item[configParamKey].order >= 0 &&
            item[configParamKey].order <= 999
    );

    // 根据配置参数排序
    filterList.sort((a, b) => a[configParamKey].order - b[configParamKey].order);

    return filterList;
}

function filterQueryConfig(metadataConfigList) {
    return filterConfigList(metadataConfigList, CONFIG_PARAM_KEY.QUERY);
}

function filterListConfig(metadataConfigList) {
    return filterConfigList(metadataConfigList, CONFIG_PARAM_KEY.LIST);
}

function filterDetailConfig(metadataConfigList) {
    return filterConfigList(metadataConfigList, CONFIG_PARAM_KEY.DETAIL);
}

function filterEditConfig(metadataConfigList) {
    return filterConfigList(metadataConfigList, CONFIG_PARAM_KEY.EDIT);
}

function filterExcelConfig(metadataConfigList) {
    return filterConfigList(metadataConfigList, CONFIG_PARAM_KEY.EXCEL).filter(
        (item) => item[CONFIG_PARAM_KEY.EXCEL].componentId !== 'ImageUpload' && item[CONFIG_PARAM_KEY.EXCEL].componentId !== 'FileUpload'
    );
}

/**
 * 数据默认值获取
 */
function getConfigDefaultData(metadataConfigList, configParamKey) {
    const formationBmValue = (configItem, target) => {
        const targetBmRule = configItem.bmRule[0] || { ruleParam: {} };
        const options = targetBmRule.ruleParam.bmOptions || [];
        const singleOpt = options.find((one) => one.value === target) || options.find((one) => one.label === target) || {};

        return singleOpt.value || '';
    };

    return metadataConfigList
        .filter((item) => item.enabled)
        .reduce((obj, item) => {
            let value = item[configParamKey].defaultValue || '';

            if (value !== '' && Array.isArray(item.bmRule) && item.bmRule.length > 0) {
                // 多值校验
                if (
                    item[configParamKey].componentId.includes('MultiSelect') ||
                    item[configParamKey].componentId.includes('CheckboxGroup')
                ) {
                    value = value
                        .split(',')
                        .map((one) => formationBmValue(item, one))
                        .filter((one) => one !== '')
                        .join(',');
                } else {
                    value = formationBmValue(item, value);
                }
            }
            obj[item.fieldCode] = value;

            return obj;
        }, {});
}

function getQueryDefaultData(metadataConfigList) {
    return getConfigDefaultData(metadataConfigList, CONFIG_PARAM_KEY.QUERY);
}

function getListDefaultData(metadataConfigList) {
    return getConfigDefaultData(metadataConfigList, CONFIG_PARAM_KEY.LIST);
}

function getDetailDefaultData(metadataConfigList) {
    return getConfigDefaultData(metadataConfigList, CONFIG_PARAM_KEY.DETAIL);
}

function getEditDefaultData(metadataConfigList) {
    return getConfigDefaultData(metadataConfigList, CONFIG_PARAM_KEY.EDIT);
}

module.exports = {
    // 配置枚举
    CONFIG_PARAM_KEY,
    RULE_CHECK_LEVEL,
    // 数据配置过滤
    getPrimaryKeyFieldCode,
    filterPrimaryKeyConfig,
    filterObjectTypeConfig,
    filterQueryConfig,
    filterListConfig,
    filterDetailConfig,
    filterEditConfig,
    filterExcelConfig,
    // 数据默认值获取
    getQueryDefaultData,
    getListDefaultData,
    getDetailDefaultData,
    getEditDefaultData,
};
