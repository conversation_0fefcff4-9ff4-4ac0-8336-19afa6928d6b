/*
 * @(#) TimeLine
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * Copyright:  Copyright (c) 2019
 * Company:厦门畅享信息技术有限公司
 * @author: huangqz
 * 2019/6/28 9:36
 */
import React, { Component, Fragment } from 'react';
import MultiClamp from '@/components/ui/MultiClamp';
import PropTypes from 'prop-types';
// styles
import styles from './TimeLine.scss';

// 获取文本节点
const getReactDOMText = (dom, value) => {
    const {
        props: { children },
    } = dom;
    const result = value || [];

    if (typeof children === 'string') {
        result.push(children);
    } else if (Array.isArray(children)) {
        children.forEach((item) => {
            if (Object.prototype.toString.call(item) === '[object Object]') {
                getReactDOMText(item, result);
            } else {
                result.push(item);
            }
        });
    }

    return result;
};

class TimeLine extends Component {
    constructor() {
        super();
        this.state = {
            collapseControl: {}, // 控制收起展开按钮
        };
    }

    handleClick = (id) => {
        if (!id) {
            return;
        }
        const { collapseControl } = this.state;

        this.setState({
            collapseControl: { ...collapseControl, [id]: !collapseControl[id] },
        });
    };

    render() {
        const { data, bsType, className, component } = this.props;
        const { collapseControl } = this.state;
        const Comp = component;

        return (
            <div className={`${styles.timeLineContainer} ${styles[bsType]} ${className}`}>
                {data &&
                    data.map((item, index) => {
                        return (
                            <div className={`${styles.timeLine} clearfix ${styles[item.bsType]}`} key={item.key || index}>
                                <div className={`${styles.left} pull-left`}>
                                    <MultiClamp title={item.date}>{item.date}</MultiClamp>
                                    <MultiClamp title={item.time}>{item.time}</MultiClamp>
                                    <span className={styles.dot} />
                                </div>
                                <div className={`${styles.right} pull-left`}>
                                    <div className={styles.contentBox}>
                                        {component && <Comp {...item} key={index} />}
                                        {!component && (
                                            <Fragment>
                                                <h3 className={`${styles.title} clearfix`}>
                                                    <MultiClamp
                                                        className={`${styles.titleText} pull-left`}
                                                        title={typeof item.title === 'string' ? item.title : undefined}
                                                    >
                                                        {item.title}
                                                    </MultiClamp>
                                                    {item.collapseContent && (
                                                        <span
                                                            className={`${styles.collapseBtn} pull-right`}
                                                            onClick={() => this.handleClick(item.id)}
                                                        >
                                                            {collapseControl[item.id] ? '收起' : '展开'}
                                                        </span>
                                                    )}
                                                </h3>
                                                {item.alwaysDisplay && <div className={styles.alwaysDisplay}>{item.alwaysDisplay()}</div>}
                                                {collapseControl[item.id] && (
                                                    <div className={styles.collapseContent}>{item.collapseContent()}</div>
                                                )}
                                            </Fragment>
                                        )}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
            </div>
        );
    }
}

TimeLine.defaultProps = {
    bsType: 'primary',
};

TimeLine.propTypes = {
    data: PropTypes.array.isRequired,
    bsType: PropTypes.string,
    className: PropTypes.string,
};

export default TimeLine;
