/*
 *@(#) DataRepairDetail.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-26
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import React, { Component } from 'react';
// 样式
import styles from '@/pages/metaData/styles/creditData.scss';
// 服务接口
import * as DataQueryApi from '@/services/data/data/DataQueryApi';
// 组件
import Detail from '@/components/business/metadata/Detail';
import { Panel, ButtonToolBar, Button } from '@share/shareui';
import { getQueryString } from '@share/utils';

class DataManageDetail extends Component {
    state = {
        detailBody: {
            data: [],
            system: {},
        },
    };

    // 请求配置信息
    componentDidMount = async () => {
        const {
            match: {
                params: { categoryId, recordId },
            },
        } = this.props;
        const detailBody = await DataQueryApi.detailByCategory(categoryId, '2', recordId);

        this.setState({ detailBody });
    };

    // 取消
    cancel = () => {
        const { history } = this.props;

        history.go(-1);
    };

    render() {
        const { detailBody } = this.state;
        const from = getQueryString(this.props.location, 'from');

        return (
            <div className={styles.creditDataDetail}>
                <div className={styles['table-list']}>
                    <Panel className={styles['table-list_item']}>
                        <Panel.Head title="数据详情" />
                        <Panel.Body full>
                            <h3 className={styles.title}>{detailBody.system.CATEGORY_NAME}</h3>
                            <Detail detail={detailBody} />
                        </Panel.Body>
                    </Panel>
                </div>
                {from !== 'model' && (
                    <ButtonToolBar>
                        <Button type="button" onClick={this.cancel}>
                            返回
                        </Button>
                    </ButtonToolBar>
                )}
            </div>
        );
    }
}

export default DataManageDetail;
