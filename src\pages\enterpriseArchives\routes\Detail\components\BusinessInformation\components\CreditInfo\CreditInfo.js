/*
 *@(#) CreditInfo.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-10-12
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React, { useState } from 'react';
import { Tabs } from 'antd';
import { emptyDefault } from '@/utils/format';
import { Empty } from '@share/shareui';
import TabContent from './TabContent';
import styles from './CreditInfo.scss';

const CreditInfo = ({ data: { blackListList, allowPunishList, creditList } }) => {
    const [tabKey, setTabKey] = useState('1');
    const onChange = (key) => {
        // console.log(key);
        console.log(key);
        setTabKey(key);
    };

    if (!(creditList !== null && typeof creditList === 'object' && Object.keys(creditList).length > 0)) {
        return (
            <div className={styles.creditInfo} style={{ padding: '100px 0' }}>
                <Empty />
            </div>
        );
    }

    const items = Object.keys(creditList).map((v, i) => {
        const subKeys = Object.keys(creditList[v] || {});

        return {
            label: v,
            key: String(i + 1),
            children: (
                <TabContent
                    data={subKeys.map((a) => {
                        return creditList[v][a].map((w) => ({
                            label: w.filedlabel,
                            context: emptyDefault(w.fieldvalue),
                        }));
                    })}
                    type={v}
                    // collapse={false}
                />
            ),
            // forceRender: true,
        };
    });

    return (
        <div className={styles.creditInfo}>
            <Tabs type="card" activeKey={tabKey} onChange={onChange} items={items} />
        </div>
    );
};

export default CreditInfo;
