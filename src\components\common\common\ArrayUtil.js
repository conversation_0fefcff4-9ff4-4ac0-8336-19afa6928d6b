/**
 * 判断是否为非空数组
 * @param arr 待判断数组
 * @returns boolean
 */
function notEmptyArr(arr) {
    return Array.isArray(arr) && arr.length > 0;
}

/** *
 * 将数组转化成逗号分隔的string
 * @param array
 */
function arrayToStr(array) {
    if (!Array.isArray(array)) {
        return '';
    }

    return array.join(',');
}

/**
 * 将逗号分隔的字符串转化成数组
 * @param str
 * @returns {Array}
 * @constructor
 */
function strToArray(str) {
    if (!str) {
        return [];
    }

    return str.split(',');
}

/**
 * 对象数组去重
 * @param array 待去重数组
 * @param key 根据的Key
 * @returns {*}
 */
function distinct(array, key) {
    if (!Array.isArray(array)) {
        return [];
    }
    if (key === null || typeof key === 'undefined') {
        return [...new Set(array)];
    }
    const newArray = [];

    array.forEach((item) => {
        if (!newArray.find((one) => one[key] === item[key])) {
            newArray.push(item);
        }
    });

    return newArray;
}

/**
 * 对数组的某个字段计算和
 * @param array
 * @param func
 */
function sum(array, func) {
    return array.map(func).reduce((a, b) => a + b, 0);
}

module.exports = {
    notEmptyArr,
    arrayToStr,
    strToArray,
    distinct,
    sum,
};
