import React, { useState } from 'react';

import { FormItem } from '@share/shareui-form';

import { Tree } from 'antd';
import { cloneDeep } from 'lodash';

const findParents = (tree, id) => {
    let parents = [];
    const stack = cloneDeep(tree);
    while (stack.length > 0) {
        const item = stack.shift();
        const children = item.children || [];
        for (let i = 0; i < children.length; i++) {
            if (children[i].id === id) {
                parents.push(item);
                parents = parents.concat(findParents(item, id));
            } else {
                stack.push(children[i]);
            }
        }
    }

    return parents;
};

const FormAntdTree = ({ options, field, label, noView = false, ...otherProps }) => {
    const [parentsId, setParentsId] = useState([]);

    return (
        <FormItem field={field} label={label} noView={noView} {...otherProps}>
            {(fieldProps) => {
                const { value, onChange } = fieldProps;
                const onCheck = (checkedKeys, { checked, checkedNodes, node, event, halfCheckedKeys }) => {
                    const parents = findParents(options, node.id);
                    const checkedKeysValue = [...parents.map((item) => item.id)];

                    console.log('onCheck', checkedKeys, checked, checkedNodes, node, event, halfCheckedKeys, parents);
                    console.log('p', parentsId);
                    if (checkedKeys.length > 0) {
                        const p = [...new Set([...parentsId, ...checkedKeysValue])];
                        setParentsId(p);

                        onChange({
                            target: {
                                value: checkedKeys,
                                parentsId: p,
                            },
                        });
                    } else {
                        setParentsId([]);
                        onChange({
                            target: {
                                value: checkedKeys,
                                parentsId: [],
                            },
                        });
                    }
                    // setCheckedKeys(checkedKeysValue);
                    // const checkedKeysValue = [...parents.map((item) => item.id)];
                    // onChange({
                    //     target: {
                    //         value: checkedKeys,
                    //         parentsId: checkedKeysValue,
                    //     },
                    // });
                };

                return (
                    <Tree
                        {...otherProps}
                        // {...fieldProps}
                        fieldNames={{ title: 'name', key: 'id', children: 'children' }}
                        checkable
                        // expandedKeys={expandedKeys}
                        // autoExpandParent={autoExpandParent}
                        onCheck={onCheck}
                        checkedKeys={value}
                        // selectedKeys={selectedKeys}
                        treeData={options}
                    />
                );
            }}
        </FormItem>
    );
};

export default FormAntdTree;
