import React, { useState, useCallback, useImperativeHandle } from 'react';
import { Button } from '@share/shareui';
import classNames from 'classnames';
import { SearchForm, Input, Select, CalendarRange, Row, useForm } from '@share/shareui-form';
import { useService } from '@share/framework';
import Service from '@/services/Service';
import { useMount } from 'ahooks';
import FormAntdSelect from '../FormAntdSelect';
import FormAntdCascader from '../FormAntdCascader';
import FormSelectList from '../FormSelectList';
import FormAntdInputRange from '../FormAntdInputRange';

import styles from './AdvancedQuery.scss';
import FormAntdTree from '../FormAntdTree';

const AdvancedQuery = ({ codeMap, showAdvancedQuery, handleAdvancedQuery, handleShowAdvancedQuery }) => {
    const { parkList, BUSINESS_STATUS, ECONOMY_TYPE, INDUSTRY_CATEGORIES, GJDM, DM_QY_DJJG } = codeMap;
    const [formData, form] = useForm(
        { zcdzgsxzqhdm: [], hylxdm: [] },
        {
            willClearEmptyField: true,
        }
    );
    const basicServices = useService(Service);
    const [tagInfoTree, setTagInfoTree] = useState([]);
    const getTagInfoTree = async () => {
        const res = await basicServices.getTagInfoTree();
        setTagInfoTree(res);
    };
    // 模拟请求，来适配市场监管归属地
    const getQyDjJgOptions = async (id) => {
        if (!id) {
            return DM_QY_DJJG.filter((item) => {
                return item.code.length === 6;
            });
        }
        if (id?.length === 8) {
            return [];
        }
        const children = await DM_QY_DJJG.filter((item) => {
            return item.code.startsWith(id) && item.code.length === 8;
        });
        console.log('🚀 ~ getQyDjJgOptions ~ children:', children);

        return children;
    };
    useMount(() => {
        getTagInfoTree();
    });

    return (
        <div
            className={classNames(styles.advancedQuery, {
                [styles.advancedQuery_show]: showAdvancedQuery,
            })}
        >
            <div className={styles.trigger} onClick={handleShowAdvancedQuery}>
                {!showAdvancedQuery && <i className="si si-com_sortright" />}
                {showAdvancedQuery && <i className="si si-com_sortleft" />}
            </div>
            <div className={styles.advancedQuery_head}>高级查询</div>
            <SearchForm grid={2} autoLayout={false} formState={form} className={styles.advancedQuery_form}>
                <Row>
                    <Input field="qymcLike" label="企业名称" placeholder="请输入企业名称" />
                    <Input field="frdbmc" label="法定代表人" placeholder="请输入法定代表人" />
                    {/* </Row>
                <Row> */}
                    {/* <FormAntdCascader field="zcdzgsxzqhdm" label="所属辖区" servicesName={basicServices.getArea} /> */}
                    {/* <FormAntdSelect */}
                    {/*    multiple */}
                    {/*    showSearch */}
                    {/*    filterOption={(input, option) => { */}
                    {/*        return (option?.name ?? '').toLowerCase().includes(input?.toLowerCase()); */}
                    {/*    }} */}
                    {/*    field="zcdzyqbhs" */}
                    {/*    label="所属园区" */}
                    {/*    options={parkList} */}
                    {/*    fieldNames={{ label: 'name', value: 'parkId' }} */}
                    {/* /> */}
                    {/* </Row>
                <Row> */}
                    <Select
                        field="hymldm"
                        label="所属门类"
                        options={INDUSTRY_CATEGORIES}
                        onChange={() => {
                            form.setFieldValue('hylxdm', '');
                        }}
                    />
                    {formData?.hymldm && (
                        <FormAntdCascader
                            field="hylxdm"
                            label="所属行业"
                            key={formData?.hymldm}
                            parentId={formData?.hymldm}
                            servicesName={basicServices.getHyCode}
                        />
                    )}
                    <FormAntdInputRange label="注册资本(万元）" field={['zczbwyRange.start', 'zczbwyRange.end']} />

                    {/* </Row>
                <Row> */}
                    <CalendarRange
                        field={['qyclrqRange.start', 'qyclrqRange.end']}
                        search-col={12}
                        label="成立日期"
                        format={{
                            data: 'YYYYMMDD',
                            display: 'YYYY-MM-DD',
                        }}
                        bound="between-in"
                        inputProps={{ placeholder: '请选择' }}
                    />
                    {/* </Row>
                <Row> */}
                    <Select field="djztdm" label="企业状态" options={BUSINESS_STATUS} />
                    <Select field="qylxdm" label="企业类型" options={ECONOMY_TYPE} />
                    {DM_QY_DJJG?.length > 0 ? (
                        <FormAntdCascader field="jgdwdmRightLike" label="市场监管归属地" servicesName={getQyDjJgOptions} />
                    ) : (
                        <FormAntdSelect
                            showSearch
                            filterOption={(input, option) => {
                                return (option?.name ?? '').toLowerCase().includes(input?.toLowerCase());
                            }}
                            field="jgdwdmRightLike"
                            label="市场监管归属地"
                            options={[]}
                        />
                    )}

                    <FormAntdSelect
                        showSearch
                        filterOption={(input, option) => {
                            return (option?.name ?? '').toLowerCase().includes(input?.toLowerCase());
                        }}
                        field="gjdm"
                        label="股东注册国家或地区"
                        options={GJDM}
                    />
                    <Input field="dzLike" label="住所/经营场所" placeholder="请输入住所/经营场所" />
                    <Input field="creditCode" label="统一社会信用代码" placeholder="请输入统一社会信用代码" />

                    <Input search-col={12} field="jyfwms" label="经营范围" placeholder="请输入经营范围" />

                    {/* <FormAntdInputRange label="就业人数" field={['jyrsRange.start', 'jyrsRange.end']} /> */}
                    {/* <FormAntdInputRange label="参保人数" field={['cbrsRange.start', 'cbrsRange.end']} /> */}
                    {/* <CalendarRange */}
                    {/*    field={['interviewDateRange.start', 'interviewDateRange.end']} */}
                    {/*    search-col={12} */}
                    {/*    label="走访日期" */}
                    {/*    format={{ */}
                    {/*        data: 'YYYY-MM-DD', */}
                    {/*        display: 'YYYY-MM-DD', */}
                    {/*    }} */}
                    {/*    bound="between-in" */}
                    {/*    inputProps={{ placeholder: '请选择' }} */}
                    {/* /> */}
                     <FormAntdTree
                        options={tagInfoTree}
                        label="产业标签"
                        field="tagIds"
                        search-col={12}
                        onChange={(event) => {
                            console.log('event', event.target);
                            form.setFieldValue('tagIdsParentsId', event?.target?.parentsId || []);
                        }}
                     />
                </Row>
            </SearchForm>
            <div className={styles.advancedQuery_footer}>
                <Button
                    onClick={() => {
                        form.reset();
                    }}
                >
                    重置
                </Button>
                <Button
                    bsStyle="primary"
                    preventDuplicateClick
                    onClick={() => {
                        handleAdvancedQuery(formData);
                    }}
                >
                    开始检索
                </Button>
            </div>
        </div>
    );
};

export default AdvancedQuery;
