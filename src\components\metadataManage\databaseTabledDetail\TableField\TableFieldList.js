import React, { Component } from 'react';
// 服务接口
import * as Meta<PERSON>ield<PERSON><PERSON> from '@/services/data/meta/MetaFieldApi';
// 工具类
import * as StringUtils from '@/components/common/common/StringUtils';
// 列表组件
import { FrontPageTable as Table } from '@/components/business/Table';
// 表单组件
import MultiClamp from '@/components/ui/MultiClamp';
import MyAlert from '@/components/ui/MyAlert/MyAlert';
import { Panel, Button, Icon } from '@share/shareui';
import { ShareForm, getComponents } from '@/components/business/Form';
import EsExportModal from '../../EsExportModal';

const { Row, Input, Select, Switch } = getComponents('div');

const defaultSearchBody = {
    fieldCode: '',
    fieldDataType: '',
    fieldComment: '',
};

const objectFieldCode = ['XDR_MC', 'ZZMC', 'XM', 'XDR_SHXYM', 'TYSHXYDM', 'XDR_GSZ<PERSON>', 'ZZJG<PERSON>', 'XDR_ZZJG', 'ZCH', 'XDR_ZJHM', 'ZJHM'];

class TableFieldList extends Component {
    state = {
        searchForm: { ...defaultSearchBody },
        searchBody: { ...defaultSearchBody },
        showEsExportModal: false,
    };

    handleSearch = () => {
        const { searchForm } = this.state;
        const body = StringUtils.deleteSpace(searchForm);

        this.setState({ searchBody: { ...body } });
    };

    handleReset = () => {
        this.setState({ searchForm: { ...defaultSearchBody } });
    };

    filterDataList = (dataList) => {
        const { fieldCode, fieldDataType, fieldComment } = this.state.searchBody;
        let filterList = dataList;

        if (fieldCode) {
            filterList = filterList.filter((item) => item.fieldCode && item.fieldCode.toUpperCase().includes(fieldCode.toUpperCase()));
        }
        if (fieldDataType) {
            filterList = filterList.filter((item) => item.fieldDataType === fieldDataType);
        }
        if (fieldComment) {
            filterList = filterList.filter(
                (item) => item.fieldComment && item.fieldComment.toUpperCase().includes(fieldComment.toUpperCase())
            );
        }

        return filterList;
    };

    defaultEsConfig = () => {
        const { metaFieldList } = this.props;

        MyAlert.confirm('该操作会恢复Es默认配置(数据长度不大于500)，请确认是否操作？', () => {
            metaFieldList.forEach((item) => {
                if (item.fieldLength.length !== 1 || item.fieldLength[0] <= 500) {
                    item.esConfig.enabled = true;
                    item.esConfig.objectKeyword = objectFieldCode.includes(item.fieldCode);
                    item.esConfig.businessKeyword = objectFieldCode.includes(item.fieldCode);
                } else {
                    item.esConfig.enabled = false;
                }
            });

            this.updateConfig(metaFieldList);
        });
    };

    updateConfig = async (metaFieldList) => {
        const { refreshDataFn } = this.props;

        await MetaFieldApi.saveTableMeta(metaFieldList);
        refreshDataFn && refreshDataFn();
    };

    render() {
        const { searchForm, showEsExportModal } = this.state;
        const { tableId, metaFieldList } = this.props;
        const fieldDataTypeOptions = Array.from(new Set(metaFieldList.map((item) => item.fieldDataType))).map((item) => ({
            label: item,
            value: item,
        }));
        // 过滤搜索条件
        const filterDataList = this.filterDataList(metaFieldList);
        // 列表展示内容
        const columns = [
            {
                title: '数据库字段名',
                key: 'fieldCode',
                dataIndex: 'fieldCode',
                width: '20%',
                render: (value) => <MultiClamp title={value}>{value}</MultiClamp>,
            },
            {
                title: '是否主键',
                key: 'primaryKey',
                dataIndex: 'primaryKey',
                width: '6%',
                render: (value) => {
                    return <span>{value ? '是' : '否'}</span>;
                },
            },
            {
                title: '数据类型',
                key: 'fieldDataType',
                dataIndex: 'fieldDataType',
                width: '10%',
            },
            {
                title: '数据长度',
                key: 'fieldLength',
                dataIndex: 'fieldLength',
                width: '10%',
                render: (value) => {
                    if (!Array.isArray(value)) {
                        return '--';
                    }
                    switch (value.length) {
                        case 1:
                            return value[0];
                        case 2:
                            return `(${value[0]},${value[1]})`;
                        default:
                            return '--';
                    }
                },
            },
            {
                title: '字段备注',
                key: 'fieldComment',
                dataIndex: 'fieldComment',
                render: (value) => <MultiClamp title={value}>{value}</MultiClamp>,
            },

            /* {
                title: 'ES字段',
                key: 'enabled',
                dataIndex: 'esConfig.enabled',
                width: '8%',
                render: (value, data) => (
                    <Switch.View
                        value={value}
                        onChange={() => {
                            data.esConfig.enabled = !data.esConfig.enabled;

                            this.updateConfig([data]);
                        }}
                    />)
            },
            {
                title: 'ES主体关键字',
                key: 'objectKeyword',
                dataIndex: 'esConfig',
                width: '8%',
                render: (value, data) => (
                    <Switch.View
                        value={value.objectKeyword}
                        disabled={!value.enabled}
                        onChange={() => {
                            data.esConfig.objectKeyword = !data.esConfig.objectKeyword;

                            this.updateConfig([data]);
                        }}
                    />)
            },
            {
                title: 'ES业务关键字',
                key: 'businessKeyword',
                dataIndex: 'esConfig',
                width: '9%',
                render: (value, data) => (
                    <Switch.View
                        value={value.businessKeyword}
                        disabled={!value.enabled}
                        onChange={() => {
                            data.esConfig.businessKeyword = !data.esConfig.businessKeyword;

                            this.updateConfig([data]);
                        }}
                    />)
            }, */
        ];
        // const esColumns = metaFieldList.filter(item => item.esConfig.enabled);

        return (
            <div>
                <Panel>
                    <Panel.Body full>
                        <ShareForm formData={searchForm} onChange={(data, callback) => this.setState({ searchForm: data }, callback)}>
                            <Input label="数据库字段名" field="fieldCode" col={10} labelCol={3} />
                            <Select label="数据类型" field="fieldDataType" options={fieldDataTypeOptions} col={10} labelCol={3} />
                            <Input label="字段备注" field="fieldComment" col={10} labelCol={3} />
                            <Row>
                                <div className="g-30">
                                    <div className="btn-item pull-right">
                                        <Button type="submit" bsStyle="primary" onClick={this.handleSearch}>
                                            查询
                                        </Button>
                                        <Button type="reset" onClick={this.handleReset}>
                                            重置
                                        </Button>
                                    </div>
                                </div>
                            </Row>
                        </ShareForm>
                    </Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Head title="字段信息">
                        {/* <Panel.HeadRight> */}
                        {/*    <ul className="ui-list-horizontal"> */}
                        {/*        { */}
                        {/*            metaFieldList.length > 0 && ( */}
                        {/*                <li> */}
                        {/*                    <Button */}
                        {/*                        type="button" */}
                        {/*                        className="btn-xs" */}
                        {/*                        border={false} */}
                        {/*                        onClick={() => this.defaultEsConfig()} */}
                        {/*                    > */}
                        {/*                        <Icon className="si si-com_backward" /> */}
                        {/*                        使用ES默认配置 */}
                        {/*                    </Button> */}
                        {/*                </li> */}
                        {/*            )} */}
                        {/*        { */}
                        {/*            metaFieldList.length > 0 && */}
                        {/*            ( */}
                        {/*                <li> */}
                        {/*                    <Button */}
                        {/*                        type="button" */}
                        {/*                        className="btn-xs" */}
                        {/*                        border={false} */}
                        {/*                        onClick={() => this.setState({ showEsExportModal: true })} */}
                        {/*                        disabled={esColumns.length === 0} */}
                        {/*                    > */}
                        {/*                        <Icon className="si si-gxxt_zyml" /> */}
                        {/*                        导出Es文件 */}
                        {/*                    </Button> */}
                        {/*                </li> */}
                        {/*            )} */}
                        {/*    </ul> */}
                        {/* </Panel.HeadRight> */}
                    </Panel.Head>
                    <Panel.Body full>
                        <Table rowKey="fieldId" columns={columns} dataSource={filterDataList} cachePage />
                    </Panel.Body>
                </Panel>
                <EsExportModal
                    show={showEsExportModal}
                    cancelFn={() => this.setState({ showEsExportModal: false })}
                    data={{
                        tableIds: [tableId],
                        esAlias: tableId.toLowerCase(),
                    }}
                />
            </div>
        );
    }
}

export default TableFieldList;
