import React, { Component } from 'react';
// 表单组件
import { TextTip } from '@share/shareui';
import { getComponents } from '@share/shareui-form';

const getComposeCustom = (type = 'table') => {
    const { Compose } = getComponents(type);

    return class extends Component {
        render() {
            const { formState, children, errorMsg, childrenFields = [], ...restProps } = this.props;
            // 获取错误信息
            let message = React.Children.map(children, (child) =>
                child && child.props && child.props.field ? formState.getFieldError(child.props.field) : ''
            )
                .filter((msg) => msg)
                .join('，');

            message += childrenFields
                .map((field) => formState.getFieldError(field))
                .filter((msg) => msg)
                .join('，');
            message = errorMsg && message ? errorMsg : message;

            return (
                <Compose {...restProps}>
                    {message ? (
                        <div className="sub-item clearfix otherPart input-hasTip has-error" style={{ padding: '0' }}>
                            {children}
                            <TextTip icon={false}>
                                <i className="fa fa-times-circle" />
                                {message}
                            </TextTip>
                        </div>
                    ) : (
                        children
                    )}
                </Compose>
            );
        }
    };
};

export default getComposeCustom;
