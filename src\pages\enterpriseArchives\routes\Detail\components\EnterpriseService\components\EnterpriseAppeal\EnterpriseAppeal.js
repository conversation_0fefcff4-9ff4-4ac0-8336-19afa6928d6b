/*
 *@(#) EnterpriseAppeal.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-10-12
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React from 'react';

const EnterpriseAppeal = () => {
    return (
        <div>
            <div>123</div>
        </div>
    );
};

export default EnterpriseAppeal;
