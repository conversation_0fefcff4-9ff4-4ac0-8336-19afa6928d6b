import React, { Component, Fragment } from 'react';
import { useSetState, useUpdateEffect } from 'ahooks';
// 工具类
import {
    SearchForm,
    useForm,
    // 表单项组件
    Row,
    Input,
    Select,
    RadioGroup,
    TableForm,
} from '@share/shareui-form';
import { deleteSpace } from '@/utils/index';
import * as formRule from '@/utils/formRule';
import cloneDeep from 'lodash/cloneDeep';
// 组件
import { Modal, Button } from '@share/shareui';
import ArrayFormItem from '../ArrayFormItem';
import styles from './FieldComponent.scss';

const FieldComponentEdit = (props) => {
    const {
        show,
        successFn,
        cancelFn,

        data: { metaFieldList = [], detail, fieldComponentList, componentTypeOptions },
    } = props;

    const [formData, form] = useForm({});
    const [state, setState] = useSetState({
        fieldComponentOptional: [],
        fieldCodeOptions: [],
    });
    const onComponentIdChange = (componentId) => {
        if (!componentId) {
            form.setFieldValue({ ...formData, componentId: '' });

            return;
        }
        const paramRuleKey = detail.componentType.replace(/^(.*)Param$/, '$1ParamRule');
        const component = fieldComponentList.find((item) => item.componentId === componentId) || {};
        const componentParamRule = component[paramRuleKey] || [];
        const componentParam = componentParamRule.reduce((obj, item) => {
            // eslint-disable-next-line no-param-reassign
            obj[item.key] = item.value;

            return obj;
        }, {});

        if (formData.componentParam.placeholder) {
            componentParam.placeholder = formData.componentParam.placeholder;
        }
        form.setFormData({ ...formData, componentId, componentParam });
    };
    const calculationComponent = (param, parentParam) => {
        let Con = Input;
        const propss = {};
        const rule = [];

        if (param.valueType === 'string') {
            const minLength = Number.parseFloat(param.minLength);
            const maxLength = Number.parseFloat(param.maxLength);

            if (minLength === 0 || minLength) {
                rule.push(formRule.checkLength(minLength, null));
            }
            if (maxLength) {
                rule.push(formRule.checkLength(null, maxLength));
            }
        } else if (param.valueType === 'int' || param.valueType === 'number') {
            propss.type = 'number';
            if (param.valueType === 'int') {
                propss.step = 1;
                rule.push(formRule.checkIsInteger());
            } else {
                rule.push(formRule.checkIsNum());
            }
            const minValue = Number.parseFloat(param.minValue);
            const maxValue = Number.parseFloat(param.maxValue);

            if (minValue === 0 || minValue) {
                propss.min = minValue;
                rule.push(formRule.checkNumRange(minValue, null));
            }
            if (maxValue === 0 || maxValue) {
                propss.max = maxValue;
                rule.push(formRule.checkNumRange(null, maxValue));
            }
        } else if (param.valueType === 'boolean') {
            Con = RadioGroup;
            propss.options = [
                { label: '是', value: true },
                { label: '否', value: false },
            ];
        } else if (param.valueType === 'bm') {
            Con = parentParam ? Select : RadioGroup;
            propss.options = param.options || [];
        } else if (param.valueType === 'array') {
            Con = ArrayFormItem;
            propss.defaultChildrenData = param.children.reduce((result, item) => {
                // eslint-disable-next-line no-param-reassign
                result[item.key] = '';

                return result;
            }, {});
            propss.children = (itemProps) => (
                <Fragment>
                    {param.children.map((node) => {
                        const calculationResult = calculationComponent(node, param);
                        const NodeCom = calculationResult.Con;
                        const nodeProps = calculationResult.props;

                        return (
                            <NodeCom.View
                                value={itemProps.value[node.key]}
                                onChange={({ target: { value } }) =>
                                    itemProps.onChange({ target: { value: { ...itemProps.value, [node.key]: value } } })
                                }
                                {...nodeProps}
                            />
                        );
                    })}
                </Fragment>
            );
            rule.push(
                formRule.checkFunction((value) => !value || value.every((item) => Object.values(item).every((one) => one)), '不能存在空项')
            );
        }
        propss.placeholder = param.tip || `请填充${param.name}`;

        return { Con, propss, rule };
    };

    const buildComponentParamFormEdit = (componentId) => {
        const paramRuleKey = detail.componentType.replace(/^(.*)Param$/, '$1ParamRule');
        const component = fieldComponentList.find((item) => item.componentId === componentId) || {};
        const componentParamRule = component[paramRuleKey] || [];

        if (!Array.isArray(componentParamRule)) {
            return '';
        }

        return componentParamRule.map((item) => {
            const { Con, propss, rule } = calculationComponent(item);

            return (
                <Row>
                    <Con {...propss} label={item.name} field={`componentParam.${item.key}`} rule={rule} />
                </Row>
            );
        });
    };
    const submit = async () => {
        if (await form.validHelper()) {
            const editBody = deleteSpace(form.getFormData());

            if (successFn) {
                successFn(editBody);
            }
        }
    };
    useUpdateEffect(() => {
        const fieldCodeOptions = metaFieldList.map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));

        setState({
            fieldCodeOptions,
        });
    }, [show, metaFieldList.length]);
    useUpdateEffect(() => {
        const paramRuleKey = detail.componentType.replace(/^(.*)Param$/, '$1ParamRule');
        const fieldComponentOptional = fieldComponentList
            .filter((item) => item[paramRuleKey])
            .map((item) => ({ label: item.componentName, value: item.componentId }));

        form.setFormData(cloneDeep(detail));
        form.cleanValidError();
        console.log('detail', detail);
        setState({
            fieldComponentOptional,
        });
    }, [show, detail]);

    return (
        <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={cancelFn} bsSize="large" backdrop="static">
            <Modal.Header closeButton>组件编辑</Modal.Header>
            <Modal.Body>
                <TableForm formState={form}>
                    <Row>
                        <Select
                            label="组件字段"
                            field="fieldCode"
                            rule={formRule.checkRequiredNotBlank()}
                            options={state.fieldCodeOptions}
                            disabled
                            required
                            placeholder="请选择组件字段"
                        />
                    </Row>
                    <Row>
                        <RadioGroup
                            label="组件类型"
                            field="componentType"
                            rule={formRule.checkRequiredNotBlank()}
                            options={componentTypeOptions}
                            disabled
                            required
                        />
                    </Row>
                    <Row>
                        <Select
                            label="组件名称"
                            field="componentId"
                            options={state.fieldComponentOptional}
                            onChange={({ target: { value } }) => onComponentIdChange(value)}
                            rule={formRule.checkRequiredNotBlank()}
                            required
                            placeholder="请选择组件名称"
                        />
                    </Row>
                    {formData.componentId && (
                        <Row>
                            <Input label="默认值" field="defaultValue" rule={formRule.checkLength(null, 50)} placeholder="请输入默认值" />
                        </Row>
                    )}
                    {formData.componentId && detail.componentType.includes('edit') && (
                        <Row>
                            <Input
                                label="输入内容示例"
                                field="tip"
                                rule={formRule.checkLength(null, 300)}
                                placeholder="请输入输入内容示例"
                            />
                        </Row>
                    )}
                    {formData.componentId && buildComponentParamFormEdit(formData.componentId)}
                </TableForm>
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={cancelFn}>关闭</Button>

                <Button bsStyle="primary" onClick={submit}>
                    确认
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default FieldComponentEdit;
