/*
 * @(#) MyHint.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2020
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2020-03-08 22:37:23
 */

import React from 'react';

export default class MyHint {
    /**
     * 提示消息框
     * @param msg 提示内容
     * @param time 自动消失时间，默认3000毫秒
     * @param func 提示消失时的回调事件
     * @param width 提示框宽度
     */
    static warning(msg, time, func, width) {
        MyHint.buildHint('warning', time, msg, func, width);
    }

    /**
     * 提示消息框
     * @param msg 提示内容
     * @param time 自动消失时间，默认3000毫秒
     * @param func 提示消失时的回调事件
     * @param width 提示框宽度
     */
    static success(msg, time, func, width) {
        MyHint.buildHint('success', time, msg, func, width);
    }

    /**
     * 提示消息框
     * @param msg 提示内容
     * @param time 自动消失时间，默认3000毫秒
     * @param func 提示消失时的回调事件
     * @param width 提示框宽度
     */
    static info(msg, time, func, width) {
        MyHint.buildHint('info', time, msg, func, width);
    }

    /**
     * 提示消息框
     * @param msg 提示内容
     * @param time 自动消失时间，默认3000毫秒
     * @param func 提示消失时的回调事件
     * @param width 提示框宽度
     */
    static danger(msg, time, func, width) {
        MyHint.buildHint('danger', time, msg, func, width);
    }

    // 构建提示框JQ对象
    static buildHint = (type, time, msg, func, width) => {
        const $alert = $(
            `<div style="position: fixed; width: 100%; top: 20%; opacity: 0.9; z-index: 2000; display: none;">
                <div class="alert alert-${type} center-block" role="alert" style="max-width: ${width || 480}px;"> 
                    <i class="fa fa-info-circle"></i>
                    <span>${msg}</span>
                    <button type="button" class="close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
             </div>`
        );

        const int = self.setInterval(() => {
            $alert.fadeOut(500, () => {
                $alert.remove();
                window.clearInterval(int);
                func && func();
            });
        }, time || 2000);

        $alert.find('.close').click(() => {
            $alert.fadeOut(500, () => {
                $alert.remove();
                window.clearInterval(int);
                func && func();
            });
        });
        $('body').append($alert);
        $alert.fadeIn(500);

        return $alert;
    };
}
