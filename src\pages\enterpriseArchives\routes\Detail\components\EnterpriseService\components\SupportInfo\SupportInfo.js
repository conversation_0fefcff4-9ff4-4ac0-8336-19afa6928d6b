/* eslint-disable react/no-unstable-nested-components */
/*
 *@(#) SupportInfo.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-10-12
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import NoData from '@/components/NoData';
import ShareList, { Column, useList } from '@share/list';
import React from 'react';
import { comas } from '@/utils';
import PaginationComponent from '@/components/PaginationComponent';
import LinkText from '../../../LinkText';
import s from './SupportInfo.scss';

const count = [
    { label: '兑现金额(万元)', value: comas(482.16) },
    { label: '一企一策(万元)', value: comas(482.16) },
    { label: '一事一议(万元)', value: comas(482.16) },
    { label: '普惠政策(万元)', value: comas(482.16) },
];

const SupportInfo = () => {
    const listState = useList({ dataSource: Array(24).fill({}) });
    // console.info(listState);
    // const { DJZTDM } = useCode('DJZTDM');

    return (
        <div className="shareListStyleCover">
            <div className={s.countBox}>
                {count.map((v) => {
                    return (
                        <div className={s.countItem}>
                            <div className={s.value}>{v.value}</div>
                            <div className={s.label}>{v.label}</div>
                        </div>
                    );
                })}
            </div>
            <ShareList listState={listState} emptyText={<NoData />} PaginationComponent={PaginationComponent}>
                <Column
                    label="扶持政策"
                    field="fczc"
                    ellipsis
                    render={(value, { fzjgtyshxydm, fzjghjqybs }, rowIndex) => {
                        return (
                            <LinkText
                            // type="tab"
                            // tabOption={{ label: '企业详情', key: `qyxq-${fzjgtyshxydm}-${rowIndex}` }}
                            // url={`${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/detail?id=${fzjgtyshxydm}`}
                            >
                                政策名称{rowIndex}
                            </LinkText>
                        );
                    }}
                />
                <Column label="兑现金额（万元）" field="dxje" render={() => comas(111322.44)} />
                <Column label="申报日期" field="dxje" render={() => '2022年10月31日'} />
                <Column label="执行部门" field="dxje" render={() => '计划财政处'} />
                <Column label="政策类型" field="dxje" render={() => '一企一策'} />
            </ShareList>
        </div>
    );
};

export default SupportInfo;
