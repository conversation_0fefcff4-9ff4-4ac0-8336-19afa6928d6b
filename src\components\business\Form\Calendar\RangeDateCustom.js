/*
 *@(#) RangeDateCustom.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-09-05
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React, { useState, useEffect } from 'react';
import moment from 'moment';
import s from './style.scss';
import CalendarRange from './CalendarRange';

const options = [
    // { label: '不限时间', value: 'all' },
    { label: '近1周', value: 'week' },
    { label: '近1月', value: 'month' },
    { label: '近1年', value: 'year' },
    { label: '自定义', value: 'custom' },
];

const getRecentDay = (n) => {
    return moment(new Date()).subtract(n, 'days').format('YYYY-MM-DD');
};

const getRecentMonth = (n) => {
    return moment(new Date()).subtract(n, 'months').format('YYYY-MM-DD');
};

const strFill = (val, str) => {
    return val && String(val).indexOf(str) === -1 ? `${val} ${str}` : val;
};

const getRecentYear = (n) => {
    return moment(new Date()).subtract(n, 'years').format('YYYY-MM-DD');
};

const RangeDateCustom = ({ value, onChange, autofillTime, placeholder }) => {
    const [show, setShow] = useState(false);
    const { type = '', start, end } = value || {};
    const simpleValue = {
        start: start ? start.split(' ')[0] : start,
        end: end ? end.split(' ')[0] : end,
    };
    const showLabelObj = options.find((v) => v.value === type) || {};
    const showLabel = showLabelObj.label;

    const handlerChange = (v) => {
        if (autofillTime) {
            v.start = strFill(v.start, '00:00:00');
            v.end = strFill(v.end, '23:59:59');
        }
        onChange({ target: { value: v } });
    };
    const handlerClick = (obj) => {
        setShow(false);
        let timeSet = {};
        const today = moment(new Date()).format('YYYY-MM-DD');

        switch (obj.value) {
            case 'week':
                timeSet = { start: getRecentDay(6), end: today };
                break;
            case 'month':
                timeSet = { start: getRecentMonth(1), end: today };
                break;
            case 'year':
                timeSet = { start: getRecentYear(1), end: today };
                break;
            default:
                timeSet = { start: '', end: '' };
                break;
        }
        handlerChange({
            ...timeSet,
            type: obj.value,
        });
    };

    const globalClick = (e) => {
        if (show) {
            // 是否点击自身
            const clickSelf = $(e.target)  //eslint-disable-line
                    .parents('.rangeDateCustomContent').length > 0;

            if (clickSelf) {
                return;
            }
            setShow(false);
        }
    };

    useEffect(() => {
        document.addEventListener('click', globalClick, true);

        return () => {
            document.removeEventListener('click', globalClick);
        };
    }, [show]);

    return (
        <div className={s.box}>
            <div className={`${s.trigger}`} onClick={() => setShow(true)}>
                {showLabel || <span style={{ color: '#a4a5a9' }}>{placeholder}</span>}
                {showLabel && <i className="si si-com_closethin" onClick={() => handlerClick({ type: '' })} />}
                <i className={show ? 'si si-com_up' : 'si si-com_lower-64'} />
            </div>
            <div className={`${s.content} rangeDateCustomContent`} style={show ? {} : { display: 'none' }}>
                {options
                    .filter((v) => v.value !== 'custom')
                    .map((v) => {
                        return (
                            <div className={`${s.item} ${v.value === type ? s.active : ''}`} onClick={() => handlerClick(v)}>
                                {v.label}
                            </div>
                        );
                    })}
                <div className={s.custom}>
                    <div className={s.text}>自定义</div>
                    <div className={s.rangeBox}>
                        <div className={s.left}>从至</div>
                        <div className={s.right}>
                            <CalendarRange
                                value={simpleValue}
                                onChange={(e) => {
                                    handlerChange({
                                        ...e.target.value,
                                        type: 'custom',
                                    });
                                }}
                            />
                        </div>
                    </div>
                    <div className={s.customBtn}>
                        <span
                            onClick={() => {
                                handlerChange({ ...value, type: 'custom' });
                                setShow(false);
                            }}
                        >
                            确认
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RangeDateCustom;
