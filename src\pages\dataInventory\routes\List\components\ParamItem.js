import React, { Fragment } from 'react';
import { Input, Select, Timing } from '@share/shareui-form';
import { CreatableSelect } from '@/components/form';
import { addErrorTip, validFunction } from '@/utils/shareFormUtil';

const InputCus = addErrorTip(Input);
const SelectCus = addErrorTip(Select);
const CreatableSelectCus = addErrorTip(CreatableSelect);

const typeOptions = [
    { value: 'string', label: '文本' },
    { value: 'date', label: '时间' },
    { value: 'number', label: '数字' },
];

const unitOptions = [
    { value: '元', label: '元' },
    { value: '条', label: '条' },
    { value: '人', label: '人' },
    { value: '岁', label: '岁' },
];

const formatOptions = [
    { value: '^[0|1]$', label: '是否【^[0|1]$】' },
    { value: '^[0-9]+$', label: '数字【^[0-9]+$】' },
    { value: '^[0-9]{4}$', label: '年度【^[0-9]{4}$】' },
    { value: '^[0-9]{4}Q[1-4]$', label: '季度【^[0-9]{4}Q[1-4]$】' },
    { value: '^[0-9]{4}(0[1-9]|1[0-2])$', label: '月度【^[0-9]{4}(0[1-9]|1[0-2])$】' },
];

const dictTypeOptions = [
    { value: 'list', label: '列表' },
    { value: 'tree', label: '树状' },
];

const ParamItem = (props) => {
    const { field, value, onChange, sourceFieldList, codeOptions, booleanOptions, existId, disabledId, width } = props;
    const fieldOptions = sourceFieldList.map((item) => ({
        value: item.columnName.toUpperCase(),
        label: `${item.columnName.toUpperCase()}(${item.columnComment})`,
    }));

    return (
        <Fragment>
            {fieldOptions.length > 0 ? (
                <CreatableSelectCus
                    field={`${field}.id`}
                    label="键名（key）（请选择或手输）"
                    rule={[
                        'required',
                        validFunction({
                            fn: (v) => {
                                return !v || existId.filter((item) => item === v).length === 0;
                            },
                            errMsg: '已存在',
                            timing: Timing.blur,
                        }),
                        validFunction({
                            fn: (v) => !v || !/^.*\."+.*$/.test(v),
                            errMsg: '不允许存在点和双引号',
                            timing: Timing.blur,
                        }),
                    ]}
                    noView
                    placeholder="键名（key）（请选择或手输）"
                    options={fieldOptions}
                    onChange={({ target: { value: v } }) => {
                        const filedItem = sourceFieldList.find((item) => item.columnName === v);

                        if (filedItem) {
                            const data = { ...value, id: v, source: v, name: filedItem.columnComment };

                            if (filedItem?.integer || filedItem?.decimal) {
                                data.type = 'number';
                            } else if (filedItem?.date || filedItem?.dateTime) {
                                data.type = 'date';
                            } else {
                                data.type = 'string';
                            }
                            onChange({
                                target: { value: data },
                            });
                        }
                    }}
                    disabled={disabledId}
                    style={{ width }}
                />
            ) : (
                <InputCus
                    field={`${field}.id`}
                    label="键名（key）"
                    rule={[
                        'required',
                        validFunction({
                            fn: (v) => {
                                return !v || existId.filter((item) => item === v).length === 0;
                            },
                            errMsg: '已存在',
                            timing: Timing.blur,
                        }),
                        validFunction({
                            fn: (v) => !v || !/^.*\."+.*$/.test(v),
                            errMsg: '不允许存在点和双引号',
                            timing: Timing.blur,
                        }),
                    ]}
                    noView
                    placeholder="键名（key）"
                    style={{ width }}
                    disabled={disabledId}
                />
            )}
            <InputCus field={`${field}.name`} label="名称（label）" rule="required" noView placeholder="名称（label）" style={{ width }} />
            {fieldOptions.length > 0 ? (
                <CreatableSelectCus
                    field={`${field}.source`}
                    label="查询项（请选择或手输）"
                    rule="required"
                    noView
                    placeholder="查询项（请选择或手输）"
                    options={fieldOptions}
                    onChange={({ target: { value: v } }) => {
                        const filedItem = sourceFieldList.find((item) => item.columnName === v);

                        if (filedItem) {
                            const data = { ...value, source: v, name: filedItem.columnComment };

                            if (filedItem?.integer || filedItem?.decimal) {
                                data.type = 'number';
                            } else if (filedItem?.date || filedItem?.dateTime) {
                                data.type = 'date';
                            } else {
                                data.type = 'string';
                            }
                            onChange({
                                target: { value: data },
                            });
                        }
                    }}
                    style={{ width }}
                />
            ) : (
                <InputCus field={`${field}.source`} label="查询项" noView placeholder="查询项" style={{ width }} />
            )}
            <SelectCus
                field={`${field}.type`}
                label="类型"
                rule="required"
                noView
                placeholder="类型"
                options={typeOptions}
                style={{ width }}
            />
            <CreatableSelectCus
                field={`${field}.unit`}
                label="单位"
                noView
                options={unitOptions}
                placeholder="单位（请选择或手输）"
                style={{ width }}
            />
            <CreatableSelectCus
                field={`${field}.format`}
                label="校验正则"
                noView
                options={formatOptions}
                placeholder="校验正则（请选择或手输）"
                style={{ width }}
            />
            <SelectCus
                field={`${field}.dict.alias`}
                label="字典别名"
                noView
                placeholder="字典别名"
                options={codeOptions}
                style={{ width }}
            />
            <SelectCus
                field={`${field}.dict.type`}
                label="字典类型"
                noView
                placeholder="字典类型"
                options={dictTypeOptions}
                style={{ width }}
            />
            <SelectCus
                field={`${field}.enabled`}
                label="是否启用"
                rule="required"
                noView
                placeholder="是否启用"
                options={booleanOptions}
                style={{ width }}
            />
        </Fragment>
    );
};

export default ParamItem;
