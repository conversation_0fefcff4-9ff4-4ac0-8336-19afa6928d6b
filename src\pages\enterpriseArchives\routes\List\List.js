/*
 *@(#) List.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-09
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React from 'react';

import { Popover, Tree } from 'antd';
import ShareList from '@share/list';
import { Button } from '@share/shareui';
import classNames from 'classnames';
import { fieldTranslate, moneyFormat, stringLengthFormat } from '@/utils/format';
import Highlighter from 'react-highlight-words';
import NoData from '@/components/NoData/NoData';
import useUrlParamsSearch from '@/hooks/useUrlParamsSearch';
// import { ExcelExport } from '@share/impexp';
import { instance } from '@share/network';
import dayjs from 'dayjs';
import AdvancedQuery from './AdvancedQuery';
import { useLogic, useCodeMap } from './hooks';
import global from '../../style.scss';
import styles from './List.scss';
import { useAuthExpressionHook } from '../Detail/hooks';

const SpaceNetwork = instance();
// todo
SpaceNetwork.setContextPath('/edp-front/basic');
const TableComponent = (props) => {
    const { api, goDetail, bm, searchVal, tagList, treeDataTranslate } = props;
    const { RYGMLXDM } = bm || {};

    if (api.list.length === 0) {
        return <NoData />;
    }

    return api.list.map((v) => {
        // const treeDataTranslateData = treeDataTranslate(tagList, v.accInfoList);
        const treeDataTranslateData = [];
        console.log('🚀 ~ returnapi.list.map ~ treeDataTranslateData:', treeDataTranslateData);

        return (
            <div
                key={v.tyshxydm}
                className={styles.listItem}
                onClick={() => {
                    goDetail(v.tyshxydm);
                }}
            >
                <div className={styles.listItemTitle}>
                    {/* <span className={styles.title}>{v.qymc}</span> */}
                    <Highlighter
                        key={v.tyshxydm}
                        className={styles.title}
                        highlightClassName={styles.titleHighLight}
                        searchWords={[searchVal]}
                        autoEscape
                        textToHighlight={`${v.zzmc} `}
                    />
                    {v.zcdzyqmc && <span className={`${styles.parkLabel} ${global.baseLabel}`}>{v.zcdzyqmc}</span>}
                </div>
                <div className={styles.listItemInfo}>
                    <div>
                        <span>法人：</span>
                        {v.frdbr}
                    </div>
                    <div>
                        <span>注册资本：</span>
                        {moneyFormat(v.zczb)}万元
                    </div>
                    <div>
                        <span>注册时间：</span>
                        {v.clrq}
                    </div>
                    {/* <div> */}
                    {/*    <span>企业规模：</span> */}
                    {/*    {fieldTranslate(v.rygmlxdm, RYGMLXDM)} */}
                    {/* </div> */}
                    <div>
                        <span>注册地址：</span>
                        {v.zs}
                    </div>
                    <div>
                        <span>经营地址：</span>
                        {v.jycs}
                    </div>
                </div>

                {Array.isArray(treeDataTranslateData.tagList) && treeDataTranslateData.tagList.length > 0 && (
                    <div className={styles.listItemTag}>
                        {treeDataTranslateData.tagList.map((item) => {
                            if (item?.children?.length === 0 && item?.parents?.length === 0) {
                                return (
                                    <span key={item.id} className={global.baseLabel} title={item.name}>
                                        {stringLengthFormat(item.name, 8)}
                                    </span>
                                );
                            }

                            return (
                                <Popover
                                    key={item.id}
                                    placement="rightBottom"
                                    content={
                                        <div className={styles['companyTag-Popover']}>
                                            {item?.parents?.length > 0 ? (
                                                <div className={styles['companyTag-Popover_parents']}>
                                                    {(item?.parents || []).map((ii) => {
                                                        return (
                                                            <div className={styles['companyTag-Popover_parents--item']} key={ii.key}>
                                                                {ii.name}
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            ) : (
                                                ''
                                            )}

                                            <div className={styles['companyTag-Popover_oneself']}>
                                                <div className={styles['companyTag-Popover_oneself--item']}>{item.name}</div>
                                            </div>
                                            {item.children?.length > 0 ? (
                                                <div className={styles['companyTag-Popover_children']}>
                                                    <Tree defaultExpandAll treeData={item.children || []} />
                                                </div>
                                            ) : (
                                                ''
                                            )}
                                        </div>
                                    }
                                >
                                    <span key={item.id} className={global.baseLabel} title={item.name}>
                                        {stringLengthFormat(item.name, 8)}
                                    </span>
                                </Popover>
                            );
                        })}
                    </div>
                )}
                {/* {Array.isArray(v.accInfoList) && v.accInfoList.length > 0 && (
                    <div className={styles.listItemTag}>
                        {v.accInfoList.map((a, i) => (
                            // eslint-disable-next-line react/no-array-index-key
                            <span className={global.baseLabel} key={`${a.zzrdlxmc}${i}`}>
                                {a.zzrdlxmc}
                            </span>
                        ))}
                    </div>
                )} */}
            </div>
        );
    });
};

const List = () => {
    const { RYGMLXDM, parkList, BUSINESS_STATUS, ECONOMY_TYPE, INDUSTRY_CATEGORIES, GJDM, DM_QY_DJJG, getQyDjJgOptions } = useCodeMap();
    const {
        listState,
        searchVal,
        setSearchVal,
        handlerQuery,
        handlerChangeOrderBys,
        renderOrderBtn,
        isFocusList,
        setIsFocusList,
        goDetail,
        showAdvancedQuery,
        handleShowAdvancedQuery,
        handleAdvancedQuery,
        setState,
        tagList,
        treeDataTranslate,
        keyWordParam,
        params,
    } = useLogic();
    const { page } = listState || {};
    const { authExpressionMap } = useAuthExpressionHook({ expressions: ['economicbrain:qyarchives:list:export'] }, false);

    const renderTableComponent = (props) => (
        <TableComponent
            {...props}
            tagList={tagList}
            treeDataTranslate={treeDataTranslate}
            goDetail={goDetail}
            searchVal={searchVal}
            bm={{ RYGMLXDM }}
        />
    );

    useUrlParamsSearch({
        listState,
        setFormData: ({ qymc }) => {
            setSearchVal(qymc);
            setState({ keyWordParam: qymc });
        },
    });

    return (
        <div className={styles.enterpriseArchivesList}>
            <AdvancedQuery
                codeMap={{
                    parkList,
                    BUSINESS_STATUS,
                    INDUSTRY_CATEGORIES,
                    ECONOMY_TYPE,
                    GJDM,
                    DM_QY_DJJG,
                }}
                showAdvancedQuery={showAdvancedQuery}
                handleShowAdvancedQuery={handleShowAdvancedQuery}
                handleAdvancedQuery={handleAdvancedQuery}
            />
            <div
                className={classNames(styles['enterpriseArchivesList-warp'], {
                    [styles.show]: showAdvancedQuery,
                })}
            >
                <div className={styles.searchBar}>
                    <span className={styles.inputBox}>
                        <input
                            id="keyword"
                            value={searchVal}
                            onChange={(e) => setSearchVal(e.target.value)}
                            placeholder="输入企业关键词进行搜索"
                        />
                        <i className="si si-com_search" />
                    </span>
                    <Button bsSize="large" bsStyle="primary" onClick={handlerQuery}>
                        查询
                    </Button>

                    <span className={styles.advancedQueryBtn} onClick={handleShowAdvancedQuery}>
                        {showAdvancedQuery ? '简要查询' : '高级查询'}
                    </span>
                </div>

                <div className={styles.toolBar}>
                    <div className={styles.toolBarLeft}>
                        企业列表
                        <span>
                            符合条件<i>{listState?.page?.totalNum}</i>家
                        </span>
                    </div>
                    <div className={styles.toolBarRight}>
                        {/* {authExpressionMap['economicbrain:qyarchives:list:export'] && ( */}
                        {/*    // <div className={styles.expBtn}> */}
                        {/*    //     <ExcelExport */}
                        {/*    //         network={SpaceNetwork} */}
                        {/*    //         schema={{ */}
                        {/*    //             name: '导出', */}
                        {/*    //             schemaId: 'enterprise/searchByExport', */}
                        {/*    //             template: 'EnterpriseExportTemplate', // 模板id */}
                        {/*    //             fileName: `企业列表_${dayjs().format('YYYYMMDD')}`, */}
                        {/*    // */}
                        {/*    //             queryData: () => ({ */}
                        {/*    //                 ...listState.requestData, */}
                        {/*    //                 qymc: keyWordParam, */}
                        {/*    //                 searchType: isFocusList ? '1' : '0', */}
                        {/*    //                 ...params, */}
                        {/*    //             }), */}
                        {/*    //         }} */}
                        {/*    //         simple */}
                        {/*    //     /> */}
                        {/*    // </div> */}
                        {/* )} */}
                        <span
                            className={classNames({
                                [global.baseButton]: true,
                                [global.active]: renderOrderBtn('zczb').isActive,
                            })}
                            onClick={() => handlerChangeOrderBys('zczb', 'desc')}
                        >
                            注册资本{renderOrderBtn('zczb').btnText}
                            <i className={global.sort} />
                        </span>
                        <span
                            className={classNames({
                                [global.baseButton]: true,
                                [global.active]: renderOrderBtn('clrq').isActive,
                            })}
                            onClick={() => handlerChangeOrderBys('clrq', 'asc')}
                        >
                            成立日期{renderOrderBtn('clrq').btnText}
                            <i className={global.sort} />
                        </span>
                        {/* <span */}
                        {/*    className={classNames({ */}
                        {/*        [global.baseButton]: true, */}
                        {/*        [global.active]: isFocusList, */}
                        {/*    })} */}
                        {/*    onClick={() => setIsFocusList(!isFocusList)} */}
                        {/* > */}
                        {/*    关注的企业 */}
                        {/* </span> */}
                    </div>
                </div>
                <div className={styles.listBox}>
                    <ShareList uniqKey="id" listState={listState} TableComponent={renderTableComponent} />
                </div>
            </div>
        </div>
    );
};

export default List;
