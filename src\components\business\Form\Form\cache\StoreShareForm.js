// 工具类
import * as Store from '@/components/common/store/Store';
// 表单组件
import Form from './CacheShareForm';

class StoreShareForm extends Form {
    static defaultProps = {
        ...Form.defaultProps,
    };

    // 缓存数据
    saveCacheData = (namespace, data) => {
        const form = Store.get('form') || {};

        form[namespace] = data;
        Store.set('form', form);
    };

    // 获取缓存数据
    getCacheData = (namespace) => {
        const form = Store.get('form') || {};

        return form[namespace];
    };
}

export default StoreShareForm;
