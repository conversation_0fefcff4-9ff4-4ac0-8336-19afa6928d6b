import React, { Component } from 'react';
import styles from '@/pages/metaData/styles/index.scss';
import * as MetaTableApi from '@/services/data/meta/MetaTableApi';
// 工具类
import * as formRule from '@/components/common/common/formRule';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
// 组件
import MyAlert from '@/components/ui/MyAlert/MyAlert';
import { Modal, Button } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';
import DatabaseTableGenList from './DatabaseTableGenList';

const { Form, Row, Input, FormItem } = getComponents();

const testList = [
    { id: 1, columnName: 'TEST_DATE', columnType: 'date', columnSize: [], columnComment: '备注TEST_DATE' },
    { id: 2, columnName: 'TEST_DATETIME', columnType: 'dateTime', columnSize: [], columnComment: '备注TEST_DATETIME' },
    { id: 3, columnName: 'TEST_INTEGER', columnType: 'integer', columnSize: [10], columnComment: '备注TEST_INTEGER' },
    { id: 4, columnName: 'TEST_DECIMAL', columnType: 'decimal', columnSize: [10, 4], columnComment: '备注TEST_DECIMAL' },
    { id: 5, columnName: 'TEST_TEXT_5', columnType: 'text', columnSize: [5], columnComment: '备注TEST_TEXT_5' },
    { id: 6, columnName: 'TEST_TEXT_100', columnType: 'text', columnSize: [100], columnComment: '备注TEXT_100' },
    { id: 7, columnName: 'TEST_TEXT_2500', columnType: 'text', columnSize: [2500], columnComment: '备注TEST_TEXT_2500' },
];

const defaultBody = {
    // tableId: 'EDATA.T_TEST',
    // tableComment: '测试备注',
    // columnList: [...testList],
    tableId: '',
    tableComment: '',
    columnList: [],
};

class DatabaseTableGenModal extends Component {
    state = {
        editForm: new FormState({ ...defaultBody }, (editForm, callback) => this.setState({ editForm }, callback)),
        existTables: [],
        systemFields: [],
    };

    async componentWillReceiveProps(nextProps) {
        const { editForm } = this.state;
        const { show } = nextProps;

        if (!this.props.show && show) {
            const existTables = await MetaTableApi.getDatabaseSource();
            const systemFields = await MetaTableApi.getSystemField();

            this.setState({ existTables, systemFields });
            editForm.setFormData({ ...defaultBody });
            editForm.cleanValidError();
        }
    }

    submit = async () => {
        const { successFn } = this.props;
        const { editForm } = this.state;

        if (FormVaildHelper.isValid(await editForm.valid())) {
            MetaTableApi.createDataTable(editForm.getFormData());
            MyAlert.ok('新增成功！');
            successFn && successFn();
        }
    };

    render() {
        const { show, cancelFn } = this.props;
        const { editForm, existTables, systemFields } = this.state;
        const existTableIds = existTables.map((item) => `${item.databaseName}.${item.tableName}`).map((item) => item.toUpperCase());

        return (
            <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>字段信息编辑</Modal.Header>
                <Modal.Body full>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <Input
                                label="库表名"
                                field="tableId"
                                rule={[
                                    formRule.checkRequiredNotBlank(),
                                    formRule.checkRegex(/\w{1,64}\.\w{1,64}/, '格式错误（格式为库名.表名，且库名表名长度最多64）'),
                                    formRule.checkFunctionByTiming(
                                        (value) => !existTableIds.includes(value.toUpperCase()),
                                        '在数据库中已存在',
                                        1
                                    ),
                                    formRule.checkRegex(/^(?!(.*_(trc|qs|audit)))(.*)$/, '后缀不能是“_trc”、“_qs”、“_audit”'),
                                ]}
                                required
                                placeholder="格式：库名.表名"
                            />
                        </Row>
                        <Row>
                            <Input label="表备注" field="tableComment" rule={[formRule.checkRequiredNotBlank()]} required />
                        </Row>
                        <Row>
                            <FormItem label="字段信息" field="columnList" rule={[formRule.checkRequiredNotBlank()]} required>
                                {(props) => <DatabaseTableGenList existTables={existTables} systemFields={systemFields} {...props} />}
                            </FormItem>
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        确认
                    </Button>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default DatabaseTableGenModal;
