import { RemoteService } from '@share/framework';

class InventoryService extends RemoteService {
    all = () => {
        return this.network.formGet('/inventory/all.do');
    };

    list = () => {
        return this.network.formGet('/inventory/list.do');
    };

    add = (param) => {
        return this.network.json('/inventory/add.do', param);
    };

    update = (param) => {
        return this.network.json('/inventory/update.do', param);
    };
}
export default InventoryService;
