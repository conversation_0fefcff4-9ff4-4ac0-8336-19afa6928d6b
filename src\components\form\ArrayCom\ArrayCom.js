import React from 'react';
import { Form } from '@share/shareui';
import { registerFormItem } from '@/utils/shareFormUtil';
import style from './ArrayCom.scss';

const ArrayCom = (props) => {
    const { value = [], onChange, defaultChildrenData, children, ...restProps } = props;

    return (
        <Form pageType="addPage" className={style.body}>
            <Form.Table>
                {Array.isArray(value) &&
                    value.map((item, index) => (
                        <Form.Tr key={index}>
                            <Form.Content>
                                <div className={style.content}>
                                    <div className={style.contentChildren}>
                                        {children &&
                                            children({
                                                ...restProps,
                                                index,
                                                list: value,
                                                value: item,
                                                onChange: ({ target: { value: itemValue } }) => {
                                                    onChange({
                                                        target: {
                                                            value: value.map((one, i) => (i !== index ? one : itemValue)),
                                                        },
                                                    });
                                                },
                                            })}
                                    </div>
                                    <div className={style.contentOperate}>
                                        {/* 新增 */}
                                        <i
                                            className={`si si-com_increase ${style.add}`}
                                            onClick={() =>
                                                onChange({
                                                    target: {
                                                        value: [
                                                            ...(value.slice(0, index + 1) || []),
                                                            defaultChildrenData ? { ...defaultChildrenData } : '',
                                                            ...(value.slice([index + 1]) || []),
                                                        ],
                                                    },
                                                })
                                            }
                                        />
                                        {/* 删除 */}
                                        <i
                                            className={`si si-com_minus-circle ${style.delete}`}
                                            onClick={() => onChange({ target: { value: value.filter((one, i) => i !== index) } })}
                                        />
                                        {/* 下移 */}
                                        {index !== value.length - 1 && (
                                            <i
                                                className={`si si-app_xy ${style.down}`}
                                                onClick={() =>
                                                    onChange({
                                                        target: {
                                                            value: [
                                                                ...(value.slice(0, index) || []),
                                                                value[index + 1],
                                                                value[index],
                                                                ...(value.slice([index + 2]) || []),
                                                            ],
                                                        },
                                                    })
                                                }
                                            />
                                        )}
                                        {/* 上移 */}
                                        {index !== 0 && (
                                            <i
                                                className={`si si-app_sy ${style.up}`}
                                                onClick={() =>
                                                    onChange({
                                                        target: {
                                                            value: [
                                                                ...(value.slice(0, index - 1) || []),
                                                                value[index],
                                                                value[index - 1],
                                                                ...(value.slice([index + 1]) || []),
                                                            ],
                                                        },
                                                    })
                                                }
                                            />
                                        )}
                                    </div>
                                </div>
                            </Form.Content>
                        </Form.Tr>
                    ))}
                {(!Array.isArray(value) || value.length === 0) && (
                    <Form.Tr key="add">
                        <Form.Content>
                            <i
                                className={`si si-com_increase ${style.addBtn}`}
                                onClick={() =>
                                    onChange({
                                        target: {
                                            value: [...value, defaultChildrenData ? { ...defaultChildrenData } : ''],
                                        },
                                    })
                                }
                            />
                        </Form.Content>
                    </Form.Tr>
                )}
            </Form.Table>
        </Form>
    );
};

export default registerFormItem(ArrayCom);
