import React, { Fragment } from 'react';

import { SearchForm, RadioGroup } from '@share/shareui-form';
import { ShareList, Toolbar } from '@share/list';
import QueryNodes from './components/QueryNodes';
import ConditionNode from './components/ConditionNode';
import { useModel } from './hooks';

const typeOptions = [
    { value: 'ent', label: '企业' },
    { value: 'person', label: '个人' },
];

const List = () => {
    const $model = useModel();
    const type = $model.form.getFieldValue('type');

    return (
        <Fragment>
            <SearchForm formState={$model.form} query={$model.query}>
                <RadioGroup
                    label="查询类型"
                    field="type"
                    rule="required"
                    options={typeOptions}
                    onChange={() => $model.form.setFieldValues({ source: [], condition: null })}
                    col={12}
                />
                <QueryNodes
                    label="查询节点"
                    field="result"
                    rule="required"
                    inventoryList={$model.inventoryList.filter((item) => item.objectType === type)}
                    col={12}
                />
                <ConditionNode
                    label="条件节点"
                    field="condition"
                    inventoryList={$model.inventoryList.filter((item) => item.objectType === type)}
                    col={12}
                />
            </SearchForm>
            <Toolbar title="查询结果">
                <Toolbar.Desc>
                    <Toolbar.DescItem label="耗时(毫秒)" value={$model.detailForm.getFieldValue('took')} bsStyle="primary" />
                </Toolbar.Desc>
            </Toolbar>
            <ShareList listState={$model.list} columns={$model.columns} />
        </Fragment>
    );
};

export default List;
