.TimelineItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-statusTime {
        &_time {
            margin-right: 12px;
        }
    }
    &-entNum {
        &_label {
            padding: 0 4px;
            height: 20px;
            font-size: 14px;
            font-weight: normal;
            display: inline-flex;
            align-items: center;
            border-radius: 3px;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-color: #09d;
            background: #09d;
            color: #fff;
        }
        .num {
            font-size: 16px;
            color: #09d;
            cursor: pointer;
            &.isZero {
                color: #f65;
                cursor: default;
            }
        }
    }
}

.EnterpriseList {
    padding-top: 20px;
    padding-left: 12px;
    &-right {
        display: flex;
        align-items: center;
        .zksq {
            margin-left: 12px;
            color: #09d;
            cursor: pointer;
        }
    }
}

.more,
.end {
    text-align: center;
    margin-top: 12px;
}
