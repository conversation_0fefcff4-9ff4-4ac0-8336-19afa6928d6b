/*
 *@(#) business.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-10
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import { Row, TableForm, Text, FormItem } from '@share/shareui-form';
import React, { Fragment } from 'react';
import { fieldTranslate, moneyFormat, timeFormat } from '@/utils/format';
import { useCode } from '@share/framework';

import styles from './Business.scss';
import AddAddressModal from './AddAddressModal';

const Business = ({ formState }) => {
    const formData = formState.getFormData();
    const { BUSINESS_STATUS, ECONOMY_TYPE, RYGMLXDM, SSGSDXZQHDM, INDUSTRY_CATEGORIES, INDUSTRY_CODE, QYXZLXDM, ZCDZGSXZQHDM } = useCode(
        'BUSINESS_STATUS',
        'ECONOMY_TYPE',
        'RYGMLXDM',
        'SSGSDXZQHDM',
        'INDUSTRY_CATEGORIES',
        'INDUSTRY_CODE',
        'QYXZLXDM',
        'ZCDZGSXZQHDM'
    );

    return (
        <div className="formTableStyleCover">
            <TableForm formState={formState}>
                <Row>
                    <Text label="企业名称" field="zzmc" format={(val) => val || '--'} />
                    <Text label="统一社会信用代码" field="tyshxydm" format={(val) => val || '--'} />
                </Row>
                <Row>
                    <Text label="法定代表人" field="frdbr" format={(val) => val || '--'} />
                    <Text label="登记状态" field="djzt" format={(val) => fieldTranslate(val, BUSINESS_STATUS)} />
                </Row>
                <Row>
                    <Text label="成立日期" field="clrq" format={(val) => val || '--'} />
                    <Text label="企业类型" field="scztlx" format={(val) => fieldTranslate(val, ECONOMY_TYPE)} />
                </Row>
                <Row>
                    <Text label="注册资本" field="zczb" format={(val) => (val ? `${moneyFormat(val)}万元` : '--')} />
                    <Text label="实缴资本" field="sjzb" format={(val) => (val ? `${moneyFormat(val)}万元` : '--')} />
                </Row>
                <Row>
                    <Text label="工商注册号" field="zch" format={(val) => val || '--'} />
                    <Text label="营业期限" field="yxqsrq" value={`${timeFormat(formData.yxqsrq)} 至 ${timeFormat(formData.yxjzrq)}`} />
                </Row>
                {/* <Row>
                    <Text label="人员规模" field="rygmlxdm" format={val => fieldTranslate(val, RYGMLXDM)} />
                    <Text label="参保人数" field="zjycnbcbrs" format={val => val || '--'} />
                </Row> */}
                {/* <Row> */}
                {/*    <Text label="纳税人识别号" field="nsrsbh" format={(val) => val || '--'} /> */}
                {/*    <Text label="组织机构代码" field="zzjgdm" format={(val) => val || '--'} /> */}
                {/* </Row> */}
                <Row>
                    <Text label="核准日期" table-colSpan={3} field="apprdate" format={(val) => (val ? timeFormat(val) : '--')} />
                    {/* <Text label="行政区划分" field="zcdzgsxzqhdm" format={(val) => fieldTranslate(val, ZCDZGSXZQHDM)} /> */}
                </Row>
                <Row>
                    <Text label="行业门类" field="hyml" format={(val) => fieldTranslate(val, INDUSTRY_CATEGORIES)} />
                    <Text label="行业代码" field="hydm" format={(val) => fieldTranslate(val, INDUSTRY_CODE)} />
                </Row>
                <Row>
                    {/* <Text label="国标行业" field="" /> */}
                    {/* <Text label="企业性质" field="qyxzlxdm" format={(val) => fieldTranslate(val, QYXZLXDM)} /> */}
                    <Text label="登记机关" table-colSpan={3} field="djjg" format={(val) => val || '--'} />
                </Row>
                <Row>
                    <FormItem label="经营地址" table-colSpan={3} field="enterpriseAddressList">
                        {(fieldProps) => {
                            const { zs, qymc } = formData;
                            const { value = [] } = fieldProps;

                            return (
                                <div className={styles.addressList}>
                                    <div className={styles['addressList-item']}>
                                        {zs}
                                        {/* <AddAddressModal jycsdz={jycsdz} tyshxydm={formData.tyshxydm} qymc={qymc} /> */}
                                    </div>
                                    {value.map((item) => {
                                        return (
                                            <div key={item.id} className={styles['addressList-item']}>
                                                {item.jydz}
                                            </div>
                                        );
                                    })}
                                </div>
                            );
                        }}
                    </FormItem>
                </Row>
                <Row>
                    <Text label="经营范围" table-colSpan={3} field="jyfw" format={(val) => val || '--'} />
                </Row>
            </TableForm>
        </div>
    );
};

export default Business;
