import React from 'react';
import { Table } from 'antd';
import styles from './ShareTable.scss';
import useHook from './hook';

const ShareTable = (props) => {
    const customProps = useHook(props);
    console.log('🚀 ~ ShareTable ~ customProps:', customProps);

    return (
        <div className={styles['share-table_container']}>
            <Table locale={{ emptyText: '暂无数据' }} {...props} {...customProps} loading={customProps?.listState?.loading} />
        </div>
    );
};

export default ShareTable;
