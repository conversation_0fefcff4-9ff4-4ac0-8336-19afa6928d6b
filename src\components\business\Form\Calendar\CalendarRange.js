import React, { Component } from 'react';
// 被封装组件
import { CalendarRange as ShareCalendarRange } from '@share/shareui';
import * as TimeUtils from '@/components/common/common/TimeUtil';

const defaultFormat = {
    data: 'YYYY-MM-DD',
    display: 'YYYY-MM-DD',
};

class CalendarRange extends Component {
    rangeChange = ({ target: { field, value } }) => {
        const { value: range = { start: '', end: '' }, onChange, autofillTime } = this.props;
        let nextRange = { ...range, [field]: value };

        if (autofillTime) {
            nextRange = TimeUtils.fillTimeRange(nextRange);
        }
        onChange && onChange({ target: { value: nextRange } });
    };

    render() {
        const { value = { start: '', end: '' }, ...restProps } = this.props;

        return (
            <ShareCalendarRange
                bound="none"
                // bound="between-in"
                format={defaultFormat}
                isValidDate={(currentDate, { start: nowStart, end: nowEnd }, field) => {
                    return field === 'start'
                        ? !nowEnd || currentDate.format('YYYY-MM-DD') <= nowEnd.format('YYYY-MM-DD')
                        : !nowStart || currentDate.format('YYYY-MM-DD') >= nowStart.format('YYYY-MM-DD');
                }}
                {...restProps}
                value={value}
                onChange={this.rangeChange}
            />
        );
    }
}

export default CalendarRange;
