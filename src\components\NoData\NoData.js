/*
 *@(#) NoData.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-09
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React from 'react';
import nodataImg from '@/assets/images/enterprise/nodata.png';
import s from './NoData.scss';

const NoData = () => {
    return (
        <div className={s.noData}>
            <img src={nodataImg} alt="暂无数据" />
            <div>暂无数据</div>
        </div>
    );
};

export default NoData;
