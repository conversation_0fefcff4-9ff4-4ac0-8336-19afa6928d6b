import React, { Component } from 'react';
// 列表组件
import * as Entity from '@/components/common/common/Entity';
import * as StringUtils from '@/components/common/common/StringUtils';
import { Button, Modal } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';

const { Form, Row, Textarea, MultiSelect } = getComponents();

class SpecialRuleEdit extends Component {
    state = {
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
    };

    componentWillReceiveProps(nextProps) {
        const {
            show,
            data: { detail },
        } = nextProps;
        const { editForm } = this.state;

        if (!this.props.show && show) {
            editForm.setFormData(Entity.simpleDeepCopy(detail));
            editForm.cleanValidError();
        }
    }

    submit = async () => {
        const { successFn } = this.props;
        const { editForm } = this.state;

        if (FormVaildHelper.isValid(await editForm.valid())) {
            const editBody = StringUtils.deleteSpace(editForm.getFormData());

            successFn && successFn(editBody);
        }
    };

    render() {
        const {
            show,
            cancelFn,
            data: { metaFieldList },
        } = this.props;
        const { editForm } = this.state;
        // 字段可选项
        const fieldOptions = metaFieldList.map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));

        return (
            <Modal className="modal-full" show={show} onHide={cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>重复规则</Modal.Header>
                <Modal.Body>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <MultiSelect label="判定字段" field="identityConfig.fieldCodes" options={fieldOptions} />
                        </Row>
                        <Row>
                            <Textarea label="提示信息" field="identityConfig.tip" rows="8" />
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        确认
                    </Button>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default SpecialRuleEdit;
