class CreditBaseError extends Error {
    constructor(message, extraData = {}) {
        super(message);
        this.data = extraData;
        this.name = 'CreditBaseError';
    }
}

class FormValidError extends CreditBaseError {
    constructor(message, validErrorArray) {
        super(message, validErrorArray);
        this.name = 'FormValidError';
    }
}

class ValidError extends CreditBaseError {
    constructor(message, extraData) {
        super(message, extraData);
        this.name = 'ValidError';
    }
}

class NetError extends CreditBaseError {
    constructor(message, extraData) {
        super(message, extraData);
        this.name = 'NetworkError';
    }
}

module.exports = {
    CreditBaseError,
    ValidError,
    NetError,
    FormValidError,
};
