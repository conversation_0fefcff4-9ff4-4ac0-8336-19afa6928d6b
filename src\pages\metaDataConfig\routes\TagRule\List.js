import React from 'react';
import { <PERSON><PERSON>, <PERSON>dal, Panel } from '@share/shareui';
import { SearchForm, TableForm, Row, Input, Select, CalendarRange, Calendar, RadioGroup } from '@share/shareui-form';
import { ShareList, Column, ActionColumn } from '@share/list';
import { useModel } from './hooks';

const List = () => {
    const {
        executeFrequencyOptions,
        formState,
        listState,
        showEditModal,
        setShowEditModal,
        editFormState,
        query,
        detail,
        edit,
        submitEdit,
        switchStatus,
        execute,
        remove,
    } = useModel();

    return (
        <div>
            <Panel>
                <Panel.Body full>
                    <SearchForm formState={formState} query={query}>
                        <Input field="taskName" label="打标任务名称" />
                        <Input field="tagName" label="关联标签名称" />
                        <Select
                            field="status"
                            label="任务状态"
                            options={[
                                { label: '启用', value: '1' },
                                { label: '禁用', value: '0' },
                            ]}
                        />
                        <CalendarRange field={['updateTimeRange.start', 'updateTimeRange.end']} label="更新时间" />
                    </SearchForm>
                </Panel.Body>
            </Panel>
            <Panel>
                <Panel.Head title="查询结果" />
                <Panel.Body full>
                    <ShareList listState={listState}>
                        <Column field="taskName" label="打标任务名称" ellipsis />
                        <Column field="tagName" label="关联标签名称" ellipsis />
                        <Column
                            field="executeFrequency"
                            label="打标频率"
                            ellipsis
                            render={(v) => (executeFrequencyOptions.find((i) => i.value === v) || {}).label}
                        />
                        <Column field="status" label="任务状态" ellipsis render={(v) => (v === '1' ? '启用' : '禁用')} />
                        <Column field="nextExecuteTime" label="下一次打标时间" ellipsis />
                        <Column field="updateTime" label="更新时间" ellipsis />
                        <ActionColumn align="center" width={210}>
                            <ActionColumn.Action label="执行明细" onClick={detail} />
                            <ActionColumn.Edit onClick={edit} />
                            <ActionColumn.Action label="启用" use={(row) => row.status === '0'} onClick={switchStatus} />
                            <ActionColumn.Action label="禁用" use={(row) => row.status === '1'} onClick={switchStatus} />
                            <ActionColumn.Action label="执行" use={(row) => row.status === '1'} onClick={execute} />
                            <ActionColumn.Action label="删除" use={(row) => row.status === '0'} onClick={remove} />
                        </ActionColumn>
                    </ShareList>
                </Panel.Body>
            </Panel>
            <Modal show={showEditModal} onHide={() => setShowEditModal(false)} backdrop="static">
                <Modal.Header closeButton>编辑打标任务</Modal.Header>
                <Modal.Body>
                    <TableForm formState={editFormState}>
                        <Row>
                            <Input field="taskName" label="任务名称" rule="required" />
                        </Row>
                        <Row>
                            <Calendar
                                field="executeTime"
                                label="执行时间"
                                format={{
                                    data: 'YYYY-MM-DD HH:mm:ss',
                                    display: 'YYYY-MM-DD HH:mm:ss',
                                }}
                                rule="required"
                            />
                        </Row>
                        <Row>
                            <Select field="executeFrequency" label="执行频率" rule="required" options="executeFrequencyEnum" />
                        </Row>
                        <Row>
                            <RadioGroup
                                field="status"
                                label="任务状态"
                                options={[
                                    { label: '启用', value: '1' },
                                    { label: '停用', value: '0' },
                                ]}
                                rule="required"
                            />
                        </Row>
                    </TableForm>
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={() => setShowEditModal(false)}>取消</Button>
                    <Button bsStyle="primary" onClick={submitEdit}>
                        保存
                    </Button>
                </Modal.Footer>
            </Modal>
        </div>
    );
};

export default List;
