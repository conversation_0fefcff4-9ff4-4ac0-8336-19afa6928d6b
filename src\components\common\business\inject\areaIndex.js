/*
 * @(#) areaIndex.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-10-28 14:31:39
 */

const DeptListManager = require('../manager/DeptListManager');
const BmManager = require('../manager/BmManager');
const withInject = require('./withInject');

function areaWithInjectDefLoader(Component, loadingComponent, errorComponent) {
    return withInject(Component, [DeptListManager.load, BmManager.load], loadingComponent, errorComponent);
}

module.exports = {
    withInject,
    areaWithInjectDefLoader,
};
