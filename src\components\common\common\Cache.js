const { b64EncodeUnicode } = require('./Base64');

const defKeyFactory = (...args) => {
    return JSON.stringify(args);
};

/**
 * 包装目标函数为缓存函数
 * @param method  目标函数
 * @param keyFactory  key生成策略  (...args) => {  }  默认生成策略 所有参数的JSON文本
 * @returns {Function}  包装后的函数
 */
function withCache(method, keyFactory = defKeyFactory) {
    const cache = {};

    const fn = function (...args) {
        const key = keyFactory(...args);

        const base64Key = b64EncodeUnicode(key);

        if (Object.keys(cache).indexOf(base64Key) === -1) {
            cache[base64Key] = method(...args);
        }

        return cache[base64Key];
    };

    fn._cache = cache;

    return fn;
}

module.exports = {
    withCache,
};
