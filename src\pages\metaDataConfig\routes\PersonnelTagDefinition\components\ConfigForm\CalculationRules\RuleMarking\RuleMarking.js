/*
 * @(#)  RuleMarking.js -----规则打标
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 * Copyright: Copyright (c) 2024
 * Company: 厦门畅享信息技术有限公司
 * Author: yujy (<EMAIL>)
 * File Created: 2024-05-08 15:36:52
 */

import React, { useState } from 'react';
import styles from './RuleMarking.scss';
import RuleConfig from './RuleConfig';
import useRuleMarkingHooks from './useRuleMarkingHooks';

const RuleMarking = (props) => {
    const { form } = props;
    const { state, event } = useRuleMarkingHooks(props);

    return <RuleConfig form={form} state={state} event={event} />;
};
export default RuleMarking;
