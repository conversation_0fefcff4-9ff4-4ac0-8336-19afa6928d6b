/*
 *@(#) Branch.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-10
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import ShareList, { Column, NumberColumn, useList } from '@share/list';
import React from 'react';
import { fieldTranslate } from '@/utils/format';
import { useCode } from '@share/framework';
import NoData from '@/components/NoData/NoData';
import LinkText from '../../../LinkText';

const Branch = ({ data = [] }) => {
    const listState = useList({ dataSource: data });
    const { BUSINESS_STATUS } = useCode('BUSINESS_STATUS');

    return (
        <div className="shareListStyleCover">
            <ShareList listState={listState} usePageBar={false} emptyText={<NoData />}>
                <NumberColumn />
                <Column
                    label="企业名称"
                    field="entname"
                    render={(value, { fzjgtyshxydm, fzjghjqybs }, rowIndex) => {
                        if (fzjghjqybs === '1') {
                            return (
                                <LinkText
                                    type="tab"
                                    tabOption={{ label: '企业档案', key: `qyxq-${fzjgtyshxydm}-${rowIndex}` }}
                                    url={`${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/detail?id=${fzjgtyshxydm}`}
                                >
                                    {value}
                                </LinkText>
                            );
                        }

                        return value;
                    }}
                />
                <Column label="成立日期" field="estdate" width={120} />
                <Column label="地址" field="addr" />
                <Column
                    label="状态"
                    field="djztdm"
                    width={120}
                    render={(val) => {
                        return <span>{fieldTranslate(val, BUSINESS_STATUS)}</span>;
                    }}
                />
            </ShareList>
        </div>
    );
};

export default Branch;
