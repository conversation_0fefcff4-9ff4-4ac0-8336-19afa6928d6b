.body {
    table {
        border: 1px solid #ddd;
    }
    i {
        cursor: pointer;
    }
    .addBtn {
        font-size: 22px;
        color:#01ba88;
    }
    .content {
        display: flex;
        align-items: center;
        .contentChildren {
            width: calc(100% - 56px);
            display: flex;
            flex-wrap: wrap;
            align-items: start;
            > * {
                flex: 1;
                padding: 2px!important;
            }
        }
        .contentOperate {
            width: 56px ;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            i {
                margin-left: 8px;
                font-size: 20px;
                &.add {
                    color: #01ba88;
                }
                &.delete {
                    color: #f65;
                }
                &.up,&.down {
                    margin-top: 8px;
                    color: #09d;
                }
            }
        }
    }
}





