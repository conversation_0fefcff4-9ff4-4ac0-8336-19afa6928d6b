import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

const ShareholderGraph = ({ data }) => {
    const svgRef = useRef();

    useEffect(() => {
        const svg = d3.select(svgRef.current);
        svg.selectAll('*').remove(); // 清空之前的内容

        const width = 1000;
        const height = 800;

        const zoom = d3
            .zoom()
            .scaleExtent([0.5, 2])
            .on('zoom', (event) => {
                container.attr('transform', event.transform);
            });

        svg.call(zoom);

        const container = svg.append('g');

        const root = d3.hierarchy(data, (d) => d.children || d.parents);
        const treeLayout = d3.tree().size([width, height]);
        treeLayout(root);

        // Links
        container
            .selectAll('path')
            .data(root.links())
            .enter()
            .append('path')
            .attr(
                'd',
                d3
                    .linkHorizontal()
                    .x((d) => d.x)
                    .y((d) => d.y)
            )
            .attr('fill', 'none')
            .attr('stroke', '#ccc')
            .attr('stroke-width', 2);

        // Nodes
        container
            .selectAll('rect')
            .data(root.descendants())
            .enter()
            .append('rect')
            .attr('x', (d) => d.x - 75)
            .attr('y', (d) => d.y - 20)
            .attr('width', 150)
            .attr('height', 40)
            .attr('fill', (d) => (d.children ? '#4CAF50' : '#2196F3'))
            .attr('stroke', '#ccc')
            .attr('stroke-width', 2)
            .attr('rx', 10)
            .attr('ry', 10);

        // Labels
        container
            .selectAll('.label')
            .data(root.descendants())
            .enter()
            .append('text')
            .attr('class', 'label')
            .attr('x', (d) => d.x)
            .attr('y', (d) => d.y - 5)
            .attr('dy', '.35em')
            .attr('text-anchor', 'middle')
            .attr('fill', 'white')
            .text((d) => d.data.name);

        // Percentages
        container
            .selectAll('.percent')
            .data(root.links())
            .enter()
            .append('text')
            .attr('class', 'percent')
            .attr('x', (d) => (d.source.x + d.target.x) / 2)
            .attr('y', (d) => (d.source.y + d.target.y) / 2)
            .attr('dy', '.35em')
            .attr('text-anchor', 'middle')
            .attr('fill', 'black')
            .text((d) => d.target.data.percent || '');

        // Money
        container
            .selectAll('.money')
            .data(root.descendants())
            .enter()
            .append('text')
            .attr('class', 'money')
            .attr('x', (d) => d.x)
            .attr('y', (d) => d.y + 15)
            .attr('dy', '.35em')
            .attr('text-anchor', 'middle')
            .attr('fill', 'white')
            .text((d) => (d.data.money ? `认缴金额：${d.data.money}万(元)` : ''));
    }, [data]);

    return <svg ref={svgRef} width="1000" height="800" />;
};

export default ShareholderGraph;
