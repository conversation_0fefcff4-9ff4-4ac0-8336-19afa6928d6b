// 请求发送者;
import { instance } from '@share/network';
import { Spin } from '@share/shareui';
import VirtualFormSender from './VirtualFormSender';
// 环境配置
import { __MOCK__, COMMON_API_PREFIX, MOCK_API_PREFIX } from './NetworkConfig';
// 服务器数据处理
import { assertServerException, resolverServerException } from './AnalysisServerData';
// 引入 加载状态 组件

class Network {
    constructor() {
        // 遮罩层数量，为 0 的时候消失
        this.maskNum = 0;
        this.noMaskNum = 0;
        // 模拟表单Sender
        this.vFormSender = new VirtualFormSender();
        // 使用公司@share/network实现（ajax底层具体实现，如FetchSender,XmlHttpRequestSender，底层根据fetch是否存在自动选择具体Sender）
        this.sender = instance();
        // 设置统一请求前缀
        this.sender.setContextPath(__MOCK__ ? MOCK_API_PREFIX : COMMON_API_PREFIX);
        // 设置 url 变更，URL_FORMATTER 为具体项目 webpack 中配置的全局变量
        try {
            // eslint-disable-next-line no-undef
            this.sender.setUrlHandle = URL_FORMATTER ? this.setUrlHandle : (v) => v;
        } catch (e) {
            this.sender.setUrlHandle = (v) => v;
        }
        // 设置统一请求参数处理
        this.sender.setTransRequest((requestData) => {
            this.addMask(requestData && requestData.noMask ? requestData.noMask : false);
            this.errorInfoResolver = requestData.errorInfoResolver;

            return requestData;
        });
        // 设置统一异常处理
        this.sender.setExceptionHandle((respnseObject, abort) => {
            this.maskNum = 0;
            this.noMaskNum = 0;
            Spin.hide();
            assertServerException(respnseObject, abort, this.errorInfoResolver);
        });
        // 设置统一返回参数处理
        this.sender.setResolver((respnseObject, abort) => {
            this.removeMask();

            return resolverServerException(respnseObject, abort);
        });
        // 设置菜单当前地址
        this.menuUrl = window.location.href;
        this.sender.setHeaders({ MenuUrl: encodeURIComponent(this.menuUrl) });
    }

    addMask = (noMask) => {
        if (!noMask) {
            if (this.maskNum === 0) {
                Spin.show('加载中，请等待');
            }
            this.maskNum += 1;
        } else {
            this.noMaskNum += 1;
        }
    };

    removeMask = () => {
        if (this.maskNum > 0) {
            this.maskNum -= 1;
        } else if (this.noMaskNum > 0) {
            this.noMaskNum -= 1;
        }
        if (this.maskNum === 0) {
            Spin.hide();
        }
    };

    /**
     * 设置sender
     * @param sender
     * @return {Network}
     */
    setSender = (sender) => {
        this.sender = sender;

        return this;
    };

    /**
     * 通用请求发送
     */
    get = (url, data = {}) => {
        // get参数添加当前时间uuidTime参数用于解决IE浏览器缓存get请求
        return this.sender.formGet(this.sender.setUrlHandle(url), { ...data, uuidTime: new Date().getTime() });
    };

    /**
     * Form头 请求发送
     */
    postForm = (url, data = {}) => {
        return this.sender.formPost(this.sender.setUrlHandle(url), data);
    };

    /**
     * JSON头 请求发送
     */
    postJson = (url, data = {}) => {
        return this.sender.json(this.sender.setUrlHandle(url), data);
    };

    // /**
    //  * ulynlist请求适配
    //  */
    // ulynlistAdapt = (url, data) => {
    //     return this.sender.formPost(url, data);
    // };

    /**
     * 表单发送Get请求
     */
    vformGet = (url, data = {}) => {
        return this.vFormSender.send(this.sender.setUrlHandle(url), data, 'get');
    };

    /**
     * 表单发送Post请求
     */
    vformPost = (url, data = {}) => {
        return this.vFormSender.send(this.sender.setUrlHandle(url), data, 'post');
    };

    /**
     * a标签发送文件下载请求
     */
    downFile = (url, fieldName) => {
        const newUrl = this.sender.setUrlHandle(url);
        const finalUrl = __MOCK__ ? MOCK_API_PREFIX : COMMON_API_PREFIX + newUrl;
        let finalFieldName;

        if (fieldName === null || typeof fieldName === 'undefined') {
            const split = newUrl.includes('/') ? '/' : '\\';
            const arr = newUrl.split(split);

            finalFieldName = arr[arr.length - 1];
        } else {
            finalFieldName = fieldName;
        }
        const a = document.createElement('a');

        a.setAttribute('style', 'display:none');
        a.setAttribute('href', finalUrl);
        a.setAttribute('download', finalFieldName);
        document.body.appendChild(a);
        a.click();
        a.parentNode.removeChild(a);
    };

    setUrlHandle = (url) => {
        try {
            // eslint-disable-next-line no-undef
            const urlFormatter = URL_FORMATTER.split(';');
            let newUrl = url;

            urlFormatter.forEach((v) => {
                const [a, b] = v.split(',');

                if (newUrl.indexOf(`/${a}/`) === 0) {
                    newUrl = newUrl.replace(`/${a}/`, `/${b}/`);
                }
            });

            return newUrl;
        } catch (e) {
            return url;
        }
    };
}

// 默认导出 Class
export default Network;

// 导出一个默认实例 + 直接导出方法
const network = new Network();
export const get = network.get.bind(network);
export const postJson = network.postJson.bind(network);
export const postForm = network.postForm.bind(network);
export const vformGet = network.vformGet.bind(network);
export const vformPost = network.vformPost.bind(network);
