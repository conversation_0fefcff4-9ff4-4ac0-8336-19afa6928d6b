// 接口请求
const commonApi = require('@/services/system/commonApi');
// 工具类
const store = require('../../store/Store');
const Log = require('../../log/Log');
// 常量
const ME_KEY = 'me';

/**
 * 用户缓存管理器
 */
class MeManager {
    constructor() {
        this.me = {};
        this.setLoader = this.setLoader.bind(this);
        this.getLoader = this.getLoader.bind(this);
        this.load = this.load.bind(this);
        this.setMe = this.setMe.bind(this);
        this.getMe = this.getMe.bind(this);
    }

    /**
     * 清除用户缓存
     */
    static cleanCache() {
        store.del(ME_KEY);
    }

    /**
     *  外部设置用户缓存加载器
     * @param loader 如果是函数返回promise
     */
    setLoader(loader) {
        this.loader = loader;
    }

    /**
     * 获取用户缓存加载器
     * @returns {*} 加载器对象promise
     */
    getLoader() {
        if (typeof this.loader === 'function') {
            return this.loader();
        }

        return commonApi.getMe();
    }

    /**
     * 从服务器加载me
     * @returns {Promise.<{}>}
     */
    load() {
        // 从sessionStorage中获取，没有的话读取接口
        const userInfo = store.get(ME_KEY);

        if (userInfo) {
            Log.debug('加载缓存用户信息');
            this.setMe(userInfo);

            return Promise.resolve();
        }

        return this.getLoader()
            .then((data) => {
                this.setMe(data);
            })
            .catch((e) => {
                Log.error('用户信息获取失败', e);
                throw e;
            });
    }

    /**
     * 手动设置用户信息
     * @param me
     */
    setMe(me) {
        this.me = { ...me };
        store.set(ME_KEY, me);
    }

    /**
     * 获取用户信息
     * @returns {{}}
     */
    getMe() {
        return { ...this.me };
    }
}
module.exports = new MeManager();
