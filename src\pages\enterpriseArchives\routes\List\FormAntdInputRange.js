import React, { useState } from 'react';
import { FormItem } from '@share/shareui-form';

import { Input } from 'antd';

const FormAntdInputRange = ({ field, label, noView = false, ...otherProps }) => {
    return (
        <FormItem field={field} label={label} noView={noView} {...otherProps}>
            {(fieldProps) => {
                const { onChange, value } = fieldProps;

                return (
                    <Input.Group compact>
                        <Input
                            style={{
                                width: '45%',
                                textAlign: 'center',
                            }}
                            field={`${field}.start`}
                            placeholder="请输入"
                            value={value?.[0] || ''}
                            onChange={(e) => {
                                const end = value?.[1] || '';
                                const minAmount = e.target.value;

                                onChange({ target: { value: [minAmount, end] } });
                            }}
                            onBlur={(e) => {
                                const end = value?.[1] || '';
                                const minAmount = e.target.value;
                                if (!minAmount || minAmount.match(/^\d{1,}(\.\d{0,2})?$/)) {
                                    if (end && minAmount && Number(minAmount) > Number(end)) {
                                        onChange({ target: { value: [undefined, end] } });
                                    }
                                }
                            }}
                        />
                        <Input
                            className="site-input-split"
                            style={{
                                width: 20,
                                textAlign: 'center',
                                borderLeft: 0,
                                borderRight: 0,
                                pointerEvents: 'none',
                                background: '#fff',
                                padding: '4px 0',
                            }}
                            placeholder="~"
                            disabled
                        />
                        <Input
                            className="site-input-right"
                            style={{
                                width: '45%',
                                textAlign: 'center',
                                borderLeft: 0,
                            }}
                            value={value?.[1] || ''}
                            placeholder="请输入"
                            onChange={(e) => {
                                const start = value?.[0] || '';
                                const maxAmount = e.target.value;

                                onChange({ target: { value: [start, maxAmount] } });
                            }}
                            onBlur={(e) => {
                                const start = value?.[0] || '';
                                const maxAmount = e.target.value;
                                if (!maxAmount || maxAmount.match(/^\d{1,}(\.\d{0,2})?$/)) {
                                    if (start && maxAmount && Number(maxAmount) < Number(start)) {
                                        onChange({ target: { value: [start, undefined] } });
                                    }
                                    // setMax(maxAmount);
                                }
                            }}
                        />
                    </Input.Group>
                );
            }}
        </FormItem>
    );
};

export default FormAntdInputRange;
