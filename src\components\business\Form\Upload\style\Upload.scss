/*!
 * @(#) Upload.scss
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-05-14 15:49:16
 */

:global {

    .anticon {
        display: inline-block;
        color: inherit;
        font-style: normal;
        line-height: 0;
        text-align: center;
        text-transform: none;
        vertical-align: -0.125em;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    .anticon > * {
        line-height: 1;
    }
    .anticon svg {
        display: inline-block;
    }
    .anticon::before {
        display: none;
    }
    .anticon .anticon-icon {
        display: block;
    }
    .anticon[tabindex] {
        cursor: pointer;
    }
    .anticon-spin::before {
        display: inline-block;
        -webkit-animation: loadingCircle 1s infinite linear;
        animation: loadingCircle 1s infinite linear;
    }
    .anticon-spin {
        display: inline-block;
        -webkit-animation: loadingCircle 1s infinite linear;
        animation: loadingCircle 1s infinite linear;
    }
    .anticon-paper-clip {
        top: 5px;
    }

    .anticon.anticon-upload {
        left: 5px;
    }
    .ant-upload-list-item.ant-upload-list-item-done {
        display: inline-block;
        padding-right: 15px;
    }

    .antd-img-crop-modal .ant-modal {
        top: 32px;
        .ant-modal-content {
            border-radius: 6px;
            .ant-modal-header {
                border-radius: 6px;
                padding: 8px 24px;
                .ant-modal-title {
                    color: #323538;
                    font-size: 15px;
                    font-family: "Microsoft YaHei";
                }
            }
            .ant-modal-close-x {
                width: 40px !important;
                height: 40px !important;;
                font-size: 14px !important;;
                line-height: 40px !important;;
            }
        }
        .ant-modal-footer {
            padding: 8px 16px;
            .ant-btn {
                font-size: 12px;
                height: 30px;
            }
            .ant-btn-primary {
                background: #08c;
                border-color: #08c;
            }
        }
    }

}
