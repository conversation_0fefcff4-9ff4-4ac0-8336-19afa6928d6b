.formItem {
    padding-left: 8px;
    padding-right: 8px;
    padding-bottom: 16px;
    label {
        float: none !important;
    }
}
.SelectList {
    &-item {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .itemBox {
            margin-bottom: 8px;
            margin-right: 8px;
            // flex: auto;
            border: 1px solid #fff;

            > span {
                margin: 3px;
            }
            svg:not(:root) {
                overflow: hidden;
            }
            svg {
                display: none;
            }
            &.active {
                border: 1px solid #0099dd;
                position: relative;

                svg {
                    display: block;
                    position: absolute;
                    right: -1px;
                    bottom: -1px;
                }
            }
        }
    }
}
