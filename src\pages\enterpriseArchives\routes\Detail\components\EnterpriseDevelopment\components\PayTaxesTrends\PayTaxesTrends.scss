.payTaxesTrends {
    :global {
        .panel .panel-head {
            padding-left: 0;
        }
    }

    .body {
        display: flex;
        padding: 24px 0;
        align-items: center;
    }

    .echartWrap {
        height: 300px;
        padding-right: 32px;
        width: 100%;
    }

    .tableWrap {
        flex: 1;

        .text {
            margin-bottom: 8px;
        }
    }

    .singleRowTable {
        border: 1px solid #d2e4ff;
        width: 100%;
        table-layout: fixed;

        :global {

            thead th,
            tbody td {
                padding: 8px 16px 8px 16px;
                text-align: center;
                border-right: 1px solid #d2e4ff;
            }

            thead th {
                background-color: #f2f8ff;
                border-bottom: 1px solid #d2e4ff;
            }

            tbody td {
                border-bottom: 1px solid #d2e4ff;
            }
        }
    }
}
