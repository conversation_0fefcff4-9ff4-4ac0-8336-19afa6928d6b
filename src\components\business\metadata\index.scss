/*!
 * @(#) index.scss
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-11-01 10:40:21
 */

.detailBody{
    :global{
        th{
            width: 375px!important;
        }
        label{
            width: 100%!important;
        }
        .item-sm.item .sub-item .textShow {
            padding-top: 8px;
            padding-bottom: 8px;
            line-height: 18px;
        }
        .form-wrap .item .labelItem {
            padding-top: 8px;
            padding-bottom: 8px;
        }
        .item-sm.item .labelItem:after {
            line-height: 11px;
        }
    }
}

.verticalAlign{
    :global{
        th{
            vertical-align: middle !important;
        }
    }
}

:global{
    .ant-popover{
        max-width: 800px;
    }

    // jiangz 高度变高时，居中会导致样式变形
    // .form-wrap .form-table>tbody>tr>td{
    //     vertical-align: middle!important;
    // }
}

