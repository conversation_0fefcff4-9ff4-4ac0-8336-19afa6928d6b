import React, { Component } from 'react';
import { download } from '@/components/common/common/FileUtil';

class FileDownloadButton extends Component {
    downField = () => {
        const { addResourceRoot, url, name } = this.props;
        const param = { url, name };

        if (!addResourceRoot) {
            param.resourceRoot = '';
        }
        download(param);
    };

    render() {
        const { addResourceRoot, url, name, children, ...restProps } = this.props;

        return (
            <span {...restProps} onClick={this.downField}>
                {children}
            </span>
        );
    }
}

export default FileDownloadButton;
