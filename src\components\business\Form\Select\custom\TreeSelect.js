import React, { Component } from 'react';
// style
import classNames from 'classnames';
// 工具
import * as ArrayUtil from '@/components/common/common/ArrayUtil';
import * as TreeUtil from '@/components/common/common/TreeUtil';
// 组件
import MultiClamp from '@/components/ui/MultiClamp/index';
import { CommonAntdTree as Tree } from '@/components/ui/Tree';
import { Icon } from '@share/shareui';
import styles from '../style/TreeSelect.scss';

function coverToArray(data) {
    if (Array.isArray(data)) {
        return data;
    }
    if (typeof data === 'string' && data !== '') {
        return data.split(',');
    }

    return [];
}

class TreeSelect extends Component {
    static defaultProps = {
        value: [], // 选中值
        treeData: [], // 选项
        valueKey: 'value', // 选中属性值
        // disabled: false,         // 是否禁用
        multi: false, // 是否多选
        acceptString: false, // 多选时，返回值是否为逗号隔开的字符串
        placeholder: '', // 空值提示语
        clearable: true, // 是否显示删除按钮
        searchAble: true, // 是否有搜索框
        searchPlaceholder: true, // 搜索框提示语
        controlAble: true, // 是否有控制按钮
        itemControlAble: false, // 子节点是否有控制按钮
    };

    constructor(props) {
        super(props);
        this.randomValue = Math.random();
        this.state = {
            isDropDown: false, // 是否显示下拉框
            selectedFilter: false, // 已选过滤
            inputFilterValue: '', // 搜索输入框的值
            expandedKeys: [],
            autoExpandParent: true,
            filterTreeData: [...this.props.treeData], // 过滤的树的数据
        };
    }

    componentDidMount() {
        document.addEventListener('click', this.globalClick, true);
    }

    componentWillUnmount() {
        document.removeEventListener('click', this.globalClick);
    }

    // 值变更事件
    onChange = (valueArray, eventKey, checked) => {
        const { multi, acceptString, onChange } = this.props;
        let valueResult = valueArray;

        // 判断出参类型
        if (!multi || acceptString) {
            valueResult = valueResult.join(',');
        }
        onChange && onChange({ target: { value: valueResult, eventKey, checked } });
    };

    // 扩展按钮
    onExtend = (item) => {
        if (!ArrayUtil.notEmptyArr(item.children)) {
            return '';
        }

        return [
            <Icon title="全选" className="si si-com_appsquare" onClick={() => this.handleControlTypeSelect([item], 'all')} />,
            <Icon title="反选" className="si si-jy_jfrx1" onClick={() => this.handleControlTypeSelect([item], 'invert')} />,
            <Icon title="清空" className="si si-com_appsquareo" onClick={() => this.handleControlTypeSelect([item], 'none')} />,
        ];
    };

    onExpand = (expandedKeys) => {
        this.setState({
            expandedKeys,
            autoExpandParent: false,
        });
    };

    getParentKey = (key, tree) => {
        let parentKey;

        for (let i = 0; i < tree.length; i++) {
            const node = tree[i];

            if (node.children) {
                if (node.children.some((item) => item.key === key)) {
                    parentKey = node.key;
                } else if (this.getParentKey(key, node.children)) {
                    parentKey = this.getParentKey(key, node.children);
                }
            }
        }

        return parentKey;
    };

    convertMenuNames = (sourceData, inputValue) => {
        if (!Array.isArray(sourceData) || sourceData.length === 0) {
            return [];
        }

        return sourceData.filter((menu) => {
            menu.children = menu.children ? this.convertMenuNames(menu.children, inputValue) : null;

            // Array.isArray(menu.children) && menu.children.length > 0 && this.convertMenuNames(menu.children, inputValue);
            // if (menu.title.includes(inputValue)) {
            //     menu.showTitle = this.convertShowName(menu.title, inputValue);
            // }
            return menu.title.includes(inputValue) || (Array.isArray(menu.children) && menu.children.length > 0);
        });
    };

    generateList = (data) => {
        if (!data || data.length === 0) {
            return [];
        }
        const dataList = [];
        const iterator = (d) => {
            for (let i = 0; i < d.length; i++) {
                const node = d[i];
                const { key, title } = node;

                dataList.push({ key, title });
                if (node.children) {
                    iterator(node.children);
                }
            }
        };

        iterator(data);

        return dataList;
    };

    // 页面点击事件
    globalClick = (e) => {
        const { isDropDown } = this.state;

        if (isDropDown) {
            // 是否点击自身
            const clickSelf = $(e.target)  //eslint-disable-line
                    .parents('.multiSelectCustomDropDown').length > 0;

            if (clickSelf) {
                return;
            }
            this.setState({ isDropDown: false });
        }
    };

    // 改变下拉框状态
    changeDropDown = () => {
        const { isDropDown } = this.state;
        const { disabled } = this.props;

        if (disabled) {
            return;
        }
        this.setState({
            isDropDown: !isDropDown,
        });
    };

    // 清空下拉
    handleClearSelect = () => {
        const { disabled } = this.props;

        if (disabled) {
            return;
        }
        this.onChange([]);
    };

    // 操作栏全选、清空、反选、按钮点击事件
    handleControlTypeSelect = (treeData, type) => {
        const { value: keys, valueKey } = this.props;
        let valueArray = coverToArray(keys);
        const operateValue = TreeUtil.treeToArray(treeData).map((item) => item[valueKey]);
        const repeatValue = operateValue.filter((value) => valueArray.includes(value));

        switch (type) {
            case 'all':
                valueArray = [...valueArray, ...operateValue];
                break;
            case 'none':
                valueArray = valueArray.filter((value) => !operateValue.includes(value));
                break;
            case 'invert':
                valueArray = [...valueArray, ...operateValue].filter((value) => !repeatValue.includes(value));
                break;
            default:
                break;
        }
        this.onChange(ArrayUtil.distinct(valueArray), type, true);
    };

    // 单选下拉选择项被选中事件
    handleSingleOptionSelect = (
        checkedKeys,
        {
            checked,
            node: {
                props: { eventKey },
            },
        }
    ) => {
        const { value: keys } = this.props;
        let valueArray = coverToArray(keys);

        if (checked) {
            valueArray = [eventKey];
        } else {
            valueArray = [];
        }
        this.onChange(valueArray, eventKey, checked);
    };

    // 多选下拉选择项被选中事件
    handleMultiOptionSelect = (
        checkedKeys,
        {
            checked,
            node: {
                props: { eventKey },
            },
        }
    ) => {
        const { value: keys, valueKey } = this.props;
        const checkedKeyArray = Array.isArray(checkedKeys)
            ? checkedKeys
            : Object.values(checkedKeys).reduce((result, item) => [...result, ...item], []);
        let valueArray = coverToArray(keys);

        if (checked) {
            valueArray = ArrayUtil.distinct([...valueArray, ...checkedKeyArray]);
        } else {
            const filterTreeData = this.filterTreeData();
            const allowSelectValue = TreeUtil.treeToArray(filterTreeData).map((item) => item[valueKey]);
            const outValue = valueArray.filter((value) => !allowSelectValue.includes(value));

            valueArray = [...outValue, ...checkedKeyArray];
        }
        this.onChange(valueArray, eventKey, checked);
    };

    // 过滤树节点
    filterTreeData = () => {
        function filterNodeByKeys(nodeList, checkKeys) {
            if (!ArrayUtil.notEmptyArr(nodeList)) {
                return [];
            }

            return nodeList
                .map((node) => {
                    const showNode = { ...node };

                    showNode.children = filterNodeByKeys(node.children, checkKeys);

                    return checkKeys.includes(node.key) || ArrayUtil.notEmptyArr(showNode.children) ? showNode : null;
                })
                .filter((node) => node);
        }
        function generateShowTitle(title, matchStr) {
            const index = title.indexOf(matchStr);
            const beforeStr = title.substr(0, index);
            const afterStr = title.substr(index + matchStr.length);

            return matchStr && index > -1 ? (
                <span>
                    {beforeStr}
                    <span style={{ color: 'red' }}>{matchStr}</span>
                    {afterStr}
                </span>
            ) : (
                <span>{title}</span>
            );
        }
        function filterNodeByMatchStr(nodeList, matchStr, parentMatch) {
            if (!ArrayUtil.notEmptyArr(nodeList)) {
                return [];
            }
            if (matchStr.trim().length === 0) {
                return nodeList;
            }

            return nodeList
                .map((node) => {
                    const showNode = { ...node };

                    showNode.match = showNode.title.includes(matchStr.trim());
                    if (showNode.match) {
                        showNode.showTitle = generateShowTitle(showNode.title, matchStr);
                    }
                    showNode.children = filterNodeByMatchStr(node.children, matchStr, showNode.match);

                    return parentMatch || showNode.match || ArrayUtil.notEmptyArr(showNode.children) ? showNode : null;
                })
                .filter((node) => node);
        }
        function lightByMatchStr(nodeList, matchStr) {
            return nodeList.map((item) => {
                const index = item.title.indexOf(matchStr);
                const beforeStr = item.title.substr(0, index);
                const afterStr = item.title.substr(index + matchStr.length);
                const title =
                    index > -1 ? (
                        <span title={item.title}>
                            {beforeStr}
                            <span style={{ color: '#f50' }}>{matchStr}</span>
                            {afterStr}
                        </span>
                    ) : (
                        <span title={item.title}>{item.title}</span>
                    );

                item.showTitle = title;

                if (item.children) {
                    lightByMatchStr(item.children, matchStr);
                }

                return item;
            });
        }
        const { value: keys, treeData, searchAble } = this.props;
        const { selectedFilter, inputFilterValue } = this.state;
        let showTreeData = treeData;

        if (selectedFilter) {
            const valueArray = coverToArray(keys);

            showTreeData = filterNodeByKeys(showTreeData, valueArray);
        }
        if (searchAble && inputFilterValue.trim().length > 0) {
            showTreeData = filterNodeByMatchStr(showTreeData, inputFilterValue.trim());
        }

        return lightByMatchStr(showTreeData, inputFilterValue.trim());
    };

    // 过滤已选选项（包含关联的父节点）
    filterSelectedOption = (filterOptions, valueArray) => {
        const { valueKey, checkStrictly } = this.props;

        if (checkStrictly) {
            return filterOptions.filter((v) => valueArray.includes(v[valueKey]));
        }

        return filterOptions.filter(
            (v) =>
                valueArray.includes(v[valueKey]) ||
                (v.children && v.children.length > 0 && this.filterSelectedOption(v.children, valueArray).length === v.children.length)
        );
    };

    render() {
        const {
            value: keys,
            treeData,
            valueKey,
            options,
            onChange,
            disabled,
            multi,
            placeholder,
            clearable,
            searchAble,
            searchPlaceholder,
            controlAble,
            itemControlAble,
            className,
            extendHead,
            extendTail,
            ...restProps
        } = this.props;
        const { isDropDown, selectedFilter, inputFilterValue, autoExpandParent, expandedKeys } = this.state;
        // 处理被选中值成数组
        const valueArray = coverToArray(keys);
        // 获取被选中项
        const selectedNodes = TreeUtil.findNode(treeData, (item) => valueArray.includes(item[valueKey]));
        const existSelectValue = ArrayUtil.notEmptyArr(selectedNodes);
        const selectLabel = existSelectValue ? selectedNodes.map((item) => item.title).join('、') : '';
        const filterTreeData = this.filterTreeData();
        const filterOptions = TreeUtil.treeToArray(filterTreeData);
        const selectedFilterOptions = this.filterSelectedOption(filterOptions, valueArray);

        return (
            <div
                className={classNames('Select Select--single w100 textAlignLeft is-clearable', {
                    'is-focused': isDropDown,
                    'is-open': isDropDown,
                    'is-disabled': disabled,
                    'has-value': existSelectValue,
                    [className]: className,
                    [styles.selectBox]: true,
                })}
                {...restProps}
            >
                <div className="Select-control">
                    {existSelectValue ? (
                        <div className="Select-multi-value-wrapper" onClick={this.changeDropDown}>
                            <div className="Select-value">
                                <span className="Select-value-label" title={selectLabel} key={selectLabel}>
                                    {selectLabel}
                                </span>
                            </div>
                        </div>
                    ) : (
                        <div className="Select-placeholder" onClick={this.changeDropDown}>
                            <span>{placeholder}</span>
                        </div>
                    )}
                    {existSelectValue && (
                        <span aria-label="清空值" className="Select-clear-zone" title="清空值" onClick={this.handleClearSelect}>
                            <span className="Select-clear">×</span>
                        </span>
                    )}
                    <span className="Select-arrow-zone" onClick={this.changeDropDown}>
                        <span className="Select-arrow" />
                    </span>
                </div>
                {isDropDown && (
                    <div className={`multiSelectCustomDropDown ${styles.dorpDownArea}`}>
                        {extendHead}
                        {searchAble && (
                            <div className={styles.searchArea} style={{ padding: 0 }}>
                                <div className={styles.input} style={{ paddingRight: 0 }}>
                                    <div id="specialSelectInput">
                                        <input
                                            type="checkbox"
                                            checked={selectedFilter}
                                            onClick={() => this.setState({ selectedFilter: !selectedFilter })}
                                            className={styles.customSelectCheck}
                                            title="过滤已选"
                                        />
                                    </div>
                                    <div id="specialSelectInput2">
                                        <input
                                            type="text"
                                            value={inputFilterValue}
                                            onChange={(e) => {
                                                const val = e.target.value;

                                                this.setState(
                                                    {
                                                        inputFilterValue: val,
                                                    },
                                                    () => {
                                                        if (val) {
                                                            const arr = [];
                                                            const expandIterator = (data) => {
                                                                if (data.children && data.children.length > 0) {
                                                                    if (data.children.some((v) => v.title.indexOf(val) !== -1)) {
                                                                        data.children.forEach((a) => {
                                                                            expandIterator(a);
                                                                        });
                                                                        arr.push(data.key);
                                                                    }
                                                                }
                                                            };

                                                            this.filterTreeData().forEach((v) => expandIterator(v));
                                                            this.setState({ expandedKeys: arr });
                                                        } else {
                                                            this.setState({ expandedKeys: [] });
                                                        }
                                                    }
                                                );
                                            }}
                                            placeholder={searchPlaceholder || '请输入内容'}
                                        />
                                    </div>
                                    <div className={styles.canleBtn2} onClick={() => this.setState({ inputFilterValue: '' })}>
                                        <i className="si si-com_closethin" />
                                    </div>
                                </div>
                            </div>
                        )}
                        {multi && controlAble && (
                            <div className={styles.controlArea}>
                                <label>
                                    <input
                                        type="radio"
                                        name="type"
                                        checked={selectedFilterOptions.length === filterOptions.length}
                                        disabled={selectedFilterOptions.length === filterOptions.length}
                                        onClick={() => this.handleControlTypeSelect(filterTreeData, 'all')}
                                    />
                                    全选
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="type"
                                        checked={selectedFilterOptions.length === 0}
                                        disabled={selectedFilterOptions.length === 0}
                                        onClick={() => this.handleControlTypeSelect(filterTreeData, 'none')}
                                    />
                                    清空
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="invert"
                                        checked={false}
                                        disabled={filterOptions.length === 0}
                                        onClick={() => this.handleControlTypeSelect(filterTreeData, 'invert')}
                                    />
                                    反选
                                </label>
                            </div>
                        )}
                        <div className={styles.optionsArea} style={{ marginTop: searchAble || controlAble ? '8px' : '0px' }}>
                            {filterOptions && filterOptions.length !== 0 ? (
                                <Tree
                                    showIcon={false}
                                    checkable
                                    checkedKeys={valueArray}
                                    onCheck={multi ? this.handleMultiOptionSelect : this.handleSingleOptionSelect}
                                    extend={itemControlAble ? this.onExtend : null}
                                    onExpand={this.onExpand}
                                    expandedKeys={expandedKeys}
                                    autoExpandParent={autoExpandParent}
                                    {...restProps}
                                    treeData={filterTreeData}
                                />
                            ) : (
                                <div className={styles.option}>查询不到结果</div>
                            )}
                        </div>
                        {extendTail}
                    </div>
                )}
            </div>
        );
    }
}

export default TreeSelect;
