import React, { useEffect, useState } from 'react';
import { Col, Row, Tabs } from 'antd';
import styles from './StockTree.scss';
import RightStructureUp from './RightStructureUp';
import RightStructureDown from './RightStructureDown';

const RightStructure = (props) => {
    // 点击按钮切换不同数据
    const handleChangeTree = (flag) => {
        // console.log(flag)
    };

    const { treeData } = props;

    return (
        <div>
            <Tabs className={`m-tabs-qyzs-detail ${styles.structureTab}`} defaultActiveKey="up" onChange={handleChangeTree}>
                <Tabs.TabPane tab={<p style={{ fontSize: '16px' }}>股东持股</p>} key="up" forceRender>
                    <RightStructureUp treeData={treeData} />
                </Tabs.TabPane>
                <Tabs.TabPane tab={<p style={{ fontSize: '16px' }}>对外投资</p>} key="down" forceRender>
                    <RightStructureDown treeData={treeData} />
                </Tabs.TabPane>
            </Tabs>
        </div>
    );
};

export default RightStructure;
