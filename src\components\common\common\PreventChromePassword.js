$(function () {
    $(document).on('click', 'input[type=password]', function (e) {
        // 为什么先失去焦点，在获取焦点，这样子可以避免第二次或更多次连续点击输入框时，出现的用户密码清单的框可以快速去除
        $(this).blur();
        $(this).focus();
    });
    $(document).on('focus', 'input[type=password]', function (e) {
        // 使用setTimeout 包住，告诉JS是异步执行，这样子，就可以阻止第一次点击获取焦点时，下拉用户密码清单的框的出现
        const $this = $(this);
        setTimeout(function () {
            $this.removeAttr('readonly');
            // 获取焦点时 同时去除只读，这样可以获取光标，进行输入
        }, 1);
    });
    $(document).on('blur', 'input[type=password]', function (e) {
        // 失去焦点立马更新为只读
        $(this).attr('readonly', 'readonly');
    });
    // 监听键盘输入事件
    // 当keyCode=8(backspace键) 和 keyCode=46(delete键)按下的时候，判断只剩下最后一个字符的时候阻止按键默认事件，自己清空输入框
    // 当keyCode=8(backspace键) 和 keyCode=46(delete键)按下的时候，判断如果处于全选状态，就阻止按键默认事件，自己清空输入框
    // 这种用来避免删除最后一个字符完后出现下拉用户密码清单的框
    $(document).on('keydown', 'input[type=password]', function (e) {
        const { keyCode } = e;
        if (keyCode === 8 || keyCode === 46) {
            // backspace 和delete
            const len = $(this).val().length;
            if (len === 1) {
                $(this).val('');

                return false;
            }
            if (e.target.selectionStart === 0 && e.target.selectionEnd === len) {
                $(this).val('');

                return false;
            }
        }

        return true;
    });

    $(document).on('mousedown', 'input[type=password]', function (e) {
        if (e.type === 'mousedown') {
            $(this).blur();
            $(this).focus();
        }
    });
});
