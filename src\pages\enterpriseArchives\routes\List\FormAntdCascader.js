import React, { useState } from 'react';

import { FormItem } from '@share/shareui-form';

import { Cascader } from 'antd';
import { useMount } from 'ahooks';
import { RegionNoUtil } from '@share/common';

const FormAntdCascader = ({
    servicesName,
    field,
    label,
    noView = false,
    onChange,
    multiple = false,
    // fieldNames = {
    //     label: 'name',
    //     value: 'code',
    //     children: 'children',
    // },
    parentId = '',
    ...otherProps
}) => {
    // const services = useService(Service);

    const [options, setOptions] = useState([]);

    const getList = async (id) => {
        try {
            const res = await servicesName(id || '');
            const data = (res || []).map((item) => {
                return {
                    ...item,
                    label: item.name,
                    value: item.code,
                    isLeaf: false,
                };
            });

            return data;
        } catch (error) {
            throw Error(error);
        }
    };

    const loadData = async (selectedOptions) => {
        const targetOption = selectedOptions[selectedOptions.length - 1];
        targetOption.loading = true;

        const res = await getList(targetOption?.code);
        targetOption.loading = false;
        const targetCode = RegionNoUtil.toSimple(targetOption?.code);
        targetOption.isLeaf = targetCode?.length === 9;
        targetOption.children = (res || []).map((item) => {
            return {
                ...item,

                isLeaf: RegionNoUtil.toSimple(item?.code)?.length === 12,
            };
        });

        setOptions([...options]);
    };

    useMount(() => {
        const getData = async () => {
            const res = await getList(parentId);
            setOptions(res);
        };
        getData();
    });

    return (
        <FormItem field={field} label={label} noView={noView} {...otherProps}>
            {(fieldProps) => {
                return (
                    <div id={`${field}Area`}>
                        <Cascader
                            // {...otherProps}
                            {...fieldProps}
                            popupClassName="sss"
                            notFoundContent="暂无数据"
                            getPopupContainer={() => document.getElementById(`${field}Area`)}
                            options={options}
                            loadData={(selectedOptions) => {
                                loadData(selectedOptions);
                            }}
                            onChange={(value) => {
                                fieldProps.onChange({ target: { value } });
                            }}
                            changeOnSelect
                            placeholder={`请选择${label}`}
                        />
                    </div>
                );
            }}
        </FormItem>
    );
};

export default FormAntdCascader;
