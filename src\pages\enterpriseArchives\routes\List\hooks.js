/*
 *@(#) hooks.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-09
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import { useList } from '@share/list';
import { useCallback, useEffect } from 'react';
import EnterpriseService from '@/services/EnterpriseService';
import { useQuery, useService, useCodeMapping } from '@share/framework';

import { useKeyPress, useMount, useSetState, useUpdateEffect } from 'ahooks';
import { openTab } from '@/utils';

export const useCodeMap = () => {
    // todo
    const [RYGMLXDM, BUSINESS_STATUS, ECONOMY_TYPE, INDUSTRY_CATEGORIES, GJDM, DM_QY_DJJG] = useCodeMapping(
        'RYGMLXDM',
        'BUSINESS_STATUS',
        'ECONOMY_TYPE',
        'INDUSTRY_CATEGORIES',
        'GJDM',
        'DM_QY_DJJG'
    );
    const [state, setState] = useSetState({
        parkList: [],
    });

    // useEffect(() => {
    // }, [getOptions]);

    return {
        RYGMLXDM,
        BUSINESS_STATUS,
        ECONOMY_TYPE,
        INDUSTRY_CATEGORIES,
        GJDM,
        DM_QY_DJJG,
        ...state,
    };
};
export const useLogic = () => {
    const { tzrzjhm, qyzylxrzjhm, ZCDZ, JYDZ } = useQuery();
    const services = useService(EnterpriseService);

    const [state, setState] = useSetState({
        isFocusList: false,
        showAdvancedQuery: false,
        keyWordParam: '',
        searchVal: '',
        orderBys: [
            // {
            //     sortField: '',
            //     sortType: 'desc',
            // },
        ],
        tagList: [],
        params: {},
    });

    const listState = useList({
        dataSource: (body) => {
            return services.searchBasicList({
                data: {
                    qymc: state.keyWordParam,
                    searchType: state.isFocusList ? '1' : '0',
                    tzrzjhm,
                    qyzylxrzjhm,
                    zcdz: ZCDZ || '',
                    jycsdz: JYDZ || '',
                    ...body.data,
                },
                page: {
                    ...body.page,
                    orderBys: state.orderBys,
                },
            });
        },
        autoLoad: false,
    });
    const handlerQuery = () => {
        if (state.searchVal !== state.keyWordParam) {
            setState({ keyWordParam: state.searchVal });
        }
    };
    const handleAdvancedQuery = (formData) => {
        const { hylxdm, tagIds, zcdzgsxzqhdm, tagIdsParentsId, jgdwdmRightLike, ...other } = formData;
        const $hylxdm = hylxdm?.[(hylxdm?.length || 0) - 1] || '';
        const $zcdzgsxzqhdm = zcdzgsxzqhdm?.[(zcdzgsxzqhdm?.length || 0) - 1] || '';
        const $tagIds = [...(tagIds || [])];
        const $jgdwdmRightLike = jgdwdmRightLike?.[jgdwdmRightLike.length - 1] || '';
        const params = {
            ...other,
            hylxdm: $hylxdm,
            zcdzgsxzqhdm: $zcdzgsxzqhdm,
            tagIds: $tagIds,
            jgdwdmRightLike: $jgdwdmRightLike,
            qymc: '', // 高级查询的时候，要重置掉外层输入框的输入值
        };
        setState({ searchVal: '' });
        listState.query(params);
        setState({
            params,
            showAdvancedQuery: false,
        });
    };
    const handlerChangeOrderBys = (field, defaultOrder) => {
        const { sortField, sortType } = state.orderBys[0] || {};

        if (field === sortField) {
            setState({
                orderBys: [
                    {
                        sortField: field,
                        sortType: sortType === 'desc' ? 'asc' : 'desc',
                    },
                ],
            });
        } else {
            setState({
                orderBys: [
                    {
                        sortField: field,
                        sortType: defaultOrder,
                    },
                ],
            });
        }
    };
    const renderOrderBtn = (field) => {
        const { sortField, sortType } = state.orderBys[0] || {};

        return {
            isActive: field === sortField,
            // eslint-disable-next-line no-nested-ternary
            btnText: field === sortField ? (sortType === 'desc' ? '降序' : '升序') : '排序',
        };
    };
    const goDetail = (id) => {
        console.info('jump', `${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/detail?id=${id}`);
        openTab({
            url: `${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/detail?id=${id}`,
            appId: id,
            label: '企业档案',
        });
    };

    const handleShowAdvancedQuery = () => {
        setState({
            showAdvancedQuery: !state.showAdvancedQuery,
        });
    };
    const setIsFocusList = () => {
        setState({
            isFocusList: !state.isFocusList,
        });
    };
    const setSearchVal = (v) => {
        setState({
            searchVal: v,
        });
    };

    const getData = async () => {
        const tagList = [];
        setState({
            tagList,
        });
    };

    useKeyPress(
        'enter',
        () => {
            handlerQuery();
        },
        {
            target: document.getElementById('keyword'),
        }
    );
    useMount(() => {
        getData();
    });

    useUpdateEffect(() => {
        console.log('sss');
        listState.query();
    }, [state.keyWordParam, state.orderBys, state.isFocusList]);

    return {
        ...state,
        listState,
        // searchVal,
        setSearchVal,
        handlerQuery,
        handlerChangeOrderBys,
        renderOrderBtn,
        // isFocusList,
        setIsFocusList,
        goDetail,
        // showAdvancedQuery,
        handleShowAdvancedQuery,
        handleAdvancedQuery,
        setState,
    };
};
