@charset "utf-8";

$lineHeight: 30px;

.selectBox {
    .dorpDownArea {
        position: absolute;
        z-index: 9;
        width: 100%;
        margin-top: -1px;
        border: 1px solid #ccc;
        background: #ebeced;

        >div {
            background: #fff;

            &:hover {
                background: #e0f6ff;
            }
        }

        .searchArea {
            position: relative;
            width: 100%;
            padding: 5px;

            .input {
                width: 100%;
                padding-right: 45px;

                input {
                    width: 100% !important;
                    height: $lineHeight;
                    padding: 0 15px 0 5px;
                    border: 1px solid #ccc;
                    outline: none;
                }

                .canleBtn {
                    position: absolute;
                    right: 55px;
                    top: 9px;
                    z-index: 8;
                    cursor: pointer;
                }
            }

            .searchBtn {
                position: absolute;
                right: 5px;
                top: 5px;
                padding: 0 5px;
                height: $lineHeight;
                line-height: $lineHeight - 1;
                background: #09d;
                border-color: #09d;
                color: #fff;
            }
        }

        .controlArea {
            padding: 5px;

            label {
                width: 32%;

                // @media (max-width: 1400px){
                //     width: 40%;
                // }

                margin: 0 2% 0 0;
                font-weight: normal;

                &:last-child {
                    margin-right: 0;
                }

                input {
                    margin: 0 2px 0 0;
                    width: 15px !important;
                    height: 15px !important;
                    -webkit-appearance: none; // 重置iphone的默认样式
                    position: relative;
                    border: 1px solid #ccc;
                    border-radius: 50%;
                    outline: none;
                    vertical-align: sub;

                    &:after {
                        content: ""; // 空白内容占位，当做盒模型处理，见下面
                        position: absolute;
                        top: 2px;
                        left: 2px;
                        width: 9px; // 勾的短边
                        height: 9px; // 勾的长边
                        border-radius: 50%;
                        background: transparent;
                    }

                    &:checked {
                        &:after {
                            background: #09d;
                        }
                    }
                }
            }
        }

        .optionsArea {
            max-height: 280px;
            overflow-y: auto;

            &::-webkit-scrollbar {
                width: 8px;
                height: 4px;
                background-color: #f5f5f5;
            }

            &::-webkit-scrollbar-track {
                background: #f1f1f1;
            }

            &::-webkit-scrollbar-thumb {
                background-color: #c1c1c1;
            }

            &:hover {
                background: #fff;
            }

            .option {
                height: $lineHeight;
                padding: 0 10px;
                border-bottom: 1px solid #ccc;
                cursor: pointer;
                line-height: $lineHeight - 1;

                input {
                    margin: 6px 5px 6px 0;
                    width: 15px !important;
                    height: 15px !important;
                    -webkit-appearance: none; // 重置iphone的默认样式
                    position: relative;
                    border: 1px solid #ccc;
                    border-radius: 2px;
                    vertical-align: middle;
                    outline: none;

                    &:after {
                        content: ""; // 空白内容占位，当做盒模型处理，见下面
                        position: absolute;
                        top: 0;
                        left: 4px;
                        width: 6px; // 勾的短边
                        height: 10px; // 勾的长边
                        border: solid transparent; // 勾的颜色
                        border-width: 0 2px 2px 0; // 勾的宽度
                        transform: rotate(45deg); // 定制宽高加上旋转可以伪装内部的白色勾
                    }

                    &:checked {
                        &:after {
                            border-color: #09d;
                        }
                    }
                }

                &:hover {
                    background: #e0f6ff;
                }

                &:last-child {
                    border-bottom: none;
                }

                >div {
                    width: calc(100% - 20px);
                }
            }
        }
    }
}


.canleBtn {
    position: absolute;
    right: 26px !important;
    top: 4px !important;
    z-index: 8 !important;
    cursor: pointer;
}

.canleBtn2 {
    position: absolute;
    right: 10px !important;
    top: 9px !important;
    z-index: 8 !important;
    cursor: pointer;
}

.customSelectCheck {
    margin: 6px 5px 6px 0;
    width: 15px !important;
    height: 15px !important;
    padding: 0 !important;
    -webkit-appearance: none; // 重置iphone的默认样式
    position: relative;
    border: 1px solid #ccc;
    border-radius: 2px;
    vertical-align: middle;
    outline: none;

    &:after {
        content: ""; // 空白内容占位，当做盒模型处理，见下面
        position: absolute;
        top: 0;
        left: 4px;
        width: 6px; // 勾的短边
        height: 10px; // 勾的长边
        border: solid transparent; // 勾的颜色
        border-width: 0 2px 2px 0; // 勾的宽度
        transform: rotate(45deg); // 定制宽高加上旋转可以伪装内部的白色勾
    }

    &:checked {
        &:after {
            border-color: #09d;
        }
    }
}

:global {
    #specialSelectInput {
        display: inline-block;
        width: 10% !important;
        text-align: center;

        input {
            width: 15px !important;
            height: 15px !important;
            margin-top: 0;
        }
    }

    .w100 {
        width: 100%;
    }

    #specialSelectInput2 {
        width: 90% !important;
        display: inline-block;
    }
}