import React, { Component } from 'react';
// 工具类
import * as formRule from '@/components/common/common/formRule';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
import * as ArrayUtil from '@/components/common/common/ArrayUtil';
// 组件
import { Modal, Button } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';

const { Form, Row, Select } = getComponents();

const defaultBody = {
    databaseName: '',
    tableName: '',
};

class DatabaseTableGenCopy extends Component {
    state = {
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
    };

    componentWillReceiveProps(nextProps) {
        if (!this.props.show && nextProps.show) {
            const { editForm } = this.state;

            editForm.setFormData({ ...defaultBody });
            editForm.cleanValidError();
        }
    }

    submit = async () => {
        const { successFn } = this.props;
        const { editForm } = this.state;

        if (FormVaildHelper.isValid(await editForm.valid())) {
            successFn && successFn(editForm.getFormData());
        }
    };

    render() {
        const {
            show,
            cancelFn,
            data: { existTables },
        } = this.props;
        const { editForm } = this.state;
        const data = editForm.getFormData();
        const databaseNameOptions = ArrayUtil.distinct(existTables.map((item) => item.databaseName)).map((item) => ({
            label: item,
            value: item,
        }));
        const tableNameOptions = existTables
            .filter((item) => !data.databaseName || item.databaseName === data.databaseName)
            .map((item) => ({
                label: `${item.databaseName}.${item.tableName}(${item.tableComment || ''})`,
                value: `${item.databaseName}.${item.tableName}`,
            }));

        return (
            <Modal show={show} onHide={cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>字段信息编辑</Modal.Header>
                <Modal.Body full>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <Select
                                label="库名"
                                field="databaseName"
                                options={databaseNameOptions}
                                rule={formRule.checkRequiredNotBlank()}
                                required
                                onChange={() => {
                                    editForm.setFieldValue('tableName', '');
                                }}
                            />
                        </Row>
                        <Row>
                            <Select
                                label="表名"
                                field="databaseNametableName"
                                options={tableNameOptions}
                                rule={formRule.checkRequiredNotBlank()}
                                required
                                onChange={({ target: { value } }) => {
                                    if (value) {
                                        const table = existTables.find((item) => `${item.databaseName}.${item.tableName}` === value) || {};

                                        editForm.setFieldValue('databaseName', table.databaseName || '');
                                        editForm.setFieldValue('tableName', table.tableName || '');
                                    }
                                }}
                            />
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        确认
                    </Button>
                    <Button onClick={cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default DatabaseTableGenCopy;
