import React, { Component } from 'react';
import { TreeSelect } from 'antd';

const bmManager = require('@/components/common/business/manager/BmManager');

const { TreeNode } = TreeSelect;

// 将options转成Antd所需格式
// [{code:'',label:'',children:[]]}] ==> <TreeNode />
function getTreeNode(options) {
    return options.map((item) =>
        item.children ? (
            <TreeNode value={item.key} title={item.title} selectable={false} key={item.key}>
                {getTreeNode(item.children)}
            </TreeNode>
        ) : (
            <TreeNode value={item.key} title={item.title} key={item.key} />
        )
    );
}

class TreeSelectCustom extends Component {
    hanleChange = (value) => {
        const { onChange } = this.props;

        onChange && onChange({ target: { value } });
    };

    render() {
        const { value, options, bmName, ...restProps } = this.props;
        const treeNodeData = bmName ? bmManager.getBmList(bmName).map((item) => ({ key: item.code, title: item.label })) : options;

        return (
            <TreeSelect
                value={value}
                style={{ width: '100%' }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder="请选择"
                allowClear
                treeDefaultExpandAll
                {...restProps}
                onChange={this.hanleChange}
            >
                {getTreeNode(treeNodeData || [])}
            </TreeSelect>
        );
    }
}
export default TreeSelectCustom;
