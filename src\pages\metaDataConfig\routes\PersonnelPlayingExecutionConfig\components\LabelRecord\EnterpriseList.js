import React, { useState } from 'react';
import { Panel, Icon, Button } from '@share/shareui';
import { ShareList, Column, useList } from '@share/list';
import MetaFieldApi from '@/services/MetaFieldApi';
import { useService } from '@share/framework';
import { Input } from 'antd';
import styles from './LabelRecord.scss';

const { Search } = Input;

const EnterpriseList = ({ handleToggleEnterpriseList, queryIds }) => {
    const services = useService(MetaFieldApi);
    const requestData = {
        requestData: {
            ...queryIds,
            qymc: '',
        },
    };
    const listState = useList({
        dataSource: (condition) => {
            return services.getTagPersonList(condition);
        },
        autoLoad: queryIds?.sourceId ? requestData : false,
    });

    const onSearch = (value) => {
        console.log('value', value);
        if (!value) {
            listState.query({
                qymc: '',
                ...queryIds,
            });

            return;
        }
        listState.query({
            qymc: value,
            ...queryIds,
        });
    };

    return (
        <div className={styles.EnterpriseList}>
            <Panel>
                <Panel.Head
                    title="企业列表"
                    extra={
                        <div className={styles['EnterpriseList-right']}>
                            <Search
                                allowClear
                                placeholder="请输入"
                                onSearch={onSearch}
                                style={{
                                    width: 200,
                                }}
                            />
                            <span
                                className={styles.zksq}
                                onClick={() => {
                                    handleToggleEnterpriseList(false);
                                }}
                            >
                                收起
                            </span>
                        </div>
                    }
                />
                <Panel.Body>
                    <ShareList listState={listState} uniqKey="id">
                        <Column field="xm" label="姓名" />
                        <Column field="zjhm" label="身份证号" />
                        <Column field="linkTime" label="打标时间" />
                    </ShareList>
                </Panel.Body>
            </Panel>
        </div>
    );
};
export default EnterpriseList;
