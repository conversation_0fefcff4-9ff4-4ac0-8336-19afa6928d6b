const BaseLog = require('./BaseLog');

class Appender extends BaseLog {
    constructor() {
        super();
    }

    print(args, level) {
        throw new Error('Appender 未实现打印');
    }
}

class ConsoleAppender extends Appender {
    print(args, level) {
        const { name } = level;
        const fn = window.console[name];

        if (typeof fn === 'function') {
            fn.apply(window.console, args);
        }
    }
}

class AlertAppender extends Appender {
    print(args, level) {
        const messageArray = argsFormat(args);

        window.alert(messageArray.join('/r'));
    }
}

window._LOG_MEMORY = [];

class MemoryAppender extends Appender {
    print(args, level) {
        const messageArray = argsFormat(args);

        _LOG_MEMORY.push(messageArray);
    }
}

class DivAppender extends Appender {
    constructor() {
        super();
        this.uuid = '7d6aa03a323a214bc988a290a5f27f82';
    }

    initDiv() {
        const { uuid } = this;
        let container = document.getElementById(uuid);
        const styleAttr = 'background:white;width:100%;height:200px';

        if (container === null) {
            container = document.createElement('div');
            container.id = uuid;
            container.setAttribute('style', styleAttr);
            document.body.appendChild(container);
        }
    }

    print(args, level) {
        this.initDiv();
        const messageArray = argsFormat(args);
        const { color } = level;
        const container = document.getElementById(this.uuid);
        const styleAttr = `color:${color}`;

        messageArray.forEach((msg) => {
            const rowEle = document.createElement('div');

            rowEle.innerText = msg;
            rowEle.setAttribute('style', styleAttr);
            container.appendChild(rowEle);
        });
    }
}

function argsFormat(argsArray) {
    return argsArray.map((it) => {
        if (it instanceof Error) {
            return it.message + it.stack;
        }
        if (typeof it === 'object') {
            return JSON.stringify(it);
        }

        return it;
    });
}

module.exports = {
    Appender,
    ConsoleAppender,
    AlertAppender,
    MemoryAppender,
    DivAppender,
};
