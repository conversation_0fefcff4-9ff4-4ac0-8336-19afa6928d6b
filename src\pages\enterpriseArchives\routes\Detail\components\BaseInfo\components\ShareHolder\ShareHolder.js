/*
 *@(#) ShareHolder.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-10
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import ShareList, { Column, NumberColumn, useList } from '@share/list';
import React from 'react';
import NoData from '@/components/NoData/NoData';
import { fieldTranslate, moneyFormat, timeFormat } from '@/utils/format';
import { useCode } from '@share/framework';
import LinkText from '../../../LinkText/LinkText';

const ShareHolder = ({ data = [] }) => {
    const listState = useList({ dataSource: data, autoLines: true });
    const { TZRLXDM, GJDM } = useCode('TZRLXDM', 'GJDM');

    return (
        <div className="shareListStyleCover">
            <ShareList listState={listState} usePageBar={false} emptyText={<NoData />}>
                <NumberColumn width={60} />
                <Column
                    label="出资股东"
                    field="tzrmc"
                    render={(val, { glqys, tzrzjhm }, rowIndex) => {
                        return (
                            <div>
                                <span style={{ marginRight: '10px' }}>{val}</span>
                                {glqys && Number(glqys) > 0 ? (
                                    <LinkText
                                        type="tab"
                                        tabOption={{ key: `qylb-${tzrzjhm}-${rowIndex}`, label: '企业档案' }}
                                        url={`${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/list?tzrzjhm=${tzrzjhm}`}
                                    >
                                        关联 {glqys} 家企业&gt;
                                    </LinkText>
                                ) : (
                                    ''
                                )}
                            </div>
                        );
                    }}
                />
                <Column label="股东注册国家或地区" field="tzrgb" render={(val) => fieldTranslate(val, GJDM)} />
                <Column label="投资人类型" field="tzrlx" render={(val) => fieldTranslate(val, TZRLXDM)} />
                {/* <Column label="持股比例" field="" /> */}
                <Column label="认缴出资额(万元)" field="rjcze" render={(val) => (val ? `${moneyFormat(val)}万元` : '--')} />
                <Column label="实缴出资额(万元)" field="sjcze" render={(val) => (val ? `${moneyFormat(val)}万元` : '--')} />
                <Column label="出资日期" field="sjczrq" render={(val) => timeFormat(val)} />
            </ShareList>
        </div>
    );
};

export default ShareHolder;
