/*
 * @(#) DataFillingList.js  --- 信用数据填报。需求12988
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-08-05 17:54:58
 */
import React, { Component } from 'react';
// 样式
import styles from '@/pages/metaData/styles/index.scss';
// 服务接口
import * as MetaCategoryApi from '@/services/data/meta/MetaCategoryApi';
import * as DataSaveApi from '@/services/data/data/DataSaveApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
import * as StringUtils from '@/components/common/common/StringUtils';
import { StringUtil } from '@share/common';
// 组件
import MyAlert from '@/components/ui/MyAlert/MyAlert';
import { Button, FormItem, Panel } from '@share/shareui';
import { FrontPageTable as Table } from '@/components/business/Table';
import { FileUploadButton } from '@/components/business/Other';
import { CacheShareForm, getComponents } from '@/components/business/Form';
import PortalMessenger from '@share/portal-messenger';

const { Select } = getComponents('div');
const portalMessenger = new PortalMessenger();

class DataFillingList extends Component {
    constructor(props) {
        super(props);
        this.urlQueryData = StringUtil.fromQueryString(window.location.href.replace(/^.*\?(.*)$/, '$1'));
        const { categoryCodes, categoryCode, CATEGORY_CODE } = this.urlQueryData;

        // 类别范围
        this.categoryCodes = categoryCode
            ? [categoryCode]
            : categoryCodes && categoryCodes.includes(',')
            ? categoryCodes.split(',')
            : categoryCodes;
        // 默认搜索条件
        this.defaultSearchBody = {
            id: categoryCode || CATEGORY_CODE || '',
            dataTableId: '',
        };
        this.state = {
            // 搜索参数
            searchForm: { ...this.defaultSearchBody },
            searchBody: { ...this.defaultSearchBody },
            // 列表参数
            dataList: [],
        };
    }

    componentDidMount = async () => {
        const list = await MetaCategoryApi.allList();
        const dataList = list
            .filter((item) => item.dataTableId)
            .filter((item) => item.status === '1')
            .filter((item) => item.type === '0')
            .filter((item) => !this.categoryCodes || this.categoryCodes.includes(item.id));

        this.setState({ dataList });
    };

    handleSearch = () => {
        const { searchForm } = this.state;
        const body = StringUtils.deleteSpace(searchForm);

        this.setState({ searchBody: { ...body } });
    };

    handleReset = () => {
        this.setState({ searchForm: { ...this.defaultSearchBody } });
    };

    filterList = () => {
        const { searchBody, dataList } = this.state;
        let list = dataList;

        if (searchBody.id) {
            list = list.filter((item) => item.id === searchBody.id);
        }
        if (searchBody.dataTableId) {
            list = list.filter((item) => item.dataTableId === searchBody.dataTableId);
        }

        return list;
    };

    // 批量上报成功接口
    batchSuccess = async (result) => {
        const {
            total = 0,
            success = 0,
            successDataInsert = 0,
            successDataUpdate = 0,
            fail = 0,
            failLevelMap = {},
            failTypeMap = {},
            categoryName,
            categoryCode,
        } = result;
        const principle = failLevelMap[MetaConfigUtils.RULE_CHECK_LEVEL.principle] || 0;
        const suspected = failLevelMap[MetaConfigUtils.RULE_CHECK_LEVEL.suspected] || 0;
        const completeRepeat = failTypeMap.completeRepeat || 0;
        const selfRepeatCheck = failTypeMap.selfRepeatCheck || 0;

        // 获取目录上报表配置
        const tableFieldInfoList = await MetaCategoryApi.metaConfig(categoryCode);
        // 获取主键
        const primaryKeyConfig = MetaConfigUtils.filterPrimaryKeyConfig(tableFieldInfoList);

        MyAlert.successModal(
            <div>
                <div style={{ color: '#0b8' }}>已完成该批数据校验！</div>
                <div style={{ fontSize: '14px', color: '#74767A', textAlign: 'left' }}>
                    情况如下：
                    <ul style={{ paddingLeft: '15px', marginTop: '12px' }}>
                        <li style={{ listStyle: 'decimal', marginBottom: '12px' }}>
                            <span style={{ color: 'red' }}> {success} </span>条数据通过系统校验，
                            {successDataUpdate > 0 && (
                                <span>
                                    因【重复】已被覆盖<span style={{ color: 'red' }}> {successDataUpdate} </span>条数据，
                                </span>
                            )}
                            <span>
                                成功存储<span style={{ color: 'red' }}> {successDataInsert} </span>条数据。
                            </span>
                        </li>
                        <li style={{ listStyle: 'decimal' }}>
                            <span style={{ color: 'red' }}>{fail}</span>条数据未通过系统校验，
                            {selfRepeatCheck > 0 && (
                                <span>
                                    因【<span style={{ color: 'red' }}>本批excel数据重复</span>
                                    】自动表格去重（不留痕迹）<span style={{ color: 'red' }}> {selfRepeatCheck} </span>条数据，
                                </span>
                            )}
                            <span>
                                待修复<span style={{ color: 'red' }}> {principle} </span>条数据。
                            </span>
                        </li>
                    </ul>
                </div>
                {primaryKeyConfig.identityConfig.tip && (
                    <div className={styles.tipText}>
                        温馨提示：
                        <pre>{primaryKeyConfig.identityConfig.tip}</pre>
                    </div>
                )}
            </div>,
            {
                okText: '我要管理',
                okAutoClose: false,
                onOk: () => this.openDataManagePage({ categoryCode, categoryName, filterCurrentDay: true }),
                cancelText: '我要修正',
                cancelAutoClose: false,
                onCancel: () => this.openDataRepairPage({ categoryCode, categoryName, filterCurrentDay: true }),
                closeBtn: true,
                className: styles['modal-pre'],
            }
        );
    };

    openDataManagePage = (param) => {
        const paramStr = Object.entries(param)
            .map(([key, value]) => `${key}=${value}`)
            .join('&');
        const url = `/edp-front/metaData.html#/DataManageList?${paramStr}`;

        portalMessenger.openTab({
            key: `meta-data-status-${param.categoryCode}`,
            label: `数据管理-${param.categoryName}`,
            url,
        });
    };

    openDataRepairPage = (param) => {
        const paramStr = Object.entries(param)
            .map(([key, value]) => `${key}=${value}`)
            .join('&');
        const url = `/edp-front/metaData.html#/DataRepairList?${paramStr}`;

        portalMessenger.openTab({
            key: `meta-data-repair-${param.categoryCode}`,
            label: `质量管理-${param.categoryName}`,
            url,
        });
    };

    render() {
        const { searchForm, dataList } = this.state;
        const { history } = this.props;
        const idOptions = dataList.filter((item) => item.id).map((item) => ({ label: item.name, value: item.id }));
        const dataTableIdOptions = dataList
            .filter((item) => item.dataTableId)
            .map((item) => ({ label: item.dataTableId, value: item.dataTableId }));
        const list = this.filterList();
        const columns = [
            {
                title: '数据类别',
                dataIndex: 'name',
                // width: '20%',
                render: (value) => value,
            },
            {
                title: '数据表名',
                dataIndex: 'dataTableId',
                width: '20%',
                render: (value) => (
                    <a title={value} href="javascript:void(0)" onClick={() => history.push(`/DatabaseTableDetail/${value}/1`)}>
                        {value}
                    </a>
                ),
            },
            {
                title: '操作',
                fixed: 'right',
                width: 310,
                key: 'system',
                render: (rowData) => {
                    const { id, name } = rowData;

                    return (
                        <div className="tableBtn">
                            <a href="javascript:void(0)" onClick={() => this.openDataManagePage({ categoryCode: id, categoryName: name })}>
                                查看数据
                            </a>
                            <a
                                href="javascript:void(0)"
                                onClick={() => {
                                    history.push(`/DataFillingAdd/${id}`);
                                }}
                            >
                                在线填报
                            </a>
                            <a
                                href="javascript:void(0)"
                                onClick={() => {
                                    DataSaveApi.templateDownload(id);
                                }}
                            >
                                下载模板
                            </a>
                            <FileUploadButton
                                url={DataSaveApi.addFromExcel(id)}
                                successFn={this.batchSuccess}
                                fileNameSize={200}
                                loadingMaskPage
                            >
                                <a href="javascript:void(0)">批量导入</a>
                            </FileUploadButton>
                        </div>
                    );
                },
            },
        ];

        return (
            <div>
                <Panel>
                    <Panel.Body full>
                        <CacheShareForm
                            formData={searchForm}
                            onChange={(data, callback) => this.setState({ searchForm: data }, callback)}
                            searchFn={() => this.handleSearch()}
                            namespace="ApplicationCodeManageForm"
                        >
                            <Select
                                label="数据类别"
                                field="id"
                                options={idOptions}
                                labelCol={3}
                                col={10}
                                disabled={this.urlQueryData.categoryCode}
                            />
                            <Select label="表名称" field="dataTableId" options={dataTableIdOptions} labelCol={3} col={10} />
                            <FormItem className="btn-item clearfix pull-right">
                                <Button type="submit" bsStyle="primary" onClick={() => this.handleSearch()}>
                                    查询
                                </Button>
                                <Button type="reset" onClick={this.handleReset}>
                                    重置
                                </Button>
                            </FormItem>
                        </CacheShareForm>
                    </Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Head title="数据填报" />
                    <Panel.Body full>
                        <Table dataSource={list} rowKey="id" columns={columns} />
                    </Panel.Body>
                </Panel>
            </div>
        );
    }
}

export default DataFillingList;
