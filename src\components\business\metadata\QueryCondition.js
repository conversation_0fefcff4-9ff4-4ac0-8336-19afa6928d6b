import React, { Component } from 'react';
// 工具类
import * as StringUtils from '@/components/common/common/StringUtils';
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 组件
import { FormItem, Button } from '@share/shareui';
import { CacheShareForm, getComponents } from '../Form/index';

const shareFormCons = getComponents('div');

class QueryCondition extends Component {
    constructor(props) {
        super(props);
        const { defaultSearchBody, metadataConfigList = [] } = this.props;

        if (defaultSearchBody) {
            this.state = { searchForm: { ...defaultSearchBody }, defaultSearchBody: { ...defaultSearchBody } };
        } else {
            const metaDefaultSearchBody = MetaConfigUtils.getQueryDefaultData(metadataConfigList);

            this.state = { searchForm: { ...metaDefaultSearchBody }, defaultSearchBody: { ...metaDefaultSearchBody } };
            this.handleSearch();
        }
    }

    // 搜索方法
    handleSearch = () => {
        const { formData, searchFn } = this.props;
        const { searchForm } = this.state;

        searchFn && searchFn(StringUtils.deleteSpace(formData || searchForm));
    };

    // 重置方法
    handleReset = () => {
        const { metadataConfigList, onChange } = this.props;
        const { defaultSearchBody } = this.state;
        const searchForm = defaultSearchBody
            ? { ...defaultSearchBody }
            : metadataConfigList.reduce((obj, item) => {
                  obj[item.fieldCode] = '';

                  return obj;
              }, {});

        this.setState({ searchForm }, () => onChange && onChange(searchForm));
    };

    // 构建组件
    buildComponent = (config) => {
        const { componentId, componentParam } = config.queryParam;
        const Con = shareFormCons[componentId];

        if (!Con) {
            return '';
        }
        const extendProps = componentParam
            ? Object.entries(componentParam).reduce((obj, [key, value]) => {
                  if (typeof value !== 'undefined' && value !== null) {
                      obj[key] = value;
                  }

                  return obj;
              }, {})
            : {};
        const comProps = {
            key: config.fieldCode,
            label: config.showLabel,
            field: config.fieldCode,
            ...extendProps,
        };

        // 填充options参数
        if (Array.isArray(config.bmRule) && config.bmRule.length > 0) {
            const targetBmRule = config.bmRule[0] || { ruleParam: {} };

            comProps.options = targetBmRule.ruleParam.bmOptions || [];
        }
        // 长文本添加紧凑样式
        if (config.showLabel.length > 8) {
            comProps.longText = true;
        }

        return <Con {...comProps} />;
    };

    render() {
        const {
            metadataConfigList = [],
            namespace = 'queryConditionNamespace',
            beforeQueryCondition = null /* 扩展前置条件 */,
            afterQueryCondition = null /* 扩展后置条件 */,
            searchFn /* 外部搜索函数 */,
            ...restProps
        } = this.props;
        const { searchForm } = this.state;
        const queryCons = metadataConfigList.map((item) => this.buildComponent(item));

        return (
            <CacheShareForm
                formData={searchForm}
                onChange={(data, callback) => this.setState({ searchForm: data }, callback)}
                namespace={namespace}
                {...restProps}
                searchFn={this.handleSearch}
            >
                {beforeQueryCondition}
                {queryCons}
                {afterQueryCondition}
                {searchFn && (
                    <FormItem className="btn-item clearfix pull-right">
                        <Button type="submit" bsStyle="primary" onClick={this.handleSearch}>
                            查询
                        </Button>
                        <Button type="reset" onClick={this.handleReset}>
                            重置
                        </Button>
                    </FormItem>
                )}
            </CacheShareForm>
        );
    }
}

export default QueryCondition;
