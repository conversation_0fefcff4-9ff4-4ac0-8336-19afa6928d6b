/*
 *@(#) CreditDataFixList.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-23
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React, { Fragment } from 'react';
// 样式
import styles from '@/pages/metaData/styles/creditData.scss';
import noData from '@/assets/images/image-noData.png';
// 服务接口
import * as MetaCategoryApi from '@/services/data/meta/MetaCategoryApi';
// 组件
import DataQuery from '@/components/credit_data/DataQuery';
import DataRepairTable from '@/components/credit_data/List/DataRepairTable';
import { Tabs, Tab, Panel } from '@share/shareui';
import { getComponents } from '@/components/business/Form';

const { RangeTime, CheckboxGroup } = getComponents('div');

const statusOptions = [
    { label: '未处理', value: '0' },
    { label: '已处理', value: '1' },
    { label: '已删除', value: '-1' },
];
let cacheTabActiveKey = '';

class DataRepairList extends DataQuery {
    constructor(props) {
        super(props);
        this.urlQueryData.errorLevels = this.defaultSearchBody.errorLevels || ['0'];
        if (!Array.isArray(this.urlQueryData.errorLevels)) {
            this.urlQueryData.errorLevels = [this.urlQueryData.errorLevels];
        }
        this.urlQueryData.errorStatus = this.defaultSearchBody.errorStatus || ['0', '1', '-1'];
        if (!Array.isArray(this.urlQueryData.errorStatus)) {
            this.urlQueryData.errorStatus = [this.urlQueryData.errorStatus];
        }
        if (this.urlQueryData.tabActiveKey && !cacheTabActiveKey) {
            cacheTabActiveKey = this.urlQueryData.tabActiveKey;
        }
        this.state = {
            ...this.state,
            tabActiveKey: cacheTabActiveKey || this.urlQueryData.errorLevels['0'],
            // 统计值
            tableTotal: {
                0: null,
                1: null,
                2: null,
            },
        };
        this.table = {};
    }

    // 初始化默认搜索条件
    initDefaultSearchBody() {
        return {
            CATEGORY_CODE: '',
            // STATUS: !this.urlQueryData.errorStatus || this.urlQueryData.errorStatus.includes('0') ? ['0'] : [],
            UPDATE_TIME: { start: '', end: '' },
            ERROR_LEVEL: '',
        };
    }

    // 初始化元数据配置Api
    initMetaConfigApi = (categoryCode, objectType) => {
        return MetaCategoryApi.metaConfig(categoryCode, objectType, 'DATA_QS');
    };

    // 刷新列表
    refreshList = () => {
        Object.keys(this.table).forEach((key) => this.table[key].refreshTable());
    };

    handleSearchBody = () => {
        const { searchBody } = this.state;
        const { errorStatus } = this.urlQueryData;

        return {
            ...searchBody,
            // STATUS: searchBody.STATUS.length === 0 ? errorStatus : searchBody.STATUS,
        };
    };

    render() {
        const { history } = this.props;
        const { searchForm, listConfig, metaConfigList, selectedRecords, categoryCodeList, tabActiveKey, tableTotal } = this.state;
        const { CATEGORY_CODE: categoryCode } = searchForm;
        const { errorLevels, errorStatus } = this.urlQueryData;
        const searchBody = this.handleSearchBody();

        return (
            <div>
                <Panel>
                    <Panel.Body full>
                        {this.renderQueryCondition(
                            'CreditDataRepairListForm',
                            <Fragment>
                                {/* <CheckboxGroup */}
                                {/*     label="处理状态" field="STATUS" */}
                                {/*     placeholder="请选择处理状态" */}
                                {/*     options={statusOptions.filter(i => errorStatus.includes(i.value))} */}
                                {/*     col={10} labelCol={3} */}
                                {/* /> */}
                                <RangeTime label="更新时间" field="UPDATE_TIME" col={13} labelCol={3} />
                            </Fragment>
                        )}
                    </Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Body full>
                        {listConfig.length === 0 ? (
                            <div className={styles.noData}>
                                <img src={noData} alt="no data" />
                                <p>请选择信息类别</p>
                            </div>
                        ) : (
                            <Tabs
                                className="tabs_full"
                                bsStyle="tabs"
                                activeKey={tabActiveKey}
                                onSelect={(eventKey) => {
                                    cacheTabActiveKey = eventKey;
                                    this.setState({ tabActiveKey: eventKey, selectedRecords: [] });
                                }}
                            >
                                {errorLevels.includes('0') && (
                                    <Tab
                                        eventKey="0"
                                        title={
                                            <span>
                                                错误数据&ensp;
                                                {tableTotal['0'] !== null ? (
                                                    <span style={{ color: '#0099dd' }}>{tableTotal['0'].toLocaleString()}</span>
                                                ) : (
                                                    '--'
                                                )}
                                            </span>
                                        }
                                    >
                                        <DataRepairTable
                                            history={history}
                                            categoryCode={categoryCode}
                                            categoryCodeList={categoryCodeList}
                                            metaConfigList={metaConfigList}
                                            body={{
                                                ...searchBody,
                                                ERROR_LEVEL: '0',
                                            }}
                                            selectedRecords={selectedRecords}
                                            onSelectedRecordsChange={(records) => this.setState({ selectedRecords: records })}
                                            refreshList={this.refreshList}
                                            // showBatchRepair
                                            // showBatchDelete
                                            namespace="DataRepairList-0"
                                            onRef={(tableRef) => {
                                                this.table['0'] = tableRef;
                                            }}
                                            onTableDataChange={(data) =>
                                                this.setState({
                                                    tableTotal: {
                                                        ...this.state.tableTotal,
                                                        0: data.page.total,
                                                    },
                                                })
                                            }
                                        />
                                    </Tab>
                                )}
                                {errorLevels.includes('1') && (
                                    <Tab
                                        eventKey="1"
                                        title={
                                            <span>
                                                疑问数据&ensp;
                                                {tableTotal['1'] !== null ? (
                                                    <span style={{ color: '#0099dd' }}>{tableTotal['1'].toLocaleString()}</span>
                                                ) : (
                                                    '--'
                                                )}
                                            </span>
                                        }
                                    >
                                        <DataRepairTable
                                            history={history}
                                            categoryCode={categoryCode}
                                            categoryCodeList={categoryCodeList}
                                            metaConfigList={metaConfigList}
                                            body={{
                                                ...searchBody,
                                                ERROR_LEVEL: '1',
                                            }}
                                            selectedRecords={selectedRecords}
                                            onSelectedRecordsChange={(records) => this.setState({ selectedRecords: records })}
                                            refreshList={this.refreshList}
                                            showBatchConfirm
                                            showBatchDelete
                                            namespace="DataRepairList-1"
                                            onRef={(tableRef) => {
                                                this.table['1'] = tableRef;
                                            }}
                                            onTableDataChange={(data) =>
                                                this.setState({
                                                    tableTotal: {
                                                        ...this.state.tableTotal,
                                                        1: data.page.total,
                                                    },
                                                })
                                            }
                                        />
                                    </Tab>
                                )}
                                {errorLevels.includes('2') && (
                                    <Tab
                                        eventKey="2"
                                        title={
                                            <span>
                                                暂缓上报&ensp;
                                                {tableTotal['2'] !== null ? (
                                                    <span style={{ color: '#0099dd' }}>{tableTotal['2'].toLocaleString()}</span>
                                                ) : (
                                                    '--'
                                                )}
                                            </span>
                                        }
                                    >
                                        <DataRepairTable
                                            history={history}
                                            categoryCode={categoryCode}
                                            categoryCodeList={categoryCodeList}
                                            metaConfigList={metaConfigList}
                                            body={{
                                                ...searchBody,
                                                ERROR_LEVEL: '2',
                                            }}
                                            selectedRecords={selectedRecords}
                                            onSelectedRecordsChange={(records) => this.setState({ selectedRecords: records })}
                                            refreshList={this.refreshList}
                                            showBatchDelete
                                            namespace="DataRepairList-2"
                                            onRef={(tableRef) => {
                                                this.table['2'] = tableRef;
                                            }}
                                            onTableDataChange={(data) =>
                                                this.setState({
                                                    tableTotal: {
                                                        ...this.state.tableTotal,
                                                        2: data.page.total,
                                                    },
                                                })
                                            }
                                        />
                                    </Tab>
                                )}
                                {/* <div */}
                                {/*     style={{ fontSize: '12px', color: 'orange', display: 'flex', padding: '0 0 12px 12px' }} */}
                                {/* > */}
                                {/*     <div>温馨提示：</div> */}
                                {/*     <div style={{ color: '#666' }}> */}
                                {/*         <ol style={{ listStyle: 'decimal', paddingLeft: '15px' }}> */}
                                {/*             { */}
                                {/*                 errorLevels.includes('0') && */}
                                {/*                 <li> */}
                                {/*                     “错误数据”列表下的数据表示【<span style={{ color: '#f59a23' }}>存在质量问题</span>】， */}
                                {/*                     需要进行修复的数据，需要经办人手动进行处理，否则影响考核成绩； */}
                                {/*                 </li> */}
                                {/*             } */}
                                {/*             { */}
                                {/*                 errorLevels.includes('1') && */}
                                {/*                 <li> */}
                                {/*                     “疑问数据”列表下的数据表示系统根据填写内容判断可能存在异常的数据， */}
                                {/*                     需要经办人手动确认（有误修改、无误提交即可），否则影响考核成绩； */}
                                {/*                 </li> */}
                                {/*             } */}
                                {/*             { */}
                                {/*                 errorLevels.includes('2') && */}
                                {/*                 <li> */}
                                {/*                     “暂缓上报”列表下的数据一般表示国家代码库不存在对应的代码，需要确认信息是否填写有误，以便准确提交； */}
                                {/*                 </li> */}
                                {/*             } */}
                                {/*         </ol> */}
                                {/*     </div> */}
                                {/* </div> */}
                            </Tabs>
                        )}
                    </Panel.Body>
                </Panel>
            </div>
        );
    }
}

export default DataRepairList;
