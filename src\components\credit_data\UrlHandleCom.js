import { Component } from 'react';
// 工具类
import { StringUtil } from '@share/common';
import * as TimeUtil from '@/components/common/common/TimeUtil';

class UrlHandleCom extends Component {
    constructor(props) {
        super(props);

        // 解析路径参数
        if (window.location.href.includes('?')) {
            this.urlQueryData = StringUtil.fromQueryString(window.location.href.replace(/^.*\?(.*)$/, '$1'));
            const { categoryCodes, categoryCode, filterCurrentDay, ...queryData } = this.urlQueryData;

            Object.keys(queryData)
                .filter((key) => queryData[key] && queryData[key].includes(','))
                .forEach((key) => {
                    queryData[key] = queryData[key].split(',');
                });
            if (filterCurrentDay) {
                const currentDay = TimeUtil.getCurrentDayStr();

                queryData.UPDATE_TIME = {
                    start: `${currentDay} 00:00:00`,
                    end: `${currentDay} 23:59:59`,
                };
            }
            this.defaultSearchBody = { ...this.initDefaultSearchBody(), ...queryData };
            // 初始化值类别
            if (categoryCode) {
                this.defaultSearchBody.CATEGORY_CODE = categoryCode.includes(',') ? categoryCode.split(',') : categoryCode;
            }
        } else {
            this.urlQueryData = {};
            this.defaultSearchBody = { ...this.initDefaultSearchBody() };
        }
    }

    // 初始化默认搜索条件
    initDefaultSearchBody() {
        return {};
    }

    render() {
        return '';
    }
}

export default UrlHandleCom;
