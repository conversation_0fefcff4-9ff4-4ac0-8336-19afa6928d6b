import React, { Children, Fragment } from 'react';
import { listCount } from '@/utils/format';
import Business from './components/Business';
import ShareHolder from './components/ShareHolder';
import MainPerson from './components/MainPerson';
import Invest from './components/Invest';
import Branch from './components/Branch';
import Contact from './components/Contact';
import TagTitle from '../TagTitle';

const BaseInfo = ({ data, formState }) => {
    const {
        shareHolderInfoList = [],
        personnelInfoList = [],
        outwardInvestmentList = [],
        contactInfoList = [],
        branchOfficeList = [],
        tyshxydm,
        qymc,
    } = data;

    return (
        <Fragment>
            <TagTitle title="工商信息" id="business" />
            <Business formState={formState} />
            <TagTitle
                title={
                    <Fragment>
                        股东信息 <span> {listCount(shareHolderInfoList)}</span>
                    </Fragment>
                }
                id="shareholder"
            />

            <ShareHolder data={shareHolderInfoList} />
            {/* <div className={s.tagTitle} id="mainPerson">
                主要人员<span>{listCount(personnelInfoList)}</span>
            </div> */}
            <TagTitle
                title={
                    <Fragment>
                        主要人员<span>{listCount(personnelInfoList)}</span>
                    </Fragment>
                }
                id="mainPerson"
            />
            <MainPerson data={personnelInfoList} />
            {/* <div className={s.tagTitle} id="invest">
                对外投资<span>{listCount(outwardInvestmentList)}</span>
            </div> */}
            <TagTitle
                title={
                    <Fragment>
                        对外投资<span>{listCount(outwardInvestmentList)}</span>
                    </Fragment>
                }
                id="invest"
            />
            <Invest data={outwardInvestmentList} />
            {/* <div className={s.tagTitle} id="branch">
                分支机构<span>{listCount(branchOfficeList)}</span>
            </div> */}
            <TagTitle
                title={
                    <Fragment>
                        分支机构<span>{listCount(branchOfficeList)}</span>
                    </Fragment>
                }
                id="branch"
            />
            <Branch data={branchOfficeList} />

            {/* <TagTitle
                title={
                    <Fragment>
                        联络信息<span>{listCount(contactInfoList)}</span>
                    </Fragment>
                }
                id="#graph"
            />

            <Contact data={contactInfoList} /> */}
        </Fragment>
    );
};

export default BaseInfo;
