import React, { Component } from 'react';
// 工具类
import { getUserComponent } from '../../../Custom/index';
// 被封装组件
import Select from '../../Select';

const UserSelect = getUserComponent(Select);
// 用户管理器
const bmManager = require('@/components/common/business/manager/BmManager');
const meManager = require('@/components/common/business/manager/MeManager');

class AutoAreaSelect extends Component {
    static defaultProps = {
        ...Select.defaultProps,
        labelKey: 'label', // label属性键
        valueKey: 'code', // value属性键
        data: null, // 外部数据源

        userManageFilter: false, // 是否过滤获取用户管理区域
        userChildFilter: false, // 是否过滤获取用户子区域
        userValueFilter: false, // 是否过滤与用户相同区域
        manageFilter: null, // 过滤获取指定区域下管理区域
        childFilter: null, // 过滤获取指定区域下子区域
        valueFilter: null, // 过滤获取指定区域
        handlerFn: null, // 处理函数（函数）

        autoCheckedInitial: false, // 任何用户初始化自动选中自身
        autoCheckedAlways: false, // 任何用户始终始自动选中自身
        deptAutoCheckedInitial: false, // 部门用户初始化自动选中自身
        deptAutoCheckedAlways: false, // 部门用户始终自动选中自身
        deptAutoDisabled: false, // 部门用户自动锁死下拉框
        onlyValueAutoCheckedInitial: false, // 唯一值初始化自动选中自身
        onlyValueAutoCheckedAlways: true, // 唯一值始终自动选中自身
        onlyValueAutoDisabled: true, // 唯一值自动锁死
    };

    componentWillMount() {
        this.me = meManager.getMe();
        this.bmData = bmManager.getBmList('BM_AREA');
    }

    get options() {
        const {
            labelKey,
            valueKey,
            data,
            userManageFilter,
            userChildFilter,
            userValueFilter,
            manageFilter,
            childFilter,
            valueFilter,
            handlerFn,
        } = this.props;
        let dataList = data || this.bmData;

        // 过滤用户管理区域
        if (userManageFilter) {
            dataList = this.handleUserManageFilter(dataList);
        }
        // 过滤用户子区域
        if (userChildFilter) {
            dataList = this.handleUserChildFilter(dataList);
        }
        // 过滤用户自身区域
        if (userValueFilter) {
            dataList = this.handleUserValueFilter(dataList);
        }
        // 过滤指定区域管理区域
        if (manageFilter) {
            dataList = this.handleManageFilter(dataList, manageFilter);
        }
        // 过滤指定区域子区域
        if (childFilter) {
            dataList = this.handleChildFilter(dataList, childFilter);
        }
        // 过滤指定区域
        if (valueFilter) {
            dataList = this.handleValueFilter(dataList, valueFilter);
        }
        // 自定义处理逻辑
        if (handlerFn) {
            dataList = handlerFn(dataList);
        }

        return dataList.map((item) => ({ label: item[labelKey], value: item[valueKey] }));
    }

    handleUserManageFilter = (areaList) => {
        const { manager, regionNo } = this.me;

        return manager ? areaList : this.handleManageFilter(areaList, regionNo);
    };

    handleUserChildFilter = (areaList) => {
        const { manager, regionNo } = this.me;

        return manager ? areaList : this.handleChildFilter(areaList, regionNo);
    };

    handleUserValueFilter = (areaList) => {
        const { regionNo } = this.me;

        return this.handleValueFilter(areaList, regionNo);
    };

    handleManageFilter = (areaList, areaId) => {
        const { valueKey } = this.props;
        const minSimpleRegionNo = areaId.replace(/^(\d*?)(00)+$/, '$1');

        return areaList.filter((item) => item[valueKey] && item[valueKey].startsWith(minSimpleRegionNo));
    };

    handleChildFilter = (areaList, areaId) => {
        const { valueKey } = this.props;
        const minSimpleRegionNo = areaId.replace(/^(\d*?)(00)+$/, '$1');

        return areaList.filter((item) => item[valueKey] && item[valueKey] !== areaId && item[valueKey].startsWith(minSimpleRegionNo));
    };

    handleValueFilter = (areaList, areaId) => {
        const { valueKey } = this.props;

        return areaList.filter((item) => item[valueKey] && item[valueKey] === areaId);
    };

    render() {
        const {
            labelKey,
            valueKey,
            data,
            userManageFilter,
            userChildFilter,
            userValueFilter,
            manageFilter,
            childFilter,
            valueFilter,
            handlerFn,
            ...restProps
        } = this.props;
        const { regionNo } = this.me;

        return <UserSelect {...restProps} options={this.options} operateDefaultValue={regionNo} />;
    }
}

export default AutoAreaSelect;
