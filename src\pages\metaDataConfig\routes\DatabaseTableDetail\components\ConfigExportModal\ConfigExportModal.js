import React from 'react';
import { But<PERSON>, Modal } from '@share/shareui';
import { TableForm, Row, Select, CheckboxGroup, FormItem } from '@share/shareui-form';
import * as formRule from '@/utils/formRule';
import CheckboxGroupCustom from '../CheckboxGroupCustom';
import useLogic from './logic';
import styles from './ConfigExportModal.scss';

const ConfigExportModal = (props) => {
    const { show } = props;
    const { submit, form, dataFieldOptions, handleCancel } = useLogic(props);

    return (
        <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={handleCancel} bsSize="large" backdrop="static">
            <Modal.Header closeButton>导出配置</Modal.Header>
            <Modal.Body>
                <TableForm pageType="addPage" formState={form}>
                    <Row>
                        <CheckboxGroupCustom
                            label="导出字段"
                            field="fieldCode"
                            options={dataFieldOptions}
                            startAllChecked
                            startReverseChecked
                            rule={[formRule.checkRequiredNotBlank()]}
                            required
                        />
                    </Row>
                </TableForm>
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={handleCancel}>关闭</Button>

                <Button bsStyle="primary" onClick={submit}>
                    应用
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default ConfigExportModal;
