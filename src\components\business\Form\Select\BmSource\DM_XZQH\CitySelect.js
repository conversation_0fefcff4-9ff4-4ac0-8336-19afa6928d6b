import React, { Component } from 'react';
// 工具类
import { isEqualObject } from '@/components/common/common/Entity';
import { getBmComponent } from '../../../Custom/index';
// 被封装组件
import Select from '../../Select';

const BmSelect = getBmComponent(Select);
// 表码管理器
const bmManager = require('@/components/common/business/manager/BmManager');

class CitySelect extends Component {
    constructor(props) {
        super(props);
        const data = this.getCacheData(props);

        this.state = { data };
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.provinceId !== this.props.provinceId || !isEqualObject(nextProps.data, this.props.data)) {
            const data = this.getCacheData(nextProps);

            this.setState({ data });
        }
    }

    getCacheData = (props) => {
        const { provinceId, data = bmManager.getBmList('DM_XZQH') } = props;
        const provinceLabel = provinceId ? bmManager.getBmLabel('DM_XZQH', provinceId) : null;
        const cacheData = data
            .filter(
                (item) =>
                    (!provinceId || new RegExp(`^${provinceId.substring(0, 2)}\\d{4}$`).exec(item.code)) &&
                    !new RegExp(/^\d{2}0{4}$/).exec(item.code) &&
                    new RegExp(/^\d{4}0{2}$/).exec(item.code)
            )
            .map((i) => {
                const parentProvinceLabel = provinceLabel || bmManager.getBmLabel('DM_XZQH', i.code.replace(/^(\d{2})\d{4}$/, '$10000'));
                const label = i.label.replace(parentProvinceLabel, '');

                return { label, code: i.code };
            });

        return cacheData;
    };

    render() {
        const { data } = this.state;
        const { provinceId, ...restProps } = this.props;

        return <BmSelect {...restProps} data={data} />;
    }
}

export default CitySelect;
