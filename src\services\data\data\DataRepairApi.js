import { postJson, get, vformPost } from '@/components/common/network/Network';

// 数据列表（类别）
export const listByCategory = (categoryId, objectType = '2', param) => {
    return postJson(`/edp-front/data/repair/list/category/${categoryId}.do?objectType=${objectType}`, param);
};

// 数据详情(类别)
export const detailByCategory = (categoryId, objectType = '2', recordId) => {
    return get(`/edp-front/data/repair/detail/category/${categoryId}/${recordId}.do?objectType=${objectType}`);
};

// 数据编辑详情(类别)
export const editDetailByCategory = (categoryId, objectType = '2', recordId) => {
    return get(`/edp-front/data/repair/edit_detail/category/${categoryId}/${recordId}.do?objectType=${objectType}`);
};

// 数据excel(类别)
export const exportByCategory = (categoryId, objectType = '2', param) => {
    return vformPost(`/edp-front/data/repair/export/category/${categoryId}.do`, { objectType, param });
};

// 数据删除(类别)
export const deleteByCategory = (categoryId, objectType = '2', param) => {
    return postJson(`/edp-front/data/repair/delete/category/${categoryId}.do?objectType=${objectType}`, param);
};

// 数据修复(类别)
export const repairByCategory = (categoryId, objectType = '2', param, ignoreSuspectedError, ignoreQuestionData) => {
    return postJson(
        `/edp-front/data/repair/from_page/category/${categoryId}.do?objectType=${objectType}${
            ignoreSuspectedError ? `&ignoreSuspectedError=${ignoreSuspectedError}` : ''
        }${ignoreQuestionData ? `&ignoreQuestionData=${ignoreQuestionData}` : ''}`,
        param
    );
};

// 批量修复(类别)
export const repairFromExcelByCategory = (categoryId, objectType = '2') => {
    return `/edp-front/data/repair/from_excel/category/${categoryId}.do?objectType=${objectType}`;
};

// 疑似确认(类别)
export const suspectedConfirmByCategory = (categoryId, objectType = '2', param) => {
    return postJson(`/edp-front/data/repair/suspected_confirm/category/${categoryId}.do?objectType=${objectType}`, param);
};
