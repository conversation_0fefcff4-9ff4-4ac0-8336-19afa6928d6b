// 金额格式化
export const moneyFormat = (val) => {
    // val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return Number(val).toFixed(2);
};

// 计算出列表总数
export const listCount = (arr) => {
    if (Array.isArray(arr)) {
        return arr.length;
    }

    return 0;
};

// 字段转码
export const fieldTranslate = (val, bmArr) => {
    if (!val || !Array.isArray(bmArr)) {
        return '--';
    }

    return bmArr.find((v) => v.value === val)?.label || val;
};

// 时间格式化
export const timeFormat = (time) => {
    if (!time) {
        return '--';
    }

    return String(time).split(' ')[0] || '--';
};
