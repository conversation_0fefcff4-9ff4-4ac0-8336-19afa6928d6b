import React, { Component } from 'react';
// 被封装组件
import { getComponents } from '@share/shareui-form';

const { Input: ShareInput } = getComponents();

class AddonInput extends Component {
    render() {
        const { frontLabel, backLabel, ...restProps } = this.props;
        const addonStyle = {
            lineHeight: '28px',
        };
        const addonPadding = {
            padding: '0 5px',
        };

        const frontAddonStyle = frontLabel ? { ...addonStyle, ...addonPadding } : { ...addonStyle };
        const backAddonStyle = backLabel ? { ...addonStyle, ...addonPadding } : { ...addonStyle };

        return (
            <div style={{ display: 'flex', width: '100%' }} className="addonInputBox">
                <span style={frontAddonStyle}>{frontLabel}</span>
                <div style={{ flex: 1 }} className="addonInputWrap">
                    <ShareInput.View {...restProps} />
                </div>
                <span style={backAddonStyle}>{backLabel}</span>
            </div>
        );
    }
}

export default AddonInput;
