/*
 *@(#) BusinessInformation.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-10-12
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React, { Fragment } from 'react';
import TagTitle from '../TagTitle';
// import BusinessInfo from './components/BusinessInfo';
import CreditInfo from './components/CreditInfo';

const BusinessInformation = ({ data: { blackListList, allowPunishList, creditList } }) => {
    return (
        <div>
            {/* <TagTitle title={<Fragment>经营信息</Fragment>} id="businessInfo" /> */}
            {/* <BusinessInfo /> */}
            <TagTitle title={<Fragment>信用信息</Fragment>} id="creditInfo" />
            <CreditInfo data={{ blackListList, allowPunishList, creditList }} />
        </div>
    );
};

export default BusinessInformation;
