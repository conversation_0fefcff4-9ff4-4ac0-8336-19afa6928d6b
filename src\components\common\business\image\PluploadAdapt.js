const { Log } = require('reg-common');
const uuid = require('uuid');
const Plupload = require('plupload/js/plupload.min');
const mOxie = require('plupload/js/moxie.min');

Plupload.addI18n(require('./plupload.cn'));

const IMAGE_BUTTON_UP_ID = 'imageUpBt';
const HIDE_LOAD_ID = '_hide_load';

const UPLOAD_PATH = '/file/upload.do';
const DOWNLOAD_PATH = '/file/download.do';

const MSG_SUCCESS = 'success';
const MSG_ERROR = 'error';
const MSG_STEP = 's';

const sendCallbackMap = {};

function getSender(fileName) {
    const realKey = Object.keys(sendCallbackMap).find((key) => fileName.startsWith(key));

    if (!realKey) {
        Log.warn('文件上传,未找到对应回调方法');

        return () => {};
    }

    return sendCallbackMap[realKey];
}

function getBrowserInfo() {
    const agent = navigator.userAgent.toLowerCase();
    const regStr_ie = /msie [\d.]+;/gi;
    const regStr_ff = /firefox\/[\d.]+/gi;
    const regStr_chrome = /chrome\/[\d.]+/gi;
    const regStr_saf = /safari\/[\d.]+/gi;

    // IE
    if (agent.indexOf('msie') > 0) {
        return agent.match(regStr_ie);
    }
    // firefox
    if (agent.indexOf('firefox') > 0) {
        return agent.match(regStr_ff);
    }
    // Chrome
    if (agent.indexOf('chrome') > 0) {
        return agent.match(regStr_chrome);
    }
    // Safari
    if (agent.indexOf('safari') > 0 && agent.indexOf('chrome') < 0) {
        return agent.match(regStr_saf);
    }
}

function isLowerIe10() {
    const browser = getBrowserInfo();

    return `${browser}`.indexOf('msie') !== -1 && `${browser}`.replace(/[^0-9.]/gi, '') < 10;
}

function initFileTag(id) {
    const inputEle = document.createElement('div');

    inputEle.setAttribute('type', 'file');
    inputEle.setAttribute('id', id);
    inputEle.setAttribute('style', 'display:none');
    document.body.appendChild(inputEle);
}
initFileTag(HIDE_LOAD_ID);
initFileTag(IMAGE_BUTTON_UP_ID);
function createPlupload(id) {
    return new Plupload.Uploader({
        browse_button: id,
        runtimes: 'html5,html4',
        url: getFullUrl(UPLOAD_PATH),
        multi_selection: false,
        filters: {
            mime_types: [{ title: '图片', extensions: 'jpg,png,jpeg,JPG,PNG,JPEG' }],
            max_file_size: isLowerIe10() ? '1500kb' : '8000kb',
            prevent_duplicates: false, // 允许选取重复文件
        },
        required_features: 'send_browser_cookies',
    });
}
const uploader = createPlupload(IMAGE_BUTTON_UP_ID);
const fileLoader = createPlupload(HIDE_LOAD_ID);

uploader.bind('Error', (uploader, error) => {
    Log.error('上传失败', error);
});
uploader.bind('FileUploaded', (up, file, response) => {
    // 上传成功后处理
    const jsonObj = JSON.parse(response.response);

    if (jsonObj.status !== '1200') {
        Log.error(`文件上传失败status=${jsonObj.status},message=${jsonObj.message}`);
        getSender(file.name)(MSG_ERROR, '文件上传失败');

        return;
    }
    Log.info('上传成功');
    const { data } = jsonObj;

    Log.debug(`文件id=${data.id}`);
    setTimeout(() => {
        getSender(file.name)(MSG_SUCCESS, data.id);
    }, 500);
});

uploader.bind('FilesAdded', (loader, files) => {
    const fileName = files[0].name;

    getSender(fileName)(MSG_STEP, '开始上传');
    loader.start();
});
uploader.init();
fileLoader.bind('FilesAdded', (loader, files) => {
    const scheme = createScheme(files[0]);

    zipScheme(scheme);
});
fileLoader.init();

const TRY_CUT_TIMES_MAX = 10;
const IMAGE_MAX = 800 * 1024;
const IMAGE_RANGE = 50 * 1024;

function validSize(imageSize) {
    return imageSize <= IMAGE_MAX + IMAGE_RANGE;
}

function createScheme(file) {
    return {
        orgin: file,
        size: file.size,
        result: file,
        time: 0,
        type: null,
    };
}

function loadImageFile(file) {
    const imageLoader = new mOxie.image.Image();

    const imageLoaderPromise = new Promise((resolve, reject) => {
        imageLoader.onload = function () {
            Log.debug('moxie 读取图片成功');
            resolve(imageLoader);
        };
        imageLoader.onerror = function (e) {
            Log.error('moxie 读取图片失败');
            console.info(arguments);
            reject(e);
        };
    });

    Log.debug('moxie 读取图片');
    imageLoader.load(file.getSource());

    return imageLoaderPromise;
}

function zipScheme(scheme) {
    if (validSize(scheme.size)) {
        Log.debug(`图片符合大小size=${scheme.size},进入上传队列`);
        uploader.addFile(scheme.result, scheme.orgin.name);

        return;
    }
    if (scheme.time === 0) {
        Log.debug('开始压缩处理');
        getSender(scheme.orgin.name)(MSG_STEP, '开始图片压缩');
    }
    if (scheme.time >= TRY_CUT_TIMES_MAX) {
        Log.debug('压缩处理失败，达到尝试次数');
        getSender(scheme.orgin.name)(MSG_ERROR, '压缩处理失败，达到尝试次数');

        return;
    }
    scheme.time++;
    loadImageFile(scheme.orgin).then((imageLoader) => {
        if (scheme.type == null) {
            scheme.type = imageLoader.type;
            scheme.width = imageLoader.width;
            scheme.height = imageLoader.height;
        }
        const scale = Math.sqrt(IMAGE_MAX / scheme.size);

        // const scale = IMAGE_MAX / scheme.size ;
        scheme.width *= scale;
        scheme.height *= scale;
        const option = {
            width: scheme.width,
            height: scheme.height,
        };

        Log.debug(`压缩处理:times=${scheme.time},scale=${scale},w=${option.width},h=${option.height}`);
        imageLoader.downsize(option);
        scheme.result = imageLoader.getAsBlob(scheme.type);
        scheme.size = scheme.result.size;
        Log.debug(`压缩后大小为${scheme.size}`);
        zipScheme(scheme);
    });
}

function addFile(fileEle, sendCb) {
    const id = uuid().replace(/-/g, '');

    sendCallbackMap[id] = sendCb;
    const nameArr = fileEle.value.split('.');
    const suf = nameArr[nameArr.length - 1];

    if (isLowerIe10()) {
        uploader.addFile(fileEle, `${id}.${suf}`);
    } else {
        fileLoader.addFile(fileEle, `${id}.${suf}`);
    }
}

function getDownLoadUrl(fileId) {
    return `${getFullUrl(DOWNLOAD_PATH)}?fileId=${fileId}`;
}

/**
 * 获取文根
 */
function getContextPath(url = window.location.href) {
    return `${url.replace(/^https?:\/\/.*?(\/.*?)([\/#].*)*$/, '$1')}/`;  //eslint-disable-line
}

/**
 * 获取完整路径
 */
function getFullUrl(path) {
    if (/^https?/.test(path)) {
        return path;
    }
    const contextPath = getContextPath();

    if (contextPath.endsWith('/') && path.startsWith('/')) {
        path = path.replace('/', '');
    }

    return contextPath + path;
}

module.exports = {
    addFile,
    getDownLoadUrl,
};
