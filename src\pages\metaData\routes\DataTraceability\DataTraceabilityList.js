/*
 * @(#) DataFillingList.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-08-05 17:54:58
 */
import React, { Fragment } from 'react';
// 样式
import styles from '@/pages/metaData/styles/index.scss';
import noData from '@/assets/images/image-noData.png';
// 服务接口
import * as MetaCategoryApi from '@/services/data/meta/MetaCategoryApi';
// 组件
import DataQuery from '@/components/credit_data/DataQuery';
import DataTraceabilityTable from '@/components/credit_data/List/DataTraceabilityTable';
import { Panel } from '@share/shareui';
import { getComponents } from '@/components/business/Form';

const { CheckboxGroup, RangeTime } = getComponents('div');

const ywTypeOptions = [
    { label: '新增', value: '1' },
    { label: '修改', value: '2' },
    // { label: '修复', value: '3' },
    // { label: '恢复', value: '4' },
];

const collTypeOptions = [
    { label: '页面填报', value: '1' },
    { label: 'Excel导入', value: '2' },
    { label: '接口填报', value: '4' },
    { label: '数据库对接', value: '3' },
    { label: '系统定时任务', value: '5' },
];

class DataTraceabilityList extends DataQuery {
    // 初始化默认搜索条件
    initDefaultSearchBody() {
        return {
            CATEGORY_CODE: '',
            // SOURCE_DEPT_ID: [],
            YW_TYPE: [],
            COLL_TYPE: [],
            CREATE_TIME: { start: '', end: '' },
        };
    }

    // 初始化元数据配置Api
    initMetaConfigApi = (categoryCode, objectType) => {
        return MetaCategoryApi.metaConfig(categoryCode, objectType, 'DATA_TRC');
    };

    render() {
        const { history } = this.props;
        const { searchForm, listConfig, metaConfigList, categoryCodeList } = this.state;
        const { CATEGORY_CODE: categoryCode } = searchForm;
        const searchBody = this.handleSearchBody();

        return (
            <div>
                <Panel>
                    <Panel.Body full>
                        {this.renderQueryCondition(
                            'CreditDataTraceabilityListForm',
                            <Fragment>
                                <CheckboxGroup label="操作类型" field="YW_TYPE" options={ywTypeOptions} col={10} labelCol={3} />
                                <CheckboxGroup label="填报方式" field="COLL_TYPE" options={collTypeOptions} col={17} labelCol={3} />
                                <RangeTime label="填报时间" field="CREATE_TIME" col={13} labelCol={3} />
                            </Fragment>
                        )}
                    </Panel.Body>
                </Panel>
                <Panel>
                    <Panel.Body full>
                        {listConfig.length === 0 ? (
                            <div className={styles.noData}>
                                <img src={noData} alt="no data" />
                                <p>请选择信息类别</p>
                            </div>
                        ) : (
                            <DataTraceabilityTable
                                history={history}
                                categoryCode={categoryCode}
                                categoryCodeList={categoryCodeList}
                                metaConfigList={metaConfigList}
                                body={searchBody}
                            />
                        )}
                    </Panel.Body>
                </Panel>
            </div>
        );
    }
}

export default DataTraceabilityList;
