import React, { Component } from 'react';
import PopModal from './PopModal';
import style from './index.scss';

class InputCom extends Component {
    state = {
        value: '',
    };

    yesClick = () => {
        const { yesClick } = this.props;
        const { value } = this.state;

        if (!value.trim()) {
            return;
        }
        this.setState({ value: '' }, () => yesClick && yesClick(value.trim()));
    };

    noClick = () => {
        const { noClick } = this.props;

        noClick && noClick();
    };

    valChange = (e) => {
        this.setState({ value: e.target.value });
    };

    render() {
        const { label, placeholder, yesText, noText } = this.props;
        const { value } = this.state;

        return (
            <div>
                {label ? (
                    <label htmlFor="popPromptInput">
                        {label}
                        <input
                            type="input"
                            className={style.input}
                            value={value}
                            name="popPromptInput"
                            onChange={this.valChange}
                            placeholder={placeholder}
                        />
                    </label>
                ) : (
                    <input
                        type="input"
                        className={style.input}
                        value={value}
                        name="popPromptInput"
                        onChange={this.valChange}
                        placeholder={placeholder}
                    />
                )}
                <div className="popPromptBtn">
                    <button onClick={this.yesClick}>{yesText}</button>
                    <button onClick={this.noClick}>{noText}</button>
                </div>
            </div>
        );
    }
}

const PopPrompt = (props) => {
    const { yesClick, noClick, children, ...restProps } = props;

    return (
        <PopModal
            content={(open, close) => (
                <InputCom
                    yesClick={(value) => {
                        yesClick && yesClick(value);
                        close();
                    }}
                    noClick={() => {
                        noClick && noClick();
                        close();
                    }}
                    {...restProps}
                />
            )}
        >
            {children}
        </PopModal>
    );
};

export default PopPrompt;
