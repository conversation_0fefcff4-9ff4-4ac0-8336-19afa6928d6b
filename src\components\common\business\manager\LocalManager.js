// 接口请求
import * as LocalConfigure from '../../common/LocalConfigure';
// 工具类
const store = require('../../store/Store');
const Log = require('../../log/Log');
// 常量
const LOCAL_KEY = 'local';

/**
 * 地域配置缓存管理器管理器
 */
class LocalManager {
    constructor() {
        this.local = {};
        this.setLoader = this.setLoader.bind(this);
        this.getLoader = this.getLoader.bind(this);
        this.load = this.load.bind(this);
        this.setLocal = this.setLocal.bind(this);
        this.getLocal = this.getLocal.bind(this);
    }

    /**
     * 清除地域配置缓存
     */
    static cleanCache() {
        store.del(LOCAL_KEY);
    }

    /**
     *  外部设置地域配置缓存加载器
     * @param loader 如果是函数返回promise
     */
    setLoader(loader) {
        this.loader = loader;
    }

    /**
     * 获取地域配置缓存加载器
     * @returns {*} 加载器对象promise
     */
    getLoader() {
        if (typeof this.loader === 'function') {
            return this.loader();
        }

        return LocalConfigure.requestLocalConfig();
    }

    /**
     * 从服务器加载local
     * @returns {Promise.<{}>}
     */
    load() {
        // 从sessionStorage中获取，没有的话读取接口
        const localInfo = store.get(LOCAL_KEY);

        if (localInfo) {
            Log.debug('加载缓存地域配置信息');
            this.setLocal(localInfo);

            return Promise.resolve();
        }

        return this.getLoader()
            .then((data) => {
                this.setLocal(data);
                store.set(LOCAL_KEY, data);
            })
            .catch((e) => {
                Log.error('地域配置信息获取失败', e);
                throw e;
            });
    }

    /**
     * 手动设置地域配置信息
     * @param local
     */
    setLocal(local) {
        this.local = { ...local };
    }

    /**
     * 获取地域配置信息
     * @returns {{}}
     */
    getLocal() {
        return { ...this.local };
    }
}

export default LocalManager;
