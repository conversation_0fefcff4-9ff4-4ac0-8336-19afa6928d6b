/**  版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 *  @Copyright:  Copyright (c) 2020
 *  @Company:厦门畅享信息技术有限公司
 *  @Author: 李家其
 *  Date: 2023/11/24 17:01
 */
// 经营信息
import React, { useEffect, useState } from 'react';
import { Panel, Select } from '@share/shareui';
import { Row, TableForm, Text } from '@share/shareui-form';
import { useDeepCompareEffect, useSetState } from 'ahooks';
import { emptyDefault, moneyFormat } from '@/utils/format';
import { useFetchHooks } from '../../hook';
import styles from './Personnel.scss';

const Personnel = ({ formState, options, tyshxydm }) => {
    const { getPersonInfo } = useFetchHooks();
    const [state, setState] = useSetState({
        info: {},
        year: '',
    });

    const getPersonInfoData = async (params) => {
        const res = await getPersonInfo(params);
        setState({
            info: res,
            year: params.year,
        });
    };

    useDeepCompareEffect(() => {
        if (options?.[0]?.value) {
            getPersonInfoData({
                year: options?.[0]?.value,
                creditCode: tyshxydm,
            });
        }
    }, [options]);

    return (
        <div className={styles.personnel}>
            <Panel toggle>
                <Panel.Head
                    title="人事信息"
                    extra={
                        <div className={styles.selectWrap}>
                            <Select
                                options={options}
                                placeholder="选择年份"
                                value={state.year}
                                onChange={({ value }) => {
                                    setState({ year: value });
                                    getPersonInfoData({
                                        year: value,
                                        creditCode: tyshxydm,
                                    });
                                }}
                                resetValue=""
                            />
                        </div>
                    }
                />
                <Panel.Body full>
                    <div className="operateInfoTableRow">
                        <div className="operateInfoDataFromText">
                            数据来源：<span>{emptyDefault(state?.info?.dataSource)}</span>
                        </div>
                        <div className="formTableStyleCover">
                            <TableForm formState={formState}>
                                <Row>
                                    {/* <Text label="从业人员数量" value={`${emptyDefault(state?.info?.cyrysl)}人`} /> */}
                                    <Text
                                        label="社保缴交人数"
                                        value={`${state?.info?.sbygsl ? `${Number(state?.info?.sbygsl).toLocaleString()}人` : '--'}`}
                                    />
                                </Row>
                                {/* <Row>
                                    <Text label="失业保险员工数量" value={`${emptyDefault(state?.info?.sybxysl)}人`} />
                                    <Text label="" field="" />
                                </Row> */}
                            </TableForm>
                        </div>
                    </div>
                </Panel.Body>
            </Panel>
        </div>
    );
};

export default Personnel;
