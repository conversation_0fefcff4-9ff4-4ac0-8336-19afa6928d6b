/*
 *@(#) hooks.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-10
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import { useAuthExpression, useQuery, useService, useAuthExpressionState } from '@share/framework';
import { useForm } from '@share/shareui-form';
import { useMount, useRequest } from 'ahooks';
import { useEffect, useMemo, useState, createContext } from 'react';
import EnterpriseService from '@/services/EnterpriseService';
import { message } from 'antd';
import Service from '@/services/Service';
import { PANELKEY } from './config';

export const DetailContext = createContext(null);

// eslint-disable-next-line no-shadow
export const filterMenus = (menus, authExpressionMap = {}) => {
    // 定义一个辅助函数来处理递归逻辑
    const filterMenuRecursive = (menu) => {
        // 如果当前菜单项没有子菜单，直接检查权限
        if (!menu.children) {
            return authExpressionMap[menu.authExpression] ? [menu] : [];
        }

        // 初始化一个数组来收集符合条件的子菜单
        const filteredChildren = [];

        // 遍历子菜单
        // eslint-disable-next-line no-restricted-syntax
        for (const child of menu.children) {
            // 递归调用辅助函数来筛选子菜单
            const filteredChild = filterMenuRecursive(child);
            // 如果子菜单有权限，添加到结果数组中
            if (filteredChild.length > 0) {
                filteredChildren.push(...filteredChild);
            }
        }

        // 如果子菜单中有至少一项有权限，并且当前菜单项也有权限，则返回当前菜单及其子菜单
        if (filteredChildren.length > 0 && authExpressionMap[menu.authExpression]) {
            return [{ ...menu, children: filteredChildren }];
        }

        // 如果没有子菜单有权限，但当前菜单项有权限，则只返回当前菜单项
        if (authExpressionMap[menu.authExpression]) {
            return [
                {
                    ...menu,
                    children: [],
                },
            ];
        }

        // 如果没有权限，则返回空数组
        return [];
    };

    // 使用辅助函数递归处理每个菜单项
    return menus.flatMap((menu) => filterMenuRecursive(menu));
};

export const detailAuthExpressionEnum = [
    // 功能
    // 'economicbrain:qyarchives:tag:add', // 添加标签
    // 'economicbrain:qyarchives:tag:edit', // 修改标签
    // 'economicbrain:qyarchives:introduction:edit', // 编辑企业简介
    // 'economicbrain:qyarchives:interview:add', // 添加走访
    // 'economicbrain:qyarchives:zwjt:add', // 添加政务接待记录
    // 页签
    // 'economicbrain:qyarchives:overview:read', // 企业概况：
    // 'economicbrain:qyarchives:business:read', // 经营信息
    // 'economicbrain:qyarchives:develop:read', // 企业发展
    'economicbrain:qyarchives:support:read', // 扶持信息
    // 'economicbrain:qyarchives:interview:read', // 企业走访
    'economicbrain:qyarchives:credit:read', // 信用信息
    // 'economicbrain:qyarchives:changelog:read', // 变更记录
    // 'economicbrain:qyarchives:earlywarning:read', // 预警监测
];

export const useAuthExpressionHook = ({ expressions, isFocus, zcdzgsxzqhdm }, waitting) => {
    const services = useService(Service);
    const [authExpressionMap, setAuthExpressionMap] = useState({});

    const getExpressionsAuthState = async () => {
        const res = await services.getExpressionsAuthState({
            expressions,
            isFocus,
            zcdzgsxzqhdm,
        });

        // 前端动态判断
        // 'economicbrain:qyarchives:services:read', // 企业服务
        // 'economicbrain:qyarchives:qyjj:read', // 企业简介
        // 'economicbrain:qyarchives:zycp:read', // 主要产品
        // 'economicbrain:qyarchives:lxxx:read', // 联系信息

        setAuthExpressionMap({
            ...res,
            'economicbrain:qyarchives:services:read':
                res['economicbrain:qyarchives:support:read'] || res['economicbrain:qyarchives:interview:read'],
            'economicbrain:qyarchives:qyjj:read': res['economicbrain:qyarchives:overview:read'],
            'economicbrain:qyarchives:zycp:read': res['economicbrain:qyarchives:overview:read'],
            'economicbrain:qyarchives:lxxx:read': res['economicbrain:qyarchives:overview:read'],
        });
    };

    useEffect(() => {
        // todo 权限去除
        // if (!waitting) {
        //     getExpressionsAuthState();
        // } else if (zcdzgsxzqhdm) {
        //     getExpressionsAuthState();
        // }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [zcdzgsxzqhdm, waitting]);

    return {
        authExpressionMap,
    };
};

const useLogic = () => {
    const { id, menu } = useQuery();
    const [, formState] = useForm({});
    const { detailBasic } = useService(EnterpriseService);
    const [menuStatus, setMenuStatus] = useState(true);
    const [menuPanel, setMenuPanel] = useState(menu || PANELKEY.BASIC);

    const [detail, setDetail] = useState({});

    const getDetail = async () => {
        if (!id || id === 'null') {
            message.error('统一社会信用代码为空!', 2);

            return;
        }
        const res = await detailBasic({ creditCode: id });

        if (res) {
            setDetail(res);
            formState.setFormData(res);
        }
    };
    // 菜单视图切换
    const menuClick = ({ keyPath }) => {
        let key = '';
        let panelKey = '';

        if (keyPath.length === 1) {
            [panelKey] = keyPath;
        } else if (keyPath.length === 2) {
            [key, panelKey] = keyPath;
        }

        const newPanelKey = key && !key.includes('#') ? key : panelKey;

        if (newPanelKey !== menuPanel) {
            setMenuPanel(newPanelKey);
        }

        if (!key || !key.includes('#')) {
            document.getElementById('contentTop').scrollIntoView(true);

            return;
        }
        const $target = document.querySelector(key);
        if ($target) {
            $target.scrollIntoView(true);
        } else {
            let timer = null;
            let count = 0;
            const run = () => {
                const $temp = document.querySelector(key);
                if ($temp) {
                    clearTimeout(timer);
                    $temp.scrollIntoView(true);
                } else {
                    count += 1;
                    if (count < 6) {
                        timer = setTimeout(run, 200);
                    } else {
                        throw new Error(`找不到${key}的元素`);
                    }
                }
            };
            run();
        }
    };

    const refresh = () => {
        getDetail();
    };
    useMount(() => {
        getDetail();
    });

    useEffect(() => {
        const $uiRoot = document.querySelector('.ui-root');
        const $menu = document.getElementById('menu');
        const scrolling = ({ target: { scrollTop } }) => {
            // console.info('scrollTop', scrollTop);
            if (scrollTop > 294) {
                $menu.style.position = 'fixed';
            } else {
                $menu.style.position = 'absolute';
            }
        };

        $uiRoot.addEventListener('scroll', scrolling);

        return () => {
            $uiRoot.removeEventListener('scroll', scrolling);
        };
    }, []);

    return {
        formState,
        menuClick,
        detail,
        menuStatus,
        setMenuStatus,
        menuPanel,
        refresh,
    };
};

export default useLogic;
