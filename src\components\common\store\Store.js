const IESaver = require('./IESaver');
const Saver = require('./Saver');

class Store extends Saver {
    constructor() {
        super();
        this.saver = IESaver;
        this.setSaver = this.setSaver.bind(this);
    }

    setSaver(saver) {
        this.saver = saver;
    }

    set(key, obj) {
        this.saver.set(key, obj);
    }

    get(key) {
        return this.saver.get(key);
    }

    del(key) {
        this.saver.del(key);
    }
}
module.exports = new Store();
