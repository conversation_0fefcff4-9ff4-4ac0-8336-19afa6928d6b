@import "@/pages/metaData/styles/minix";

:global {
  // title
  .share-table_title {
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 700;
    color: #323538;
  }

  // 改写antd的样式
  .share-table_container {
    background: #fff;
    margin: 0 0 12px;

    // title
    .ant-table-title {
      top: 0;
      padding: 0 12px 0 12px;
      line-height: 34px;
      min-height: 36px;
      border-bottom: 2px solid #09d;
      background-color: #fff;

      // title
      .share-table_title {
        margin-bottom: 0;
        font-family: "microsoft yahei";
        font-size: 12px;
        font-weight: bold;
        color: #323538;
      }
    }

    // footer
    .ant-table-footer {
      padding: 10px;
      overflow: hidden;
    }

    .ant-table th,
    .ant-table td {
      text-align: left;
      height: 34px;
      line-height: 1.2;
      padding: 2px 5px;
      vertical-align: middle;
    }

    .ant-table th {
      @include title;
      font-size: 12px;

      &:first-child {
        padding-left: 20px;
      }

      &:last-child {
        padding-right: 20px;
      }
    }

    .ant-table-tbody .ant-table-row {
      text-align: left;
      height: 34px;
      line-height: 1.2;
      padding: 2px 5px;
      vertical-align: middle;
      color: #666;
      font-size: 12px;

      &:nth-child(2n) {
        background-color: #f7f8f9;
      }

      &:nth-child(2n + 1) {
        background-color: #fff;
      }

      td {
        &:first-child {
          padding-left: 20px;
        }

        &:last-child {
          padding-right: 20px;
        }
      }

      &:hover {
        background-color: #e0f6ff;
      }
    }

    .ant-table-placeholder {
      z-index: 0;
    }
  }

  .ant-table-footer {
    margin-top: 5px;
  }
}
