import React from 'react';
import * as MeManager from '@/components/common/business/manager/MeManager';

class DeptAuthControl extends React.Component {
    render() {
        const { authDeptId, unAuthDeptId, openCenterAuth = false, openDeptAuth = false, children, unAuthDefaultEl = '' } = this.props;
        const me = MeManager.getMe();
        const { deptId, centerUser } = me;

        // 处理未授权部门
        if (unAuthDeptId && unAuthDeptId.split(',').some((item) => item === deptId)) {
            return unAuthDefaultEl;
        }
        // 处理授权部门
        if (authDeptId && authDeptId.split(',').some((item) => item === deptId)) {
            return children;
        }
        // 处理允许中心用户
        if (openCenterAuth && centerUser) {
            return children;
        }
        // 处理允许部门用户
        if (openDeptAuth && !centerUser) {
            return children;
        }

        return unAuthDefaultEl;
    }
}
export default DeptAuthControl;
