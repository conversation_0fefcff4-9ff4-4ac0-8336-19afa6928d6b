/*
 * @(#)  DatabaseTableDetail.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 * Copyright: Copyright (c) 2023
 * Company: 厦门畅享信息技术有限公司
 * Author: yujy (<EMAIL>)
 * File Created: 2023-12-21 10:34:57
 */
import React, { Fragment } from 'react';
import { Button, ButtonToolBar, Icon, Panel, Tab, Tabs } from '@share/shareui';
import DetailHeader from './components/DetailHeader';
import ConfigCopyModal from './components/ConfigCopyModal';
import ConfigExportModal from './components/ConfigExportModal';
import TableFieldList from './components/TableFieldList';
import FieldComponentList from './components/FieldComponent/FieldComponentList';
import useHook from './hooks';
import './DatabaseTableDetail.scss';

const DatabaseTableDetail = () => {
    const {
        state,
        tableId,
        switchTab,
        refreshTabledMetaConfig,
        handleSetShowModal,
        refreshTableFieldList,
        handleGoBack,
        handleSetFieldCodeEventKey,
    } = useHook();
    const { tabActiveKey, renderTabMap, tableInfo, metaFieldList, showExportModal, showCopyModal } = state;

    return (
        <Fragment>
            <DetailHeader tableInfo={tableInfo} metaFieldList={metaFieldList} />
            <Panel>
                <Tabs bsStyle="tabs" id="dataSearch" activeKey={tabActiveKey} onSelect={switchTab} className="tabs_full">
                    <ul className="ui-list-horizontal" style={{ position: 'absolute', zIndex: 2, top: '0px', right: 0 }}>
                        <li>
                            <Button type="button" className="btn-xs" border={false} onClick={refreshTabledMetaConfig}>
                                <Icon className="si si-com_rotate" />
                                {metaFieldList.length === 0 ? '初始化字段' : '刷新字段'}
                            </Button>
                        </li>
                        {metaFieldList.length > 0 && (
                            <li>
                                <Button
                                    type="button"
                                    className="btn-xs"
                                    border={false}
                                    onClick={() => handleSetShowModal('showExportModal', true)}
                                >
                                    <Icon className="si si-com_dc" />
                                    导出配置
                                </Button>
                            </li>
                        )}
                        {/* {metaFieldList.length > 0 && (
                            <li>
                                <FileUploadButton url={MetaFieldApi.importTableMetaJson(tableId)} successFn={this.importConfigSuccess}>
                                    <Button type="button" className="btn-xs" border={false}>
                                        <Icon className="si si-com_dr" />
                                        导入配置
                                    </Button>
                                </FileUploadButton>
                            </li>
                        )} */}
                        {metaFieldList.length > 0 && (
                            <li>
                                <Button
                                    type="button"
                                    className="btn-xs"
                                    border={false}
                                    onClick={() => handleSetShowModal('showCopyModal', true)}
                                >
                                    <Icon className="si si-app_hdgz" />
                                    拷贝配置
                                </Button>
                            </li>
                        )}
                    </ul>
                    <Tab eventKey="1" title="字段配置">
                        {renderTabMap['1'] && (
                            <TableFieldList
                                tableId={tableId}
                                metaFieldList={metaFieldList}
                                refreshDataFn={refreshTableFieldList}
                                turnTable={(fieldCode, eventKey) => handleSetFieldCodeEventKey({ fieldCode, eventKey })}
                            />
                        )}
                    </Tab>
                    {metaFieldList.length !== 0 && (
                        <Tab eventKey="2" title="组件配置">
                            {renderTabMap['2'] && (
                                <FieldComponentList tableId={tableId} metaFieldList={metaFieldList} refreshDataFn={refreshTableFieldList} />
                            )}
                        </Tab>
                    )}
                    {/*   {metaFieldList.length !== 0 && (
                        <Tab eventKey="3" title="规则配置">
                            {renderTabMap['3'] && (
                                <FieldRuleList tableId={tableId} metaFieldList={metaFieldList} refreshDataFn={refreshTableFieldList} />
                            )}
                        </Tab>
                    )}
                    {metaFieldList.length !== 0 && (
                        <Tab eventKey="4" title="展现配置">
                            {renderTabMap['4'] && (
                                <FieldApplication tableId={tableId} metaFieldList={metaFieldList} refreshDataFn={refreshTableFieldList} />
                            )}
                        </Tab>
                    )}
                    {metaFieldList.length !== 0 && (
                        <Tab eventKey="5" title="模拟试用">
                            {renderTabMap['5'] && (
                                <SimulationMetaResult
                                    tableId={tableId}
                                    metaFieldList={metaFieldList}
                                    refreshDataFn={refreshTableFieldList}
                                />
                            )}
                        </Tab>
                    )}
                    {metaFieldList.length !== 0 && (
                        <Tab eventKey="6" title="数据查询">
                            {renderTabMap['6'] && metaFieldList.length !== 0 && (
                                <TableDataList tableId={tableId} metaFieldList={metaFieldList} refreshDataFn={refreshTableFieldList} />
                            )}
                        </Tab>
                    )} */}
                </Tabs>
            </Panel>
            <ButtonToolBar>
                <Button type="button" onClick={handleGoBack}>
                    返回
                </Button>
            </ButtonToolBar>
            <ConfigExportModal
                show={showExportModal}
                cancelFn={() => handleSetShowModal('showExportModal', false)}
                data={{
                    tableId,
                    metaFieldList,
                }}
            />
            <ConfigCopyModal
                show={showCopyModal}
                successFn={refreshTableFieldList}
                cancelFn={() => handleSetShowModal('showCopyModal', false)}
                data={{
                    tableId,
                    metaFieldList,
                }}
            />
        </Fragment>
    );
};

export default DatabaseTableDetail;
