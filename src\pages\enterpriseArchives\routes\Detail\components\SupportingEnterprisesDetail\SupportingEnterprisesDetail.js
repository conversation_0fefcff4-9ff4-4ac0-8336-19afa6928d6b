/*
 *@(#) SupportingEnterprisesDetail.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-25
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import React from 'react';
import { Timeline, Tag, Popover } from 'antd';
import { UpOutlined, DownOutlined } from '@ant-design/icons';

import { numberFormatTP, moneyFormatThousandth, fieldTranslate, getExecuteDeptName, moneyThonsand } from '@/utils/format';
import PageAboutLayout from '@/components/PageAboutLayout/PageAboutLayout';
import PanelTitle from '@/components/PanelTitle/PanelTitle';
import { Empty } from '@share/shareui';
import classNames from 'classnames';
import NoData from '@/components/NoData';
import styles from './SupportingEnterprisesDetail.scss';
import { useModel } from './hooks';

const SupportingEnterprisesDetail = ({ noLeft, id }) => {
    const model = useModel({ id });
    const {
        ZXBMDM,
        ZCFLDM,
        detailInfo,
        supportRecList,
        lookMore,
        showLookMore,
        toggleYear,
        getYearSupportTimes,
        getYearSupportMoney,
        goEnterpriseArchivesDetail,
        filterList,
        activeFilter,
    } = model;

    const {
        companyName, // 企业名称
        supportDeptList = [], // 扶持信息(表格)
        supportCount, // 扶持次数
        supportAmount, // 扶持金额
        comPolAmount, // 一企一策
        busMeetAmount, // 一事一议
        financeAmount, // 普惠政策
        creditCode, // 统一社会信用代码
        legalPerName, // 法定代表人
        regCapital, // 注册资本
        supportDept, // 扶持部门(数量)
    } = detailInfo;

    return (
        <div className={styles.container}>
            <PageAboutLayout>
                {!noLeft && (
                    <PageAboutLayout.Left>
                        <div className={styles.leftContent}>
                            <Popover content={companyName} placement="bottom">
                                <div className={classNames(styles.name)} onClick={goEnterpriseArchivesDetail}>
                                    {companyName}
                                </div>
                            </Popover>
                            <div className={styles.infoBox}>
                                <div className={styles.infoItem}>
                                    <div className={styles.label}>统一社会信用代码</div>
                                    <div className={styles.value}>{creditCode}</div>
                                </div>
                                <div className={styles.infoItem}>
                                    <div className={styles.label}>法定代表人</div>
                                    <div className={styles.value}>{legalPerName}</div>
                                </div>
                                <div className={styles.infoItem}>
                                    <div className={styles.label}>注册资本</div>
                                    <div className={styles.value}>{moneyFormatThousandth(regCapital, false)}万</div>
                                </div>
                            </div>
                        </div>
                    </PageAboutLayout.Left>
                )}
                <PageAboutLayout.Right>
                    <div className={styles.rightContent}>
                        {/* 卡片 */}
                        <div className={styles.cardBox}>
                            <div className={styles.cardLeft}>
                                <div className={styles.label}>共扶持次数</div>
                                <div className={styles.value}>{numberFormatTP(supportCount)}</div>
                            </div>
                            <div className={styles.cardRight}>
                                <div className={styles.supportMoneyCard}>
                                    <div className={styles.label}>兑现金额（万元）</div>
                                    <div className={styles.value}>{moneyFormatThousandth(supportAmount)}</div>
                                </div>
                                <div className={styles.supportDetailCard}>
                                    <div className={styles.detailItem}>
                                        <span className={styles.label}>一企一策：</span>
                                        <span className={styles.value}>{moneyFormatThousandth(comPolAmount)}</span>
                                    </div>
                                    <div className={styles.detailItem}>
                                        <span className={styles.label}>一事一议：</span>
                                        <span className={styles.value}>{moneyFormatThousandth(busMeetAmount)}</span>
                                    </div>
                                    <div className={styles.detailItem}>
                                        <span className={styles.label}>普惠政策：</span>
                                        <span className={styles.value}>{moneyFormatThousandth(financeAmount)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {/* 扶持信息表格 */}
                        <div className="m-t-20">
                            <PanelTitle title="扶持信息">
                                <span className={styles.supportTitleDesc}>
                                    共有 <span className={styles.blue}>{numberFormatTP(supportDept)}</span> 个部门， 共扶持{' '}
                                    <span className={styles.blue}>{numberFormatTP(supportCount)}</span> 次
                                </span>
                            </PanelTitle>
                            <div className="shareListStyleCover">
                                {Array.isArray(supportDeptList) && supportDeptList.length > 0 ? (
                                    <div>
                                        {supportDeptList.map((value, index) => {
                                            return (
                                                <div className={styles.listBox}>
                                                    <div className={styles.listItem}>
                                                        {index === supportDeptList.length - 1 ? (
                                                            <div>
                                                                <span>合计：</span>
                                                                <span />
                                                            </div>
                                                        ) : (
                                                            <div>
                                                                <span>执行部门：</span>
                                                                <span
                                                                    onClick={() =>
                                                                        filterList({
                                                                            deptId: value.deptId,
                                                                            key: `deptId-${value.deptId}`,
                                                                        })
                                                                    }
                                                                    className={classNames({
                                                                        [styles.active]: activeFilter === `deptId-${value.deptId}`,
                                                                    })}
                                                                >
                                                                    {ZXBMDM.find((itemBM) => itemBM.code === value.deptId)?.name ||
                                                                        value.deptId}
                                                                </span>
                                                            </div>
                                                        )}
                                                    </div>
                                                    <div className={styles.listItem}>
                                                        <span>总扶持：</span>
                                                        <span
                                                            onClick={() =>
                                                                filterList({
                                                                    deptId: value.deptId,
                                                                    key: `supportAmount-${value.deptId}`,
                                                                })
                                                            }
                                                            className={classNames({
                                                                [styles.active]: activeFilter === `supportAmount-${value.deptId}`,
                                                            })}
                                                        >
                                                            {numberFormatTP(value.supportCount, 0, 0)}次/
                                                            {numberFormatTP(moneyThonsand(value.supportAmount), 2, 0)}
                                                            万元
                                                        </span>
                                                    </div>
                                                    {supportDeptList[supportDeptList.length - 1].phzcAmount !== 0 && (
                                                        <div className={styles.listItem}>
                                                            <span>普惠政策：</span>
                                                            <span
                                                                onClick={() =>
                                                                    filterList({
                                                                        deptId: value.deptId,
                                                                        policyType: 'phzc',
                                                                        key: `phzc-${value.deptId}`,
                                                                    })
                                                                }
                                                                className={classNames({
                                                                    [styles.active]: activeFilter === `phzc-${value.deptId}`,
                                                                })}
                                                            >
                                                                {numberFormatTP(value.phzcCount, 0, 0)}次/
                                                                {numberFormatTP(moneyThonsand(value.phzcAmount), 2, 0)}
                                                                万元
                                                            </span>
                                                        </div>
                                                    )}
                                                    {supportDeptList[supportDeptList.length - 1].yqycAmount !== 0 && (
                                                        <div className={styles.listItem}>
                                                            <span>一企一策：</span>
                                                            <span
                                                                onClick={() =>
                                                                    filterList({
                                                                        deptId: value.deptId,
                                                                        policyType: 'yqyc',
                                                                        key: `yqyc-${value.deptId}`,
                                                                    })
                                                                }
                                                                className={classNames({
                                                                    [styles.active]: activeFilter === `yqyc-${value.deptId}`,
                                                                })}
                                                            >
                                                                {numberFormatTP(value.yqycCount, 0, 0)}次/
                                                                {numberFormatTP(moneyThonsand(value.yqycAmount), 2, 0)}
                                                                万元
                                                            </span>
                                                        </div>
                                                    )}
                                                    {supportDeptList[supportDeptList.length - 1].ysyyAmount !== 0 && (
                                                        <div className={styles.listItem}>
                                                            <span>一事一议：</span>
                                                            <span
                                                                onClick={() =>
                                                                    filterList({
                                                                        deptId: value.deptId,
                                                                        policyType: 'ysyy',
                                                                        key: `ysyy-${value.deptId}`,
                                                                    })
                                                                }
                                                                className={classNames({
                                                                    [styles.active]: activeFilter === `ysyy-${value.deptId}`,
                                                                })}
                                                            >
                                                                {numberFormatTP(value.ysyyCount, 0, 0)}次/
                                                                {numberFormatTP(moneyThonsand(value.ysyyAmount), 2, 0)}
                                                                万元
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                ) : (
                                    <NoData />
                                )}
                            </div>
                        </div>
                        {/* 扶持记录 */}
                        <div className="m-t-20">
                            <PanelTitle title="扶持记录" />
                            <div className={styles.timelineBox}>
                                {supportRecList.filter((item) => !item.hidden).length > 0 ? (
                                    <Timeline>
                                        {supportRecList
                                            .filter((item) => !item.hidden)
                                            .map((item, index) => {
                                                const {
                                                    isYear,
                                                    isClose,
                                                    auditTime,
                                                    policyName,
                                                    policyType,
                                                    docList,
                                                    executeDept,
                                                    cashAmount,
                                                    decTime,
                                                } = item;

                                                return (
                                                    <Timeline.Item key={index} data-isYear={isYear}>
                                                        {isYear && (
                                                            <div className={styles.yearBox}>
                                                                <div className={styles.yearLeft}>
                                                                    <span className={styles.yearTitle}>{auditTime}</span>
                                                                    <span className={styles.yearDesc}>
                                                                        本年度扶持
                                                                        <span className={styles.blue}>
                                                                            {numberFormatTP(getYearSupportTimes(item))}
                                                                        </span>
                                                                        次，扶持金额
                                                                        <span className={styles.blue}>
                                                                            {moneyFormatThousandth(getYearSupportMoney(item))}
                                                                        </span>
                                                                        万元
                                                                    </span>
                                                                </div>
                                                                <div className={styles.yearRight} onClick={() => toggleYear(auditTime)}>
                                                                    <span className="m-r-5">{isClose ? '展开' : '收起'}</span>
                                                                    {!isClose && <UpOutlined />}
                                                                    {isClose && <DownOutlined />}
                                                                </div>
                                                            </div>
                                                        )}
                                                        {!item.isYear && (
                                                            <div className={styles.dayBox}>
                                                                <div className={styles.approvedDate}>{auditTime}&nbsp;&nbsp;审核日期</div>
                                                                <div className={styles.approvedContent}>
                                                                    <div className={styles.approvedTitleBox}>
                                                                        <Popover placement="bottom" content={policyName}>
                                                                            <span
                                                                                className={classNames(
                                                                                    styles.approvedTitle,
                                                                                    'text-over-ellipsis'
                                                                                )}
                                                                            >
                                                                                {policyName}
                                                                            </span>
                                                                        </Popover>
                                                                        {(policyType ? policyType.split(',') : []).map((policyTypeItem) => {
                                                                            return (
                                                                                <Tag color="processing" className="m-l-15">
                                                                                    {fieldTranslate(policyTypeItem, ZCFLDM)}
                                                                                </Tag>
                                                                            );
                                                                        })}
                                                                    </div>
                                                                    <div className={styles.policyBasis}>
                                                                        <div className={styles.label}>政策依据：</div>
                                                                        <Popover placement="bottom" content={docList.join('，') || '--'}>
                                                                            <div className={classNames(styles.value, 'text-over-ellipsis')}>
                                                                                {docList.join('，') || '--'}
                                                                            </div>
                                                                        </Popover>
                                                                    </div>
                                                                    <div className={styles.approvedDetail}>
                                                                        <div className={styles.deptShow}>
                                                                            执行部门：
                                                                            <Popover
                                                                                placement="bottom"
                                                                                content={getExecuteDeptName(executeDept, ZXBMDM)}
                                                                            >
                                                                                <span
                                                                                    className={classNames(
                                                                                        styles.deptContent,
                                                                                        'text-over-ellipsis'
                                                                                    )}
                                                                                >
                                                                                    {getExecuteDeptName(executeDept, ZXBMDM)}
                                                                                </span>
                                                                            </Popover>
                                                                        </div>
                                                                        <div>兑现金额：{moneyFormatThousandth(cashAmount)}万元</div>
                                                                        <div>申报日期：{decTime}</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        )}
                                                    </Timeline.Item>
                                                );
                                            })}
                                    </Timeline>
                                ) : (
                                    <div>
                                        <NoData />
                                    </div>
                                )}
                            </div>
                            {showLookMore && (
                                <div className={styles.more}>
                                    <span onClick={lookMore}>点击查看更多</span>
                                </div>
                            )}
                        </div>
                    </div>
                </PageAboutLayout.Right>
            </PageAboutLayout>
        </div>
    );
};

export default SupportingEnterprisesDetail;
