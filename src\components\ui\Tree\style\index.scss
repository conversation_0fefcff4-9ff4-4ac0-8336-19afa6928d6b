:global {
  .share-tree_container {
    .rc-tree {
      .rc-tree-title {
        display: inline-block;
        height: 22px;
        line-height: 22px;
      }
      .rc-tree-node-content-wrapper {
        height: 22px;
        line-height: 22px;
        padding: 0 3px;
        margin-bottom: 2px;
        &.rc-tree-node-selected {
          background-color: #c0ecff;
          border: none;
          border-radius: 4px;
          opacity: 1;
          &:hover {
            background-color: #c0ecff;
          }
        }
        &:hover {
          background-color: #eaf9ff;
          .title_pane {
            .title_operate {
              display: inline-block;
            }
          }
        }
      }
      .rc-tree-switcher {
        &.rc-tree-switcher_open,
        &.rc-tree-switcher_close {
          width: 9px;
          height: 9px;
        }
        &.rc-tree-switcher_open {
          background: url("@/assets/icons/ico_reduce.png") no-repeat
            center center;
        }
        &.rc-tree-switcher_close {
          background: url("@/assets/icons/ico_plus.png") no-repeat
            center center;
        }
      }
      .rc-tree-iconEle {
        &.rc-tree-icon__open,
        &.rc-tree-icon__close {
          width: 16px;
          height: 13px;
          background: url("@/assets/icons/ico_directory.png")
            no-repeat center center;
        }
        &.rc-tree-icon__docu {
          width: 13px;
          height: 14px;
          background: url("@/assets/icons/ico_file.png") no-repeat
            center center;
        }
      }
      .rc-tree-checkbox {
        width: 14px;
        height: 14px;
        background: url("@/assets/icons/ico_checkbox.png") no-repeat
          center center;
        &.rc-tree-checkbox-indeterminate {
          background: url("@/assets/icons/ico_checkbox_indeterminate.png")
            no-repeat center center;
        }
        &.rc-tree-checkbox-checked {
          background: url("@/assets/icons/ico_checkbox_checked.png")
            no-repeat center center;
        }
      }
    }
    .title_pane {
      .title_content {
        display: inline-block;
        vertical-align: middle;
      }
      .title_operate {
        display: none;
        padding: 0 5px 0 10px;
        vertical-align: middle;
        li {
          float: left;
          &.operate_icon {
            width: 13px;
            height: 12px;
          }
          &.operate_text {
            color: #0099dd;
          }
        }
        li + li {
          margin-left: 4px;
        }
        .operate_add {
          background: url("@/assets/icons/ico_add.png") no-repeat
            center center;
        }
        .operate_edit {
          background: url("@/assets/icons/ico_edit.png") no-repeat
            center center;
        }
        .operate_del {
          background: url("@/assets/icons/ico_del.png") no-repeat
            center center;
        }
      }
    }
  }
}
