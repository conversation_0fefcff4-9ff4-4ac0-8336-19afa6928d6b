const React = require('react');

const funcMap = {
    link(text, props, href) {
        return (
            <a {...props} href={href}>
                {text}
            </a>
        );
    },
};
const keyWordMap = {
    small: {
        fontSize: '11px ',
    },
    normal: {
        fontSize: '13px ',
    },
    large: {
        fontSize: '15px ',
    },
    bold: {
        fontWeight: 'bold ',
    },
    italic: {
        fontStyle: 'italic ',
    },
    primary: {
        color: 'blue ',
    },
    danger: {
        color: 'red ',
    },

    blue: {
        color: '#015E85 ',
    },
    red: {
        color: 'red ',
    },
    orange: {
        color: '#FF6C00 ',
    },
    gray: {
        color: '#F2EEE3 ',
    },
    size16: {
        fontSize: '16px ',
    },
    size14: {
        fontSize: '14px ',
    },
};

function customSplit(text) {
    const textArray = text;
    const result = [];
    let temp = [];

    for (let i = 0, len = textArray.length; i < len; i++) {
        const currentChar = textArray[i];

        if (currentChar === '{' && temp.length > 0) {
            result.push(temp.join(''));
            temp = [];
        }
        temp.push(currentChar);
        if (currentChar === '}') {
            result.push(temp.join(''));
            temp = [];
        }
    }
    if (temp.length > 0) {
        result.push(temp.join(''));
    }

    return result;
}
function parse(text) {
    if (typeof text !== 'string' || text === '') {
        return '';
    }

    const rowArray = text.split('\\n');

    const result = rowArray.map((rowText) => {
        const textSplitArray = customSplit(rowText).filter((item) => item !== '');

        // const textSplitArray = rowText.split(/({.*?})/g).filter(item => item !== "");
        const rowResult = textSplitArray.map((text) => {
            // 常规文本
            if (!(text.startsWith('{') && text.endsWith('}'))) {
                return text;
            }

            // 拆分配置文本和配置信息
            const textConfigArray = text.substring(1, text.length - 1).split(',');
            const configObject = {
                text: textConfigArray.shift(),
                props: {},
            };

            // 处理配置信息
            textConfigArray.forEach((directive) => {
                if (isFunConfig(directive)) {
                    funcHandle(configObject, directive);
                }
                if (isKeyWordConfig(directive)) {
                    keyWordHandle(configObject, directive);
                }
            });

            return configObject;
        });

        if (rowResult.length === 1) {
            return rowResult[0];
        }
    });

    if (result.length === 1) {
        return result[0];
    }

    return result;
}

function isFunConfig(config) {
    return config.startsWith('@');
}
function isKeyWordConfig(config) {
    return config.startsWith('#');
}
function splitByRange(text, open, close) {
    const textArray = text;
    const result = [];
    let temp = [];

    for (let i = 0, len = textArray.length; i < len; i++) {
        const currentChar = textArray[i];

        if (currentChar === open && temp.length > 0) {
            result.push(temp.join(''));
            temp = [];
        }
        temp.push(currentChar);
        if (currentChar === close) {
            result.push(temp.join(''));
            temp = [];
        }
    }
    if (temp.length > 0) {
        result.push(temp.join(''));
    }

    return result;
}
function funcHandle(config, directive) {
    // const funcConfigArray = directive.split(/(\(.*?\))/).filter(item => item );
    const funcConfigArray = splitByRange(directive, '(', ')').filter((item) => item !== '');
    const fn = funcMap[typeof funcConfigArray[0] === 'string' ? funcConfigArray[0].replace('@', '') : ''];

    if (!fn) {
        return;
    }
    config.render = function (text, props) {
        return fn(text, props, funcConfigArray[1].replace(/\(|\)/g, ''));
    };
}

function keyWordHandle(config, directive) {
    if (!config.props.style) {
        config.props.style = {};
    }

    const { style } = config.props;

    const styleObj = keyWordMap[typeof directive === 'string' ? directive.replace('#', '') : ''];

    if (!styleObj) {
        return;
    }

    Object.assign(style, styleObj);
}

module.exports = {
    funcMap,
    keyWordMap,
    parse,
};
