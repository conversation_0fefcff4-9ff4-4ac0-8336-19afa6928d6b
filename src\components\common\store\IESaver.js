const Saver = require('./Saver');

class IESaver extends Saver {
    constructor() {
        super();
    }

    set(key, obj) {
        window.sessionStorage.setItem(key, JSON.stringify(obj));
    }

    get(key) {
        const item = window.sessionStorage.getItem(key);

        if (!item) {
            return null;
        }

        return JSON.parse(item);
    }

    del(key) {
        window.sessionStorage.removeItem(key);
    }
}

module.exports = new IESaver();
