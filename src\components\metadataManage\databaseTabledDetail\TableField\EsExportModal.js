import React, { Component } from 'react';
// 请求接口
import * as Meta<PERSON>ieldA<PERSON> from '@/services/data/meta/MetaFieldApi';
// 工具
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
import * as formRule from '@/components/common/common/formRule';
import * as TimeUtil from '@/components/common/common/TimeUtil';
// 列表组件
import { Button, Modal } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';

const { Form, Row, Input, RadioGroup } = getComponents();

const defaultBody = {
    esIndex: '',
    esAlias: '',
    relateCatalog: true,
    generateKeyword: true,
    generateTableName: true,
    humpHump: false,
};

const booleanOptions = [
    { label: '是', value: true },
    { label: '否', value: false },
];

class EsExportModal extends Component {
    state = {
        editForm: new FormState(
            {
                ...defaultBody,
                esIndex: `${this.props.data.tableId.toLowerCase()}_v${TimeUtil.getFormateTimeStr2(TimeUtil.getCurrentDayStr())}`,
                esAlias: this.props.data.tableId.toLowerCase(),
            },
            (editForm, callback) => this.setState({ editForm }, callback)
        ),
        dataFieldOptions: [],
    };

    submit = async () => {
        const {
            successFn,
            data: { tableId },
        } = this.props;
        const { editForm } = this.state;
        const data = editForm.getFormData();

        if (!FormVaildHelper.isValid(await editForm.valid())) {
            return;
        }
        MetaFieldApi.exportEsFile(tableId, data);

        successFn && successFn();
        this.cancelFn();
    };

    cancelFn = () => {
        const { cancelFn } = this.props;

        cancelFn && cancelFn();
    };

    render() {
        const { show } = this.props;
        const { editForm } = this.state;

        return (
            <Modal className="modal-full" show={show} onHide={this.cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>导出配置</Modal.Header>
                <Modal.Body>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <Input
                                label="索引名称"
                                field="esIndex"
                                rule={[
                                    formRule.checkRequiredNotBlank(),
                                    formRule.checkFunction((value) => value === value.trim(), '前后不能存在空格'),
                                    formRule.checkFunction((value, formData) => !value || value !== formData.esAlias, '不能与索引名称相同'),
                                ]}
                                required
                            />
                        </Row>
                        <Row>
                            <Input
                                label="索引别名"
                                field="esAlias"
                                disabled
                                rule={[
                                    formRule.checkRequiredNotBlank(),
                                    formRule.checkFunction((value) => !value || value === value.trim(), '前后不能存在空格'),
                                    formRule.checkFunction((value, formData) => !value || value !== formData.esIndex, '不能与索引名称相同'),
                                ]}
                                required
                            />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="关联目录"
                                field="relateCatalog"
                                options={booleanOptions}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="生成关键字"
                                field="generateKeyword"
                                options={booleanOptions}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="生成表名"
                                field="generateTableName"
                                options={booleanOptions}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                        {/* <Row> */}
                        {/*    <RadioGroup */}
                        {/*        label="驼峰转换" field="humpHump" */}
                        {/*        options={booleanOptions} */}
                        {/*        rule={[formRule.checkRequiredNotBlank()]} */}
                        {/*        required */}
                        {/*    /> */}
                        {/* </Row> */}
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        应用
                    </Button>
                    <Button onClick={this.cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default EsExportModal;
