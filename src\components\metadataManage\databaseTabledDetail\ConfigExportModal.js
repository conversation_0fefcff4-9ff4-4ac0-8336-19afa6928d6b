import React, { Component } from 'react';
import styles from '@/pages/metaData/styles/index.scss';
// 请求接口
import * as MetaFieldApi from '@/services/data/meta/MetaFieldApi';
// 工具
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
import * as formRule from '@/components/common/common/formRule';
// 列表组件
import { Button, Modal } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';

const { Form, Row, CheckboxGroupCustom } = getComponents();

const defaultBody = {
    fieldCode: [],
};

class ConfigExportModal extends Component {
    state = {
        editForm: new FormState({ ...defaultBody }, (editForm, callback) => this.setState({ editForm }, callback)),
        dataFieldOptions: [],
    };

    componentWillReceiveProps(nextProps) {
        const {
            show,
            data: { metaFieldList },
        } = nextProps;
        const { editForm } = this.state;

        if (!this.props.show && show) {
            const dataFieldOptions = metaFieldList.map((item) => ({
                label: `${item.fieldCode}（${item.showLabel}）`,
                value: item.fieldCode,
            }));
            const fieldCode = metaFieldList.map((item) => item.fieldCode);

            this.setState({ dataFieldOptions });
            editForm.setFieldValue('fieldCode', fieldCode);
        }
    }

    submit = async () => {
        const {
            successFn,
            data: { tableId },
        } = this.props;
        const { editForm } = this.state;
        const { fieldCode } = editForm.getFormData();

        if (!FormVaildHelper.isValid(await editForm.valid())) {
            return;
        }
        MetaFieldApi.exportTableMetaJson(tableId, { data: fieldCode });

        successFn && successFn();
        this.cancelFn();
    };

    cancelFn = () => {
        const { cancelFn } = this.props;
        const { editForm } = this.state;

        editForm.setFormData({ ...defaultBody });
        cancelFn && cancelFn();
    };

    render() {
        const { show } = this.props;
        const { editForm, dataFieldOptions } = this.state;

        return (
            <Modal className={`modal-full ${styles.w1100}`} show={show} onHide={this.cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>导出配置</Modal.Header>
                <Modal.Body>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <CheckboxGroupCustom
                                label="导出字段"
                                field="fieldCode"
                                options={dataFieldOptions}
                                startAllChecked
                                startReverseChecked
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        应用
                    </Button>
                    <Button onClick={this.cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default ConfigExportModal;
