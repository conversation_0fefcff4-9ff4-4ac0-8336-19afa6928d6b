import { useEffect, useState } from 'react';
import { useForm } from '@share/shareui-form';
import { useList } from '@share/list';
import { useService } from '@share/framework';
import InventoryService from '@/services/InventoryService';
import QueryService from '@/services/QueryService';
import { sleep } from '@/utils';

export const useModel = () => {
    // 清单
    const inventoryService = useService(InventoryService);
    const [inventoryList, setInventoryList] = useState([]);
    useEffect(() => {
        const request = async () => {
            const result = await inventoryService.list();
            setInventoryList(result);
        };
        request();
    }, [inventoryService]);
    // 表单
    const [formData, form] = useForm(
        {
            type: 'ent',
            id: '',
            result: [],
            group: [],
            condition: {},
        },
        { willClearEmptyField: true }
    );
    // 列表
    const [, detailForm] = useForm({});
    const [columns, setColumns] = useState([]);
    const queryService = useService(QueryService);
    const roundListRequest = async (request) => {
        let result = await queryService.detailQuery(request);
        let sleepCount = 0;
        let sleepTime = 200;

        while (true) {
            if (result.status === 'doing') {
                if (++sleepCount > 15) {
                    sleepTime = 1000;
                }
                await sleep(sleepTime);
                result = await queryService.taskResult(result.taskId);
            } else if (result.status === 'finish') {
                return result.data;
            } else {
                throw new Error(result.message);
            }
        }
    };
    const list = useList({
        dataSource: (param) => {
            const {
                page: { currentPage, linesPerPage },
                data,
            } = param;
            data.result = data.result.reduce((r, i) => {
                r[i.alias] = i.data;

                return r;
            }, {});
            const request = { pageNum: currentPage, pageSize: linesPerPage, ...data };
            detailForm.setFormData(null);

            return roundListRequest(request).then((d) => {
                const { pageNum, pageSize, total, pages, result } = d;
                detailForm.setFormData({ ...d, param: request });

                return { page: { currentPage: pageNum, linesPerPage: pageSize, totalNum: total, totalPage: pages }, list: result };
            });
        },
        autoLoad: false,
    });
    const updateColumns = () => {
        const queryColumns = formData.result.map((item) => ({ field: item.alias, label: item.alias }));
        setColumns(queryColumns);
    };
    const query = (condition) => {
        form.cleanValidError();
        updateColumns();
        list.query(condition);
    };
    // 交互
    const [inventoryOptions, setInventoryOptions] = useState([]);
    const [paramOptions, setParamOptions] = useState([]);
    const [resultOptions, setResultOptions] = useState([]);
    useEffect(() => {
        const options = inventoryList
            .filter((item) => item.objectType === formData.type)
            .map((item) => ({ value: item.id, label: item.name }));
        setInventoryOptions(options);
        form.setFormData({ type: formData.type });
    }, [inventoryList, formData.type]);
    useEffect(() => {
        const inventory = inventoryList.find((item) => item.id === formData.id);
        const resultList = inventory?.result.map((item) => ({ value: item.id, label: item.name })) || [];
        const conditionList =
            inventory?.param.map((item) => ({
                value: item.id,
                label: item.name,
                options: item.dict?.alias,
            })) || [];

        setResultOptions(resultList);
        setParamOptions(conditionList);
        form.setFormData({ type: formData.type, id: formData.id });
    }, [formData.id]);

    return {
        form,
        columns,
        detailForm,
        list,
        query,
        inventoryOptions,
        resultOptions,
        paramOptions,
    };
};
