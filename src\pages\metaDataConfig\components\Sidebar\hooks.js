import React, { useState, useMemo, useCallback, Fragment } from 'react';
import { useDeepCompareEffect, useMount, useSize, useUpdateEffect } from 'ahooks';
import { Modal, message } from 'antd';
import { useService } from '@share/framework';
import MetaFieldApi from '@/services/MetaFieldApi';
import cloneDeep from 'lodash/cloneDeep';
import { treeToArray, arrayToTree2 } from '@/utils/TreeUtil';
import { v4 as uuid } from 'uuid';

const findAndAddNode = (tree, id, newNode) => {
    // eslint-disable-next-line no-restricted-syntax
    for (const node of tree) {
        if (node.id === id) {
            if (!node.children) {
                node.children = [];
            }
            node.children.push(newNode);

            return;
        }
        if (node.children) {
            findAndAddNode(node.children, id, newNode);
        }
    }
};

const useHooks = ({ treeDataSource, refresh, onSelect, selectNode, isEdit = true }) => {
    const services = useService(MetaFieldApi);
    // const pageSize = useSize(document.getElementById('root'));
    const [gData, setGData] = useState([]);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [autoExpandParent, setAutoExpandParent] = useState(true);
    const [searchValue, setSearchValue] = useState('');

    const updateAllTagList = async (params, dragNodeId) => {
        // 遍历树并修改sort值
        const updateSort = () => {
            const treeData = cloneDeep(params);

            const resetSort = (tree, parent) => {
                if (tree) {
                    tree.forEach((node, index) => {
                        const nodeSort = `${index + 1}`.padStart(3, '0');
                        const newSort = parent.sort ? `${parent.sort}${nodeSort}` : `${nodeSort}`;
                        // eslint-disable-next-line no-param-reassign
                        node.sort = newSort;
                        // eslint-disable-next-line no-param-reassign
                        node.parentId = parent.id || '';
                        // eslint-disable-next-line no-param-reassign
                        node.nodeType = (node?.children || [])?.length === 0 ? '1' : '2';
                        if (node.children) {
                            resetSort(node.children || [], node);
                        }
                    });
                }
            };
            resetSort(treeData, '');

            return treeData;
        };

        // 调用函数并展示修改后的树
        const newData = updateSort();
        const data = treeToArray(newData);
        // if (!isOnDrop) {
        // 编写一个循环来遍历数组中的每个对象
        for (let i = 0; i < data.length; i++) {
            // 检查每个对象的name、queryRange和tagStyle属性是否都有值
            if (!data[i].name) {
                // 如果发现任何一个对象中有属性没有值的情况，就跳出循环并阻止后续代码的运行

                message.error('请填写标签名称', 1.5, () => {
                    console.log('请填写标签名称', data[i]);
                    onSelect(data[i].id, { node: data[i] });
                });

                return;
            }
            if (!data[i].queryRange) {
                // 如果发现任何一个对象中有属性没有值的情况，就跳出循环并阻止后续代码的运行
                message.error('请选择使用范围', 1.5, () => {
                    console.log('请选择使用范围', data[i]);
                    onSelect(data[i].id, { node: data[i] });
                });

                return;
            }
            if (!data[i].tagStyle) {
                // 如果发现任何一个对象中有属性没有值的情况，就跳出循环并阻止后续代码的运行

                message.error('请选择标签样式', 1.5, () => {
                    console.log('请选择标签样式', data[i]);
                    onSelect(data[i].id, { node: data[i] });
                });

                return;
            }
        }
        // 使用find方法查找具有给定id的对象
        // 获取到当前移动的节点，如果有，则默认选中当前移动完成的节点
        const result = data.find((obj) => obj.id === dragNodeId);
        if (result) {
            onSelect(result?.id, {
                node: result,
            });
        }
        // }
        // 将数据转换成树结构的数据
        const dataTree = arrayToTree2(
            data,
            (item) => !item.parentId || data.every((one) => one.id !== item.parentId),
            (one, two) => one.id === two.parentId,
            (a, b) => (a.sort || 0) - (b.sort || 0),
            (item, children) => ({
                ...item,
                key: item.id,
                title: item.name,
                value: item.id,
                children: children.length > 0 ? children.map((i) => ({ ...i })) : null,
            })
        );
        setGData(dataTree);
        const res = await services.updateAllTagList(data);
        // if (res) {
        // }
    };
    const getParentKey = (key, tree) => {
        let parentKey;
        for (let i = 0; i < tree.length; i++) {
            const node = tree[i];
            if (node.children) {
                if (node.children.some((item) => item.key === key)) {
                    parentKey = node.key;
                } else if (getParentKey(key, node.children)) {
                    parentKey = getParentKey(key, node.children);
                }
            }
        }

        return parentKey;
    };
    const onExpand = (newExpandedKeys) => {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(false);
    };
    const onChange = (value) => {
        const newExpandedKeys = treeDataSource.dataSource
            .map((item) => {
                if (item.name.indexOf(value) > -1) {
                    return getParentKey(item.id, treeDataSource.treeData);
                }

                return null;
            })
            .filter((item, i, self) => item && self.indexOf(item) === i);
        setExpandedKeys(newExpandedKeys);
        setSearchValue(value);
        setAutoExpandParent(true);
    };
    const onDrop = (info) => {
        console.log('onDrop', info);
        const dropKey = info.node.key;
        const dragKey = info.dragNode.key;
        const dropPos = info.node.pos.split('-');
        const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

        // eslint-disable-next-line consistent-return
        const loop = (data, key, callback) => {
            for (let i = 0; i < data.length; i++) {
                if (data[i].key === key) {
                    return callback(data[i], i, data);
                }
                if (data[i].children) {
                    loop(data[i].children, key, callback);
                }
            }
        };
        const data = cloneDeep([...gData]);

        // Find dragObject
        let dragObj;
        loop(data, dragKey, (item, index, arr) => {
            arr.splice(index, 1);
            dragObj = item;
        });
        // dropToGap：boolean类型，true代表拖拽到节点之间的缝隙中，false代表拖拽到节点上，即节点的内容区。
        // dropPosition：拖拽的时候，针对一个节点有三种情况，即拖拽到节点之上，拖拽到节点上，拖拽到节点之下。

        if (!info.dropToGap) {
            // Drop on the content
            loop(data, dropKey, (item) => {
                // eslint-disable-next-line no-param-reassign
                item.children = item.children || [];
                // where to insert 示例添加到头部，可以是随意位置
                item.children.unshift(dragObj);
            });
        } else if (
            (info.node.props.children || []).length > 0 &&
            // Has children
            info.node.props.expanded &&
            // Is expanded
            dropPosition === 1 // 添加到尾巴
        ) {
            loop(data, dropKey, (item) => {
                // eslint-disable-next-line no-param-reassign
                item.children = item.children || [];
                // where to insert 示例添加到头部，可以是随意位置
                item.children.unshift(dragObj);

                // in previous version, we use item.children.push(dragObj) to insert the
                // item to the tail of the children
            });
        } else {
            let ar = [];
            let i;
            loop(data, dropKey, (_item, index, arr) => {
                ar = arr;
                i = index;
            });

            // 添加到具体的索引的位置
            if (dropPosition === -1) {
                ar.splice(i, 0, dragObj);
            } else {
                ar.splice(i + 1, 0, dragObj);
            }
        }

        updateAllTagList(data, dragKey);
    };

    const removeTreeNode = useCallback(
        (node) => {
            Modal.confirm({
                content: '您正在删除标签内容，将导致相关主体无法标注，确认删除吗？',
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                    console.log('删除了');
                    if (node?.id?.includes('新建标签')) {
                        message.success('删除成功', 1.5, () => {
                            window.location.reload();
                        });
                    } else {
                        const res = await services.tagInfoRemove(node.id);
                        if (res) {
                            message.success('删除成功', 1.5, () => {
                                window.location.reload();
                            });
                        }
                    }
                },
            });
        },
        [services]
    );
    const addTreeNode = useCallback(
        (node) => {
            const newTree = cloneDeep(gData);
            const childrenLength = (node?.children || [])?.length;
            if (selectNode?.id?.includes('新建标签')) {
                return;
            }
            if (node) {
                const uuidStr = uuid();
                const nodeVal = {
                    name: '新建标签',
                    title: '新建标签',
                    id: `新建标签${uuidStr}`,
                    key: `新建标签${uuidStr}`,
                    nodeType: '1',
                    parentId: node.id,
                    // eslint-disable-next-line no-unsafe-optional-chaining
                    sort: `${node.sort}${`${childrenLength + 1}`.padStart(3, '0')}`,
                };
                findAndAddNode(newTree, node.id, nodeVal);
                setTimeout(() => {
                    onSelect('新建标签', { node: nodeVal });
                }, 0);
            } else {
                const uuidStr = uuid();
                const nodeVal = {
                    name: '新建标签',
                    title: '新建标签',
                    id: `新建标签${uuidStr}`,
                    key: `新建标签${uuidStr}`,
                    nodeType: '1',
                    parentId: '',
                    // eslint-disable-next-line no-unsafe-optional-chaining
                    sort: `${gData.length + 1}`.padStart(3, '0'),
                };
                newTree.push(nodeVal);
                onSelect('新建标签', { node: nodeVal });
            }

            setGData(newTree);
        },
        [gData, onSelect, selectNode?.id]
    );
    const treeData = useMemo(() => {
        const loop = (data) =>
            data.map((item) => {
                const strTitle = item.title;
                const index = strTitle.indexOf(searchValue);
                const beforeStr = strTitle.substring(0, index);
                const afterStr = strTitle.slice(index + searchValue.length);
                const filterText =
                    index > -1 ? (
                        <div>
                            {beforeStr}
                            <span style={{ color: 'red' }}>{searchValue}</span>
                            {afterStr}
                        </div>
                    ) : (
                        strTitle
                    );
                const title = (
                    <div className="treeNode">
                        {filterText}
                        {isEdit && (
                            <Fragment>
                                <i
                                    id="addTreeNode"
                                    className="add si si-com_increase"
                                    onClick={() => {
                                        addTreeNode(item);
                                    }}
                                />
                                {(item?.children || [])?.length === 0 && (
                                    <i
                                        className="remove si si-com_error-09"
                                        onClick={() => {
                                            removeTreeNode(item);
                                        }}
                                    />
                                )}
                            </Fragment>
                        )}
                    </div>
                );
                if (item.children) {
                    return {
                        ...item,
                        title,
                        key: item.key,
                        children: loop(item.children),
                    };
                }

                return {
                    ...item,
                    title,
                    key: item.key,
                };
            });
        console.log('gData', gData);

        return loop(gData);
    }, [gData, searchValue, isEdit, addTreeNode, removeTreeNode]);

    useDeepCompareEffect(() => {
        setExpandedKeys(treeDataSource.expandedKeys || []);
        setGData(treeDataSource.treeData);
    }, [treeDataSource.treeData]);

    return {
        treeData,
        onChange,
        onExpand,
        expandedKeys,
        autoExpandParent,
        // pageSize,
        onDrop,
        gData,
        addTreeNode,
    };
};

export default useHooks;
