// 工具类
import * as Entity from '@/components/common/common/Entity';
// 子组件
import Table from './FrontPageTable';

const tableCacheData = {};

class CacheFrontPageTable extends Table {
    static defaultProps = {
        ...Table.defaultProps,
        namespace: 'namespace', // 缓存key
    };

    // 初始化组件，从dva中获取数据
    componentWillMount() {
        const { dataSource, onChange, namespace } = this.props;
        const cacheData = this.getCacheData(namespace);
        const tableData = cacheData || { ...this.state, dataSource: [...dataSource] };

        this.setState({ ...tableData }, this.refreshTable);
        if (cacheData) {
            onChange && onChange(cacheData);
        }
    }

    // 当数据源发生变化时，当前页置为1
    componentWillReceiveProps(nextProps) {
        const { namespace } = this.props;
        const { dataSource: newDataSource, namespace: newNamespace } = nextProps;
        const {
            dataSource,
            page: { pageSize },
        } = this.state;

        if (namespace !== newNamespace || !Entity.isEqualObject(dataSource, newDataSource)) {
            this.setState({ dataSource: newDataSource, page: { currentPage: 1, pageSize } }, this.refreshTable);
        }
    }

    // 表格数据变化
    onTableDataChange = (data) => {
        const { namespace, onTableDataChange } = this.props;

        this.saveCacheData(namespace, data);
        onTableDataChange && onTableDataChange(data);
    };

    // 缓存数据
    saveCacheData = (namespace, data) => {
        tableCacheData[namespace] = data;
    };

    // 获取缓存数据
    getCacheData = (namespace) => {
        return tableCacheData[namespace];
    };
}

export default CacheFrontPageTable;
