import dva from 'dva';
// shareui
import 'bootstrap/dist/css/bootstrap.min.css';
import 'font-awesome/css/font-awesome.min.css';
import '@share/shareui-html';
import '@share/shareui-font';
import '@/pages/metaData/styles/share.scss';
import { RouterConfig } from '@/pages/metaData/router';

// 1. Initialize
const app = dva();

// 2. Plugins
// app.use({});

// 3. Model
// app.model(require('@/components/models/form'));
// app.model(require('@/components/models/table'));

// 4. Router
app.router(RouterConfig);

// 5. Start
app.start('#root');
