import { useEffect, useState } from 'react';
import { message } from '@share/shareui';
import { useForm } from '@share/shareui-form';
import { useList } from '@share/list';
import { useService, CodeService } from '@share/framework';
import InventoryService from '@/services/InventoryService';

export const useModel = () => {
    // 编辑
    const [showEdit, setShowEdit] = useState(false);
    const [, editForm] = useForm({}, {});
    // 列表
    const [inventoryList, setInventoryList] = useState([]);
    const [searchList, setSearchList] = useState([]);
    const list = useList({
        dataSource: searchList,
    });
    // 表单
    const [, form] = useForm({}, {});
    const [codeOptions, setCodeOptions] = useState([]);
    const filter = (condition, dataList) => {
        const { id, name, objectType, resultId, resultName, paramId, paramName, sourceType, sourceId, enabled } = condition;

        return dataList
            .filter((item) => !id || item.id.toUpperCase().includes(id.toUpperCase()))
            .filter((item) => !name || item.name.toUpperCase().includes(name.toUpperCase()))
            .filter((item) => !objectType || item.objectType === objectType)
            .filter((item) => !resultId || item.result.some((i) => i.id.toUpperCase().includes(resultId.toUpperCase())))
            .filter((item) => !resultName || item.result.some((i) => i.name.toUpperCase().includes(resultName.toUpperCase())))
            .filter((item) => !paramId || item.param.some((i) => i.id.toUpperCase().includes(paramId.toUpperCase())))
            .filter((item) => !paramName || item.param.some((i) => i.name.toUpperCase().includes(paramName.toUpperCase())))
            .filter((item) => !sourceType || item.source.type === sourceType)
            .filter((item) => !sourceId || item.source.id.toUpperCase().includes(sourceId.toUpperCase()))
            .filter((item) => !enabled || item.enabled === enabled);
    };
    const query = (condition) => {
        const result = filter(condition, inventoryList);
        setSearchList(result);
        list.jump(0);
    };
    // 请求
    const inventoryService = useService(InventoryService);
    const requestList = async () => {
        const result = await inventoryService.all();
        const filterResult = filter(form.getFormData(), result);

        setInventoryList(result);
        setSearchList(filterResult);
    };
    const codeService = useService(CodeService);
    const requestCode = async () => {
        const result = await codeService.getAll();

        const options = result.map((item) => ({ value: item.alias, label: item.alias }));
        setCodeOptions(options);
    };
    // 操作
    const add = () => {
        editForm.cleanValidError();
        editForm.setFormData({ objectType: 'ent', source: { type: 'sql' }, param: [], result: [], sortNum: 999, enabled: '1' });
        setShowEdit(true);
    };
    const edit = (row) => {
        editForm.cleanValidError();
        editForm.setFormData({ ...row, originalId: row.id });
        setShowEdit(true);
    };
    const editSubmit = async () => {
        if (!(await editForm.validHelper())) {
            return;
        }
        const data = editForm.getFormData();

        if (data.originalId) {
            await inventoryService.update(data);
            message.success('更新成功');
        } else {
            await inventoryService.add(data);
            message.success('新增成功');
        }
        await requestList();
        setShowEdit(false);
    };
    // 初始化
    useEffect(() => {
        requestList();
        requestCode();
    }, [inventoryService]);

    return {
        inventoryList,
        codeOptions,
        form,
        list,
        query,
        add,
        edit,
        editModal: {
            showEdit,
            hiddenEdit: () => setShowEdit(false),
            editForm,
            editSubmit,
        },
    };
};
