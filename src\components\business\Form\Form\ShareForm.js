import React, { Component } from 'react';
// 工具类
import { isEqualObject } from '@/components/common/common/Entity';
// 表单组件
import { FormState, getComponents } from '@share/shareui-form';

const { Form: SearchForm } = getComponents('div');
const { Form: EditForm } = getComponents();

class ShareForm extends Component {
    static defaultProps = {
        formData: {}, // 表单数据对象
        onChange: null, // 表单数据对应变更函数
        pageType: 'queryPage', // 表单显示类型
        children: null, // 表单Form子节点元素
        onRef: null, // 绑定本对象函数
    };

    state = {
        formState: new FormState({ ...this.props.formData }, (formState, callback) =>
            this.setState({ formState }, () => {
                this.refreshForm();
                callback && callback();
            })
        ),
    };

    // 自身引用传递给父组件
    componentDidMount() {
        const { onRef } = this.props;

        onRef && onRef(this);
    }

    // 父组件改值时，通知本组件变更
    componentWillReceiveProps(nextProps) {
        const { formData: newFormData } = nextProps;
        const { formState } = this.state;
        const formData = formState.getFormData();

        if (!isEqualObject(formData, newFormData)) {
            formState.setFormData(newFormData);
        }
    }

    // 列表变更事件
    onFormChange = (data) => {
        const { onChange } = this.props;

        onChange && onChange(data);
    };

    // 刷新数据
    refreshForm = () => {
        const { formState } = this.state;
        const formData = formState.getFormData();

        this.onFormChange(formData);
    };

    render() {
        const { children, pageType } = this.props;
        const { formState } = this.state;
        const Form = pageType === 'queryPage' ? SearchForm : EditForm;

        return (
            <Form pageType={pageType} formState={formState}>
                {children}
            </Form>
        );
    }
}

export default ShareForm;
