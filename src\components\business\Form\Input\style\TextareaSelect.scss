:global{
    ul {
        padding: 0;
        margin: 0;
        list-style: none;
    }
    .dsgcp {
        /*font-family: "Microsoft Himalaya";*/
        box-sizing: border-box;
        width: 100%;
        padding: 10px;
        border: 1px solid #d0d1d4;
        font-size: 12px;
    }
    .dsgcp .dsgcpList li {
        display: inline-block;
        height: 20px;
        border: 1px solid #c0ecff;
        border-radius: 3px;
        background: #eaf9ff;
        margin-right: 5px;
        margin-bottom: 10px;
        padding-left: 5px;
        color: #0099dd;
        vertical-align: top;
    }
    .dsgcp .dsgcpList li.changeValueArea {
        border: 1px solid #d0d1d4;
        background: #fff;
        padding: 0 5px;
    }
    .dsgcp .dsgcpList li.changeValueArea span {
        padding: 0;
    }
    .dsgcp .dsgcpList li.changeValueArea input{
        font-family: sans-serif;
        display: block;
        line-height: 17px;
        border: none;
        outline: none;
        font-size: 12px;
    }
    .dsgcp .dsgcpList li:hover {
        cursor: pointer;
    }
    .dsgcp .dsgcpList li input{
        display: inline-block;
    }
    .dsgcp .dsgcpList li span {
        display: inline-block;
        //line-height: 20px;
        padding-right: 5px;
        padding-left: 5px;
    }
    .dsgcp .inputArea {
        display: block;
        height: 30px;
        width: 100%;
        border: none;
        outline: none;
    }
}

.multiInput {
    padding: 0 10px;
    max-height: 180px;
    overflow: auto;

    :global{

        input{
            width: 100% !important;
        }

        .dsgcpList{
            margin-bottom: 0!important;
        }

        li{
            margin: 5px 5px 5px 0!important;
        }
    }
}
