/*
 * @Author: your name
 * @Date: 2021-08-23 09:24:06
 * @LastEditTime: 2021-10-11 16:55:40
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /credit-management-front-3.1/credit_common_front/src/components/business/Form/Form/cache/DvaShareForm.js
 */
// dva
import { connect } from 'dva';
// 表单组件
import Form from './CacheShareForm';

class DvaShareForm extends Form {
    static defaultProps = {
        ...Form.defaultProps,
    };

    // 缓存数据
    saveCacheData = (namespace, data) => {
        const { dispatch } = this.props;

        dispatch({
            type: 'form/refreshFormData',
            namespace,
            ...data,
        });
    };

    // 获取缓存数据
    getCacheData = (namespace) => {
        const { form } = this.props;

        return form[namespace];
    };
}

export default connect(({ form }) => ({ form }))(DvaShareForm);
