function b64EncodeUnicode(str) {
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => String.fromCharCode(`0x${p1}`)));
}

function b64EncodeUnicodeObj(obj) {
    return b64EncodeUnicode(JSON.stringify(obj));
}

function b64DecodeUnicode(str) {
    return decodeURIComponent(
        atob(str)
            .split('')
            .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
            .join('')
    );
}
module.exports = {
    b64EncodeUnicode,
    b64EncodeUnicodeObj,
    b64DecodeUnicode,
};
