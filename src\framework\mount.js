import { createApplication } from '@share/framework';
import { PresetShareui } from '@share/framework-preset-shareui';
import { version, ConfigProvider } from 'antd';
import React from 'react';
import zhCN from 'antd/es/locale/zh_CN';
import { initCode } from './code';
import { initConfig } from './config';

export const mount = (routes) => {
    createApplication({
        appId: 'application-v1',
        presets: [
            PresetShareui({
                router: {
                    routes,
                },
                view: {
                    dynamicContainer: true,
                },
                code: {
                    init: initCode,
                },
                config: {
                    init: initConfig,
                },
            }),
        ],
        plugins: [
            {
                name: 'antd',
                setup(context) {
                    context.addAppProvider(({ children }) => {
                        return <ConfigProvider locale={zhCN}>{children}</ConfigProvider>;
                    });
                },
            },
        ],
    });
};
