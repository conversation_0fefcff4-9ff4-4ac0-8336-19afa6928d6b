import React, { Component } from 'react';
import { Rate } from 'antd';

import styles from './style/Rate.scss';

class RateCustom extends Component {
    hanleChange = (value) => {
        const { onChange } = this.props;

        onChange && onChange({ target: { value } });
    };

    render() {
        const { value, ...restProps } = this.props;

        return (
            <div className={styles.rate}>
                <Rate {...restProps} onChange={this.hanleChange} />
                <span className={styles.value}>{value}分</span>
            </div>
        );
    }
}
export default RateCustom;
