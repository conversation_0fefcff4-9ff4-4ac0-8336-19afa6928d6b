import { MathUtil } from '@share/common';
import Decimal from 'decimal.js';
import { v4 as uuid } from 'uuid';
import dayjs from "dayjs";

export const emptyDefault = (d, formatter = '--') => {
    return d || formatter;
};

// 金额格式化
// 目前是保留两位小数
export const moneyFormat = (val, formatter) => {
    // if (val === 0 || val === '0') {
    //     return 0;
    // }
    if (!val) {
        return emptyDefault('', formatter);
    }

    // val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    // return String(MathUtil.round(val, 2)).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
    return String(MathUtil.round(val, 2)).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 金额格式化带单位的
// 目前是保留两位小数
export const moneyFormatUnit = (val, unit = '万元') => {
    if (val === 0 || val === '0') {
        return 0 + unit;
    }
    if (!val) {
        return emptyDefault('', '--');
    }
    const money = String(MathUtil.round(val, 2)).replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return money + unit;
};

// 数字格式化为千分位字符串
export const numberFormatTP = (val, toFixed, formatter, addCharacter = true) => {
    // if (val === 0 || val === '0') {
    //     return 0;
    // }

    const fixed = toFixed || 2;
    if (!val) {
        return emptyDefault('', formatter);
    }

    if (fixed) {
        if (addCharacter) {
            const v = String(MathUtil.round(val, fixed)).replace(/\B(?=(\d{3})+(?!\d))/g, ',');

            return v;
        }

        return String(MathUtil.round(val, fixed));
        // return Number(val).toFixed(toFixed).toLocaleString('en-US');

        // return Number(val)
        //     .toFixed(toFixed)
        //     .replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
        // return String(MathUtil.round(val, toFixed)).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    return Number(val).toLocaleString('en-US');
};

// 小数点后面补足两个0
export const keepTwoDecimal = (val) => {
    const valStr = val.toString();
    const arrayNum = valStr.split('.');

    if (arrayNum.length === 1) {
        return `${valStr}.00`;
    }

    if (arrayNum.length > 1) {
        // 小数点右侧 如果小于两位 则补一个0
        if (arrayNum[1].length < 2) {
            return `${valStr}0`;
        }

        return valStr;
    }
};

// 金额格式化 - 后续陆续替换成这个
// 1、元转万元；2、千分位；3、暂不保留两位小数
export const moneyFormatThousandth = (val, dividedByTenThousand = true, roundNumber = 2) => {
    if (!val) {
        return '--';
    }
    let valHandle = Number(val);

    if (dividedByTenThousand) {
        valHandle = new Decimal(valHandle).div(new Decimal(10000)).toNumber();
    }

    if (roundNumber >= 0) {
        valHandle = Number(MathUtil.round(valHandle, roundNumber).toFixed(roundNumber));
    }

    const formatThousandsVal = MathUtil.formatThousands(valHandle);

    return roundNumber === 2 ? keepTwoDecimal(formatThousandsVal) : formatThousandsVal;
};

export const moneyThonsand = (val) => {
    if (val) {
        return Number(val) / 10000;
    }

    return 0;
};

// 字段转码
export const fieldTranslate = (val, bmArr) => {
    if (!val || !Array.isArray(bmArr)) {
        return '--';
    }

    return bmArr.find((v) => v.value === val)?.label || val;
};

export const fieldTranslateBoolean = (val) => {
    if (!val) {
        return '--';
    }

    return val === '1' ? '是' : val === '0' ? '否' : '--';
};

// 时间格式化
// 2023-12-12 12:12:12 -> 2023-12-12
export const timeFormat = (time) => {
    if (!time) {
        return '--';
    }

    return String(time).split(' ')[0] || '--';
};

// 计算出列表总数
export const listCount = (arr) => {
    if (Array.isArray(arr)) {
        return arr.length;
    }

    return 0;
};

export const stringLengthFormat = (val, len) => {
    if (!val || !len) {
        return '';
    }

    if (String(val).length <= len) {
        return val;
    }

    return `${String(val).substring(0, 8)}...`;
};

export const calendarDataFormat = {
    data: 'YYYY-MM-DD',
    display: 'YYYY-MM-DD',
};

// 拼接完整的文件地址
export const getFullFileUrl = (fileUrl) => {
    return `${window.location.origin}/edp-front/api/file/file.do?fileId=${fileUrl}`;
};

// 手机号格式化
export const telFormat = (tel) => {
    if (typeof tel === 'string' && tel.length === 11) {
        return `${tel.slice(0, 3)} ${tel.slice(3, 7)} ${tel.slice(7)}`;
    }

    return tel;
};

// 获取政策-执行部门('1,2,3')的name
export const getExecuteDeptName = (executeDept = '', ZXBMDM) => {
    if (!executeDept) {
        return '--';
    }
    const executeDeptArr = executeDept.split(',');
    const executeDeptNameArr = [];

    executeDeptArr.forEach((deptId) => {
        const findRes = ZXBMDM.find((itemBM) => itemBM.code === deptId);

        executeDeptNameArr.push(findRes?.name || deptId);
    });

    return executeDeptNameArr.join('，');
};

// 将[{"interviewOpen":"0","serviceType":null,"interviewTime":null,"type":null,"newOrgFollow":null,"id":26,"enterpriseUnitName":null,"demand":null,"draftType":null,"receptionPersonnel":null,"thirdName":"C01","operate":null,"interviewContent":null,"name":null,"parkFollowName":"","enterpriseUnitId":null,"serviceTypeName":"","createName":null,"status":"0","interviewPersonnelName":null,"newOrgFollowName":"","interviewCompanyName":null,"parkAdminId":null,"typeName":"","firstName":"软件园三期（B区）","delFlag":"0","thirdId":null,"categoryName":"","interviewRemarks":null,"demandName":"","updateBy":"79c3b606e43e5e6ce3b7ec5188f24d50","parkAdminName":null,"operateName":"","thirdName":"厦门畅享信息技术有限公司术","annex":[],"interviewAddress":null,"demandOther":null,"handleTime":null,"interviewObject":null,"firstId":null,"parkFollow":null,"updateTime":"2023-12-18 16:57:57","interviewCompanyId":null,"createBy":"79c3b606e43e5e6ce3b7ec5188f24d50","followContent":null,"createTime":"2023-12-18 16:57:57","interviewPersonnelId":null,"thirdId":"1","category":null,"taskId":23}]
// 这样的数据转换为树组件需要的数据格式，一般是园区、地块、楼栋、企业这样
// 定义一个函数，将扁平数组转换为树形结构
export const convertToTree = (inputArray) => {
    // 创建一个空的树形结构
    const tree = [];
    const expandedKeys = [];
    const map = ['firstId', 'secondId', 'thirdId', 'fourthId'];
    const nameMap = ['firstName', 'secondName', 'thirdName', 'fourthName'];

    inputArray.forEach((item, index) => {
        let currentLevel = tree;

        map.forEach((id, level) => {
            if (item[id]) {
                const key = `${id}-${item[id]}-${item[nameMap[level]]}${index}`;
                let node = currentLevel.find((n) => n.value === item[id]);

                if (!node) {
                    node = {
                        ...item,
                        name: item[nameMap[level]],
                        title: item[nameMap[level]],
                        key,
                        value: item[id],
                        sort: level + 1,
                        children: [],
                    };
                    currentLevel.push(node);
                    expandedKeys.push(key);
                }

                currentLevel = node.children;
            }
        });
    });
    // // 创建一个函数，用于在父节点中查找或创建子节点
    // const findOrCreateChild = (parentNode, childProps) => {
    //     const identifier = childProps.id ? `${childProps.id}-${childProps.name}` : childProps.name;
    //     // const key = `${childProps.prefix}-${identifier}`;
    //     const key = uuid();
    //     // if (!map.has(key)) {}
    //     let childNode = parentNode.find((node) => node.key === key);
    //     if (!childNode) {
    //         childNode = {
    //             ...childProps,
    //             key,
    //             name: childProps.name,
    //             title: childProps.name,
    //             value: childProps.id,
    //             sort: childProps.level,
    //             children: [],
    //         };
    //         parentNode.push(childNode);
    //     }
    //     expandedKeys.push(key);

    //     return childNode.children;
    // };
    // inputArray.forEach((item, index) => {
    //     const { firstId, firstName, secondId, secondName, thirdId, thirdName, fourthId, fourthName } = item;

    //     let currentLevel = findOrCreateChild(tree, { id: firstId, name: firstName, prefix: 'firstId', level: 1, index });
    //     if (secondId) {
    //         currentLevel = findOrCreateChild(currentLevel, { id: secondId, name: secondName, prefix: 'secondId', level: 2, index });
    //     }
    //     if (thirdId) {
    //         currentLevel = findOrCreateChild(currentLevel, { id: thirdId, name: thirdName, prefix: 'thirdId', level: 3, index });
    //     }
    //     if (fourthId) {
    //         findOrCreateChild(currentLevel, { id: fourthId, name: fourthName, prefix: 'fourthId', level: 4, index });
    //     }
    // });
    // // 遍历输入数组的每个元素
    // inputArray.forEach((item, index) => {
    //     const firstIdKey = `firstId-${item.firstId}-${item.firstName}${index}`;
    //     const secondIdKey = `secondId-${item.secondId}-${item.secondName}${index}`;
    //     const thirdIdKey = `thirdId-${item.thirdId}-${item.thirdName}${index}`;
    //     const fourthIdKey = `fourthId-${item.fourthId}-${item.fourthName}${index}`;

    //     // 获取当前元素的 parkId
    //     const { firstId } = item;
    //     // 在树中查找是否已经存在具有相同 parkId 的节点
    //     let parkNode = tree.find((node) => node.value === firstId);

    //     // 如果不存在，创建一个新的 park 节点
    //     if (!parkNode) {
    //         parkNode = {
    //             ...item,
    //             name: item.firstName,
    //             title: item.firstName,
    //             key: firstIdKey,
    //             value: item.firstId,
    //             sort: 1,
    //             children: [],
    //         };
    //         expandedKeys.push(firstIdKey);

    //         tree.push(parkNode);
    //     }

    //     // 获取当前元素的 buildingId
    //     const { secondId } = item;

    //     if (secondId) {
    //         // 在 park 节点的 children 中查找是否已经存在具有相同 buildingId 的节点
    //         let buildingNode = parkNode.children.find((node) => node.value === secondId);
    //         expandedKeys.push(secondIdKey);
    //         // 如果不存在，创建一个新的 building 节点
    //         if (!buildingNode) {
    //             buildingNode = {
    //                 ...item,
    //                 name: item.secondName,
    //                 title: item.secondName,
    //                 key: secondIdKey,
    //                 value: item.secondId,
    //                 sort: 2,
    //                 children: [],
    //             };
    //             parkNode.children.push(buildingNode);
    //         }

    //         // 获取当前元素的 unitId
    //         const { thirdId } = item;

    //         if (thirdId) {
    //             // 在 building 节点的 children 中查找是否已经存在具有相同 unitId 的节点
    //             let unitNode = buildingNode.children.find((node) => node.value === thirdId);
    //             expandedKeys.push(thirdIdKey);
    //             // 如果不存在，创建一个新的 unit 节点
    //             if (!unitNode) {
    //                 unitNode = {
    //                     ...item,
    //                     name: item.thirdName,
    //                     title: item.thirdName,
    //                     // value: `unitId-${item.unitId}`,
    //                     key: thirdIdKey,
    //                     value: item.thirdId,
    //                     sort: 3,
    //                     children: [],
    //                 };
    //                 buildingNode.children.push(unitNode);
    //             }
    //             // 获取当前元素的 enterpriseId
    //             const { fourthId } = item;

    //             if (fourthId) {
    //                 // 在 unit 节点的 children 中查找是否已经存在具有相同 enterpriseId 的节点
    //                 let enterpriseNode = unitNode.children.find((node) => node.value === fourthId);
    //                 expandedKeys.push(fourthIdKey);
    //                 // 如果不存在，创建一个新的 enterprise 节点
    //                 if (!enterpriseNode) {
    //                     enterpriseNode = {
    //                         ...item,
    //                         name: item.fourthName,
    //                         title: item.fourthName,
    //                         key: fourthIdKey,
    //                         value: item.fourthId,
    //                         sort: 4,
    //                         children: [],
    //                     };
    //                     unitNode.children.push(enterpriseNode);
    //                 }
    //             }
    //         }
    //     }
    // });

    // 返回构建好的树形结构
    return {
        treeData: tree,
        expandedKeys: ['all', ...new Set(expandedKeys)],
    };
};

export const timeToStr = (val) => (val ? dayjs(Number(val)).format('YYYY-MM-DD') : '-');
