function checkNotNull(str) {
    return typeof str !== 'undefined' && str !== null;
}

function checkNotBlank(str) {
    return typeof str !== 'undefined' && str !== null && str.toString().trim().length !== 0;
}

function checkRichTextNotBlank(str) {
    return (
        typeof str !== 'undefined' &&
        str !== null &&
        str
            .toString()
            .trim()
            .replace(/<p>(&nbsp;)*(<br>)*<\/p>/g, '').length !== 0
    );
}

function checkIsNum(str) {
    return new RegExp(/(?!((^-?0+(\d)+$)|(^-?0{2,}\..*$)))((^-?(\d)+$)|(^-?(\d)+\.(\d)+$))/).exec(str);
}

function checkIsInteger(str) {
    return new RegExp(/(?!(^-?0+(\d)+$))(^-?(\d)+$)/).exec(str);
}

function checkIsPositiveInteger(str) {
    return new RegExp(/^[0-9]*[1-9][0-9]*$/).exec(str);
}

function checkTel(str) {
    const text = str.replace(/[\(\()\)]/g, '');

    return new RegExp(/^1\d{10}$|^0\d{2,3}-?\d{7,8}$/).exec(text);
}

function checkMobile(str) {
    return new RegExp(/^1\d{10}$/).exec(str);
}

function checkIsEmail(str) {
    return new RegExp(/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+((\.\w+)+)$/).exec(str);
}

function checkArrayRepeat(arr, field) {
    for (let i = 0; i < arr.length - 1; i++) {
        for (let j = i + 1; j < arr.length; j++) {
            if ((field && arr[i][field] === arr[j][field]) || (!field && arr[i] === arr[j])) {
                return true;
            }
        }
    }

    return false;
}

export {
    checkNotNull,
    checkNotBlank,
    checkRichTextNotBlank,
    checkIsNum,
    checkIsInteger,
    checkIsPositiveInteger,
    checkTel,
    checkMobile,
    checkIsEmail,
    checkArrayRepeat,
};
