import React, { Component } from 'react';
// 被封装组件
import { Button } from '@share/shareui';
import Calendar from './Calendar';
import '../style/index.scss';

const defaultFormat = {
    data: 'YYYY-MM-DD',
    display: 'YYYY-MM-DD',
};

class Date extends Component {
    render() {
        const { showLongTimeButton, ...restProps } = this.props;

        return (
            <div style={{ width: '100%' }}>
                {showLongTimeButton && (
                    <Button
                        style={{ width: '60px', float: 'right' }}
                        onClick={() => this.props.onChange({ target: { value: '2099-12-31' } })}
                    >
                        长期
                    </Button>
                )}
                <Calendar format={defaultFormat} {...restProps} className={showLongTimeButton ? 'calendarMarginRight' : ''} />
            </div>
        );
    }
}

export default Date;
