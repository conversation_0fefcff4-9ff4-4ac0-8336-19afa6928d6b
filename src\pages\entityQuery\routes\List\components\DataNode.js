import React from 'react';
import { registerFormItem } from '@/utils/shareFormUtil';
import DataValueNode from './DataValueNode';
import DataComposeNode from './DataComposeNode';

const DataNode = (props) => {
    const { value, field, inventoryList, prefix, suffix } = props;
    const nodeType = Object.keys(value || {}).includes('composeLogic') ? 'compose' : 'value';
    const NodeCom = nodeType === 'compose' ? DataComposeNode : DataValueNode;

    return <NodeCom field={field} inventoryList={inventoryList} noView prefix={prefix} suffix={suffix} />;
};

export default registerFormItem(DataNode);
