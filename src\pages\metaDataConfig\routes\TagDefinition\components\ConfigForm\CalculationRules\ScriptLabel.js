import React, { Fragment, useState } from 'react';
import {TableF<PERSON>, Textarea, Row, useForm} from "@share/shareui-form";
import {<PERSON><PERSON>, Modal} from '@share/shareui';
import {message} from "antd";
import NoData from "@/components/NoData";
import ShareList, {Column, NumberColumn, useList} from "@share/list";
import {useService} from "@share/framework";
import MetaFieldApi from "@/services/MetaFieldApi";

const ScriptLabel = ({ selectNode }) => {
    const [showEditModal, setShowEditModal] = useState(false);
    const services = useService(MetaFieldApi);
    const [fileList, setFileList] = useState([]);
    const [, form] = useForm({"sql":"SELECT ZZMC ent_name, TYSHXYDM credit_code\nFROM T_ZZK_JC WHERE TYSHXYDM = \"\";"});


    function scriptLabelPreview() {
        const params = {
            tagId: selectNode.id,
            sql: form.getFormData().sql
        };
        services.tagEntPreview(params).then((res) => {
            setFileList(res);
            setShowEditModal(true)
        });
    }

    function scriptLabelExec() {
        const params = {
            tagId: selectNode.id,
            sql: form.getFormData().sql
        };
        services.tagEntConfirm(params).then((res) => {
            message.success(`执行完成，最终打标企业数：${res}家`);
            setShowEditModal(false)
        });
    }

    return (
        <Fragment>
            <TableForm formState={form}>
                {/*<Row>*/}
                {/*    <Select field="db" label="数据库" options={options} />*/}
                {/*</Row>*/}
                <Row>
                    <Textarea field="sql" label="查询片段"/>
                </Row>
            </TableForm>
            <div style={{ color: "red"}}>TIP：sql需要按照默认的返回的统一社会信用代码和企业名称进行返回，请谨慎使用，不要尝试执行复杂sql</div>
            <div style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button onClick={scriptLabelPreview}>脚本打标执行</Button>
            </div>
            <Modal show={showEditModal} onHide={() => setShowEditModal(false)} backdrop="static">
                <Modal.Header closeButton>脚本打标预览</Modal.Header>
                <Modal.Body>
                    <ShareList dataSource={fileList} usePageBar={false} emptyText={<NoData/>}>
                        <NumberColumn/>
                        <Column label="企业名称" field="entName"/>
                        <Column label="统一社会信用代码" field="creditCode"/>
                    </ShareList>
                    <div
                        style={{color: "red"}}>TIP：查询出来的企业作为一个粗略参考，最多展示100家，并且没有排除已打过的，仅在最终执行时忽略已打标企业
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={() => setShowEditModal(false)}>取消</Button>
                    <Button bsStyle="primary" onClick={scriptLabelExec}>
                        执行
                    </Button>
                </Modal.Footer>
            </Modal>
        </Fragment>
    );
};
export default ScriptLabel;
