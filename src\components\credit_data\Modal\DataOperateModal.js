import React, { Component } from 'react';
import * as formRule from '@/components/common/common/formRule';
import { Modal, Button } from '@share/shareui';
import { getComponents } from '@/components/business/Form';

const { Form, Row, Select, Textarea } = getComponents();
// 表码管理器
const bmManage = require('@/components/common/business/manager/BmManager');

const actionMap = {
    3: '修改',
    4: '上线',
    5: '下线',
    7: '归档',
    9: '删除',
};

const actionTip = {
    3: '本次修改将同步本平台中所有数据，并且可能影响对外共享(如：国家信用平台)的信息，请谨慎操作。',
    4: '本操作数据将进入“公示”状态该状态下数据将可以在互联网中查看。',
    5: '本操作数据将进入“下线”状态该状态下数据将无法在互联网中查看。',
    7: '本操作数据将进入“归档”状态该状态下数据将无法应用（即不可在本平台中任何模块查看）',
    9: '本操作将使数据不在系统中留痕（即无可查询）且不可恢复请谨慎使用该操作！',
};

class DataOperateModal extends Component {
    render() {
        const { formState, show, submitFn, cancelFn } = this.props;
        const { action } = formState.getFormData();
        const reasonOptions = bmManage
            .getBmList('DM_DATA_AUDIT_ACTION_REASON')
            .filter((item) => item.code.startsWith(action))
            .map((item) => ({ label: item.label, value: item.code }));

        return (
            <Modal show={show} onHide={cancelFn}>
                <Modal.Header closeButton>{actionMap[action]}提示</Modal.Header>
                <Modal.Body full>
                    <Form pageType="addPage" formState={formState}>
                        <Row>
                            <Select
                                label="操作原因"
                                field="reason"
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                                options={reasonOptions}
                                rows="6"
                            />
                        </Row>
                        <Row>
                            <Textarea
                                label="操作说明"
                                field="explain"
                                rule={[formRule.checkRequiredNotBlank(), formRule.checkLength(1, 200)]}
                                required
                                placeholder="请输入"
                                rows="6"
                            />
                        </Row>
                    </Form>
                    <div style={{ display: 'flex', padding: '12px', fontSize: '12px' }}>
                        <div style={{ width: '136px', color: 'orange', paddingRight: '12px', textAlign: 'right' }}>温馨提示：</div>
                        <div style={{ flex: 1 }}>{actionTip[action]}</div>
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={submitFn}>
                        确定
                    </Button>
                    <Button onClick={cancelFn}>取消</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default DataOperateModal;
