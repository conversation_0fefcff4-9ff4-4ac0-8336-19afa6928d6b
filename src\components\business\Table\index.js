// 特殊缓存高级函数
// import getDvaCacheTable from './cache/getDvaCacheTable';
import getStoreCacheTable from './cache/getStoreCacheTable';
// antd
import AntdTable from './ui/CommonUiTable';
// 无分页
import NoPageTable from './noPage/CommonNoPageTable';
// 分页
import CommonPageTable from './ui/CommonPageTable';
// 前端分页
import FrontPageTable from './frontPage/FrontPageTable';
import CacheFrontPageTable from './frontPage/CacheFrontPageTable';

// 后端分页
import ServicePageTable from './servicePage/ServicePageTable';
import CacheServicePageTable from './servicePage/CacheServicePageTable';
// const DvaFrontPageTable = getDvaCacheTable(CacheFrontPageTable);
const StoreFrontPageTable = getStoreCacheTable(CacheFrontPageTable);
// const DvaServicePageTable = getDvaCacheTable(CacheServicePageTable);
const StoreServicePageTable = getStoreCacheTable(CacheServicePageTable);

// 拖拽工具
// import DragSortingTable from './DragTool';

export {
    AntdTable,
    NoPageTable,
    CommonPageTable,
    FrontPageTable,
    CacheFrontPageTable,
    // DvaFrontPageTable,
    StoreFrontPageTable,
    ServicePageTable,
    CacheServicePageTable,
    // DvaServicePageTable,
    StoreServicePageTable,
    // DragSortingTable,
};
