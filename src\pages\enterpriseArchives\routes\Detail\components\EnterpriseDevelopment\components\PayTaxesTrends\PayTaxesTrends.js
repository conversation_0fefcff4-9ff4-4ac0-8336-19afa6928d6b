import React, { useRef } from 'react';
import { Panel } from '@share/shareui';
import ReactEcharts from 'echarts-for-react';
import { useBindEchartResize } from '@/utils/echartResize';
import { emptyDefault } from '@/utils/format';
import styles from './PayTaxesTrends.scss';
import { usePayTaxesTrendsHandle } from './hook';

const PayTaxesTrends = ({ data = [] }) => {
    const echartspieRef = useRef({});
    useBindEchartResize(echartspieRef);
    // const { option } = usePayTaxesTrendsHandle({
    //     czsrLineData: [100,200,300],
    //     czzcLineData: [400,500,600],
    //     fczcLineData: [700,800,1000],
    //     xAxisData: ['2022年','2023年','2024年'],
    // });
    const { option } = usePayTaxesTrendsHandle({
        czsrLineData: (data || []).map((v) => v.czzsr),
        czzcLineData: (data || []).map((v) => v.czzzc),
        fczcLineData: (data || []).map((v) => v.fczzc),
        xAxisData: (data || []).map((v) => `${v.year}年`),
    });

    return (
        <div className={styles.payTaxesTrends}>
            <Panel>
                <Panel.Head title="扶持成效" />
                <Panel.Body full>
                    <div className={styles.body}>
                        <div className={styles.echartWrap}>
                            <ReactEcharts
                                option={option}
                                notMerge
                                lazyUpdate
                                ref={echartspieRef}
                                style={{ width: '100%', height: '100%' }}
                            />
                        </div>
                    </div>
                </Panel.Body>
            </Panel>
        </div>
    );
};

export default PayTaxesTrends;
