const { shareKitConfigHelper } = require('@share/kit');
const { importer, jqueryGlobal } = require('./bin/webpack.config.block');
// 提供了配置提示

// 额外配置，会和默认配置合并
// env下的配置会和其他配置合并，对应不同环境下的最终配置
// 其他配置项请查看文档 http://*************:20000/share-tools/share-kit

module.exports = shareKitConfigHelper({
    extraBabelOptions: {
        plugins: [...importer],
    },
    extraProvidePlugin: {
        ...jqueryGlobal,
    },
    // extraRules: [
    //     {
    //         test: /\.jsx?$/,
    //         exclude: /node_modules/,
    //         use: ['babel-plugin-add-module-exports'],
    //     },
    // ],
    proxy: {
        // 代理配置，内置.do代理
        '/aaa/ab': {
            target: 'http://localhost:4000',
            changeOrigin: true,
            pathRewrite: {
                '^/aaa/ab': '', // 把 /api 前缀去掉
            },
        },
    },
    define: {
        // 定义编译时的环境变量
        // DEBUG: true
        CONTEXT_PATH: '',
    },
    alias: {
        // moment: 'node_modules/moment',
        // '@babel/runtime': 'node_modules/@share/kit/node_modules/@babel/runtime',
        // jquery: 'node_modules/jquery',
        // '@share/network': 'node_modules/@share/network',
        // '@share/form': 'node_modules/@share/form',
    },
    // devtool: 'source-map', // 内置自动判断
    devServer: {},
    externals: {
        '@mshare/mshareui': 'window.MSHARE', // PC端屏蔽@mshare/mshareui组件引用
    },
});
