import { Select, Spin } from 'antd';
import debounce from 'lodash/debounce';
import React, { useCallback, useMemo, useRef, useState } from 'react';

const { Option } = Select;
const DebounceSelect = ({ fetchOptions, debounceTimeout = 800, isCreated = false, onChange, ...props }) => {
    const [fetching, setFetching] = useState(false);
    const [options, setOptions] = useState([]);
    const fetchRef = useRef(0);
    const debounceFetcher = useMemo(() => {
        const loadOptions = (value) => {
            if (!value) {
                setOptions([]);

                return;
            }
            fetchRef.current += 1;
            const fetchId = fetchRef.current;

            setOptions([]);
            setFetching(true);
            fetchOptions(value).then((newOptions) => {
                if (fetchId !== fetchRef.current) {
                    // for fetch callback order
                    return;
                }
                if (isCreated && newOptions.length === 0) {
                    setOptions([
                        {
                            label: value,
                            value,
                        },
                    ]);
                } else {
                    const op =
                        isCreated && value
                            ? [
                                  {
                                      label: value,
                                      value,
                                  },
                                  ...newOptions,
                              ]
                            : newOptions;
                    setOptions(op);
                }

                setFetching(false);
            });
        };

        return debounce(loadOptions, debounceTimeout);
    }, [debounceTimeout, fetchOptions, isCreated]);
    const handleChange = useCallback(
        (e) => {
            // Access additional data based on selectedValue
            const selectedOptionData = options.find((item) => item.value === e.value);
            if (onChange) {
                onChange({
                    ...e,
                    ...(selectedOptionData || {}),
                });
            }
        },
        [onChange, options]
    );

    return (
        <Select
            labelInValue
            filterOption={false}
            onSearch={debounceFetcher}
            notFoundContent={fetching ? <Spin size="small" /> : null}
            onChange={handleChange}
            {...props}
        >
            {options.map((d) => (
                <Option key={d.value}>{d.label}</Option>
            ))}
        </Select>
    );
};

export default DebounceSelect;
