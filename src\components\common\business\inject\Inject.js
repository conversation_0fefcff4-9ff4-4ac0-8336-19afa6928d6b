const React = require('react');

const { Component, Fragment } = React;
const NO_ERROR = '__NO__ERROR';

class Inject extends Component {
    constructor(props) {
        super(props);
        this.state = {
            loading: true,
            error: NO_ERROR,
        };
        this.setError = this.setError.bind(this);
        this.hasError = this.hasError.bind(this);
        this.load = this.load.bind(this);
    }

    componentDidMount() {
        const { loaders } = this.props;

        this.load(loaders, this.setError);
    }

    setError(error) {
        this.setState({ error });
    }

    hasError() {
        return this.state.error !== NO_ERROR;
    }

    load(loaders, setError) {
        const loaderPromises = loaders.map((loader) => loader());

        Promise.all(loaderPromises)
            .then(() => {
                this.setState({
                    loading: false,
                });
            })
            .catch((e) => {
                setError(e);
            });
    }

    render() {
        const { errorComponent: Error, loadingComponent: Loading, children } = this.props;
        const { loading, error } = this.state;

        if (loading) {
            return <Loading />;
        }
        if (this.hasError()) {
            return <Error error={error} />;
        }

        return <Fragment>{children}</Fragment>;
    }
}

Inject.defaultProps = {
    loadingComponent: () => <div>loading</div>,
    errorComponent: (props) => <div>{props.message || props}</div>,
    loaders: [],
};

module.exports = Inject;
