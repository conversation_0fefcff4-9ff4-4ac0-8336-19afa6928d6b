/* eslint-disable no-param-reassign */
import { useForm } from '@share/shareui-form';
import { useUpdateEffect, useSetState } from 'ahooks';
import { Modal } from 'antd';
import { deleteSpace } from '@/utils/index';
import { useList } from '@share/list';
import { useServiceMap } from '../../hooks';

const defaultSearchBody = {
    fieldCode: '',
    fieldDataType: '',
    fieldComment: '',
};
const objectFieldCode = ['XDR_MC', 'ZZMC', 'XM', 'XDR_SHXYM', 'TYSHXYDM', 'XDR_GSZC', 'ZZJGDM', 'XDR_ZZJG', 'ZCH', 'XDR_ZJHM', 'ZJHM'];

const useLogic = ({ metaFieldList, refreshDataFn }) => {
    const { metaFieldApi } = useServiceMap();
    const [formData, form] = useForm({ ...defaultSearchBody });
    const [state, setState] = useSetState({
        showEsExportModal: false,
    });
    const filterDataList = ({
        data,
        page,
        params = {
            fieldCode: '',
            fieldDataType: '',
            fieldComment: '',
        },
    }) => {
        const { fieldCode, fieldDataType, fieldComment } = params;
        // const pageSize =
        const { currentPage, linesPerPage } = page;
        let filterList = data;

        if (fieldCode) {
            filterList = filterList.filter((item) => item.fieldCode && item.fieldCode.toUpperCase().includes(fieldCode.toUpperCase()));
        }
        if (fieldDataType) {
            filterList = filterList.filter((item) => item.fieldDataType === fieldDataType);
        }
        if (fieldComment) {
            filterList = filterList.filter(
                (item) => item.fieldComment && item.fieldComment.toUpperCase().includes(fieldComment.toUpperCase())
            );
        }
        const list = filterList.slice((currentPage - 1) * linesPerPage, currentPage * linesPerPage);

        return {
            list,
            page: {
                ...page,
                totalNum: filterList.length,
            },
        };
    };
    const listState = useList({
        uniqKey: 'fieldId',
        // dataSource: metaFieldList,
        dataSource: async (condition) => {
            console.log('condition', condition);
            const res = await filterDataList({
                params: condition.data,
                data: metaFieldList,
                page: condition.page,
            });

            return res;
        },
        autoLoad: true,
    });

    const updateConfig = async (mfl) => {
        await metaFieldApi.saveTableMeta(mfl);
        // eslint-disable-next-line no-unused-expressions
        refreshDataFn && refreshDataFn();
    };
    const defaultEsConfig = () => {
        Modal.confirm({
            content: '该操作会恢复Es默认配置(数据长度不大于500)，请确认是否操作？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
                metaFieldList.forEach((item) => {
                    if (item.fieldLength.length !== 1 || item.fieldLength[0] <= 500) {
                        item.esConfig.enabled = true;
                        item.esConfig.objectKeyword = objectFieldCode.includes(item.fieldCode);
                        item.esConfig.businessKeyword = objectFieldCode.includes(item.fieldCode);
                    } else {
                        item.esConfig.enabled = false;
                    }
                });

                updateConfig(metaFieldList);
            },
        });
    };

    const handleSearch = (data) => {
        const body = deleteSpace(data);

        listState.query(body);
    };
    const handleToggleShowEsExportModal = (bool = false) => {
        setState({
            showEsExportModal: bool,
        });
    };
    useUpdateEffect(() => {
        listState.query();
    }, [metaFieldList]);

    return {
        form,
        handleSearch,
        formData,
        defaultEsConfig,
        handleToggleShowEsExportModal,
        state,
        listState,
    };
};

export default useLogic;
