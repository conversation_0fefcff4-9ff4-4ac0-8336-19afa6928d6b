import React, { Fragment } from 'react';
// 样式
import classnames from 'classnames';
import styles from '@/pages/metaData/styles/index.scss';
// 服务接口
import * as DataSaveApi from '@/services/data/data/DataSaveApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 组件
import DataFilling from '@/components/credit_data/DataFilling';
import RoleAuthControl from '@/components/business/auth/RoleAuthControl/RoleAuthControl';
import Edit from '@/components/business/metadata/Edit';
import { Panel, ButtonToolBar, Button } from '@share/shareui';

class DataFillingAdd extends DataFilling {
    // 提交Api
    submitApi = (ignoreSuspectedError, ignoreQuestionData) => {
        const { categoryId } = this.props.match.params;
        const { editForm } = this.state;
        const data = editForm.getFormData();

        return DataSaveApi.addFromPage(categoryId, data, ignoreSuspectedError, ignoreQuestionData);
    };

    // 提交成功
    afterSuccess = () => {
        this.resetData();
    };

    render() {
        const { editConfigList, editForm, checkErrorLevel, composeCheckErrorMsg } = this.state;

        return (
            <Fragment>
                <Panel>
                    <Panel.Head title="在线数据填报" />
                    <Panel.Body full>
                        <div
                            className={classnames({
                                [styles.isError]: composeCheckErrorMsg.length !== 0,
                            })}
                        >
                            <Edit formState={editForm} editType="add" metadataConfigList={editConfigList} />
                        </div>
                        {composeCheckErrorMsg.length !== 0 && (
                            <div className={`${styles.pl20} input-hasTip has-error`}>
                                {composeCheckErrorMsg.map((item, index) => (
                                    <p className="text text-tip pull-left" key={index}>
                                        <i className="fa fa-times-circle" />
                                        {item}
                                    </p>
                                ))}
                            </div>
                        )}
                    </Panel.Body>
                </Panel>
                <ButtonToolBar>
                    <Button type="button" bsSize="large" onClick={this.cancel}>
                        取消
                    </Button>
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspend && (
                        <RoleAuthControl buttonKey="meta-data-filling-suspend-question">
                            <Button type="button" bsSize="large" bsStyle="warning" onClick={() => this.submit(true, false)}>
                                暂缓上报
                            </Button>
                        </RoleAuthControl>
                    )}
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                        <RoleAuthControl buttonKey="meta-data-filling-suspected-question">
                            <Button type="button" bsSize="large" bsStyle="warning" onClick={() => this.submit(false, false)}>
                                疑问暂存
                            </Button>
                        </RoleAuthControl>
                    )}
                    {checkErrorLevel === MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                        <RoleAuthControl buttonKey="meta-data-filling-suspected-confirm">
                            <Button type="button" bsSize="large" bsStyle="success" onClick={() => this.submit(true, true)}>
                                确认
                            </Button>
                        </RoleAuthControl>
                    )}
                    {checkErrorLevel !== MetaConfigUtils.RULE_CHECK_LEVEL.suspend &&
                        checkErrorLevel !== MetaConfigUtils.RULE_CHECK_LEVEL.suspected && (
                            <Button
                                type="button"
                                bsSize="large"
                                bsStyle="primary"
                                onClick={() => this.submit(false, true)}
                                disabled={editConfigList.length === 0}
                            >
                                提交
                            </Button>
                        )}
                </ButtonToolBar>
            </Fragment>
        );
    }
}

export default DataFillingAdd;
