.baseLabel {
    height: 24px;
    border-radius: 2px;
    padding: 2px 8px;
    font-size: 14px;
    line-height: 22px;
    display: inline-block;
}

.baseButton {
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    background-color: #ffffff;
    border-radius: 4px;
    border: 1px solid #d0d1d4;
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.85);
    padding: 0 16px;
    display: flex;
    align-items: center;

    i.sort {
        width: 14px;
        height: 14px;
        margin-left: 4px;
        background: url('../../assets/images/enterprise/sort.png') no-repeat 0 0;
        background-size: 100% 100%;
    }

    i.heart {
        margin-right: 4px;
    }

    &:hover,
    &.active {
        border-color: #1677ff;
        color: #1677ff;

        i.sort {
            background: url('../../assets/images/enterprise/sort-active.png') no-repeat 0 0;
            background-size: 100% 100%;
        }
    }

    &.reverse {
        background-color: #1677ff;
        color: #fff;
    }
}

:global {
    .tooltip .tooltip-arrow {
        border-top-color: #fff !important;
        // box-shadow: 1px 1px 10px #ccc;
    }

    .tooltip-inner {
        background-color: #fff !important;
        box-shadow: 1px 1px 10px #ccc;
    }

    .tooltip {
        // padding: 12px 0!important;

    }

    .timelineCover {
        .timelineLeft{
            width: 130px!important;
        }

        .timelineDot{
            top: 38px!important;
            width: 14px!important;
            height: 14px!important;
            background: #1677ff!important;
        }
    }
}