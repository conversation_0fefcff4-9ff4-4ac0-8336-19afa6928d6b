import React, { Component } from 'react';
// 服务接口
import * as Meta<PERSON>ield<PERSON><PERSON> from '@/services/data/meta/MetaFieldApi';
// 列表组件
import * as Entity from '@/components/common/common/Entity';
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
import { Panel, Button, ButtonToolBar } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';
import * as formRule from '@/components/common/common/formRule';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';

const { Form, Row, RadioGroup, Time, Textarea } = getComponents();

class UpdateProtocol extends Component {
    state = {
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
    };

    componentDidMount() {
        const { metaFieldList } = this.props;
        const { editForm } = this.state;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaFieldList);
        const primaryKeyField = metaFieldList.find((item) => item.fieldCode === primaryKey);

        editForm.setFormData(Entity.simpleDeepCopy(primaryKeyField.updateConfig));
    }

    submit = async () => {
        const { metaFieldList, refreshDataFn } = this.props;
        const { editForm } = this.state;

        if (FormVaildHelper.isValid(await editForm.valid())) {
            const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaFieldList);
            const primaryKeyField = metaFieldList.find((item) => item.fieldCode === primaryKey);

            primaryKeyField.updateConfig = editForm.getFormData();
            await MetaFieldApi.saveTableMeta([primaryKeyField]);
            refreshDataFn && refreshDataFn();
        }
    };

    render() {
        const { editForm } = this.state;

        return (
            <Panel>
                <Panel.Body full>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <RadioGroup
                                label="更新方式"
                                field="way"
                                options={[{ label: '数据表同步', value: '0' }]}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                            <Time label="执行时间" field="time" rule={[formRule.checkRequiredNotBlank()]} required />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="更新频率"
                                field="frequency"
                                options={[
                                    { label: '每年', value: '01' },
                                    { label: '每季度', value: '02' },
                                    { label: '每月', value: '03' },
                                    { label: '每周', value: '04' },
                                    { label: '每天', value: '05' },
                                    { label: '每小时', value: '06' },
                                ]}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                                colSpan={3}
                            />
                        </Row>
                        <Row>
                            <Textarea label="备注" field="remark" rows="8" colSpan={3} />
                        </Row>
                    </Form>
                    <ButtonToolBar>
                        <Button
                            type="button"
                            bsStyle="primary"
                            style={{
                                marginRight: '20px',
                            }}
                            onClick={this.submit}
                        >
                            提交
                        </Button>
                    </ButtonToolBar>
                </Panel.Body>
            </Panel>
        );
    }
}

export default UpdateProtocol;
