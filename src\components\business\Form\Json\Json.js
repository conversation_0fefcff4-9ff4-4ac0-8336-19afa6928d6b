import React, { Component, Fragment } from 'react';
import { <PERSON><PERSON>, Modal } from '@share/shareui';
import WebCodeBeauty from './WebCodeBeauty';
// import AceEditor from 'react-ace';
// import 'ace-builds/webpack-resolver';
// import 'ace-builds/src-noconflict/mode-json';
// import 'ace-builds/src-noconflict/theme-github';
// import 'ace-builds/src-noconflict/ext-language_tools';

class Json extends Component {
    state = {
        showModal: false,
        editText: '',
    };

    open = () => {
        const { value } = this.props;
        const jsonStr = typeof value === 'string' ? value : WebCodeBeauty.json(JSON.stringify(value || {}));

        this.setState({ showModal: true, editText: jsonStr });
    };

    close = () => {
        this.setState({ showModal: false });
    };

    beautify = () => {
        const { editText } = this.state;

        try {
            this.setState({ editText: WebCodeBeauty.json(editText) });
        } catch (e) {
            console.log(e);
        }
    };

    submit = () => {
        const { onChange, acceptString = true } = this.props;
        const { editText } = this.state;

        try {
            JSON.parse(editText);
            if (acceptString) {
                onChange && onChange({ target: { value: editText } });
            } else {
                onChange && onChange({ target: { value: JSON.parse(editText) } });
            }
            this.setState({ showModal: false });
        } catch (e) {
            console.log(e);
        }
    };

    render() {
        const { showModal, editText } = this.state;

        return (
            <Fragment>
                <Button type="button" bsStyle="primary" onClick={this.open}>
                    打开编辑器
                </Button>
                <Modal show={showModal} onHide={this.close} bsSize="large" backdrop="static">
                    <Modal.Header closeButton />
                    <Modal.Body full>
                        {/* <AceEditor */}
                        {/* value={editText} */}
                        {/* onChange={text => this.setState({ editText: text })} */}
                        {/* editorProps={{ $blockScrolling: true }} */}
                        {/* mode="json" theme="github" width="100%" */}
                        {/* showPrintMargin={false} */}
                        {/* /> */}
                    </Modal.Body>
                    <Modal.Footer>
                        <Button onClick={this.close}>取消</Button>
                        <Button onClick={this.beautify}>美化</Button>
                        <Button type="button" bsStyle="primary" onClick={this.submit}>
                            确认
                        </Button>
                    </Modal.Footer>
                </Modal>
            </Fragment>
        );
    }
}

export default Json;
