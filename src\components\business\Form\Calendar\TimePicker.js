/*
 *@(#) TimePicker.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2020
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2020-02-25
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import React from 'react';
import { TimePicker as Time } from 'antd';
import moment from 'moment';

class TimePicker extends React.Component {
    static defaultProps = {
        format: 'HH:mm:ss',
    };

    onChange = (time, timeString) => {
        const { onChange } = this.props;

        onChange && onChange({ target: { value: timeString || '' } });
    };

    render() {
        const { value, format } = this.props;

        return <Time {...this.props} value={value ? moment(value, format) : ''} onChange={this.onChange} />;
    }
}

export default TimePicker;
