import { __MOCK__, COMMON_API_PREFIX, MOCK_API_PREFIX } from '../NetworkConfig';

const { NetError } = require('../../common/Errors');
const Log = require('../../log/Log');

export const CONTENT_TYPE = {
    JSON: 'application/json',
    FORM: 'application/x-www-form-urlencoded',
};

export class Sender {
    constructor() {
        this.post = this.post.bind(this);
        this.get = this.get.bind(this);
    }

    post(url, data, contentType = CONTENT_TYPE.JSON) {
        throw new Error('Sender 未实现 send');
    }

    get(url, data) {
        throw new Error('Sender 未实现 get');
    }
}

Sender.getJsonData = function (data) {
    return JSON.stringify(data);
};

Sender.getContextPath = function () {
    return getContextPath();
};

Sender.getUrl = function (path) {
    // url参数中带mock，访问mock地址
    if (getParamByKey(path, 'mock')) {
        return MOCK_API_PREFIX + path;
    }
    const API_PREFIX = __MOCK__ ? MOCK_API_PREFIX : COMMON_API_PREFIX;

    return API_PREFIX + path;
};

Sender.parseToJsonObj = function (responseText) {
    let jsonObject = {};

    try {
        jsonObject = JSON.parse(responseText);

        return Promise.resolve(jsonObject);
    } catch (e) {
        Log.warn('[JSON api 解析失败]', e);
    }
    try {
        jsonObject = new Function(`var rs=${text};return rs`)(responseText);

        return Promise.resolve(jsonObject);
    } catch (e) {
        return Promise.reject(new NetError(`[解析异常][${e}]`, responseText));
    }
};

function getParamByKey(url, key) {
    const regex = new RegExp(`[\\?&]${key}=([^&#]*)&?`, ['i']);
    const qs = regex.exec(url);

    if (qs == null) {
        return '';
    }

    return qs[1];
}

function getContextPath() {
    const locationUrl = window.location.href.replace(/\#.*/, '');
    const urlArr = locationUrl.split('/');

    urlArr.pop();

    return `${urlArr.join('/')}/`;
}
