/*
 *@(#) DataRepairDetail.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-26
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */
import React, { Component } from 'react';
// 样式
import styles from '@/pages/metaData/styles/creditData.scss';
// 服务接口
import * as DataRepairApi from '@/services/data/data/DataRepairApi';
// 组件
import Detail from '@/components/business/metadata/Detail';
import { Panel, ButtonToolBar, Button } from '@share/shareui';
import bmManager from '@/components/common/business/manager/BmManager';

class DataRepairDetail extends Component {
    state = {
        detailBody: {
            data: [],
            system: {},
        },
    };

    // 请求配置信息
    componentDidMount = async () => {
        const {
            match: {
                params: { categoryId, recordId },
            },
        } = this.props;
        const detailBody = await DataRepairApi.detailByCategory(categoryId, '2', recordId);
        const errorType = (detailBody.system.ERROR_TYPES || [])
            .filter((item) => item)
            .map((item) => bmManager.getBmLabel('DM_DATA_VALID_ERROR_TYPE', item))
            .join(',');
        const errorMsg = Object.values(JSON.parse(detailBody.system.ERROR_MSG || '{}'))
            .reduce((r, i) => [...r, ...i], [])
            .join(';');

        detailBody.data.push({
            fieldCode: 'ERROR_TYPES',
            fieldAlias: 'ERROR_TYPES',
            label: '错误类型',
            value: errorType,
            stringValue: errorType,
            type: 'string',
            order: 1000,
        });
        detailBody.data.push({
            fieldCode: 'ERROR_MSG',
            fieldAlias: 'ERROR_MSG',
            label: '错误描述',
            value: errorMsg,
            stringValue: errorMsg,
            type: 'string',
            order: 1000,
        });
        this.setState({ detailBody });
    };

    // 取消
    cancel = () => {
        const { history } = this.props;

        history.go(-1);
    };

    render() {
        const { detailBody } = this.state;

        return (
            <div className={styles.creditDataDetail}>
                <div className={styles['table-list']}>
                    <Panel className={styles['table-list_item']}>
                        <Panel.Head title="修复数据详情" />
                        <Panel.Body full>
                            <h3 className={styles.title}>{detailBody.system.CATEGORY_NAME}</h3>
                            <Detail detail={detailBody} />
                        </Panel.Body>
                    </Panel>
                </div>
                <ButtonToolBar>
                    <Button type="button" onClick={this.cancel}>
                        返回
                    </Button>
                </ButtonToolBar>
            </div>
        );
    }
}

export default DataRepairDetail;
