import React, { Component } from 'react';
import MultiClamp from '@/components/ui/MultiClamp/index';
import classnames from 'classnames';
import styles from './style/Tip.scss';

class Tip extends Component {
    render() {
        const { message, type = 'td', icon = true, clamp = 2, ...restProps } = this.props;
        const ElementType = type === 'td' ? 'td' : 'div';
        const FrameType = typeof message === 'string' ? MultiClamp : 'span';

        return (
            <ElementType
                className={classnames({
                    [styles.tips]: true,
                    [styles[ElementType]]: true,
                    clearfix: true,
                })}
                {...restProps}
            >
                {icon && <i className="si si-com_problem" />}
                <FrameType className={`${styles.tipText}`} clamp={clamp || 2} title={typeof message === 'string' ? message : ''}>
                    {message}
                </FrameType>
            </ElementType>
        );
    }
}

export default Tip;
