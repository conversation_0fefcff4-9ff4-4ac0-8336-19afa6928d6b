import React, { Component, Fragment } from 'react';
// 接口
import * as DataRepairApi from '@/services/data/data/DataRepairApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 组件
import RoleAuthControl from '@/components/business/auth/RoleAuthControl';
import MultiClamp from '@/components/ui/MultiClamp';
import QueryList from '@/components/business/metadata/QueryList';
import MyAlert from '@/components/ui/MyAlert';
import Message from '@/components/ui/MyAlert/Message';
import { FileUploadButton } from '@/components/business/Other';
import { Button, Icon, Panel } from '@share/shareui';
import bmManager from '@/components/common/business/manager/BmManager';
import { FormState } from '@/components/business/Form';
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
import DataRepairModal from '../Modal/DataRepairModal';

const errorLevelStatusMap = {
    0: {
        0: '待修正',
        1: '已修正',
        '-1': '已删除',
    },
    1: {
        0: '待确认',
        1: '已确认',
        '-1': '已删除',
    },
    2: {
        0: '循环上报中',
        1: '已上报',
        '-1': '已删除',
    },
};

const defaultSubmitForm = {
    reason: '',
    explain: '',
};

class DataRepairTable extends Component {
    state = {
        // 表数据量
        tableTotal: null,
        // 操作模态框
        showOperateModal: false,
        submitForm: new FormState({ ...defaultSubmitForm }, (submitForm, callback) => this.setState({ submitForm }, callback)),
    };

    // 数据列表请求接口
    buildDataListApi = (searchBody) => {
        const { categoryCode } = this.props;

        return DataRepairApi.listByCategory(categoryCode, '2', searchBody);
    };

    // 列表展示列
    extendHeadColumns = () => {
        return [
            {
                title: '信息类别',
                width: 200,
                // fixed: 'left',
                dataIndex: ['system', 'CATEGORY_NAME'],
                render: (value) => <MultiClamp title={value}>{value}</MultiClamp>,
            },
        ];
    };

    extendTailColumns = () => {
        const { history, metaConfigList } = this.props;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);

        return [
            {
                title: '错误类型',
                width: 100,
                fixed: 'right',
                dataIndex: ['system', 'ERROR_TYPES'],
                render: (value) => {
                    const convertValue = (value || [])
                        .filter((item) => item)
                        .map((item) => bmManager.getBmLabel('DM_DATA_VALID_ERROR_TYPE', item))
                        .join(',');

                    return <MultiClamp title={convertValue}>{convertValue}</MultiClamp>;
                },
            },
            {
                title: '错误描述',
                width: 150,
                fixed: 'right',
                dataIndex: ['system', 'ERROR_MSG'],
                ellipsis: true,
                render: (value) => {
                    const map = JSON.parse(value || '{}');
                    const result = Object.values(map)
                        .reduce((r, i) => [...r, ...i], [])
                        .join(';');

                    return <MultiClamp title={result}>{result}</MultiClamp>;
                },
            },
            {
                title: '数据状态',
                width: 70,
                fixed: 'right',
                key: 'status',
                dataIndex: 'system',
                render: (rowData) => {
                    const { ERROR_LEVEL, STATUS, STATUS_EXPLAIN, ISSUE_DATA_SOURCES, ISSUE_ERROR_MSG } = rowData;
                    // const issueErrorMsg = STATUS === '0' && ISSUE_DATA_SOURCES !== 'B' && ISSUE_ERROR_MSG ? ISSUE_ERROR_MSG : '';
                    let color = '#666';

                    if (STATUS === '0') {
                        color = '#ff6655';
                    } else if (STATUS === '1') {
                        color = '#01ba88';
                    }

                    return (
                        <span style={{ color }}>
                            <span title={STATUS === '-1' ? STATUS_EXPLAIN : ''}>
                                {(errorLevelStatusMap[ERROR_LEVEL] || errorLevelStatusMap[0])[STATUS]}
                            </span>
                            {/* {issueErrorMsg && ( */}
                            {/*     <span style={{ paddingLeft: '2px' }}> */}
                            {/*         <Icon className="si si-com_problem" title={issueErrorMsg} /> */}
                            {/*     </span> */}
                            {/* )} */}
                        </span>
                    );
                },
            },
            {
                title: '操作',
                width: 165,
                fixed: 'right',
                key: 'operate',
                dataIndex: 'system',
                render: (rowData) => (
                    <div className="tableBtn">
                        {/* 待修复数据可修复 */}
                        {/* { */}
                        {/*     rowData.STATUS === '0' && */}
                        {/*     rowData.ERROR_LEVEL === '0' && */}
                        {/*     <RoleAuthControl buttonKey="meta-data-repair-correct"> */}
                        {/*         <a */}
                        {/*             onClick={() => */}
                        {/*                 history.push( */}
                        {/*                     `/DataRepairEdit/${rowData.CATEGORY_CODE}/${rowData[primaryKey]}` */}
                        {/*                 ) */}
                        {/*             } */}
                        {/*         >修正</a> */}
                        {/*     </RoleAuthControl> */}
                        {/* } */}
                        {/* { */}
                        {/*     rowData.STATUS === '0' && */}
                        {/*     rowData.ERROR_LEVEL === '1' && */}
                        {/*     <RoleAuthControl buttonKey="meta-data-repair-confirm"> */}
                        {/*         <a */}
                        {/*             onClick={() => */}
                        {/*                 history.push( */}
                        {/*                     `/DataRepairEdit/${rowData.CATEGORY_CODE}/${rowData[primaryKey]}` */}
                        {/*                 ) */}
                        {/*             } */}
                        {/*         >确认</a> */}
                        {/*     </RoleAuthControl> */}
                        {/* } */}
                        {/* { */}
                        {/*     rowData.STATUS === '0' && */}
                        {/*     rowData.ERROR_LEVEL === '2' && */}
                        {/*     <RoleAuthControl buttonKey="meta-data-repair-update"> */}
                        {/*         <a */}
                        {/*             onClick={() => */}
                        {/*                 history.push( */}
                        {/*                     `/DataRepairEdit/${rowData.CATEGORY_CODE}/${rowData[primaryKey]}` */}
                        {/*                 ) */}
                        {/*             } */}
                        {/*         >修改</a> */}
                        {/*     </RoleAuthControl> */}
                        {/* } */}
                        <a onClick={() => history.push(`/DataRepairDetail/${rowData.CATEGORY_CODE}/${rowData[primaryKey]}`)}>详情</a>
                        {/* <a */}
                        {/*     onClick={() => */}
                        {/*         history.push( */}
                        {/*             `/DataRepairAudit/${rowData.CATEGORY_CODE}/${rowData.YW_ID}` */}
                        {/*         ) */}
                        {/*     } */}
                        {/* >操作记录</a> */}
                        {/* 待修正数据可删除 */}
                        {/* { */}
                        {/*     rowData.STATUS === '0' && */}
                        {/*     <RoleAuthControl buttonKey="meta-data-repair-delete"> */}
                        {/*         <a */}
                        {/*             onClick={() => { */}
                        {/*                 this.confirmDelete( */}
                        {/*                     [rowData[primaryKey]], */}
                        {/*                     () => { */}
                        {/*                         const { selectedRecords, onSelectedRecordsChange } = this.props; */}
                        {/*                         const newSelectedRecords = selectedRecords.filter(item => */}
                        {/*                             item.system[primaryKey] !== rowData[primaryKey]); */}

                        {/*                         onSelectedRecordsChange(newSelectedRecords); */}
                        {/*                     }); */}
                        {/*             }} */}
                        {/*         >删除</a> */}
                        {/*     </RoleAuthControl> */}
                        {/* } */}
                    </div>
                ),
            },
        ];
    };

    // 导出Excel
    excelExport = (recordIds) => {
        const { categoryCode, categoryCodeList, body } = this.props;
        const category = categoryCodeList.find((v) => v.id === categoryCode) || {};
        const param = JSON.stringify({
            fileName: `${category.name}`,
            recordIds,
            modeConditionMap: body,
        });

        Message.success('导出中...');
        DataRepairApi.exportByCategory(categoryCode, '2', param);
    };

    // 确认
    confirmRecord = (recordIds, callback) => {
        const { categoryCode, refreshList } = this.props;

        MyAlert.warning('该操作将确认数据，且不可恢复，确认执行？', async () => {
            const result = await DataRepairApi.suspectedConfirmByCategory(categoryCode, '2', { recordIds });

            MyAlert.ok(`操作数据${recordIds.length}条，确认成功${result.success}条，确认失败${result.fail}条`);
            callback && callback();
            refreshList && refreshList();
        });
    };

    // 删除
    deleteRecord = async (data, callback) => {
        const { categoryCode, refreshList } = this.props;

        await DataRepairApi.deleteByCategory(categoryCode, '2', data);
        MyAlert.ok('删除成功');
        callback && callback();
        refreshList && refreshList();
    };

    // 确认删除
    confirmDelete = (recordIds, callback) => {
        const { metaConfigList } = this.props;
        const { submitForm } = this.state;
        const statusExplainConfig = metaConfigList.find((item) => item.fieldCode === 'STATUS_EXPLAIN');

        if (statusExplainConfig) {
            submitForm.setFormData({ ...defaultSubmitForm, recordIds, callback });
            this.setState({ showOperateModal: true });
        } else {
            MyAlert.warning('该操作将使错误数据在本系统中被删除，且不可恢复，确认删除？', () => this.deleteRecord({ recordIds }, callback));
        }
    };

    handleModalSubmit = async () => {
        const { submitForm } = this.state;
        const valids = await submitForm.valid();
        const data = submitForm.getFormData();

        if (!FormVaildHelper.isValid(valids)) {
            return;
        }
        const { callback, ...restData } = data;

        this.deleteRecord(restData, () => this.setState({ showOperateModal: false }, callback));
    };

    // 上传成功
    uploadSuccess = (result) => {
        const { refreshList } = this.props;

        refreshList && refreshList();
        const message = (
            <div>
                <div style={{ fontSize: '14px', color: '#74767A', textAlign: 'left' }}>
                    已完成该批数据修复！情况如下：
                    <ul style={{ paddingLeft: '15px', marginTop: '12px' }}>
                        <li style={{ listStyle: 'decimal', marginBottom: '12px' }}>
                            <span>
                                修复成功<span style={{ color: 'red' }}> {result.success}</span>条
                            </span>
                        </li>
                        <li style={{ listStyle: 'decimal' }}>
                            <span>
                                修复失败<span style={{ color: 'red' }}> {result.fail + result.discard} </span>条，
                            </span>
                            <span>
                                因未通过系统校验<span style={{ color: 'red' }}> {result.fail} </span>条数据，
                            </span>
                            <span>
                                已被删除、已修复过<span style={{ color: 'red' }}> {result.discard} </span>条数据。
                            </span>
                            <span>
                                本批excel数据重复<span style={{ color: 'red' }}> {result.failTypeMap.selfRepeatCheck || 0} </span>条数据。
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        );

        MyAlert.ok(message);
    };

    render() {
        const {
            categoryCode,
            metaConfigList,
            body,
            showBatchRepair,
            showBatchConfirm,
            showBatchDelete,
            selectedRecords,
            onSelectedRecordsChange,
            ...otherProps
        } = this.props;
        const { tableTotal, showOperateModal, submitForm } = this.state;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);
        const listConfig = MetaConfigUtils.filterListConfig(metaConfigList);
        const selectedRowKeys = selectedRecords.map((item) => item.system[primaryKey]);
        const unRepairSelectedRowKeys = selectedRecords.filter((item) => item.system.STATUS === '0').map((item) => item.system[primaryKey]);
        const unConfirmSelectedRowKeys = selectedRecords
            .filter((item) => item.system.STATUS === '0' && item.system.ERROR_LEVEL === '1')
            .map((item) => item.system[primaryKey]);

        return (
            <Fragment>
                <ul className="ui-list-horizontal" style={{ position: 'absolute', zIndex: 2, top: '-30px', right: 0 }}>
                    <RoleAuthControl buttonKey="meta-data-repair-batch-export">
                        <li>
                            <Button
                                type="button"
                                className="btn-xs"
                                border={false}
                                onClick={() => this.excelExport(selectedRowKeys)}
                                disabled={selectedRowKeys.length === 0}
                            >
                                <Icon className="si si-com_export" />
                                选择导出
                            </Button>
                        </li>
                    </RoleAuthControl>
                    <RoleAuthControl buttonKey="meta-data-repair-query-export">
                        <li>
                            <Button
                                type="button"
                                className="btn-xs"
                                border={false}
                                disabled={!tableTotal}
                                onClick={() => {
                                    if (tableTotal > 5000) {
                                        Message.warn('系统每次导出最多支持5000条，建议分批导出~');

                                        return;
                                    }
                                    this.excelExport([]);
                                }}
                            >
                                <Icon className="si si-app_xy" />
                                导出查询结果
                            </Button>
                        </li>
                    </RoleAuthControl>
                    {showBatchRepair && (
                        <RoleAuthControl buttonKey="meta-data-repair-batch-update">
                            <li>
                                <FileUploadButton
                                    url={DataRepairApi.repairFromExcelByCategory(categoryCode, '2')}
                                    successFn={this.uploadSuccess}
                                >
                                    <Button type="button" className="btn-xs" border={false}>
                                        <Icon className="si si-xy_yydh" />
                                        批量修正
                                    </Button>
                                </FileUploadButton>
                            </li>
                        </RoleAuthControl>
                    )}
                    {showBatchConfirm && (
                        <RoleAuthControl buttonKey="meta-data-repair-batch-confirm">
                            <li>
                                <Button
                                    type="button"
                                    className="btn-xs"
                                    border={false}
                                    disabled={unConfirmSelectedRowKeys.length === 0}
                                    onClick={() => {
                                        this.confirmRecord(unConfirmSelectedRowKeys, () => onSelectedRecordsChange([]));
                                    }}
                                >
                                    <Icon className="si si-xy_yydh" />
                                    批量确认
                                </Button>
                            </li>
                        </RoleAuthControl>
                    )}
                    {showBatchDelete && (
                        <RoleAuthControl buttonKey="meta-data-repair-batch-delete">
                            <li>
                                <Button
                                    type="button"
                                    className="btn-xs"
                                    border={false}
                                    disabled={unRepairSelectedRowKeys.length === 0}
                                    onClick={() => {
                                        this.confirmDelete(unRepairSelectedRowKeys, () => onSelectedRecordsChange([]));
                                    }}
                                >
                                    <Icon className="si si-com_delete" />
                                    批量删除
                                </Button>
                            </li>
                        </RoleAuthControl>
                    )}
                </ul>
                <Panel>
                    <Panel.Body full>
                        <QueryList
                            namespace="DataRepairTable"
                            service={{
                                api: this.buildDataListApi,
                                body,
                            }}
                            rowKey={(data) => data.system[primaryKey]}
                            metadataConfigList={listConfig}
                            extendHeadColumns={this.extendHeadColumns()}
                            extendTailColumns={this.extendTailColumns()}
                            rowSelection={{
                                fixed: true,
                                selectedRowKeys,
                                onChange: (_selectedRowKeys, selectedRows) => onSelectedRecordsChange(selectedRows),
                            }}
                            pagination={{ detectPage: true, showTotal: true }}
                            {...otherProps}
                            onTableDataChange={(data) => {
                                this.setState({
                                    tableTotal: data.page.total,
                                });
                                if (otherProps.onTableDataChange) {
                                    otherProps.onTableDataChange(data);
                                }
                            }}
                        />
                    </Panel.Body>
                </Panel>
                <DataRepairModal
                    formState={submitForm}
                    show={showOperateModal}
                    submitFn={this.handleModalSubmit}
                    cancelFn={() => {
                        this.setState({ showOperateModal: false });
                        submitForm.setFormData(defaultSubmitForm);
                        submitForm.cleanValidError();
                    }}
                />
            </Fragment>
        );
    }
}

export default DataRepairTable;
