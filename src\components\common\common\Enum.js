const CONTENT_TYPE = {
    JSON: 'application/json',
    FORM: 'application/x-www-form-urlencoded',
};
const NODE_ENV_TYPE = {
    DEV: 'development',
    PROD: 'production',
};

const RESPONSE_STATUS = {
    SUCCESS: '1200',
    NOT_AUTH: '1401',
    VALID_FIELD_ILLEGAL: '1800',
    LOGIN_ON_OTHER_DEVICE: '1903',
    ERR_CODE_LOGIN: '1991',
};

const FAIL_STATUS_PRE = {
    14: '权限验证异常',
    15: '系统内部异常',
    17: '入参报文异常',
    18: '字段校验异常',
    19: '自定义异常',
};
const XM_XZQH = {
    SM: '350203000000',
    HC: '350205000000',
    HL: '350206000000',
    JM: '350211000000',
    TA: '350212000000',
    XA: '350213000000',
    SS: '350201000000',
};
const ENUM = {};

ENUM.CONTENT_TYPE = CONTENT_TYPE;
ENUM.NODE_ENV_TYPE = NODE_ENV_TYPE;
ENUM.RESPONSE_STATUS = RESPONSE_STATUS;
ENUM.FAIL_STATUS_PRE = FAIL_STATUS_PRE;
ENUM.XM_XZQH = XM_XZQH;

exports.ENUM = ENUM;
exports.CONTENT_TYPE = CONTENT_TYPE;
exports.NODE_ENV_TYPE = NODE_ENV_TYPE;
exports.RESPONSE_STATUS = RESPONSE_STATUS;
exports.FAIL_STATUS_PRE = FAIL_STATUS_PRE;
exports.XM_XZQH = XM_XZQH;
