import React, { Fragment } from 'react';
import { Popover, Tree } from 'antd';
import { stringLengthFormat } from '@/utils/format';
import { useDeepCompareEffect, useSetState } from 'ahooks';

import useTagTree from '@/hooks/useTagTree';
import s from './Top.scss';
import g from '../../../../style.scss';
import TagTree from './TagTree';

const CompanyTag = ({ accInfoList = [], canTagEdit }) => {
    const [state, setState] = useSetState({
        needMore: false,
        moreStatus: false,
        clicked: false,
        tagList: [],
        treeDataSource: {
            treeData: [],
            checkedKeys: [],
        },
    });

    const { getTreeData, treeDataTranslate } = useTagTree();

    const getData = async () => {
        const data = await getTreeData(accInfoList);
        const treeDataTranslateData = treeDataTranslate(data, accInfoList);
        console.log('🚀 ~ getData ~ treeDataTranslateData:', treeDataTranslateData);
        setState(treeDataTranslateData);
        // // todo 设置标签
        // const tagList = (accInfoList || []).map((v) => ({ id: v.id, name: v.zzrdlxmc }));
        // // const tagList = [{id:"A",name:"半导体"},{id:"B",name:"计算机"},{id:"C",name:"软件"},{id:"C",name:"软件"},{id:"C",name:"软件"},{id:"C",name:"软件"},{id:"C",name:"软件"},{id:"C",name:"软件"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"},{id:"C",name:"111111111111111111111111111111111111111111111111111111111"}];
        // setState({ ...state, tagList });
    };

    useDeepCompareEffect(() => {
        getData();
    }, [accInfoList]);

    useDeepCompareEffect(() => {
        const $dom = document.getElementById('labelBox');
        setState({
            needMore: $dom?.clientHeight > 60,
        });
    }, [accInfoList, setState]);

    const labelBoxStyle = state.needMore ? { overflow: 'hidden', height: state.moreStatus ? 'auto' : '32px' } : {};

    return (
        // eslint-disable-next-line react/jsx-no-useless-fragment
        <Fragment>
            <div className={s.tagWrap}>
                {Array.isArray(state.tagList) && state.tagList.length > 0 ? (
                    <div className={s.companyTag}>
                        <div id="labelBox" style={labelBoxStyle}>
                            {state.tagList.map((v) => {
                                const [bgColor, color] = v.tagStyle ? v.tagStyle.split('-') : [];

                                if (v?.children?.length === 0 && v?.parents?.length === 0) {
                                    return (
                                        <span key={v.id} className={g.baseLabel} title={v.name}>
                                            {stringLengthFormat(v.name, 8)}
                                        </span>
                                    );
                                }

                                return (
                                    <Popover
                                        key={v.id}
                                        placement="rightBottom"
                                        content={
                                            <div className={s['companyTag-Popover']}>
                                                {v?.parent?.name ? (
                                                    <div className={s['companyTag-Popover_parents']}>
                                                        <div className={s['companyTag-Popover_parents--item']}>{v?.parent?.name}</div>
                                                    </div>
                                                ) : (
                                                    ''
                                                )}

                                                <div className={s['companyTag-Popover_oneself']}>
                                                    <div className={s['companyTag-Popover_oneself--item']}>{v.name}</div>
                                                </div>
                                                {v.children?.length > 0 ? (
                                                    <div className={s['companyTag-Popover_children']}>
                                                        <Tree defaultExpandAll treeData={v.children || []} />
                                                    </div>
                                                ) : (
                                                    ''
                                                )}
                                            </div>
                                        }
                                    >
                                        <span key={v.id} className={g.baseLabel} title={v.name}>
                                            {stringLengthFormat(v.name, 8)}
                                        </span>
                                    </Popover>
                                );
                            })}
                        </div>

                        {state.needMore && (
                            <span
                                className={s.more}
                                // onMouseMove={() => setState({ moreStatus: true })}
                                // onMouseOut={() => setState({ moreStatus: false })}
                                onClick={() => setState({ moreStatus: !state.moreStatus })}
                            >
                                {!state.moreStatus && (
                                    <Fragment>
                                        更多 <i className="si si-com_angledownthin" />
                                    </Fragment>
                                )}
                                {state.moreStatus && (
                                    <Fragment>
                                        收起 <i className="si si-com_angleupthin" />
                                    </Fragment>
                                )}
                            </span>
                        )}
                    </div>
                ) : (
                    <div className={s.companyTag} />
                )}

                <Popover
                    className="ss"
                    placement="leftTop"
                    getPopupContainer={(trigger) => trigger.parentNode}
                    content={
                        <TagTree
                            treeDataSource={state.treeDataSource}
                            defaultExpandedKeys={state.extractIdsWithParents || []}
                            onHide={() => {
                                setState({ clicked: false });
                            }}
                        />
                    }
                    open={state.clicked}
                    trigger="click"
                    title={
                        <div className={s['tagTree-title']}>
                            <span>产业标签</span>
                            <i
                                className="si si-com_closethin"
                                onClick={() => {
                                    setState({ clicked: false });
                                }}
                            />
                        </div>
                    }
                    onOpenChange={() => {
                        setState({ clicked: true });
                    }}
                >
                    <span className={s.addTag}>
                        <i className="si si-com_addto" />
                    </span>
                </Popover>
            </div>
        </Fragment>
    );
};

export default CompanyTag;
