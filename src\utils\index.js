/* eslint-disable no-promise-executor-return */
import React from 'react';
import { RegionNoUtil } from '@share/common';
import dayjs from 'dayjs';
import cloneDeep from 'lodash/cloneDeep';
import { Modal } from 'antd';

import sharePortalMessenger from '@share/portal-messenger';
import { emptyDefault } from './format';

const portalMessenger = sharePortalMessenger(); // 如果没有指定targetId，使用iframe的name

export const sleep = (time) => new Promise((resolve) => setTimeout(resolve, time));

export const getContextPath = () => {
    const basePath = window.SHARE.CONTEXT_PATH;

    return basePath.replace(location.origin, '');
};
export const getPath = (src, name) => `${getContextPath()}resource${src}?${name ? `name=${name}` : ''}`;

export const getFileFormat = (data) => {
    const tranValue = typeof data === 'string' ? data.replace(/,\//g, '~~/') : null;
    const pictureList = typeof tranValue === 'string' ? tranValue.split('~~').filter((item) => Boolean(item)) : [];

    return pictureList
        .filter((v) => Boolean(v))
        .map((item) => {
            // console.log('item', item, item.split(','));

            const [src, nameStr = ''] = item.split(',');
            const [name] = nameStr ? nameStr.split('$$') : '';

            return { src, name };
        });
};

/**
 * 判断字段是否改标红显示
 * @param {String} type  ---需要判断的页面的类型
 * @param {Object} data  ---需要判断的页面的源数据
 * @param {String} filedName  ---需要判断是否有值的字段
 * @param {String} labelText  ---需要标红显示的字段中文名称
 * @param {Boolean} isJudgmentInvestmentStatus --是否需要判断招商状态
 */
// 待完善字段集合
export const toBePerfectedField = {
    // 楼栋名称、层高、占地面积、建筑面积、使用面积、招商信息-物业管理费价格、招商信息-联系人、招商信息-联系电话、招商信息-描述招商信息-当前状态、招商信息-是否对外公布、招商信息-租售模式、招商信息-租金水平、招商信息-装修情况、招商信息-闲置楼层、招商信息-可招商面积
    building: [
        'buildingName',
        'buildingFloors',
        'areaCovered',
        'builtUpArea',
        'useArea',
        'propertyManagePrice',
        'investContact',
        'investContactTel',
        'investDescribe',
        'investStatus',
        'investOpenStatus',
        'investType',
        'investPriceRangeMin',
        'investPriceRangeMax',
        'decorationStatus',
        'vacantFloors',
        'investArea',
    ],
    // 地址、地块登记时间、地块使用期限、 首拍企业、当前受让企业、招商信息-联系人、招商信息-项目优势、招商信息-当前状态、招商信息-是否对外公布、招商信息-联系电话、招商信息-招商情况、招商情况-空间定位、监管状态、总用地面积、建设用地面积、总建设面积、容积率
    block: [
        'address',
        'registerTime',
        'blockUseAgeLimit',
        'landBuildDeptName',
        'blockCurrentOwner',
        'investContact',
        'projectAdvantage',
        'investStatus',
        'investOpenStatus',
        'investContactTel',
        'investmentSituation',
        'spaceLocation',
        'blockSupervisionState',
        'landArea',
        'buildUseArea',
        'buildArea',
        'planPlotRatio',
    ],
};

export const fieldToRed = (type = '', data = {}, filedName = '', labelText = '', isJudgmentInvestmentStatus = true) => {
    if (isJudgmentInvestmentStatus) {
        if (toBePerfectedField[type].includes(filedName) && !data[filedName]) {
            return <span style={{ color: 'red' }}>{labelText}</span>;
        }
    }

    return labelText;
};
export const reserveIntDecimal = (value, label, required = false, int = 10, dec = 2) => {
    if (required && !value) {
        return new Error(`${label}不能为空`);
    }
    if (!required && !value) {
        return true;
    }
    if (isNaN(Number(value))) {
        return new Error(`${label}输入格式错误`);
    }
    // 生成正则表达式的字符串
    const regexStr = `^(0|[1-9]\\d{0,${int - 1}})(\\.\\d{1,${dec}})?$`;
    // 将字符串转化为正则表达式
    const regex = new RegExp(regexStr);

    if (regex.test(value)) {
        return true;
    }

    return new Error(`格式错误，最多只能输入${int}位整数，${dec}位小数`);
};
// 亩转换为平方米
export const muToSquareMeter = (mu) => {
    if (!mu) {
        return '--';
    }
    const squareMeter = Number(mu) / 0.0015;

    return squareMeter;
};
// 平方米转换为亩
export const squareMeterToMu = (squareMeter) => {
    if (!squareMeter || !Number(squareMeter)) {
        return emptyDefault();
    }

    return `${Number(squareMeter).toLocaleString()} m²（${(squareMeter * 0.0015).toFixed(2)}亩）`;
};
// 平方米转换为公顷
export const squareMeterToHectares = (squareMeter) => {
    if (!squareMeter) {
        return '--';
    }

    return `${Number(squareMeter).toLocaleString()} m²（${(squareMeter / 10000).toFixed(2)}公顷）`;
};

/**
 * 判断是否属于区域
 * @param {*} userXzqh  ----登录用户的行政区划
 * @param {*} ssxzqh  ------当前数据的行政区划
 */
export const whetherBelongsToArea = (userXzqh = '', ssxzqh = '') => {
    const regionSimple = RegionNoUtil.toSimple(userXzqh || '');

    return (ssxzqh || '').startsWith(regionSimple);
};

/**
 * 地块使用期限计算
 * 公式：地块使用期限 = 地块登记时间 + 地块使用期限
 * @param {*} landRegistrationTime  ----地块登记时间
 * @param {*} landAge ----地块使用期限
 */
export const landUsePeriod = (landRegistrationTime = '', landAge = 0) => {
    if (!landRegistrationTime) {
        return '--';
    }
    const afterYear = dayjs(landRegistrationTime).add(landAge, 'year');
    const r = `${dayjs(landRegistrationTime).format('YYYY年MM月DD日')} - ${afterYear.format('YYYY年MM月DD日')}`;

    return r;
};

/**
 * 计容面积计算
 * 公式：计容面积 = 总建筑面积 - 地下空间面积 + 地上层高8米的建筑面积
 * @param {*} totalSurfaceArea  -----总建筑面积
 * @param {*} undergroundSpaceArea  -----地下空间面积
 * @param {*} heightEightMeterBuildArea -----地上层高8米的建筑面积
 */
export const calculateCapacityArea = (totalSurfaceArea = 0, undergroundSpaceArea = 0, heightEightMeterBuildArea = 0) => {
    if (!totalSurfaceArea) {
        return '';
    }

    return `${(Number(totalSurfaceArea) - Number(undergroundSpaceArea) + Number(heightEightMeterBuildArea)).toLocaleString()} `;
};

/**
 * 将万元/公顷转换为万元/平方米，返回万元/平方米
 * @param {Number} pricePerHectare 万元/公顷
 * @returns Number
 */
export const convertPricePerHectareToPricePerSquareMeter = (pricePerHectare) => {
    const pricePerSquareMeter = pricePerHectare / 10000;

    return `${pricePerSquareMeter}`;
};

/** 将万元/亩转换为万元/平方米，返回万元/平方米
 * 将万元/公顷转换为万元/平方米，返回万元/平方米
 * @param {Number} pricePerMu 万元/公顷
 * @returns Number
 */
export const convertPricePerMuToPricePerSquareMeter = (pricePerMu) => {
    const pricePerSquareMeter = pricePerMu / 666.67;

    return pricePerSquareMeter;
};

/** 将万元/平方米转换为万元/公顷,返回万元/公顷
 *
 * @param {Number} pricePerSquareMeter 万元/平方米
 * @returns Number
 */
export const convertPricePerSquareMeterToPricePerHectare = (pricePerSquareMeter) => {
    const pricePerHectare = pricePerSquareMeter * 10000;

    return `${pricePerHectare}`;
};

/**
 * 根据行政区划的值获取街道、社区
 * @param {*} xzqh  ----行政区划
 * @param {*} xzqhList ---- 行政区划列表
 * @returns
 */
export const getStreetAndCommunity = (xzqh, xzqhList) => {
    if (!xzqh || !Array.isArray(xzqhList)) {
        return '';
    }
    const xzqhDataSource = new Map();

    if (xzqhDataSource.size === 0) {
        if (!xzqhDataSource.has(xzqh)) {
            xzqhList.forEach((element) => {
                xzqhDataSource.set(element.code || element.value, element.label);
            });
        }
    }
    const xzqhObj = RegionNoUtil.toObject(xzqh);

    const { STREET, COMMUNITY } = xzqhObj;
    const street = xzqhDataSource.get(STREET);
    const community = xzqhDataSource.get(COMMUNITY);

    const r = [...new Set([street, community])];

    return r.filter((v) => Boolean(v)).join('-');
};

// { onOk, bsStyle, content, cancelText }
export const modalTip = ({
    bsStyle = 'error', // error success info fail
    content = '接口异常，请刷新重试',
    cancelText = null,
    ...rest
}) => {
    const methodMap = {
        error: Modal.error,
        success: Modal.success,
        info: Modal.info,
        fail: Modal.error,
    };

    const method = methodMap[bsStyle] || methodMap.info;

    method({
        content,
        cancelText,
        ...rest,
    });
};

/**
 * 校验输入的是否是金额
 */

export const isMoney = (value, label, required = false) => {
    if (required && !value) {
        return new Error(`${label}不能为空`);
    }
    if (!required && !value) {
        return true;
    }
    if (isNaN(Number(value))) {
        return new Error(`${label}输入格式错误`);
    }
    const reg = /^\d{1,4}$|^\d{1,4}[.]\d{1,3}$/;

    if (reg.test(value)) {
        return true;
    }

    return new Error('格式错误，最多只能输入4位数字，最多保留三位小数');
};
// 获取经营地址
export const getAddressObj = (address) => {
    if (Array.isArray(address)) {
        return (
            address.filter((ite) => {
                return Number(ite.type) === 1;
            })[0] || {}
        );
    }

    return {};
};
// 树结构转换为数组
export const treeToArray = (treeData, { childrenName = 'children' } = {}) => {
    const res = [];
    let newArr = cloneDeep(treeData);

    while (newArr.length) {
        // 每次都把第一个项取出
        const first = newArr.shift();

        if (first[childrenName]) {
            // 如果第一个项有children属性，那么就把这个children放到newArr的最后一项
            newArr = newArr.concat(first[childrenName]);
            delete first[childrenName];
        }
        res.push(first);
    }

    return res;
};
// 数组转换为树结构数据
export const arrayToTree = (dataSource, parentId, { idName = 'id', parentIdName = 'parentId', childrenName = 'children' } = {}) => {
    // 存放结果
    const result = [];
    // 存储id与item
    const itemMap = {};

    for (const item of dataSource) {
        itemMap[item[idName]] = { ...item, [childrenName]: [] };
    }
    for (const item of dataSource) {
        const id = item[idName];
        const pid = item[parentIdName];
        const treeItem = itemMap[id];

        if (pid === parentId) {
            result.push(treeItem);
        } else {
            if (!itemMap[pid]) {
                itemMap[pid] = {
                    children: [],
                };
            }
            itemMap[pid].children.push(treeItem);
        }
    }

    return result;
};

export const openTab = ({ url, label, appId }) => {
    portalMessenger.openTab({
        key: appId || new Date().getTime(),
        // eslint-disable-next-line camelcase
        app_id: appId || new Date().getTime(),
        label,
        url,
    });
    // portalMessenger.send('iframeRefresh', { key: appId });
};

export const strFormat = (str) => {
    if (str === 0) {
        return 0;
    }

    return str || '--';
};

export const comas = (num) => {
    if (num === 0 || num === '0') {
        return '0';
    }

    return num ? Number(num).toLocaleString() : '--';
};

// 扁平化
export const dataList = (arr) => {
    const list = [];
    const generateList = (data) => {
        data.forEach((node) => {
            const { key, title } = node;

            list.push({ key, title });
            if (node.children) {
                generateList(node.children);
            }
        });
    };

    generateList(arr);

    return list;
};

// 获取父级的key
export const getParentKey = (res, key) => {
    function treeFindPath(tree, func, path = []) {
        if (!tree) {
            return [];
        }
        for (const data of tree) {
            // 这里按照你的需求来存放最后返回的内容吧
            path.push(data.key);
            if (func(data)) {
                return path;
            }
            if (data.children) {
                const findChildren = treeFindPath(data.children, func, path);

                if (findChildren.length) {
                    return findChildren;
                }
            }
            path.pop();
        }

        return [];
    }

    return treeFindPath(res, (data) => data.key === key);
};

// 搜索完成时获取父级的expandedKeys
export const searchExpandedKeys = (value, list) => {
    return dataList(list)
        .map((item) => {
            if (item.title.indexOf(value) > -1) {
                return getParentKey(list, item.key);
            }

            return null;
        })
        .flat(Infinity)
        .filter((item, i, self) => item && self.indexOf(item) === i);
};

export function flatten(list = [], parentNode = null, acc) {
    // 数组扁平化
    list.forEach((item) => {
        //
        if (!Array.isArray(item.children) || item.children.length === 0) {
            acc.push({ ...item, parentNode });
        }

        const { children, ...parent } = item;

        flatten(item.children, parent); // 写入父节点信息
    });
}

/**
 * 字符串或对象(支持树状结构)去除前后空格
 */
export const deleteSpace = (data) => {
    // 如果null、undefined返回空串
    if (data === null || typeof data === 'undefined') {
        return null;
    }
    // 字符串类型直接去前后空格
    if (typeof data === 'string') {
        // 根据正则去前后空格
        return data.replace(/(^\s*)|(\s*$)/g, '');
    }
    // 数字类型，boolean类型返回原值
    if (typeof data === 'number' || typeof data === 'boolean') {
        return data;
    }
    // 对象根据类型创建目标对象
    const result = Array.isArray(data) ? [] : {};

    // 遍历子元素去空格
    for (const [key, value] of Object.entries(data)) {
        result[key] = deleteSpace(value);
    }

    return result;
};

/**
 * 表码翻译
 * @param {String} val
 * @param {Array} arr
 * @returns {String}
 */
export const translateBm = (val, arr) => arr.find((v) => v.code === val)?.name || '';

/**
 @params dom dom元素
 @params chartOption echarts的Option
 @params timer 定时器
 */
export const setTimerToolTips = (dom, chartOption, selectSymbol, defaultSymbol) => {
    let timer;
    let index = -1;

    if (timer) {
        clearInterval(timer); // 清除定时器，防止轮播出现混乱
    } else {
        const echartsInstance = dom.getEchartsInstance();
        const echartsEvents = () => {
            index = (index + 1) % chartOption.series[0].data.length; // 计算索引
            const d = chartOption.series[0].data.map((item, idx) => {
                return {
                    ...item,
                    symbol: idx === index ? selectSymbol : defaultSymbol,
                };
            });
            echartsInstance.dispatchAction({
                type: 'downplay',
                seriesIndex: 0,
                dataIndex: index,
            });
            echartsInstance.dispatchAction({
                // Echarts提供的方法
                type: 'showTip',
                seriesIndex: 0,
                dataIndex: index,
            });
            echartsInstance.dispatchAction({
                type: 'highlight',
                seriesIndex: 0,
                dataIndex: index,
            });

            chartOption.series[0].data = d;
            echartsInstance.setOption(chartOption);
        };
        if (chartOption?.series?.[0]?.data?.length > 1) {
            timer = setInterval(echartsEvents, 3000); // 每隔3秒轮播一次

            return timer;
        }
        echartsEvents();
    }
};

export const getCurrentWeek = (value = null, separate = '-') => {
    // 如果为无效时间,则格式化当前时间
    if (!value) {
        value = Number(new Date());
    }
    // 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式
    if (String(value).length == 10 && value == Number(value)) {
        value *= 1000;
    }
    value = Number(value);

    const one_day = 86400000; // 24 * 60 * 60 * 1000;
    const weekDate = new Date(value);
    const day = weekDate.getDay() === 0 ? 7 : weekDate.getDay(); // 返回1-7,7表示周日
    // 设置时间为当天的0点
    weekDate.setHours(0, 0, 0, 0);

    // 算出本周开始时间结束时间
    const week_start_time = new Date(weekDate.getTime() - (day - 1) * one_day);
    const week_end_time = new Date(weekDate.getTime() + (7 - day) * one_day);

    // 格式化日期
    const filters = (n) => {
        // eslint-disable-next-line no-return-assign
        return n < 10 ? (n = `0${n}`) : n;
    };
    const startmonth = filters(week_start_time.getMonth() + 1);
    const startDay = filters(week_start_time.getDate());
    const endmonth = filters(week_end_time.getMonth() + 1);
    const endDay = filters(week_end_time.getDate());

    const startDateTime = week_start_time.getFullYear() + separate + startmonth + separate + startDay;
    const endDateTime = week_end_time.getFullYear() + separate + endmonth + separate + endDay;

    return {
        startDateTime: `${startDateTime}`,
        endDateTime: `${endDateTime}`,
    };
};

/**
 * 给手机号码打上掩饰码
 * @param {string} phoneNumber  -----手机号码
 * @returns
 */
export const maskPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) {
        return '--';
    }
    // 确保输入是一个字符串
    const str = phoneNumber.toString().trim();

    // 如果字符串长度小于7位，则无法进行遮罩
    if (str.length < 7) {
        return str;
    }

    // 构建遮罩字符串，用星号填充中间部分
    const mask = '*'.repeat(str.length - 5);

    // 返回遮罩后的手机号码
    return str.substring(0, 3) + mask + str.substring(str.length - 2);
};

// 渲染企业名称
export const renderCompanyName = (companyName) => {
    if (!companyName) {
        return '';
    }
    const companyNameXiamen = companyName.replace(/^(福建省厦门市|厦门市|厦门)/g, '');

    const $companyNameXiamen = companyNameXiamen.replace('有限公司', '');

    return $companyNameXiamen;
    // }
};

// 根据子节点中的数据是否有disabled来获取对应的父节点（企业档案详情页面标签模块使用）
export const findParentsWithDisabledChildren = (dataTree) => {
    // 递归函数，用于遍历树结构并过滤节点
    const recursiveFilter = (node) => {
        const children = (item) => {
            if (item.children) {
                // 过滤子节点中isChecked为true的节点
                const checkedChildren = item.children.filter((child) => child.isChecked);

                // 如果有子节点被选中，则当前节点是父级
                if (checkedChildren.length > 0) {
                    item.children = checkedChildren.map(recursiveFilter); // 递归处理子节点

                    return item;
                }
                item.children = []; // 清空子节点
            }
        };

        // if (!node.parentId && node.isChecked) {
        //     children(node);

        //     return node;
        // }
        console.log('🚀 ~ recursiveFilter ~ node:', node);

        // 如果当前节点被选中，返回当前节点
        if (node.isChecked) {
            children(node);

            return node;
        }

        children(node);

        return null; // 如果没有子节点被选中且当前节点未被选中，则返回null
    };

    // 从根节点开始递归过滤
    return cloneDeep(dataTree)
        .map(recursiveFilter)
        .filter((node) => node !== null);
};

export const filterChecked = (data) => {
    return data
        .map((item) => {
            if (item.children) {
                const filteredChildren = filterChecked(item.children);
                if (item.isChecked || filteredChildren.length > 0) {
                    return { ...item, children: filteredChildren };
                }
            } else if (item.isChecked) {
                return { ...item };
            }

            return null;
        })
        .filter((item) => item !== null);
};
export const fileIdAdapter = (d) => {
    if (!d && !Array.isArray(d)) {
        return [];
    }

    return d
        .map((v) => {
            if (typeof v !== 'string') {
                return v.url || '';
            }

            return v;
        })
        .filter((v) => v);
};

export const compare = (dateStr) => {
    if (!dateStr) {
        return true;
    }
    const date = new Date(dateStr);
    const time = new Date();
    const nowDate = new Date(`${time.getFullYear()}-${time.getMonth() + 1}-${time.getDate()} 00:00:00`);

    return date.getTime() < nowDate.getTime();
};
