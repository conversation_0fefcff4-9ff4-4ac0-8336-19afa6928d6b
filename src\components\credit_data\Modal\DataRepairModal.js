import React, { Component } from 'react';
import * as formRule from '@/components/common/common/formRule';
import { Modal, Button } from '@share/shareui';
import { getComponents } from '@/components/business/Form';

const { Form, Row, Textarea } = getComponents();

class DataRepairModal extends Component {
    render() {
        const { formState, show, submitFn, cancelFn } = this.props;

        return (
            <Modal show={show} onHide={cancelFn}>
                <Modal.Header closeButton>删除提示</Modal.Header>
                <Modal.Body full>
                    <Form pageType="addPage" formState={formState}>
                        <Row>
                            <Textarea
                                label="删除意见"
                                field="explain"
                                rule={[formRule.checkRequiredNotBlank(), formRule.checkLength(1, 200)]}
                                required
                                placeholder="请输入"
                                rows="6"
                            />
                        </Row>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={submitFn}>
                        确定
                    </Button>
                    <Button onClick={cancelFn}>取消</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default DataRepairModal;
