import React, { Component } from 'react';
// 样式
import './style/commonTree.scss';
import classnames from 'classnames';
import { Icon } from '@share/shareui';
import directoryImg from '../../../assets/icons/ico_directory.png';
import fileImg from '../../../assets/icons/ico_file.png';

class CommonTree extends Component {
    render() {
        const { Tree, TreeNode, treeData = [], onAdd, onEdit, onDel, extend, children, ...restProps } = this.props;
        const editGroup = (currentNode, parentNode) => {
            return (
                <span
                    className={classnames({
                        'title_operate clearfix pull-left': true,
                        operateAlwaysShow: currentNode.operateAlwaysShow,
                    })}
                >
                    {onAdd && <Icon className="text-success si si-com_plus" title="新增" onClick={() => onAdd(currentNode, parentNode)} />}
                    {onEdit && <Icon className="text-primary si si-com_b6" title="编辑" onClick={() => onEdit(currentNode, parentNode)} />}
                    {onDel && (
                        <Icon className="text-danger si si-com_error-49" title="删除" onClick={() => onDel(currentNode, parentNode)} />
                    )}
                    {extend && extend(currentNode, parentNode)}
                </span>
            );
        };
        const loop = (data = [], parentNode) =>
            data.map((item) => {
                const { title: originalTitle, showTitle, ...rest } = item;
                const icon = <img src={Array.isArray(item.children) ? directoryImg : fileImg} alt="" />;
                const title = (
                    <span className="title_pane clearfix">
                        <span
                            className="title_content pull-left"
                            title={originalTitle}
                            key={originalTitle}
                            style={{ overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis', display: 'inline-block' }}
                        >
                            {showTitle || originalTitle}
                        </span>
                        {(onAdd || onEdit || onDel || extend) && editGroup(item, parentNode)}
                    </span>
                );
                const props = {
                    key: item.key,
                    icon,
                    title,
                    ...rest,
                };

                return <TreeNode {...props}>{Array.isArray(item.children) && loop(item.children, item)}</TreeNode>;
            });
        const convertTreeData = children || loop(treeData);

        return (
            <div className="share-tree_container">
                <Tree {...restProps}>{convertTreeData}</Tree>
            </div>
        );
    }
}

export default CommonTree;
