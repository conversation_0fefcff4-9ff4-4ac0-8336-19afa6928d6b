import React, { Component, Fragment } from 'react';
// 被封装组件
import { getComponents } from '@share/shareui-form';

const { CheckboxGroup: ShareCheckboxGroup } = getComponents();

function coverToArray(data) {
    if (Array.isArray(data)) {
        return data;
    }
    if (typeof data === 'string' && data !== '') {
        return data.split(',');
    }

    return [];
}

class CheckboxGroup extends Component {
    onChange = ({ target: { value = [] } }) => {
        const { onChange, acceptString } = this.props;
        let newValue = Array.isArray(value) ? value.map((item) => (typeof item.value === 'undefined' ? item : item.value)) : [value];

        if (acceptString) {
            newValue = newValue.join(',');
        }
        onChange && onChange({ target: { value: newValue } });
    };

    getComProps = () => {
        const { value, options, ...restProps } = this.props;
        const keyArray = coverToArray(value).map((item) => (typeof item.value === 'undefined' ? item : item.value));
        const valueArray = keyArray.map((key) => options.find((item) => key === item.value) || { value: key });

        return { ...restProps, value: valueArray, options, onChange: this.onChange };
    };

    render() {
        const comProps = this.getComProps();

        return (
            <Fragment>
                <ShareCheckboxGroup.View {...comProps} />
                {this.props.children}
            </Fragment>
        );
    }
}

export default CheckboxGroup;
