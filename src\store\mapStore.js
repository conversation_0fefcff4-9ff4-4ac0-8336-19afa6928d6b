import { useHistory } from 'react-router';
import create from 'zustand';

export const useMapStore = create((set) => ({
    mapProperty: null,
    setMapProperty: (v) => {
        set({ mapProperty: v });
    },
    mapFunc: null, // 存放封装好的地图相关方法
    setMapFunc: (mapFunc) =>
        set({
            mapFunc,
        }),
}));

export const usePageStore = create((set) => {
    return {
        enterpriseList: [],
        setEnterpriseList: (v) => {
            set({ enterpriseList: v });
        },
        searchData: { searchValue: '', searchType: 4, tyshxydms: [] },
        setSearchData: (v) => {
            set({ searchData: v });
        },
        tagIds: {},
        setTagIds: (v) => {
            set({ tagIds: v });
        },
    };
});
