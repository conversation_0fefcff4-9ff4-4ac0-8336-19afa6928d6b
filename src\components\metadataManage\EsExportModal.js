import React, { Component } from 'react';
// 请求接口
import * as Meta<PERSON>ield<PERSON><PERSON> from '@/services/data/meta/MetaFieldApi';
// 工具
import FormVaildHelper from '@/components/common/common/FormVaildHelper';
import * as formRule from '@/components/common/common/formRule';
import * as TimeUtil from '@/components/common/common/TimeUtil';
// 列表组件
import { Button, Modal } from '@share/shareui';
import { FormState, getComponents } from '@/components/business/Form';

const { Form, Row, Input, RadioGroup } = getComponents();

const defaultBody = {
    tableIds: [],
    esIndex: '',
    esAlias: '',
    version: TimeUtil.getFormateTimeStr2(TimeUtil.getCurrentDayStr()),
    relateCatalog: true,
    generateKeyword: true,
    generateTableName: true,
    humpHump: false,
};

const booleanOptions = [
    { label: '是', value: true },
    { label: '否', value: false },
];

class EsExportModal extends Component {
    state = {
        editForm: new FormState({}, (editForm, callback) => this.setState({ editForm }, callback)),
        dataFieldOptions: [],
    };

    componentWillReceiveProps(nextProps) {
        const {
            show,
            data: { tableIds, esAlias },
        } = nextProps;
        const { editForm } = this.state;

        if (!this.props.show && show) {
            editForm.setFormData({ ...defaultBody, tableIds, esAlias, esIndex: `${esAlias}_v${defaultBody.version}` });
            editForm.cleanValidError();
        }
    }

    submit = async () => {
        const { successFn } = this.props;
        const { editForm } = this.state;
        const data = editForm.getFormData();

        if (!FormVaildHelper.isValid(await editForm.valid())) {
            return;
        }
        MetaFieldApi.exportEsFile(data);

        successFn && successFn();
        this.cancelFn();
    };

    cancelFn = () => {
        const { cancelFn } = this.props;

        cancelFn && cancelFn();
    };

    render() {
        const { show } = this.props;
        const { editForm } = this.state;
        const { esAlias } = editForm.getFormData();

        return (
            <Modal className="modal-full" show={show} onHide={this.cancelFn} bsSize="large" backdrop="static">
                <Modal.Header closeButton>导出ES配置</Modal.Header>
                <Modal.Body>
                    <Form pageType="addPage" formState={editForm}>
                        <Row>
                            <Input label="索引名称" field="esIndex" disabled required />
                        </Row>
                        <Row>
                            <Input label="索引别名" field="esAlias" disabled required />
                        </Row>
                        <Row>
                            <Input
                                label="版本号"
                                field="version"
                                rule={[
                                    formRule.checkRequiredNotBlank(),
                                    formRule.checkFunction((value) => /^[a-z0-9\\-]+$/.test(value), '仅能“数字”、“小写字母”、“-”组成'),
                                ]}
                                required
                                onChange={({ target: { value } }) => editForm.setFieldValue('esIndex', `${esAlias}_v${value}`)}
                            />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="关联目录"
                                field="relateCatalog"
                                options={booleanOptions}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="生成关键字"
                                field="generateKeyword"
                                options={booleanOptions}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                        <Row>
                            <RadioGroup
                                label="生成表名"
                                field="generateTableName"
                                options={booleanOptions}
                                rule={[formRule.checkRequiredNotBlank()]}
                                required
                            />
                        </Row>
                        {/* <Row> */}
                        {/*    <RadioGroup */}
                        {/*        label="驼峰转换" field="humpHump" */}
                        {/*        options={booleanOptions} */}
                        {/*        rule={[formRule.checkRequiredNotBlank()]} */}
                        {/*        required */}
                        {/*    /> */}
                        {/* </Row> */}
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button bsStyle="primary" onClick={this.submit}>
                        应用
                    </Button>
                    <Button onClick={this.cancelFn}>关闭</Button>
                </Modal.Footer>
            </Modal>
        );
    }
}

export default EsExportModal;
