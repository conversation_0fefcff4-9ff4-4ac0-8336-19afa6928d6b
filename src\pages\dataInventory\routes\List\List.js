import React, { Fragment } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON> } from '@share/shareui';
import { SearchForm, Input, Select } from '@share/shareui-form';
import { <PERSON><PERSON><PERSON>, ShareList, Column, ActionColumn } from '@share/list';
import { convertLabel } from '@/utils/codeUtil';
import Edit from './components/Edit';
import { useModel } from './hooks';
import style from './List.scss';

const objectTypeOptions = [
    { value: 'ent', label: '企业' },
    { value: 'person', label: '个人' },
];

const sourceTypeOptions = [
    { value: 'sql', label: 'sql' },
    { value: 'das', label: 'das' },
    { value: 'rpc', label: 'rpc' },
];

const booleanOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' },
];

const List = () => {
    const $model = useModel();

    return (
        <Fragment>
            <SearchForm formState={$model.form} query={$model.query}>
                <Input field="id" label="清单ID" />
                <Input field="name" label="清单名称" />
                <Select field="objectType" label="主体类型" options={objectTypeOptions} />
                <Select field="sourceType" label="来源类型" options={sourceTypeOptions} />
                <Input field="sourceId" label="来源ID" />
                <Input field="paramId" label="参数ID" />
                <Input field="paramName" label="参数名称" />
                <Input field="resultId" label="结果ID" />
                <Input field="resultName" label="结果名称" />
                <Select field="enabled" label="启用" options={booleanOptions} />
            </SearchForm>
            <Toolbar title="查询结果">
                <Toolbar.Extra>
                    <Toolbar.ExtraButton onClick={$model.add}>新增</Toolbar.ExtraButton>
                </Toolbar.Extra>
            </Toolbar>
            <ShareList listState={$model.list}>
                <Column field="id" label="清单ID" width={250} ellipsis />
                <Column field="name" label="清单名称" ellipsis />
                <Column field="objectType" label="主体类型" render={(v) => convertLabel(objectTypeOptions, v)} width={70} />
                <Column field="source" label="来源类型" render={(v) => convertLabel(sourceTypeOptions, v.type)} width={70} />
                <Column field="source" label="来源ID" render={(v) => v.id} width={250} ellipsis />
                <Column field="enabled" label="启用" width={40} render={(v) => convertLabel(booleanOptions, v)} />
                <ActionColumn align="center" width={70}>
                    <ActionColumn.Edit onClick={$model.edit} />
                </ActionColumn>
            </ShareList>
            <Modal
                show={$model.editModal.showEdit}
                onHide={$model.editModal.hiddenEdit}
                className={`modal-full ${style.modalMax}`}
                backdrop="static"
            >
                <Modal.Header closeButton>
                    <Modal.Title>{$model.editModal.editForm.getFieldValue('originalId') ? '编辑' : '新增'}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Edit
                        editForm={$model.editModal.editForm}
                        inventoryList={$model.inventoryList}
                        objectTypeOptions={objectTypeOptions}
                        sourceTypeOptions={sourceTypeOptions}
                        codeOptions={$model.codeOptions}
                        booleanOptions={booleanOptions}
                    />
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={$model.editModal.hiddenEdit}>关闭</Button>
                    <Button preventDuplicateClick bsStyle="primary" onClick={$model.editModal.editSubmit}>
                        确定
                    </Button>
                </Modal.Footer>
            </Modal>
        </Fragment>
    );
};

export default List;
