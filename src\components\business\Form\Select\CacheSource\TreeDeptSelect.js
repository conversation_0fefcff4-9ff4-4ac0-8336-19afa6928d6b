import React, { Component } from 'react';
// 工具类
import { getUserComponent } from '@/components/business/Form/Custom';
import * as TreeUtil from '@/components/common/common/TreeUtil';
// 被封装组件
import TreeSelect from '../custom/TreeSelect';

const UserTreeSelect = getUserComponent(TreeSelect);
// 用户管理器
const meManager = require('@/components/common/business/manager/MeManager');
const deptListManager = require('@/components/common/business/manager/DeptListManager');

class TreeDeptSelect extends Component {
    static defaultProps = {
        ...TreeSelect.defaultProps,
        labelKey: 'shortName', // label属性键
        valueKey: 'deptId', // value属性键
        areaIdKey: 'regionId', // 区域属性键
        data: null, // 外部数据源

        userRegionManageFilter: false, // 过滤与用户管理区域部门
        userRegionValueFilter: false, // 过滤与用户相同区域部门
        userManageFilter: false, // 是否过滤获取用户管理部门
        userChildFilter: false, // 是否过滤获取用户子部门
        userValueFilter: false, // 是否过滤与用户相同部门
        valueFilter: null, // 过滤获取指定部门
        existFilter: true, // 过滤去除已删除部门
        handlerFn: null, // 处理函数（函数）

        autoCheckedInitial: false, // 任何用户初始化自动选中自身
        autoCheckedAlways: false, // 任何用户始终始自动选中自身
        deptAutoCheckedInitial: false, // 部门用户初始化自动选中自身
        deptAutoCheckedAlways: false, // 部门用户始终自动选中自身
        deptAutoDisabled: false, // 部门用户自动锁死下拉框
        onlyValueAutoCheckedInitial: false, // 唯一值初始化自动选中自身
        onlyValueAutoCheckedAlways: false, // 唯一值始终自动选中自身
        onlyValueAutoDisabled: false, // 唯一值自动锁死
    };

    componentWillMount() {
        this.me = meManager.getMe();
        this.deptData = deptListManager.getDeptList();
    }

    get treeData() {
        const {
            data,
            userRegionManageFilter,
            userRegionValueFilter,
            userManageFilter,
            userChildFilter,
            userValueFilter,
            valueFilter,
            existFilter,
            handlerFn,
        } = this.props;
        let dataList = data || this.deptData;

        // 过滤获取用户管理区域部门
        if (userRegionManageFilter) {
            dataList = this.handleUserRegionManageFilter(dataList);
        }
        // 过滤获取用户自身区域部门
        if (userRegionValueFilter) {
            dataList = this.handleUserRegionValueFilter(dataList);
        }
        // 过滤用户管理部门
        if (userManageFilter) {
            dataList = this.handleUserManageFilter(dataList);
        }
        // 过滤用户子部门
        if (userChildFilter) {
            dataList = this.handleUserChildFilter(dataList);
        }
        // 过滤用户自身部门
        if (userValueFilter) {
            dataList = this.handleUserValueFilter(dataList);
        }
        // 过滤获取指定部门
        if (valueFilter) {
            dataList = this.handleValueFilter(dataList, valueFilter);
        }
        // 过滤去除删除部门
        if (existFilter) {
            dataList = this.handleExistFilter(dataList);
        }
        // 自定义处理逻辑
        if (handlerFn) {
            dataList = handlerFn(dataList);
        }

        return this.handleListToTree(dataList);
    }

    handleListToTree = (list) => {
        const { labelKey, valueKey } = this.props;

        function handleListToTree(sourceList, targetList) {
            if (!Array.isArray(targetList) || targetList.length === 0) {
                return [];
            }

            return targetList.map((item) => ({
                key: item[valueKey],
                title: item[labelKey],
                value: item[valueKey],
                children: handleListToTree(
                    sourceList,
                    sourceList.filter((one) => one.pid === item[valueKey])
                ),
            }));
        }

        return handleListToTree(
            list,
            list.filter((item) => !item.pid || list.every((one) => item.pid !== one.deptId))
        );
    };

    handleUserRegionManageFilter = (deptList) => {
        const { areaIdKey } = this.props;
        const { manager, regionNo } = this.me;
        const minSimpleRegionNo = regionNo.replace(/^(\d*?)(00)+$/, '$1');

        return manager ? deptList : deptList.filter((dept) => dept[areaIdKey] && dept[areaIdKey].startsWith(minSimpleRegionNo));
    };

    handleUserRegionValueFilter = (deptList) => {
        const { areaIdKey } = this.props;
        const { regionNo } = this.me;

        return deptList.filter((dept) => dept[areaIdKey] === regionNo);
    };

    handleUserManageFilter = (deptList) => {
        const { valueKey } = this.props;
        const { manager, manageDeptIds } = this.me;

        return manager ? deptList : deptList.filter((dept) => manageDeptIds.includes(dept[valueKey]));
    };

    handleUserChildFilter = (deptList) => {
        const { valueKey } = this.props;
        const { manager, deptId, manageDeptIds } = this.me;

        return manager ? deptList : deptList.filter((dept) => deptId !== dept[valueKey] && manageDeptIds.includes(dept[valueKey]));
    };

    handleUserValueFilter = (deptList) => {
        const { deptId } = this.me;

        return this.handleValueFilter(deptList, deptId);
    };

    handleValueFilter = (deptList, deptId) => {
        const { valueKey } = this.props;

        return deptList.filter((dept) => dept[valueKey] === deptId);
    };

    handleExistFilter = (deptList) => {
        return deptList.filter((dept) => !dept.deleted);
    };

    render() {
        const {
            labelKey,
            valueKey,
            areaIdKey,
            data,
            userRegionManageFilter,
            userRegionValueFilter,
            userManageFilter,
            userChildFilter,
            userValueFilter,
            valueFilter,
            existFilter,
            handlerFn,
            ...restProps
        } = this.props;
        const { deptId } = this.me;
        const treeData = this.treeData || [];
        const options = TreeUtil.treeToArray(treeData);

        return (
            <UserTreeSelect
                {...restProps}
                searchPlaceholder="请输入部门名称"
                checkStrictly
                treeData={treeData}
                options={options}
                operateDefaultValue={deptId}
            />
        );
    }
}

export default TreeDeptSelect;
