import React, { Component, Fragment } from 'react';
// 被封装组件
import { getComponents } from '@share/shareui-form';
import SelectCustom from './custom/SelectCustom';

const { Select: ShareFormSelect } = getComponents();
// 空值显示头
const baseOptions = { query: { label: '全部', value: '' }, input: { label: '选择', value: '' }, empty: {} };

function coverToArray(data) {
    if (Array.isArray(data)) {
        return data;
    }
    if (typeof data === 'string' && data !== '') {
        return data.split(',');
    }

    return [];
}

class Select extends Component {
    static defaultProps = {
        type: null, // 空值显示模式
        multi: false, // 是否多选
        acceptString: false, // 多选时，返回值是否为逗号隔开的字符串
        custom: false, // 定制化样式
        extendHeadOptions: [], // 前置扩展项
        extendTailOptions: [], // 后置扩展项
    };

    onMultiChange = ({ target: { value = [] } }) => {
        const { onChange, acceptString } = this.props;
        let newValue = Array.isArray(value) ? value.map((item) => (typeof item.value === 'undefined' ? item : item.value)) : [value];

        if (acceptString) {
            newValue = newValue.join(',');
        }
        onChange && onChange({ target: { value: newValue } });
    };

    getCom = () => {
        const { custom } = this.props;

        if (custom) {
            return SelectCustom;
        }

        return ShareFormSelect.View;
    };

    getComProps = () => {
        const { value, options = [], onChange, multi, type, extendHeadOptions = [], extendTailOptions = [], ...restProps } = this.props;
        const convertValue = multi ? coverToArray(value) : value;
        const extendOption = [...extendHeadOptions, ...options, ...extendTailOptions];
        const convertOptions = !multi && type ? [baseOptions[type], ...extendOption] : extendOption;
        const convertOnChange = multi ? this.onMultiChange : onChange;

        return { ...restProps, value: convertValue, options: convertOptions, onChange: convertOnChange, multi };
    };

    render() {
        const Com = this.getCom();
        const comProps = this.getComProps();

        return (
            <Fragment>
                <Com {...comProps} />
                {this.props.children}
            </Fragment>
        );
    }
}

export default Select;
