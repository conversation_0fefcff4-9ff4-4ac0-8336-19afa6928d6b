@charset "utf-8";

.w1100 {
    :global {
        .modal-dialog {
            width: 1100px;
        }
    }
}

.noData {
    padding: 100px 0 140px 0;
    text-align: center;
    &-sm {
        padding: 10px 0 10px;
    }
    p {
        color: #999;
    }
}

.isError {
    border: 1px solid #a94442;
}
.pl20 {
    p {
        padding-left: 20px;
    }
}

.batchOperation {
    position: absolute;
    top: -29px;
    right: 0;
}

.headerBtn {
    float: right;
    margin-top: 40px;
    > * {
        margin-right: 8px;
    }
}
:global {
    .form-search-wrap .item .sub-item {
        float: none !important;
    }
}
.tipText{
    text-align: left;
    color: #f59a23;
    font-size: 14px;
    pre {
        color: #f59a23;
        background: transparent;
        border: none;
        white-space: break-spaces;
        font-size: 14px;
        padding: 0;
        font-family: 'Arial Normal', 'Arial', sans-serif;
    }
    &-content {
        font-size: 14px;
        color: #f59a23;
    }
}
.modal-pre {
    :global {
        .modal-dialog {
            width: 600px !important;
        }
    }

}
