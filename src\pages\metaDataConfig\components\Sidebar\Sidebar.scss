.Sidebar {
    height: 100%;
    display: flex;
    flex-direction: column;
    &-tree {
        flex: auto;
        overflow: auto;
    }
    .addRootNode {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-bottom: 12px;
        font-size: 18px;
        color: #169bd5;
        cursor: pointer;
    }
    :global {
        .ant-tree {
            .ant-tree-node-content-wrapper {
                position: relative;
                .treeNode {
                    display: flex;
                    align-items: center;
                    > div {
                        flex: 1;
                        margin-right: 12px;
                    }
                    > i {
                        visibility: hidden;
                        font-size: 18px;
                        margin-left: 12px;
                        position: absolute;
                        right: 5px;
                        top: 50%;
                        transform: translateY(-50%);
                        &.remove {
                            color: red;
                        }
                        &.add {
                            right: 35px;
                            color: #169bd5;
                        }
                    }
                }
                &:hover {
                    .treeNode {
                        > i {
                            visibility: visible;
                        }
                    }
                }
            }
        }
    }
}
