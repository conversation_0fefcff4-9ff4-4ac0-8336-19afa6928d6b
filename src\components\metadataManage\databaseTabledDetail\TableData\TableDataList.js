import React, { Component } from 'react';
import noData from '@/assets/images/image-noData.png';
// 服务接口
import * as MetaDataApi from '@/services/data/meta/MetaDataApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
// 组件
import QueryCondition from '@/components/business/metadata/QueryCondition';
import QueryList from '@/components/business/metadata/QueryList';
import Detail from '@/components/business/metadata/Detail';
import { Panel, Modal, Button } from '@share/shareui';
import styles from './TableDataList.scss';

class TableDataList extends Component {
    state = {
        // 搜索参数状态
        searchBody: {},
        // 详情模态框参数
        showDetailModal: false,
        detailBody: {},
    };

    // 创建列表请求API
    buildDataCollListApi = (body) => {
        const { tableId, objectType = '2' } = this.props;

        return MetaDataApi.list(tableId, objectType, body);
    };

    // 详情展示
    detail = async (recordId) => {
        const { tableId, objectType = '2' } = this.props;
        const detailBody = await MetaDataApi.detail(tableId, objectType, recordId);

        this.setState({ showDetailModal: true, detailBody });
    };

    render() {
        const { tableId, metaFieldList } = this.props;
        const { searchBody, showDetailModal, detailBody } = this.state;
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaFieldList);
        const queryConfigList = MetaConfigUtils.filterQueryConfig(metaFieldList);
        const listConfigList = MetaConfigUtils.filterListConfig(metaFieldList);
        const defaultSearchBody = MetaConfigUtils.getQueryDefaultData(metaFieldList);
        const extendTailColumns = [
            {
                title: '操作',
                width: 100,
                fixed: 'right',
                key: 'operations',
                dataIndex: `system.${primaryKey}`,
                render: (primaryKeyValue) => {
                    if (!primaryKeyValue) {
                        return '';
                    }

                    return (
                        <div className="tableBtn">
                            <a onClick={() => this.detail(primaryKeyValue)}>查看</a>
                        </div>
                    );
                },
            },
        ];

        return (
            <div>
                <Panel>
                    <Panel.Body full>
                        {/* 搜索条件 */}
                        {queryConfigList.length !== 0 && listConfigList.length !== 0 && (
                            <QueryCondition
                                defaultSearchBody={defaultSearchBody}
                                metadataConfigList={queryConfigList}
                                searchFn={(searchForm) => this.setState({ searchBody: searchForm })}
                            />
                        )}
                        {/* 搜索列表 */}
                        {listConfigList.length === 0 ? (
                            <div className={styles.noData}>
                                <img src={noData} alt="no data" />
                                <p>未配置列表展示列信息</p>
                            </div>
                        ) : (
                            <Panel>
                                <Panel.Head title="查询结果" />
                                <Panel.Body full>
                                    <QueryList
                                        metadataConfigList={listConfigList}
                                        service={{ api: this.buildDataCollListApi, body: searchBody }}
                                        rowKey={(data) => data.system[primaryKey]}
                                        extendTailColumns={extendTailColumns}
                                        namespace={`tableDataList-${tableId}`}
                                    />
                                </Panel.Body>
                            </Panel>
                        )}
                    </Panel.Body>
                </Panel>
                {/* 详情模态框 */}
                <Modal
                    className={`modal-full ${styles.w1100}`}
                    show={showDetailModal}
                    onHide={() => this.setState({ showDetailModal: false })}
                    bsSize="large"
                    backdrop="static"
                >
                    <Modal.Header closeButton>详情</Modal.Header>
                    <Modal.Body>
                        <Detail detail={detailBody} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button onClick={() => this.setState({ showDetailModal: false })}>关闭</Button>
                    </Modal.Footer>
                </Modal>
            </div>
        );
    }
}

export default TableDataList;
