/*
 *@(#) MainPerson.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-10
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import ShareList, { Column, NumberColumn, useList } from '@share/list';
import React from 'react';
import { fieldTranslate, timeFormat } from '@/utils/format';
import { useCode } from '@share/framework';
import NoData from '@/components/NoData/NoData';
import LinkText from '../../../LinkText/LinkText';

const MainPerson = ({ data = [] }) => {
    const listState = useList({ dataSource: data });
    const { GJDM } = useCode('GJDM');

    return (
        <div className="shareListStyleCover">
            <ShareList listState={listState} usePageBar={false} emptyText={<NoData />}>
                <NumberColumn />
                <Column
                    label="姓名"
                    field="xm"
                    render={(val, { glqys, zyryzjhm }, rowIndex) => {
                        return (
                            <div>
                                <span style={{ marginRight: '10px' }}>{val}</span>
                                {glqys && Number(glqys) > 0 ? (
                                    <LinkText
                                        type="tab"
                                        tabOption={{ key: `qylb-${zyryzjhm}-${rowIndex}`, label: '企业档案' }}
                                        url={`${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/list?qyzylxrzjhm=${zyryzjhm}`}
                                    >
                                        关联 {glqys} 家企业&gt;
                                    </LinkText>
                                ) : (
                                    ''
                                )}
                            </div>
                        );
                    }}
                />
                <Column label="国家或地区" field="gjMc" render={(val) => fieldTranslate(val, GJDM)} />
                {/* <Column label="持股比例" field="" /> */}
                {/* <Column label="投资数额" field="sjczzewy" /> */}
                <Column label="职务" field="zw" />
                <Column label="任职起始日期" field="rzqsrq" render={(val) => timeFormat(val)} />
            </ShareList>
        </div>
    );
};

export default MainPerson;
