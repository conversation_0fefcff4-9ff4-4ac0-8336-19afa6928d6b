/*
 *@(#) AnnualReport.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2024
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2024-07-22
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import TagTitle from '@/pages/enterpriseArchives/routes/Detail/components/TagTitle';
import React, { useEffect, useState } from 'react';
import { Row, SearchForm, Select, TableForm, Text, useForm } from '@share/shareui-form';
import { useService } from '@share/framework';
import Service from '@/services/Service';
import { useMount } from 'ahooks';
import NoData from '@/components/NoData';
import classNames from 'classnames';
import ShareList, { Column, NumberColumn, useList } from '@share/list';
import styles from './AnnualReport.scss';
import LinkText from '../LinkText';

const formConfig = [
    [
        { field: 'baseInfo.companyName', label: '企业名称' },
        { field: 'baseInfo.reportYear', label: '年报年度' },
        { field: 'baseInfo.creditCode', label: '统一社会信用代码' },
    ],
    [
        { field: 'baseInfo.operatorName', label: '经营者名称' },
        { field: 'baseInfo.employeeNum', label: '从业人数' },
        { field: 'baseInfo.totalSales', label: '销售总额（营业总收入）' },
    ],
    [
        { field: 'baseInfo.totalLiability', label: '负债总额' },
        { field: 'baseInfo.retainedProfit', label: '净利润' },
        { field: 'baseInfo.totalAssets', label: '资产总额' },
    ],
    [
        { field: 'baseInfo.totalTax', label: '纳税总额' },
        { field: 'baseInfo.totalProfit', label: '利润总额' },
        { field: 'baseInfo.totalEquity', label: '所有者权益合计' },
    ],
    [
        // { field: '', label: '纳税人识别号' },
        { field: 'baseInfo.regNumber', label: '注册号' },
        { field: 'baseInfo.phoneNumber', label: '企业联系电话' },
        { field: 'baseInfo.postalAddress', label: '企业通信地址' },
    ],
    [
        { field: 'baseInfo.primeBusProfit', label: '主营业收入' },
        { field: 'baseInfo.manageState', label: '企业经营状态' },
        { field: 'baseInfo.email', label: '电子邮箱' },
    ],
];

const AnnualReport = ({ id }) => {
    const [formData, formState] = useForm({});
    const { outboundInvestmentList, shareholderList, equityChangeInfoList, changeRecordList, webInfoList, outGuaranteeInfoList } = formData;
    const [searchFormData, searchFormState] = useForm({});
    const { currentYear } = searchFormData;
    const [years, setYears] = useState([]);
    const [totalData, setTotalData] = useState([]);
    const services = useService(Service);
    const [count, setCount] = useState(0);
    const gdjczListState = useList({ dataSource: shareholderList });
    const dwtzqyListState = useList({ dataSource: outboundInvestmentList });
    const gqbgListState = useList({ dataSource: equityChangeInfoList });
    const xgxxListState = useList({ dataSource: changeRecordList });
    const wzxxListState = useList({ dataSource: webInfoList });
    const dwdbListState = useList({ dataSource: outGuaranteeInfoList });
    const getFormData = async () => {
        const { items = [] } = await services.getDetailCommonData('/enterprise/ic/annualreport.do', {
            creditCode: id,
        });

        const yearsTemp = items.map((v) => ({
            label: v.baseInfo.reportYear,
            value: v.baseInfo.reportYear,
        }));

        setCount(items.length);
        setTotalData(items);
        setYears(yearsTemp);

        const firstYear = yearsTemp[0]?.value;

        searchFormState.setFieldValue('currentYear', firstYear);

        if (firstYear) {
            formState.setFieldValues(items.find((v) => v.baseInfo.reportYear === firstYear) || {});
        }
    };

    useEffect(() => {
        formState.setFieldValues(totalData.find((v) => v.baseInfo.reportYear === currentYear) || {});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [totalData, currentYear]);

    useMount(() => {
        getFormData();
    });

    return (
        <div>
            <TagTitle title="企业年报" />
            {!count > 0 ? (
                <NoData />
            ) : (
                <div>
                    <div className={classNames('clearfix', styles.searchBox)}>
                        <SearchForm formState={searchFormState} autoLayout={false}>
                            <Select label="年度" options={years} field="currentYear" />
                        </SearchForm>
                    </div>
                    <div className={styles.subTitle}>年报基本信息</div>
                    <div className="formTableStyleCover">
                        <TableForm formState={formState}>
                            {formConfig.map((formConfigItem, formConfigIndex) => {
                                return (
                                    // eslint-disable-next-line react/no-array-index-key
                                    <Row key={formConfigIndex}>
                                        {formConfigItem.map((item) => {
                                            return (
                                                <Text
                                                    key={item.field}
                                                    {...item}
                                                    // eslint-disable-next-line react/no-unstable-nested-components
                                                    format={(val) => {
                                                        if (item.format) {
                                                            return item.format(val, formData);
                                                        }
                                                        if (item.field) {
                                                            if (val === 'null') {
                                                                return '-';
                                                            }

                                                            return val || '-';
                                                        }

                                                        return <span style={{ color: 'red' }}>找不到啊</span>;
                                                    }}
                                                />
                                            );
                                        })}
                                    </Row>
                                );
                            })}
                        </TableForm>
                    </div>
                    <div className={styles.subTitle}>股东及出资列表</div>
                    <div className="shareListStyleCover">
                        <ShareList listState={gdjczListState} usePageBar={false} emptyText={<NoData />}>
                            <NumberColumn />
                            <Column label="股东名称" field="investorName" align="center" render={(val) => val || '-'} />
                            <Column label="认缴出资时间" field="subscribeTime" align="center" render={(val) => val || '-'} />
                            <Column label="认缴出资额" field="subscribeAmount" align="center" render={(val) => val || '-'} />
                            <Column label="实缴出资时间" field="paidTime" align="center" render={(val) => val || '-'} />
                            <Column label="实缴出资额" field="paidAmount" align="center" render={(val) => val || '-'} />
                        </ShareList>
                    </div>
                    <div className={styles.subTitle}>对外投资企业</div>
                    <div className="shareListStyleCover">
                        <ShareList listState={dwtzqyListState} usePageBar={false} emptyText={<NoData />}>
                            <NumberColumn />
                            <Column
                                label="对外投资企业"
                                field="outCompanyName"
                                align="center"
                                render={(val) => val || '-'}
                            />
                            <Column label="统一信用代码" field="creditCode" align="center" render={(val) => val || '-'} />
                            <Column label="注册号" field="regNum" align="center" render={(val) => val || '-'} />
                        </ShareList>
                    </div>
                    <div className={styles.subTitle}>股权变更</div>
                    <div className="shareListStyleCover">
                        <ShareList listState={gqbgListState} usePageBar={false} emptyText={<NoData />}>
                            <NumberColumn />
                            <Column label="股权变更日期" field="changeTime" align="center" render={(val) => val || '-'} />
                            <Column label="股东（发起人）" field="investorName" align="center" render={(val) => val || '-'} />
                            <Column label="变更后股权比例" field="ratioAfter" align="center" render={(val) => val || '-'} />
                            <Column label="变更前股权比例" field="ratioBefore" align="center" render={(val) => val || '-'} />
                        </ShareList>
                    </div>
                    <div className={styles.subTitle}>修改信息</div>
                    <div className="shareListStyleCover">
                        <ShareList listState={xgxxListState} usePageBar={false} emptyText={<NoData />}>
                            <NumberColumn />
                            <Column label="修改日期" field="changeTime" align="center" render={(val) => val || '-'} />
                            <Column label="修改事项" field="changeItem" align="center" render={(val) => val || '-'} />
                            <Column label="修改前" field="contentBefore" align="center" render={(val) => val || '-'} />
                            <Column label="修改后" field="contentAfter" align="center" render={(val) => val || '-'} />
                        </ShareList>
                    </div>
                    <div className={styles.subTitle}>网站信息</div>
                    <div className="shareListStyleCover">
                        <ShareList listState={wzxxListState} usePageBar={false} emptyText={<NoData />}>
                            <NumberColumn />
                            <Column label="网站类型" field="webType" align="center" render={(val) => val || '-'} />
                            <Column
                                label="网址"
                                field="website"
                                align="center"
                                render={(val) => (
                                    <LinkText
                                        type="tab"
                                        tabOption={{ key: `manageInfo-wzlx`, label: '网址' }}
                                        onClick={() => window.open(val)}
                                    >
                                        {val}
                                    </LinkText>
                                )}
                            />
                            <Column label="名称" field="name" align="center" render={(val) => val || '-'} />
                        </ShareList>
                    </div>
                    <div className={styles.subTitle}>对外担保</div>
                    <div className="shareListStyleCover">
                        <ShareList listState={dwdbListState} usePageBar={false} emptyText={<NoData />}>
                            <NumberColumn />
                            <Column label="债权人" field="creditor" align="center" render={(val) => val || '-'} />
                            <Column label="债务人" field="obligor" align="center" render={(val) => val || '-'} />
                            <Column label="主债权种类" field="creditoType" align="center" render={(val) => val || '-'} />
                            <Column label="主债权数额" field="creditoAmount" align="center" render={(val) => val || '-'} />
                            <Column label="履行债务的期限" field="creditoTerm" align="center" render={(val) => val || '-'} />
                            <Column label="保证的期间" field="guaranteeTerm" align="center" render={(val) => val || '-'} />
                            <Column label="保证的方式" field="guaranteeWay" align="center" render={(val) => val || '-'} />
                            <Column label="保证担保的范围" field="guaranteeScope" align="center" render={(val) => val || '-'} />
                        </ShareList>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AnnualReport;
