.body {
    margin: 10px 0 10px 10px;
}

.operate {
    margin: 5px 0;
    border: 1px solid rgb(217, 218, 220);
    padding: 10px;
    background-color: #e8f7ff;
    display: flex;
    align-items: flex-start;

    .composeLogic {
        flex: 1;
        display: inline-block;
        vertical-align: middle;
    }
    > button {
        display: inline-block;
        vertical-align: middle;
    }
}

.composeNodes {
    margin-left: 10px;
}

.composeNode {
    margin: 10px 0;
}
