import { RemoteService } from '@share/framework';

class MetaTableApi extends RemoteService {
    /**
     * 获取数据库表信息列表
     */
    getDatabaseSource = () => {
        return this.network.json('/edp-front/meta/table/source.do');
    };

    /**
     * 获取数据表信息列表
     */
    getDataTableList = () => {
        return this.network.json('/edp-front/meta/table/all.do', {});
    };

    /**
     * 获取数据表信息详情
     * @param tableId 数据表名
     * @returns Promise 表信息详情对象
     */
    getDataTableDetail = (tableId) => {
        return this.network.formGet(`/edp-front/meta/table/detail/${tableId}.do`);
    };

    /**
     * 获取数据库表信息详情
     * @param tableId 数据表名
     * @param param 参数
     * @returns Promise 表信息详情对象
     */
    getDataTableRecordCount = (tableId, param) => {
        return this.network.formGet(`/edp-front/meta/table/record_count/${tableId}.do`, param);
    };

    /**
     * 添加数据库表信息
     * @param submitBody 数据表信息对象
     * @returns Promise 是否操作成功
     */
    addDataTable = (submitBody) => {
        return this.network.json('/edp-front/meta/table/add.do', submitBody);
    };

    /**
     * 修改数据库表信息
     * @param submitBody 数据表信息对象
     * @returns Promise 是否操作成功
     */
    updateDataTable = (submitBody) => {
        return this.network.json('/edp-front/meta/table/update.do', submitBody);
    };

    /**
     * 删除数据库表信息
     * @param tableId 数据表名
     * @returns Promise 是否操作成功
     */
    deleteDataTable = (tableId) => {
        return this.network.json(`/edp-front/meta/table/delete/${tableId}.do`);
    };
}

export default MetaTableApi;
