import React, { Component } from 'react';
import classNames from 'classnames';
import style from './index.scss';

export default class PopPrompt extends Component {
    constructor(props) {
        super(props);
        this.randomValue = Math.random();
        this.state = {
            modelShow: false,
        };
    }

    componentDidMount() {
        document.addEventListener('click', this.globalClick, true);
    }

    componentWillUnmount() {
        document.removeEventListener('click', this.globalClick);
    }

    globalClick = (e) => {
        const { modelShow } = this.state;

        if (modelShow) {
            // 是否点击自身
            const clickSelf = $(e.target)          //eslint-disable-line
                .parentsUntil(`.popPromptModelTrigger${this.randomValue}`)
                .hasClass(`popPromptModelTrigger${this.randomValue}`);
            // button.control-item 是为了处理输入外链的时候空格导致btn 被触发？？？
            const ifBtnTrigger = $(e.target).hasClass('control-item'); //eslint-disable-line

            if (clickSelf || ifBtnTrigger) {
                return;
            }
            this.close();
        }
    };

    toggleBox = () => {
        const { modelShow } = this.state;

        modelShow ? this.close() : this.open();
    };

    open = () => {
        this.setState({ modelShow: true });
    };

    close = () => {
        this.setState({ modelShow: false });
    };

    content = () => {
        const { content } = this.props;

        return typeof content === 'function' ? content(this.open, this.close) : content;
    };

    render() {
        const { children } = this.props;
        const { modelShow } = this.state;

        return (
            <div className={`${style.popPromptBox} popPromptModelTrigger${this.randomValue}`}>
                <div className={classNames({ popPromptModel: true, ifShow: modelShow })}>{this.content()}</div>
                <div onClick={this.toggleBox}>{children}</div>
            </div>
        );
    }
}
