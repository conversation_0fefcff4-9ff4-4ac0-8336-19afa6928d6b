.LabelingManagement {
    background-color: #fff;
    .searchBar {
        padding: 32px 0;
        text-align: center;
        border-bottom: 1px solid #ebeced;
        display: flex;
        align-items: center;
        justify-content: center;

        input {
            // width: 640px;
            width: 100%;
            height: 40px;
            background-color: #ffffff;
            border-radius: 4px;
            border: solid 1px #d0d1d4;
            padding: 10px 12px 10px 34px;
            font-size: 14px;
            // margin-right: 8px;
            display: inline-block;
            outline: 0;

            &:focus {
                box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
                border-color: #09d;
            }
        }

        .inputBox {
            position: relative;
            display: inline-block;
            margin-right: 8px;
            width: 48%;
            max-width: 640px;
            i {
                position: absolute;
                left: 12px;
                top: 12px;
                color: #a4a5a9;
            }
        }
    }
    .listBox {
        margin: 20px;
    }

    .listItem {
        padding: 18px 16px 16px;
        cursor: pointer;

        > div {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }

        border-bottom: 1px solid #ebeced;

        &:hover {
            background-color: rgba(22, 119, 255, 0.06);

            .title {
                color: #1677ff;
            }
        }
    }

    .listItemTitle {
        margin-bottom: 17px;

        .title {
            font-size: 16px;
            font-weight: bold;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.85);
            margin-right: 16px;
        }

        .parkLabel {
            color: #1677ff;
            background-color: #e4efff;
        }
    }

    .listItemInfo {
        flex-wrap: wrap;

        > div {
            width: 33%;
            padding-right: 12px;
            display: flex;
            align-items: center;
            line-height: 20px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: 8px;

            span {
                color: #8c8c8c;
            }
        }
    }

    .listItemTag {
        margin-top: 8px;
        > span {
            color: #8c8c8c;
            background-color: #f3f3f3;
            margin-right: 8px;
            margin-bottom: 8px;
        }
    }

    .titleHighLight {
        color: #1677ff;
        padding: 0;
    }
}

.tagTree {
    width: 100%;
    &-main {
        max-height: 500px;
        overflow: auto;
    }
    &-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        i {
            cursor: pointer;
        }
    }
    &-footer {
        text-align: right;
        margin: 8px -16px 0;
        border-top: 1px solid #ddd;
        padding: 12px 16px 0;
    }
}

.tagWrap {
    display: flex;
    align-items: flex-start !important;
    position: relative;
    .companyTag {
        // flex: 1; /* div1 自动铺满 */
        margin-bottom: 16px;
        display: flex;
        flex-wrap: nowrap;
        max-width: 96%;
        div {
            flex: 1;
        }

        .baseLabel {
            height: 24px;
            border-radius: 2px;
            padding: 2px 8px;
            font-size: 14px;
            line-height: 22px;
            display: inline-block;
            position: relative;
            color: #1677ff;
            background-color: #e4efff;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
            .tagBox {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .removeTag {
                    width: 20px;
                    margin-left: 5px;
                }
            }
        }

        .more {
            background-color: transparent;
            flex-shrink: 0;
            margin-left: 16px;

            i {
                line-height: 22px;
            }
        }
    }
    .addTag {
        flex: 0;
        align-items: flex-end; /* div2 水平居右 */
        margin-right: 0;
        background-color: transparent;
        margin-left: 16px;
        color: #1677ff;
        cursor: pointer;
        margin-bottom: 8px;
        i {
            line-height: inherit;
            font-size: 16px;
        }
    }
}
