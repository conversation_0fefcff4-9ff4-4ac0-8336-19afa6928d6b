/*
 *@(#) hooks.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2023
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2023-05-09
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import { useList } from '@share/list';
import PersonService from '@/services/PersonService';
import { useService } from '@share/framework';
import { Modal, message } from 'antd';
import { openTab } from '@/utils';
import * as IdUtil from '@/utils/IdUtil';
import MetaFieldApi from '@/services/MetaFieldApi';
import { useMount, useSetState, useUpdateEffect } from 'ahooks';
import {isIdCardSimple} from "@/utils/IdUtil";

export const useLogic = () => {
    const services = useService(PersonService);
    const metaFieldApi = useService(MetaFieldApi);

    const [state, setState] = useSetState({
        keyWordParam: '',
        tagList: [],
    });
    const listState = useList({
        dataSource: (body) => {
            return services.searchBasicList(body);
        },
        // autoLoad: false,
    });
    const handlerQuery = (searchVal) => {
        setState({ keyWordParam: searchVal });
    };

    // const goDetail = (id) => {
    //     console.info('jump', `${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/detail?id=${id}`);
    //     openTab({
    //         url: `${window.SHARE.CONTEXT_PATH}enterpriseArchives.html#/detail?id=${id}`,
    //         appId: id,
    //         label: '企业档案',
    //     });
    // };
    const getTreeData = async () => {
        const res = (await metaFieldApi.getPersonTagList({})) || [];
        setState({ tagList: res });
    };
    const handleRemoveTagPerson = (id) => {
        Modal.confirm({
            content: '是否确认删除该标签？',
            okText: '确认',
            cancelText: '取消',
            onOk: async () => {
                console.log('删除了');
                const res = await metaFieldApi.removeTagPerson(id);
                if (res) {
                    message.success('删除成功', 1.5, listState.refresh);
                }
            },
        });
    };

    const refresh = () => {
        listState.refresh();
    };
    useMount(() => {
        getTreeData();
    });
    useUpdateEffect(() => {
        const { keyWordParam } = state;
        const idNums = {
            idNums: [keyWordParam],
        };
        const xm = {
            xm: keyWordParam,
        };
        const queryData = IdUtil.isIdCardSimple(keyWordParam) ? idNums : xm;
        listState.query(queryData);
    }, [state.keyWordParam]);

    return {
        ...state,
        listState,
        handlerQuery,
        handleRemoveTagPerson,
        refresh,
    };
};
