/*
 * @(#) DetailHeader
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * Copyright:  Copyright (c) 2019
 * Company:厦门畅享信息技术有限公司
 * @author: huangqz
 * 2019/7/18 11:22
 */
import React, { Fragment } from 'react';
// 样式
import img from '@/assets/images/metaDataConfig/icon_database_table.png';
// 组件
import { Panel } from '@share/shareui';
import MultiClamp from '@/components/ui/MultiClamp';
import styles from './DetailHeader.scss';

const DetailHeader = ({ tableInfo, metaFieldList }) => {
    const fieldCount = metaFieldList.length;
    const businessFieldCount = metaFieldList.filter((item) => item.businessField).length;
    const sysFieldCount = fieldCount - businessFieldCount;

    return (
        <Panel>
            <div className={styles.detialHeader}>
                <div className={styles.img}>
                    <img src={img} alt="" />
                </div>
                <div className={styles.info}>
                    <h3 className={styles.title}>{tableInfo.tableId}</h3>
                    <div className={styles.detailInfo}>
                        <div className={styles.line}>
                            <div className={`${styles.item} clearfix`}>
                                <span className={`${styles.label} pull-left`}>数据库说明：</span>
                                <MultiClamp className={`${styles.value} pull-left`} title={tableInfo.tableDescription} clamp={4}>
                                    {tableInfo.tableDescription}
                                </MultiClamp>
                            </div>
                            <Panel>
                                <div className={styles.statisticLine}>
                                    <div className={styles.statisticItem} style={{ width: '25%' }}>
                                        <span>字段总数</span>
                                        <span className={styles.count}>{fieldCount}个</span>
                                    </div>
                                    <div className={styles.statisticItem} style={{ width: '25%' }}>
                                        <span>业务字段数</span>
                                        <span className={styles.count}>{businessFieldCount}个</span>
                                    </div>
                                    <div className={styles.statisticItem} style={{ width: '25%' }}>
                                        <span>系统字段数</span>
                                        <span className={styles.count}>{sysFieldCount}个</span>
                                    </div>
                                    <div className={styles.statisticItem} style={{ width: '25%' }}>
                                        <span>可用数据量</span>
                                        <span className={styles.count}>{tableInfo.recordCount}条</span>
                                    </div>
                                </div>
                            </Panel>
                        </div>
                    </div>
                </div>
            </div>
        </Panel>
    );
};

export default DetailHeader;
