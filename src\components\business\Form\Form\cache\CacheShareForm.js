// 表单组件
import Form from '../ShareForm';
// 表单缓存
const formCacheData = {};

class CacheShareForm extends Form {
    static defaultProps = {
        ...Form.defaultProps,
        namespace: 'namespace', // 缓存key
        searchFn: null, // 初始加载存在缓存回调函数（父组件搜索按钮函数）
    };

    // 初始化组件，从dva中获取数据
    componentWillMount = () => {
        const { formState } = this.state;
        const { formData, searchFn, onChange, namespace } = this.props;
        const cacheData = this.getCacheData(namespace);

        if (cacheData) {
            onChange && onChange(cacheData, searchFn);
        } else {
            formState.setFormData(formData);
        }
    };

    // 缓存数据
    onFormChange = (data) => {
        const { namespace, onChange } = this.props;

        this.saveCacheData(namespace, data);
        onChange && onChange(data);
    };

    // 缓存数据
    saveCacheData = (namespace, data) => {
        formCacheData[namespace] = data;
    };

    // 获取缓存数据
    getCacheData = (namespace) => {
        return formCacheData[namespace];
    };
}

export default CacheShareForm;
