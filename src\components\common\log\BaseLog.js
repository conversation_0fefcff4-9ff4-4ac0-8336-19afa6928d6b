class BaseLog {
    constructor() {
        this.print = this.print.bind(this);
        this.error = this.error.bind(this);
        this.info = this.info.bind(this);
        this.warn = this.warn.bind(this);
        this.debug = this.debug.bind(this);
    }

    error(...args) {
        this.print(args, BaseLog.Level.ERROR);
    }

    info(...args) {
        this.print(args, BaseLog.Level.INFO);
    }

    warn(...args) {
        this.print(args, BaseLog.Level.WARN);
    }

    debug(...args) {
        this.print(args, BaseLog.Level.DEBUG);
    }

    print(args, level) {
        throw new Error('未实现日志打印');
    }
}

BaseLog.Level = {
    DEBUG: {
        name: 'debug',
        code: 4,
        color: 'blue',
    },
    INFO: {
        name: 'info',
        code: 3,
        color: 'black',
    },
    WARN: {
        name: 'warn',
        code: 2,
        color: 'orange',
    },
    ERROR: {
        name: 'error',
        code: 1,
        color: 'red',
    },
};

module.exports = BaseLog;
