import React, { useState, useEffect } from 'react';
import { Select } from '@share/shareui';
import { registerFormItem } from '@/utils/shareFormUtil';
import style from './CreatableSelect.scss';

const CreatableSelect = (props) => {
    const { value = '', onChange, options, ...restProps } = props;
    const [optionsCus, setOptionsCus] = useState([...options]);

    // 自动填充不存在的下拉选项
    useEffect(() => {
        const list = [...options];

        if (value && list.every((item) => item.value !== value)) {
            list.push({ label: value, value });
        }
        setOptionsCus(list);
    }, [options, value]);

    return (
        <Select.Creatable
            value={value}
            onChange={(v) => {
                props?.onChange({ target: { value: v || '' } });
            }}
            options={optionsCus}
            placeholder="请选择或手输"
            isClearable
            simpleValue
            {...restProps}
            className={style.body}
        />
    );
};

export default registerFormItem(CreatableSelect);
