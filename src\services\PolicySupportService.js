class Service {
    network;

    constructor(network) {
        this.network = network;
    }

    // 获取扶持企业列表
    searchCompanyList = (params) => {
        return this.network.json('/policy/searchSupportComList.do', params);
    };

    // 获取扶持企业详情
    getDetailCompany = async (params) => {
        // return {
        //     companyName: '企业名称',
        //     creditCode: '统一社会信用代码',
        //     legalPerName: '法定代表人',
        //     regCapital: 1, // 注册资本
        //     parkName: '园区名称',
        //     supportCount: 1, // 扶持次数
        //     supportAmount: 2, // 扶持金额
        //     comPolAmount: 3, // 一企一策
        //     busMeetAmount: 4, // 一事一议
        //     financeAmount: 5, // 普惠政策
        //     supportDept: '扶持部门',
        //     comLabelList: ['企业标签122', '企业标签222'], // 企业标签
        //     supportDeptList: [ // 扶持信息
        //         {
        //             deptId: 'id1', // 部门id
        //             supportCount: 12, // 扶持次数
        //             supportAmount: 23, // 扶持金额
        //         },
        //         {
        //             deptId: 'id2', // 部门id
        //             supportCount: 12, // 扶持次数
        //             supportAmount: 23, // 扶持金额
        //         }
        //     ],
        //     supportRecList: [ // 扶持记录
        //         {
        //             gsmcMock: '公司名称', // 接口文档没有该字段！！！！！！！！！！！！！！！
        //             auditTime: '2023-02-01', // 审核日期
        //             decTime: '2023-02-01', // 申报日期
        //             policyName: '政策名称', // 政策名称
        //             policyType: '1', // 政策类型标识
        //             docName: '政策依据', // 政策依据
        //             docList: [],
        //             cashAmount: 11111, // 兑现金额
        //             executeDept: '经济发展处', // 执行部门
        //         },
        //         {
        //             gsmcMock: '公司名称', // 接口文档没有该字段！！！！！！！！！！！！！！！
        //             auditTime: '2023-01-01', // 审核日期
        //             decTime: '2023-01-01', // 申报日期
        //             policyName: '政策名称', // 政策名称
        //             policyType: '2', // 政策类型标识
        //             docName: '政策依据', // 政策依据
        //             docList: [],
        //             cashAmount: 22222, // 兑现金额
        //             executeDept: '经济发展处', // 执行部门
        //         },
        //         {
        //             gsmcMock: '公司名称', // 接口文档没有该字段！！！！！！！！！！！！！！！
        //             auditTime: '2023-01-01', // 审核日期
        //             decTime: '2023-01-01', // 申报日期
        //             policyName: '政策名称', // 政策名称
        //             policyType: '2', // 政策类型标识
        //             docName: '政策依据', // 政策依据
        //             docList: [],
        //             cashAmount: 33333.01, // 兑现金额
        //             executeDept: '经济发展处', // 执行部门
        //         },
        //         {
        //             gsmcMock: '公司名称', // 接口文档没有该字段！！！！！！！！！！！！！！！
        //             auditTime: '2022-02-01', // 审核日期
        //             decTime: '2022-02-01', // 申报日期
        //             policyName: '政策名称', // 政策名称
        //             policyType: '1', // 政策类型标识
        //             docName: '政策依据', // 政策依据
        //             docList: [],
        //             cashAmount: 123, // 兑现金额
        //             executeDept: '经济发展处', // 执行部门
        //         },
        //         {
        //             gsmcMock: '公司名称', // 接口文档没有该字段！！！！！！！！！！！！！！！
        //             auditTime: '2022-01-01', // 审核日期
        //             decTime: '2022-01-01', // 申报日期
        //             policyName: '政策名称', // 政策名称
        //             policyType: '2', // 政策类型标识
        //             docName: '政策依据', // 政策依据
        //             docList: [],
        //             cashAmount: 123, // 兑现金额
        //             executeDept: '经济发展处', // 执行部门
        //         },
        //         {
        //             gsmcMock: '公司名称', // 接口文档没有该字段！！！！！！！！！！！！！！！
        //             auditTime: '2021-02-01', // 审核日期
        //             decTime: '2021-02-01', // 申报日期
        //             policyName: '政策名称', // 政策名称
        //             policyType: '1', // 政策类型标识
        //             docName: '政策依据', // 政策依据
        //             docList: [],
        //             cashAmount: 123, // 兑现金额
        //             executeDept: '经济发展处', // 执行部门
        //         },
        //         {
        //             gsmcMock: '公司名称', // 接口文档没有该字段！！！！！！！！！！！！！！！
        //             auditTime: '2021-01-01', // 审核日期
        //             decTime: '2021-01-01', // 申报日期
        //             policyName: '政策名称', // 政策名称
        //             policyType: '2', // 政策类型标识
        //             docName: '政策依据', // 政策依据
        //             docList: [],
        //             cashAmount: 123, // 兑现金额
        //             executeDept: '经济发展处', // 执行部门
        //         },
        //         {
        //             gsmcMock: '公司名称', // 接口文档没有该字段！！！！！！！！！！！！！！！
        //             auditTime: '2020-02-01', // 审核日期
        //             decTime: '2020-02-01', // 申报日期
        //             policyName: '政策名称', // 政策名称
        //             policyType: '1', // 政策类型标识
        //             docName: '政策依据', // 政策依据
        //             docList: [],
        //             cashAmount: 1, // 兑现金额
        //             executeDept: '经济发展处', // 执行部门
        //         },
        //         {
        //             gsmcMock: '公司名称', // 接口文档没有该字段！！！！！！！！！！！！！！！
        //             auditTime: '2020-01-01', // 审核日期
        //             decTime: '2020-01-01', // 申报日期
        //             policyName: '政策名称', // 政策名称
        //             policyType: '2', // 政策类型标识
        //             docName: '政策依据', // 政策依据
        //             docList: [],
        //             cashAmount: 1, // 兑现金额
        //             executeDept: '经济发展处', // 执行部门
        //         }
        //     ],
        // };

        return await this.network.formGet('/policy/detailSupportCom.do', params);
    };

    // 获取扶持政策列表
    searchPolicyList = (params) => {
        return this.network.json('/policy/searchSupportPolicyList.do', params);
    };

    // 获取扶持政策详情
    searchPolicyDetail = (params) => {
        return this.network.formGet('/policy/detailSupportPolicy.do', params);
    };

    // 获取扶持政策兑现情况
    searchPolicyCashList = (params) => {
        return this.network.json('/policy/searchSupportCashList.do', params);
    };
}

export default Service;
