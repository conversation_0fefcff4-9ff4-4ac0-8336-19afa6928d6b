import React, { useState } from 'react';
import { Col, Row, Slider, Spin, message } from 'antd';
import styles from './App.scss';
import RightPenetration from './RightPenetration';

const APP = () => {
    return (
        <div id="comChartOne" style={{ backgroundColor: 'white' }}>
            <Spin spinning={isLoading}>
                <Row>
                    <Col className="left">
                        <Slider style={{ width: '20rem' }} min={0.3} max={2} step={0.1} onChange={onScaleChange} value={scaleN} />
                    </Col>
                </Row>
            </Spin>
            <RightPenetration />
        </div>
    );
};
export default APP;
