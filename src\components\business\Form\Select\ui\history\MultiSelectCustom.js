import React, { Component } from 'react';
// style
import classNames from 'classnames';
// 工具
import * as ArrayUtil from '@/components/common/common/ArrayUtil';
import PropTypes from 'prop-types';
import MultiClamp from '@/components/ui/MultiClamp/index';
import styles from '@/styles/MultiSelectCustom.scss';

function coverToArray(data) {
    if (Array.isArray(data)) {
        return data;
    }
    if (typeof data === 'string' && data !== '') {
        return data.split(',');
    }

    return [];
}

class MultiSelectCustom extends Component {
    static defaultProps = {
        value: [], // 选中值
        options: [], // 选项
        disabled: false, // 是否禁用
        multi: false, // 是否多选
        acceptString: false, // 多选时，返回值是否为逗号隔开的字符串
        placeholder: '', // 空值提示语
        searchAble: true, // 是否可搜索
        controlAble: true, // 是否可控制
    };

    constructor(props) {
        super(props);
        this.randomValue = Math.random();
        this.state = {
            isDropDown: false, // 是否显示下拉框
            searchInputValue: '', // 未确认搜索输入框的值
            searchValue: '', // 已确认搜索输入框的值
            controlType: '', // 全选、全不选、反选
        };
    }

    componentDidMount() {
        document.addEventListener('click', this.globalClick, true);
    }

    componentWillUnmount() {
        document.removeEventListener('click', this.globalClick);
    }

    // 值变更事件
    onChange = (valueArray) => {
        const { acceptString, onChange } = this.props;
        let valueResult = valueArray;

        // 判断出参类型
        if (acceptString) {
            valueResult = valueResult.join(',');
        }
        onChange && onChange({ target: { value: valueResult } });
    };

    // 页面点击事件
    globalClick = (e) => {
        const { controlType, isDropDown } = this.state;

        if (isDropDown) {
            // 是否点击自身
            const clickSelf = $(e.target)  //eslint-disable-line
                .parentsUntil(`.multiSelectCustom${this.randomValue}`)
                .hasClass(`multiSelectCustom${this.randomValue}`);

            if (clickSelf) {
                return;
            }
            this.setState({
                isDropDown: false,
                searchValue: '',
                searchInputValue: '',
                controlType: controlType === 'invert' ? '' : controlType,
            });
        }
    };

    // 改变下拉框状态
    changeDropDown = () => {
        const { isDropDown } = this.state;
        const { disabled } = this.props;

        if (disabled) {
            return;
        }
        this.setState({
            isDropDown: !isDropDown,
        });
    };

    // 全选、全不选、反选按钮点击事件
    handleControlTypeSelect = (type) => {
        const { value: keys, options } = this.props;
        let valueArray = coverToArray(keys);

        switch (type) {
            case 'all':
                valueArray = options.map((item) => item.value);
                break;
            case 'none':
                valueArray = [];
                break;
            case 'invert':
                valueArray = options.map((item) => item.value).filter((value) => !valueArray.includes(value));
                break;
            default:
                break;
        }
        this.setState({ controlType: type }, () => this.onChange(valueArray));
    };

    // 下拉选择项被选中事件
    handleOptionSelect = (item) => {
        const { value: keys } = this.props;
        let valueArray = coverToArray(keys);

        // 如果当前项已选择，就删除
        if (valueArray.includes(item.value)) {
            valueArray = valueArray.filter((value) => value !== item.value);
        } else {
            // 未选择，就添加
            valueArray = [...valueArray, item.value];
        }
        this.onChange(valueArray);
    };

    render() {
        const { value: keys, options, disabled, placeholder, searchAble, controlAble, className, onChange, ...restProps } = this.props;
        const { isDropDown, searchInputValue, searchValue, controlType } = this.state;

        // 处理被选中值成数组
        const valueArray = coverToArray(keys);
        // 获取被选中项
        const selectedOptions = options.filter((item) => valueArray.includes(item.value));
        // 获取被输入框过滤后选择项
        const filterOptions = searchValue.toString() ? options.filter((item) => item.label.includes(searchValue)) : options;

        return (
            <div className={`${styles.selectBox} ${className} multiSelectCustom${this.randomValue}`} {...restProps}>
                <div
                    className={classNames({
                        [styles.selectArea]: true,
                        [styles.disabled]: disabled,
                    })}
                    onClick={this.changeDropDown}
                >
                    {ArrayUtil.notEmptyArr(selectedOptions) ? (
                        <MultiClamp className={styles.value}>{selectedOptions.map((item) => item.label).join('、')}</MultiClamp>
                    ) : (
                        <span className={styles.placeHolder}>{placeholder}</span>
                    )}
                    <div className={styles.dropDownBtn}>
                        <i
                            className={classNames({
                                'si si-com_lower-64': !isDropDown,
                                'si si-com_up': isDropDown,
                            })}
                        />
                    </div>
                </div>
                {isDropDown && (
                    <div className={styles.dorpDownArea}>
                        {searchAble && (
                            <div className={styles.searchArea}>
                                <div className={styles.input}>
                                    <input
                                        type="text"
                                        value={searchInputValue}
                                        onChange={(e) => this.setState({ searchInputValue: e.target.value })}
                                        placeholder="请输入内容"
                                    />
                                    <div className={styles.canleBtn} onClick={() => this.setState({ searchInputValue: '' })}>
                                        <i className="si si-com_closethin" />
                                    </div>
                                </div>
                                <button
                                    className={`${styles.searchBtn} btn`}
                                    onClick={() =>
                                        this.setState({
                                            searchValue: searchInputValue,
                                        })
                                    }
                                >
                                    搜索
                                </button>
                            </div>
                        )}
                        {controlAble && (
                            <div className={styles.controlArea}>
                                <label>
                                    <input
                                        type="radio"
                                        name="type"
                                        checked={valueArray.length === options.length}
                                        onClick={() => this.handleControlTypeSelect('all')}
                                    />
                                    全选
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="type"
                                        checked={valueArray.length === 0}
                                        onClick={() => this.handleControlTypeSelect('none')}
                                    />
                                    全不选
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="invert"
                                        checked={controlType === 'invert'}
                                        onClick={() => this.handleControlTypeSelect('invert')}
                                    />
                                    反选
                                </label>
                            </div>
                        )}
                        <div className={styles.optionsArea} style={{ marginTop: searchAble || controlAble ? '8px' : '0px' }}>
                            {filterOptions && filterOptions.length !== 0 ? (
                                filterOptions.map((item, index) => (
                                    <div
                                        className={`${styles.option} clearfix`}
                                        key={index}
                                        onClick={() => this.handleOptionSelect(item)}
                                        title={item.label}
                                    >
                                        <input
                                            type="checkbox"
                                            className="pull-left"
                                            checked={valueArray && valueArray.includes(item.value)}
                                        />
                                        <MultiClamp className="pull-left">{item.label}</MultiClamp>
                                    </div>
                                ))
                            ) : (
                                <div className={styles.option}>查询不到结果</div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        );
    }
}

export default MultiSelectCustom;

MultiSelectCustom.propTypes = {
    options: PropTypes.array.isRequired,
    placeholder: PropTypes.string,
    searchAble: PropTypes.bool,
    controlAble: PropTypes.bool,
    className: PropTypes.string,
    acceptString: PropTypes.bool,
    disabled: PropTypes.bool,
};
