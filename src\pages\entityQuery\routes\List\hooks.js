import { useEffect, useState } from 'react';
import { useForm } from '@share/shareui-form';
import { useList } from '@share/list';
import { useService } from '@share/framework';
import InventoryService from '@/services/InventoryService';
import QueryService from '@/services/QueryService';
import { sleep } from '@/utils';

export const useModel = () => {
    // 清单
    const inventoryService = useService(InventoryService);
    const [inventoryList, setInventoryList] = useState([]);
    useEffect(() => {
        const request = async () => {
            const result = await inventoryService.list();
            setInventoryList(result);
        };
        request();
    }, [inventoryService]);
    // 表单
    const [formData, form] = useForm(
        {
            type: 'ent',
            source: [],
            condition: null,
        },
        { willClearEmptyField: true }
    );
    // 列表
    const [columns, setColumns] = useState([]);
    const [, detailForm] = useForm({});
    const queryService = useService(QueryService);
    const roundListRequest = async (request) => {
        let result = await queryService.entityQuery(request);
        let sleepCount = 0;
        let sleepTime = 200;

        while (true) {
            if (result.status === 'doing') {
                if (++sleepCount > 15) {
                    sleepTime = 1000;
                }
                await sleep(sleepTime);
                result = await queryService.taskResult(result.taskId);
            } else if (result.status === 'finish') {
                return result.data;
            } else {
                throw new Error(result.message);
            }
        }
    };
    const list = useList({
        dataSource: (param) => {
            const {
                page: { currentPage, linesPerPage },
                data,
            } = param;
            const convertData = { ...data };
            convertData.result = data.result.reduce((r, i) => {
                r[i.alias] = i.data;

                return r;
            }, {});
            const request = { pageNum: currentPage, pageSize: linesPerPage, ...convertData };
            detailForm.setFormData(null);

            return roundListRequest(request).then((d) => {
                const { pageNum, pageSize, total, pages, result } = d;
                detailForm.setFormData({ ...d, param: request });

                return { page: { currentPage: pageNum, linesPerPage: pageSize, totalNum: total, totalPage: pages }, list: result };
            });
        },
        autoLoad: false,
    });
    const updateColumns = () => {
        const queryColumns = formData.result.map((item) => ({ field: item.alias, label: item.alias }));
        setColumns(queryColumns);
    };
    const query = (condition) => {
        form.cleanValidError();

        updateColumns();
        list.query(condition);
    };

    return {
        inventoryList,
        form,
        columns,
        detailForm,
        list,
        query,
    };
};
