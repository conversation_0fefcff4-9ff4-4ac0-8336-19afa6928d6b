import React, { Component, Fragment } from 'react';
// 服务接口
import * as commonApi from '@/services/system/commonApi';
import * as MetaFieldApi from '@/services/data/meta/MetaFieldApi';
// 工具类
import * as StringUtils from '@/components/common/common/StringUtils';
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
import FieldRuleEdit from './FieldRuleEdit';
import FieldRuleDetail from './FieldRuleDetail';
// 列表组件
import MultiClamp from '@/components/ui/MultiClamp';
import { FrontPageTable as Table } from '@/components/business/Table';
// 表单组件
import { Panel, FormItem, Button, Icon } from '@share/shareui';
import { ShareForm, getComponents } from '@/components/business/Form';
import MyAlert from '@/components/ui/MyAlert';
import SpecialRuleEdit from './SpecialRuleEdit';
const { Input, Select, CheckboxGroup } = getComponents('div');
// 表码管理器
const bmManager = require('@/components/common/business/manager/BmManager');

const defaultSearchBody = {
    ruleId: '',
    fieldCode: '',
    describe: '',
    preConditions: [],
    checkLevel: '',
    checkFailType: '',
    ruleTypes: [],
};

const ruleTypeKeyMap = {
    0: 'bmRule',
    1: 'convertRule',
    2: 'checkRule',
    3: 'showRule',
};

const ruleTypeOptions = [
    { label: '表码规则', value: '0' },
    { label: '转换规则', value: '1' },
    { label: '校验规则', value: '2' },
    { label: '展示规则', value: '3' },
];

const checkLevelOptions = [
    { label: '严重错误', value: 'S' },
    { label: '明确错误', value: '0' },
    { label: '疑似错误', value: '1' },
    { label: '暂缓错误', value: '2' },
];

class FieldRuleList extends Component {
    state = {
        ruleOptional: [],
        bmTableOption: [],
        searchForm: { ...defaultSearchBody },
        searchBody: { ...defaultSearchBody },
        showDetailModal: false,
        showEditModal: false,
        operateData: {},
        showSpecialEditModal: false,
    };

    componentDidMount = async () => {
        // 获取规则
        const allTabledFieldRule = await MetaFieldApi.fieldRules();
        const ruleOptional = allTabledFieldRule
            .filter((item) => !item.ruleBuiltIn && item.ruleParam)
            .sort((a, b) => a.ruleParam.sort - b.ruleParam.sort);
        // 获取表码
        const bmInitLoadList = await commonApi.getAllCode();
        const bmTableOption = bmInitLoadList.map((item) => ({ label: item.alias, value: item.alias }));

        this.setState({ ruleOptional, bmTableOption });
    };

    handleSearch = () => {
        const { searchForm } = this.state;
        const body = StringUtils.deleteSpace(searchForm);

        this.setState({ searchBody: { ...body } });
    };

    handleReset = () => {
        this.setState({ searchForm: { ...defaultSearchBody } });
    };

    convertRuleList = (dataList) => {
        return dataList.reduce((result, item) => {
            const bmRuleList = item.bmRule.map((rule, i) => ({
                ...rule,
                field: item,
                fieldCode: item.fieldCode,
                ruleType: '0',
                index: i,
                id: `${item.fieldCode}_0_${i}`,
            }));
            const convertRuleList = item.convertRule.map((rule, i) => ({
                ...rule,
                field: item,
                fieldCode: item.fieldCode,
                ruleType: '1',
                index: i,
                id: `${item.fieldCode}_1_${i}`,
            }));
            const checkRuleList = item.checkRule.map((rule, i) => ({
                ...rule,
                field: item,
                fieldCode: item.fieldCode,
                ruleType: '2',
                index: i,
                id: `${item.fieldCode}_2_${i}`,
            }));
            const showRuleList = item.showRule.map((rule, i) => ({
                ...rule,
                field: item,
                fieldCode: item.fieldCode,
                ruleType: '3',
                index: i,
                id: `${item.fieldCode}_3_${i}`,
            }));

            return [...result, ...bmRuleList, ...convertRuleList, ...checkRuleList, ...showRuleList];
        }, []);
    };

    filterDataList = (ruleList) => {
        const { ruleId, fieldCode, describe, preConditions, checkLevel, checkFailType, ruleTypes } = this.state.searchBody;
        let filterList = ruleList;

        if (ruleId) {
            filterList = filterList.filter((item) => item.ruleId === ruleId);
        }
        if (fieldCode) {
            filterList = filterList.filter((item) => item.field.fieldCode === fieldCode);
        }
        if (describe) {
            filterList = filterList.filter((item) => item.ruleParam.describe && item.ruleParam.describe.includes(describe));
        }
        if (Array.isArray(preConditions) && preConditions.length === 1) {
            filterList = filterList.filter((item) => {
                const existPreConditions = Array.isArray(item.ruleParam.preConditions) && item.ruleParam.preConditions.length > 0;

                return preConditions[0] === 'true' ? existPreConditions : !existPreConditions;
            });
        }
        if (checkLevel) {
            filterList = filterList.filter((item) => item.ruleParam.checkLevel === checkLevel);
        }
        if (checkFailType) {
            filterList = filterList.filter((item) => item.ruleParam.checkFailType === checkFailType);
        }
        if (Array.isArray(ruleTypes) && ruleTypes.length > 0) {
            filterList = filterList.filter((item) => ruleTypes.includes(item.ruleType));
        }

        return filterList;
    };

    saveRule = async (rule) => {
        const { metaFieldList, refreshDataFn } = this.props;
        const config = metaFieldList.find((item) => item.fieldCode === rule.fieldCode);
        const ruleKey = ruleTypeKeyMap[rule.ruleType];
        const rules = config[ruleKey];
        let newRules;

        if (rule.index === -1) {
            newRules = [...rules, rule];
        } else {
            newRules = rules.map((item, i) => (rule.index === i ? rule : item));
        }
        newRules.sort((a, b) => a.ruleParam.sort - b.ruleParam.sort);
        config[ruleKey] = newRules;
        await MetaFieldApi.saveTableMeta([config]);
        refreshDataFn && refreshDataFn();
        this.setState({ showEditModal: false });
    };

    removeRule = async (rule) => {
        const { metaFieldList, refreshDataFn } = this.props;
        const config = metaFieldList.find((item) => item.fieldCode === rule.fieldCode);
        const ruleKey = ruleTypeKeyMap[rule.ruleType];

        config[ruleKey] = config[ruleKey].filter((item, i) => i !== rule.index);
        await MetaFieldApi.saveTableMeta([config]);
        refreshDataFn && refreshDataFn();
    };

    submitPrimaryKeyField = async (primaryKeyField) => {
        const { refreshDataFn } = this.props;

        await MetaFieldApi.saveTableMeta([primaryKeyField]);
        refreshDataFn && refreshDataFn();
    };

    render() {
        const { metaFieldList } = this.props;
        const { ruleOptional, bmTableOption, searchForm, showEditModal, showDetailModal, operateData, showSpecialEditModal } = this.state;
        const ruleOptions = ruleOptional.map((item) => ({ label: item.ruleName, value: item.ruleId }));
        const fieldCodeOptions = metaFieldList.map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));
        // 过滤搜索条件
        const ruleList = this.filterDataList(this.convertRuleList(metaFieldList));
        // 列表展示内容
        const columns = [
            {
                title: '规则名称',
                key: 'ruleName',
                dataIndex: 'ruleName',
                width: 100,
                render: (rowData) => <MultiClamp title={rowData}>{rowData}</MultiClamp>,
            },
            {
                title: '规则类型',
                key: 'ruleType',
                dataIndex: 'ruleType',
                width: 60,
                render: (rowData) => (ruleTypeOptions.find((item) => item.value === rowData) || {}).label,
            },
            {
                title: '规则字段',
                key: 'fieldCode',
                width: 180,
                render: (rowData) => {
                    const value = `${rowData.field.fieldCode}（${rowData.field.showLabel}）`;

                    return <MultiClamp title={value}>{value}</MultiClamp>;
                },
            },
            {
                title: '规则描述',
                key: 'describe',
                dataIndex: 'ruleParam.describe',
                render: (rowData) => <MultiClamp title={rowData}>{rowData}</MultiClamp>,
            },
            {
                title: '前置条件',
                key: 'preConditions',
                dataIndex: 'ruleParam.preConditions',
                width: 60,
                render: (rowData) => (Array.isArray(rowData) && rowData.length > 0 ? '有' : '无'),
            },
            {
                title: '校验级别',
                key: 'checkLevel',
                dataIndex: 'ruleParam.checkLevel',
                width: 60,
                render: (rowData) => (checkLevelOptions.find((item) => item.value === rowData) || {}).label,
            },
            {
                title: '错误类型',
                key: 'checkFailType',
                dataIndex: 'ruleParam.checkFailType',
                width: 60,
                render: (rowData) => {
                    const value = bmManager.getBmLabel('DM_DATA_VALID_ERROR_TYPE', rowData);

                    return <MultiClamp title={value}>{value}</MultiClamp>;
                },
            },

            /* {
                title: '适用范围',
                key: 'useScenes',
                dataIndex: 'ruleParam.useScenes',
                width: 200,
                render: (rowData, data) => {
                    const bmName = data.ruleType === '3' ? 'DM_CREDIT_APPLICATION_SCENE' : 'BM_OBJECT_TYPE';
                    const bmList = bmManager.getBmList(bmName);
                    const result = bmList.filter(item => rowData.includes(item.code)).map(item => item.label)
                        .join('；');

                    return <MultiClamp title={result} >{result}</MultiClamp>;
                }
            }, */
            {
                title: '排序',
                key: 'sort',
                width: 40,
                dataIndex: 'ruleParam.sort',
            },
            {
                title: '操作',
                key: 'operate',
                width: 120,
                render: (rowData) => {
                    return (
                        <div className="tableBtn">
                            <a
                                href="javascript:void(0)"
                                onClick={() => {
                                    const { id, field, ...rule } = rowData;

                                    this.setState({ operateData: rule, showDetailModal: true });
                                }}
                            >
                                查看
                            </a>
                            <a
                                href="javascript:void(0)"
                                onClick={() => {
                                    const { id, field, ...rule } = rowData;

                                    this.setState({ operateData: rule, showEditModal: true });
                                }}
                            >
                                修改
                            </a>
                            <a
                                href="javascript:void(0)"
                                onClick={() => MyAlert.confirm('请确认是否删除该规则', () => this.removeRule(rowData))}
                            >
                                删除
                            </a>
                        </div>
                    );
                },
            },
        ];
        const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaFieldList);
        const primaryKeyField = metaFieldList.find((item) => item.fieldCode === primaryKey);

        return (
            <div>
                <Panel>
                        <Panel.Body full>
                            <ShareForm
                                formData={searchForm}
                                onChange={(data, callback) => this.setState({ searchForm: data }, callback)}
                            >
                                <Select
                                    label="规则名称"
                                    field="ruleId"
                                    options={ruleOptions}
                                    col={10}
                                    labelCol={3}
                                />
                                <Select
                                    label="规则字段"
                                    field="fieldCode"
                                    options={fieldCodeOptions}
                                    col={10}
                                    labelCol={3}
                                />
                                <Input
                                    label="规则描述" field="describe"
                                    col={10} labelCol={3}
                                />
                                <CheckboxGroup
                                    label="前置条件"
                                    field="preConditions"
                                    options={[
                                        { label: '有', value: 'true' },
                                        { label: '无', value: 'false' },
                                    ]}
                                    col={10}
                                    labelCol={3}
                                />
                                <Select
                                    label="校验级别"
                                    field="checkLevel"
                                    options={checkLevelOptions}
                                    col={10}
                                    labelCol={3}
                                />
                                <Select
                                    label="错误类型"
                                    field="checkFailType"
                                    bmName="DM_DATA_VALID_ERROR_TYPE"
                                    col={10}
                                    labelCol={3}
                                />
                                <CheckboxGroup
                                    label="规则类型"
                                    field="ruleTypes"
                                    options={ruleTypeOptions}
                                    col={15}
                                    labelCol={3}
                                />
                                <FormItem className="btn-item clearfix pull-right">
                                    <Button type="submit" bsStyle="primary" onClick={this.handleSearch}>
                                        查询
                                    </Button>
                                    <Button type="reset" onClick={this.handleReset}>
                                        重置
                                    </Button>
                                </FormItem>
                            </ShareForm>
                        </Panel.Body>
                    </Panel>
                    <Panel>
                        <Panel.Head title="规则信息">
                            <Panel.HeadRight>
                                <ul className="ui-list-horizontal">
                                    <li>
                                        <Button
                                            type="button"
                                            className="btn-xs"
                                            border={false}
                                            onClick={() => {
                                                this.setState({
                                                    primaryKeyField,
                                                    showSpecialEditModal: true
                                                });
                                            }}
                                        >
                                            <Icon className="si si-com_b6" />
                                            重复规则
                                        </Button>
                                    </li>
                                    <li>
                                        <Button
                                            type="button"
                                            className="btn-xs"
                                            border={false}
                                            onClick={() => {
                                                this.setState({
                                                    operateData: {
                                                        fieldCode: searchForm.fieldCode || '',
                                                        ruleType: '3', ruleId: '', index: -1
                                                    },
                                                    showEditModal: true
                                                });
                                            }}
                                        >
                                            <Icon className="si si si-com_plus" on="true" />
                                            新增
                                        </Button>
                                    </li>
                                </ul>
                            </Panel.HeadRight>
                        </Panel.Head>
                        <Panel.Body full>
                            <Table rowKey="id" columns={columns} dataSource={ruleList} />
                        </Panel.Body>
                    </Panel>
                {/* 规则编辑 */}
                <FieldRuleEdit
                    data={{
                        rule: operateData,
                        ruleOptional,
                        ruleTypeOptions,
                        bmTableOption,
                        metaFieldList,
                    }}
                    show={showEditModal}
                    successFn={this.saveRule}
                    cancelFn={() => this.setState({ showEditModal: false })}
                />
                {/* 规则详情 */}
                <FieldRuleDetail
                    data={{
                        rule: operateData,
                        ruleOptional,
                        ruleTypeOptions,
                        metaFieldList,
                    }}
                    show={showDetailModal}
                    cancelFn={() => this.setState({ showDetailModal: false })}
                />
                {/* 特殊规则编辑 */}
                <SpecialRuleEdit
                    show={showSpecialEditModal}
                    successFn={(data) => {
                        this.submitPrimaryKeyField(data);
                        this.setState({ showSpecialEditModal: false });
                    }}
                    cancelFn={() => this.setState({ showSpecialEditModal: false })}
                    data={{
                        detail: primaryKeyField,
                        metaFieldList,
                    }}
                />
            </div>
        );
    }
}

export default FieldRuleList;
