import { postJson, get, vformGet, vformPost } from '@/components/common/network/Network';

// 元数据配置
export const metaConfig = (tableId, objectType = '2') => {
    return get(`/edp-front/meta/data/${tableId}/meta/config.do`, { objectType });
};

// 详情
export const detail = (tableId, objectType = '2', id) => {
    return get(`/edp-front/meta/data/${tableId}/detail/${id}.do`, { objectType });
};

// 编辑详情
export const editDetail = (tableId, objectType = '2', id) => {
    return get(`/edp-front/meta/data/${tableId}/edit_detail/${id}.do`, { objectType });
};

// 列表
export const list = (tableId, objectType = '2', submitBody) => {
    return postJson(`/edp-front/meta/data/${tableId}/list.do?objectType=${objectType}`, submitBody);
};

// 批量导出
export const exportExcel = (tableId, objectType = '2', param) => {
    vformPost(`/edp-front/meta/data/${tableId}/export.do`, { objectType, param });
};

// 模板下载
export const downloadTemplate = (tableId, objectType = '2') => {
    return vformGet(`/edp-front/meta/data/${tableId}/template/download.do`, { objectType });
};

// excel校验
export const validFromExcel = (tableId, objectType = '2') => {
    return `/edp-front/meta/data/${tableId}/valid/from_excel.do?objectType=${objectType}`;
};

// 批量校验Excel下载
export const downloadValidResult = (tableId, objectType = '2', excelUrl) => {
    vformPost(`/edp-front/meta/data/${tableId}/valid_result/download.do`, { objectType, excelUrl });
};

// 页面新增
export const addFromPage = (tableId, objectType = '2', param) => {
    return postJson(`/edp-front/meta/data/${tableId}/add/from_page.do?objectType=${objectType}`, param);
};

// 批量保存
export const addFromExcelUrl = (tableId, objectType = '2', excelUrl) => {
    return get(`/edp-front/meta/data/${tableId}/add/from_excel_url.do`, { objectType, excelUrl });
};

// 页面更新
export const updateFromPage = (tableId, objectType = '2', param) => {
    return postJson(`/edp-front/meta/data/${tableId}/update/from_page.do?objectType=${objectType}`, param);
};

// 页面删除
export const remove = (tableId, objectType = '2', param) => {
    return postJson(`/edp-front/meta/data/${tableId}/delete.do?objectType=${objectType}`, param);
};
