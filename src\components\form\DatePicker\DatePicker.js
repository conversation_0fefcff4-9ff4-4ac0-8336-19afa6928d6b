import React from 'react';
import { DatePicker } from 'antd';
import moment from 'moment';
import { registerFormItem } from '@/utils/shareFormUtil';
import style from './DatePicker.scss';

const AdapterDate = (props) => {
    return (
        <DatePicker
            className={style.body}
            {...props}
            value={props?.value ? moment(props?.value, props?.format) : null}
            onChange={(date, dateString) => {
                props?.onChange({ target: { value: dateString } });
            }}
        />
    );
};

export default registerFormItem(AdapterDate);
