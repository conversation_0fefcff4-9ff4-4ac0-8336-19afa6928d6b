/*
 * @(#) TimeLine
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * Copyright:  Copyright (c) 2019
 * Company:厦门畅享信息技术有限公司
 * @author: huangqz
 * 2019/6/28 9:36
 */
import React, { Fragment } from 'react';
import MultiClamp from '@/components/ui/MultiClamp';
// styles
import noData from 'assets/images/image-noData.png';
import styles from './SimpleTimeLine.scss';

const SimpleTimeLine = (props) => {
    const { data, notDataMessage = '暂无记录。' } = props;

    return (
        <div className={styles.panel}>
            {data && data.length > 0 ? (
                data.map((item) => (
                    <div>
                        <div className={`${styles.left}`}>
                            <span className={styles.dot} />
                        </div>
                        <div className={`${styles.right}`}>
                            <MultiClamp title={item.time} class={styles.time}>
                                {item.time}
                            </MultiClamp>
                            <div>{item.content()}</div>
                        </div>
                    </div>
                ))
            ) : (
                <div style={{ paddingBottom: 20, textAlign: 'center', background: '#fff' }}>
                    <img src={noData} alt="no data" />
                    <p style={{ fontSize: 16 }}>{notDataMessage}</p>
                </div>
            )}
        </div>
    );
};

export default SimpleTimeLine;
