import { vformGet, postForm, postJson } from '@/components/common/network/Network';

// 模板下载（目录）
export const templateDownload = (categoryId) => {
    return vformGet(`/edp-front/data/save/template/download/category/${categoryId}.do`);
};

// 数据填报（目录）
export const addFromPage = (categoryId, param, ignoreSuspectedError, ignoreQuestionData) => {
    return postJson(
        `/edp-front/data/save/add/from_page/category/${categoryId}.do?param=param
        ${ignoreSuspectedError ? `&ignoreSuspectedError=${ignoreSuspectedError}` : ''}
        ${ignoreQuestionData ? `&ignoreQuestionData=${ignoreQuestionData}` : ''}`,
        param
    );
};

// 批量填报（Excel）（目录）
export const addFromExcel = (categoryId) => {
    return `/edp-front/data/save/add/from_excel/category/${categoryId}.do`;
};

// 批量填报（ExcelUrl）（目录）
export const addFromCompletionExcel = (categoryId, param) => {
    return postForm(`/edp-front/data/save/add/from_excel_url/category/${categoryId}.do`, param);
};

// 单条更新（类别）
export const updateFromPage = (categoryId, objectType = '2', param, ignoreSuspectedError) => {
    return postJson(
        `/edp-front/data/save/update/from_page/category/${categoryId}.do?objectType=${objectType}${
            ignoreSuspectedError ? `&ignoreSuspectedError=${ignoreSuspectedError}` : ''
        }`,
        param
    );
};

// 恢复更新（类别）
export const updateFromTrc = (categoryId, objectType = '2', trcId) => {
    return postJson(`/edp-front/data/save/update/from_trc/category/${categoryId}/${trcId}.do?objectType=${objectType}`, {});
};

// 批量变更状态（类别）
export const statusUpdateFromPage = (categoryId, objectType = '2', param) => {
    return postJson(`/edp-front/data/save/status/category/${categoryId}.do?objectType=${objectType}`, param);
};
