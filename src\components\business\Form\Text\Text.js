/*
 *@(#) Text.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2021
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2021-09-06
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import React, { Component } from 'react';

const bmManager = require('@/components/common/business/manager/BmManager');

class Text extends Component {
    render() {
        const { value, bmName, options } = this.props;

        let result = '';

        if (!value) {
            return '';
        }

        try {
            if (bmName) {
                // 表码形式
                if (Array.isArray(value)) {
                    result = value.map((v) => bmManager.getBmLabel(bmName, v)).join('，');
                } else if (String(value).indexOf(',') !== -1) {
                    result = value
                        .split('，')
                        .map((v) => bmManager.getBmLabel(bmName, v))
                        .join('，');
                } else {
                    result = bmManager.getBmLabel(bmName, value);
                }
            } else if (options) {
                // options 形式
                if (Array.isArray(value)) {
                    result = value.map((v) => options.find((a) => a.value === v).label).join('，');
                } else if (String(value).indexOf(',') !== -1) {
                    result = value
                        .split('，')
                        .map((v) => options.find((a) => a.value === v).label)
                        .join('，');
                } else {
                    result = options.find((a) => a.value === value).label;
                }
            } else {
                result = value;
            }

            return <div style={{ paddingTop: '6px', lineHeight: '18px', display: 'inline-block', wordBreak: 'break-all' }}>{result}</div>;
        } catch (e) {
            return '';
        }
    }
}

Text.defaultProps = {};

export default Text;
