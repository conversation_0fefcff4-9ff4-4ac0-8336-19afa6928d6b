const { applicationConfigHelper } = require('@share/kit');

module.exports = applicationConfigHelper({
    port: 4000, // 前端开发服务器端口
    context: '', // 前端开发服务器文根 /开头,未配置的话将使用动态文根
    // 后端开发服务器，开发环境默认代理.do接口请求到该服务器，自动转换文根和cookie
    development: {
        server: '192.168.11.26', // 后端开发服务器地址
        port: 9400, // 后端开发服务器端口
        context: '/edp', // 后端开发服务器文根 /开头
    },
    // YAPI服务器,在开启mock或请求头带上mock的话，代理接口请求到yapi模拟服务
    yapi: {
        server: '************',
        port: 3000,
        projectId: 323,
    },
    mock: false, // 全局接口mock服务
    scurd: false,
    mscurd: false,
    // 多后端调用，通过前缀区分，yqfkBase为前缀
    prefixProxy: {
        // edp: {
        //     server: 'localhost',
        //     port: 9400,
        //     context: '/edp',
        // },
    },
});
