// 工具类
import * as Store from '@/components/common/store/Store';

const getStoreCacheTable = (CacheTable) => {
    class StorePageTable extends CacheTable {
        static defaultProps = {
            ...CacheTable.defaultProps,
        };

        // 缓存数据
        saveCacheData = (namespace, data) => {
            const table = Store.get('table') || {};

            table[namespace] = data;
            Store.set('table', table);
        };

        // 获取缓存数据
        getCacheData = (namespace) => {
            const table = Store.get('table') || {};

            return table[namespace];
        };
    }

    return StorePageTable;
};

export default getStoreCacheTable;
