/**
 * 校验是否是时间格式 2019-09-18 09:18:58
 */
function isValidDateTimeFormat(timeStr) {
    if (!timeStr) {
        return false;
    }

    return /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29) ((0[0-9])|(1[0-9])|(2[0-3]))(:[0-5][0-9]){2}$/.test(
        timeStr
    );
}

/**
 * 校验是否是日期格式 2019-09-18
 */
function isValidDateFormat(timeStr) {
    if (!timeStr) {
        return false;
    }

    return /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/.test(
        timeStr
    );
}

/**
 * 将20180202 || 20180202181601 转成 2018-02-02 || 2018-02-02 18:16:01
 */
function getFormateTimeStr(timeStr) {
    if (!timeStr || !(timeStr.length === 8 || timeStr.length === 14)) {
        return '';
    }
    let res = `${timeStr.substr(0, 4)}-${timeStr.substr(4, 2)}-${timeStr.substr(6, 2)}`;

    if (timeStr.length === 14) {
        res = `${res} ${timeStr.substr(8, 2)}:${timeStr.substr(10, 2)}:${timeStr.substr(12, 2)}`;
    }

    return res;
}

/**
 * 将 2018-02-02 || 2018-02-02 18:16:01 转成 20180202 || 20180202181601
 */
function getFormateTimeStr2(timeStr) {
    if (!timeStr || !(timeStr.length === 10 || timeStr.length === 19)) {
        return '';
    }
    let res = `${timeStr.substr(0, 4)}${timeStr.substr(5, 2)}${timeStr.substr(8, 2)}`;

    if (timeStr.length === 19) {
        res = `${res}${timeStr.substr(11, 2)}${timeStr.substr(14, 2)}${timeStr.substr(17, 2)}`;
    }

    return res;
}

/**
 * 截取提供时间到天（将 2018-07-28 12:12:12 改成2018-07-28）
 */
function getDateStr(timeStr) {
    if (!timeStr || timeStr.length < 10) {
        return '';
    }

    return `${timeStr.substr(0, 4)}-${timeStr.substr(5, 2)}-${timeStr.substr(8, 2)}`;
}

/**
 * 截取提供时间到天（将 2018-07-28 12:12:12 改成20180728）
 */
function getDateStr2(timeStr) {
    if (!timeStr || timeStr.length < 10) {
        return '';
    }

    return `${timeStr.substr(0, 4)}${timeStr.substr(5, 2)}${timeStr.substr(8, 2)}`;
}

/**
 * 把 2019-06-17 00:00:00 截取为 2019年06月17日
 * @param timeStr
 * @returns {string}
 */
function getDateStr3(timeStr) {
    if (!timeStr || timeStr.length < 10) {
        return '';
    }

    return `${timeStr.substr(0, 4)}年${timeStr.substr(5, 2)}月${timeStr.substr(8, 2)}日`;
}

function getDateStr4(date) {
    const targetYear = date.getFullYear();
    const targetMonth = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
    const targetDay = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();

    return `${targetYear}-${targetMonth}-${targetDay}`;
}

/**
 * 获取当前时间（精度到天）2018-08-08
 */
function getCurrentDayStr() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1 < 10 ? `0${today.getMonth() + 1}` : today.getMonth() + 1;
    const day = today.getDate() < 10 ? `0${today.getDate()}` : today.getDate();

    return `${year}-${month}-${day}`;
}

/**
 * 获取当前时间（精度到月）2018-08
 */
function getCurrentMonthStr() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1 < 10 ? `0${today.getMonth() + 1}` : today.getMonth() + 1;

    return `${year}-${month}`;
}

/**
 * 获取当前时间（精度到天）--中文 2018年08月08日
 */
function getCurrentDayStrChinese() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1 < 10 ? `0${today.getMonth() + 1}` : today.getMonth() + 1;
    const day = today.getDate() < 10 ? `0${today.getDate()}` : today.getDate();

    return `${year}年${month}月${day}日`;
}

/**
 * 获取相对当前时间的时间（参数为天数：正数为往后，负数为往前退，如1是明天，-1为昨天）2018-08-08
 */
function getRelativeCurrentDayStr(dayNum) {
    const targetDay = new Date(new Date().getTime() + Number.parseInt(dayNum) * 24 * 3600 * 1000);
    const year = targetDay.getFullYear();
    const month = targetDay.getMonth() + 1 < 10 ? `0${targetDay.getMonth() + 1}` : targetDay.getMonth() + 1;
    const day = targetDay.getDate() < 10 ? `0${targetDay.getDate()}` : targetDay.getDate();

    return `${year}-${month}-${day}`;
}

/**
 * 获取相对当前时间的时间（参数为天数：正数为往后，负数为往前退，如1是明天，-1为昨天）--中文 2018年08月08日
 */
function getRelativeCurrentDayStrChinese(dayNum) {
    const today = new Date();
    const targetDay = new Date(today.getTime() + Number.parseInt(dayNum) * 24 * 3600 * 1000);
    const year = targetDay.getFullYear();
    const month = targetDay.getMonth() + 1 < 10 ? `0${targetDay.getMonth() + 1}` : targetDay.getMonth() + 1;
    const day = targetDay.getDate() < 10 ? `0${targetDay.getDate()}` : targetDay.getDate();

    return `${year}年${month}月${day}日`;
}

/**
 * 获取相对当前时间的时间（参数为月数：正数为往后，负数为往前退，如1是下月，-1为上月）2018-08-08
 */
function getRelativeCurrentDayStrWithMonth(monthNum) {
    const today = new Date();

    today.setMonth(today.getMonth() + monthNum);
    const year = today.getFullYear();
    const month = today.getMonth() + 1 < 10 ? `0${today.getMonth() + 1}` : today.getMonth() + 1;
    const day = today.getDate() < 10 ? `0${today.getDate()}` : today.getDate();

    return `${year}-${month}-${day}`;
}

/**
 * 格式化时间对象转为字符串（精度到秒）2018-07-28 12:12:12
 * @param date
 * @returns {string}
 */
function formatTimeStr(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1;
    const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
    const hour = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours();
    const minute = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();
    const seconds = date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds();

    return `${year}-${month}-${day} ${hour}:${minute}:${seconds}`;
}

/**
 * 获取当前时间（精度到秒）2018-07-28 12:12:12
 */
function getCurrentTimeStr() {
    const today = new Date();

    return formatTimeStr(today);
}

/**
 * 获取时间（2018-08-08）当天第一秒
 */
function beginTime(date) {
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return `${date} 00:00:00`;
    }

    return date;
}

/**
 * 获取时间（2018-08-08）当天最后一秒
 */
function endTime(date) {
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return `${date} 23:59:59`;
    }

    return date;
}

function fillTimeRange(range) {
    if (!range) {
        return;
    }

    return {
        start: beginTime(range.start),
        end: endTime(range.end),
    };
}

function clearTimeRange(range) {
    if (!range) {
        return;
    }

    return {
        start: getDateStr(range.start),
        end: getDateStr(range.end),
    };
}

/**
 * 计算出相差天数
 * @param startDate
 * @param endDate
 * @returns {string}
 */
function diffTime(startDate, endDate) {
    startDate = new Date(startDate);
    endDate = new Date(endDate);
    const diff = endDate.getTime() - startDate.getTime(); // 时间差的毫秒数

    // 计算出相差天数
    const days = Math.floor(diff / (24 * 3600 * 1000));

    // 计算出小时数
    // const leave1 = diff % (24 * 3600 * 1000);    // 计算天数后剩余的毫秒数
    // const hours = Math.floor(leave1 / (3600 * 1000));
    // 计算相差分钟数
    // const leave2 = leave1 % (3600 * 1000);        // 计算小时数后剩余的毫秒数
    // const minutes = Math.floor(leave2 / (60 * 1000));

    // 计算相差秒数
    // const leave3 = leave2 % (60 * 1000);      // 计算分钟数后剩余的毫秒数
    // const seconds = Math.round(leave3 / 1000);

    let returnStr = '';

    if (days > 0) {
        returnStr = `${days}`;
    }

    return returnStr;
}

module.exports = {
    isValidDateTimeFormat,
    isValidDateFormat,
    getFormateTimeStr,
    getFormateTimeStr2,
    getDateStr,
    getDateStr2,
    getDateStr3,
    getDateStr4,
    formatTimeStr,
    getCurrentDayStr,
    getCurrentDayStrChinese,
    getRelativeCurrentDayStr,
    getRelativeCurrentDayStrChinese,
    getRelativeCurrentDayStrWithMonth,
    getCurrentMonthStr,
    getCurrentTimeStr,
    beginTime,
    endTime,
    fillTimeRange,
    clearTimeRange,
    diffTime,
};
