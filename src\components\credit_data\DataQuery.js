import React, { Fragment } from 'react';
// 服务接口
import * as MetaCategoryApi from '@/services/data/meta/MetaCategoryApi';
// 工具类
import * as MetaConfigUtils from '@/components/common/common/MetaConfigUtils';
import * as StringUtils from '@/components/common/common/StringUtils';
// 组件
import QueryCondition from '@/components/business/metadata/QueryCondition';
import { FormItem, Button, Icon } from '@share/shareui';
import { getComponents } from '@/components/business/Form';
import UrlHandleCom from './UrlHandleCom';

const { CategoryOption } = getComponents('div');

class DataQuery extends UrlHandleCom {
    constructor(props) {
        super(props);

        this.state = {
            // 搜索条件
            searchForm: { ...this.defaultSearchBody },
            searchBody: { ...this.defaultSearchBody },
            advSearch: true,
            // 配置参数
            primaryKey: '',
            queryConfig: [],
            listConfig: [],
            metaConfigList: [],
            selectedRecords: [],
            // 类别码
            categoryCodeList: [],
        };
    }

    // 初始化默认搜索条件
    initDefaultSearchBody() {
        return {
            CATEGORY_CODE: '',
            SOURCE_DEPT_ID: [],
        };
    }

    componentDidMount = async () => {
        // 获取类别
        const categoryCodeList = await MetaCategoryApi.allList();

        this.setState({ categoryCodeList });
        // 页面加载结束未搜索时，尝试初次搜索
        setTimeout(() => {
            // 配置初始化
            const { searchForm } = this.state;
            const { CATEGORY_CODE } = searchForm;

            if (CATEGORY_CODE && !this.CATEGORY_CODE) {
                this.requestMetaConfigList(CATEGORY_CODE, searchForm);
            }
        }, 1);
    };

    // 初始化元数据配置Api
    initMetaConfigApi = () => {
        return Promise.resolve([]);
    };

    // 获取元数据配置参数
    requestMetaConfigList = async (CATEGORY_CODE, extendParam = {}) => {
        if (CATEGORY_CODE === this.CATEGORY_CODE) {
            return;
        }
        this.CATEGORY_CODE = CATEGORY_CODE;
        if (CATEGORY_CODE) {
            const keys = Object.keys(this.initDefaultSearchBody());
            const metaConfigList = await this.initMetaConfigApi(CATEGORY_CODE, '2');
            const primaryKey = MetaConfigUtils.getPrimaryKeyFieldCode(metaConfigList);
            const listConfig = MetaConfigUtils.filterListConfig(metaConfigList);
            const queryConfig = MetaConfigUtils.filterQueryConfig(metaConfigList).filter((item) => !keys.includes(item.fieldCode));
            const defaultQueryBody = MetaConfigUtils.getQueryDefaultData(queryConfig);
            const body = { ...defaultQueryBody, ...this.defaultSearchBody, CATEGORY_CODE, ...extendParam };

            if (this.state.primaryKey) {
                this.extendOldParam(body, queryConfig);
            }
            this.setState({
                searchForm: { ...body },
                searchBody: { ...body },
                primaryKey,
                queryConfig,
                listConfig,
                metaConfigList,
                selectedRecords: [],
            });
        } else {
            this.setState({
                primaryKey: '',
                queryConfig: [],
                listConfig: [],
                metaConfigList: [],
                selectedRecords: [],
            });
        }
    };

    // 继承旧参数
    extendOldParam = (newSearchBody, newQueryConfig) => {
        const { searchBody, queryConfig } = this.state;
        const keys = Object.keys(this.initDefaultSearchBody());

        keys.filter((item) => item !== 'CATEGORY_CODE').forEach((item) => {
            newSearchBody[item] = searchBody[item];
        });
        newQueryConfig.forEach((item) => {
            queryConfig
                .filter(
                    (i) =>
                        i.fieldCode === item.fieldCode ||
                        i.fieldCode === item.fieldAlias ||
                        i.fieldAlias === item.fieldCode ||
                        i.fieldAlias === item.fieldAlias
                )
                .forEach((i) => {
                    newSearchBody[item.fieldCode] = searchBody[i.fieldCode];
                });
        });
    };

    // 查询
    handleSearch = () => {
        const { searchForm } = this.state;
        const body = StringUtils.deleteSpace(searchForm);

        this.setState({ searchBody: { ...body }, selectedRecords: [] });
    };

    // 重置
    handleReset = () => {
        const {
            searchForm: { CATEGORY_CODE },
            queryConfig,
        } = this.state;
        const defaultQueryBody = MetaConfigUtils.getQueryDefaultData(queryConfig);
        const body = { ...defaultQueryBody, ...this.defaultSearchBody, CATEGORY_CODE };

        this.setState({
            searchForm: { ...body },
        });
    };

    // 渲染查询条件
    renderQueryCondition = (namespace = 'namespace', extend) => {
        const { searchForm, advSearch, queryConfig, categoryCodeList } = this.state;
        const { CATEGORY_CODE } = searchForm;

        return (
            <QueryCondition
                metadataConfigList={advSearch && queryConfig.length !== 0 ? queryConfig : []}
                namespace={namespace}
                formData={searchForm}
                onChange={(data, callback) => {
                    // 存在callback时为缓存回填
                    if (callback || searchForm.CATEGORY_CODE !== data.CATEGORY_CODE) {
                        this.requestMetaConfigList(data.CATEGORY_CODE, callback ? data : {});
                    }
                    this.setState({ searchForm: data }, callback);
                }}
                beforeQueryCondition={
                    <CategoryOption
                        label="信息类别"
                        field="CATEGORY_CODE"
                        placeholder="请选择信息类别"
                        type="0"
                        required
                        data={categoryCodeList}
                        treeMode={false}
                        relateTable
                        allowValue={this.urlQueryData.categoryCodes}
                        disabled={this.urlQueryData.categoryCode}
                        col={this.urlQueryData.categoryCodes ? 30 : 10}
                        labelCol={3}
                    />
                }
                afterQueryCondition={
                    <Fragment>
                        {extend}
                        <FormItem className="btn-item clearfix pull-right">
                            <Button
                                type="submit"
                                bsStyle="primary"
                                onClick={this.handleSearch}
                                disabled={!CATEGORY_CODE}
                                title={!CATEGORY_CODE ? '请先选择信息类别' : ''}
                            >
                                查询
                            </Button>
                            <Button
                                type="reset"
                                onClick={this.handleReset}
                                disabled={!CATEGORY_CODE}
                                title={!CATEGORY_CODE ? '请先选择信息类别' : ''}
                            >
                                重置
                            </Button>
                            <Button
                                type="button"
                                bsStyle="default"
                                disabled={!CATEGORY_CODE}
                                onClick={() => this.setState({ advSearch: !advSearch })}
                                title={!CATEGORY_CODE ? '请先选择信息类别' : ''}
                            >
                                高级查询
                                <Icon
                                    className={advSearch ? 'si si-com_angleupthin' : 'si si-com_angledownthin'}
                                    style={{
                                        lineHeight: 'normal',
                                        verticalAlign: 'middle',
                                        marginLeft: '4px',
                                        marginRight: 0,
                                    }}
                                />
                            </Button>
                        </FormItem>
                    </Fragment>
                }
            />
        );
    };

    handleSearchBody = () => {
        const { searchBody } = this.state;

        return {
            ...searchBody,
        };
    };

    render() {
        return this.renderQueryCondition();
    }
}

export default DataQuery;
