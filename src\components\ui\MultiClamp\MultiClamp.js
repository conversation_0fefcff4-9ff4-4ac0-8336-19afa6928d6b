/*
 * @(#) MultiClamp
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * Copyright:  Copyright (c) 2019
 * Company:厦门畅享信息技术有限公司
 * @author: huangqz
 * 2019/8/13 15:35
 */
import React from 'react';
import ReactMultiClamp from 'react-multi-clamp';

const MultiClamp = (props) => {
    const { children, clamp = 1, style, ...restProps } = props;

    return (
        <ReactMultiClamp
            ellipsis="..."
            disableCssClamp={false}
            clamp={clamp || 1}
            key={children}
            {...restProps}
            style={{ wordBreak: 'break-word', ...style }}
        >
            {children}
        </ReactMultiClamp>
    );
};

export default MultiClamp;
