import React, { Component } from 'react';
// 被封装组件
import { Calendar as ShareCalendar } from '@share/shareui';

const defaultFormat = {
    data: 'YYYY-MM-DD',
    display: 'YYYY-MM-DD',
};

class Calendar extends Component {
    render() {
        const { value = '', ...restProps } = this.props;
        const inputProps = restProps.inputProps || {};

        return (
            <ShareCalendar
                format={defaultFormat}
                {...restProps}
                inputProps={{ placeholder: restProps.placeholder, ...inputProps }}
                value={value}
            />
        );
    }
}

export default Calendar;
