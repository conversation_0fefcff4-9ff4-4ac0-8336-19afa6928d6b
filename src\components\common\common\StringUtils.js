/**
 * 字符串去除前后空格
 */
function trims(str) {
    if (str === null || typeof str === 'undefined') {
        return '';
    }

    return str.toString().replace(/(^\s*)|(\s*$)/g, '');
}

/**
 * 字符串或对象(支持树状结构)去除前后空格
 */
function deleteSpace(data) {
    // 如果null、undefined返回空串
    if (data === null || typeof data === 'undefined') {
        return null;
    }
    // 字符串类型直接去前后空格
    if (typeof data === 'string') {
        // 根据正则去前后空格
        return data.replace(/(^\s*)|(\s*$)/g, '');
    }
    // 数字类型，boolean类型返回原值
    if (typeof data === 'number' || typeof data === 'boolean') {
        return data;
    }
    // 对象根据类型创建目标对象
    const result = Array.isArray(data) ? [] : {};

    // 遍历子元素去空格
    for (const [key, value] of Object.entries(data)) {
        result[key] = deleteSpace(value);
    }

    return result;
}

function allIndexOf(str, substr) {
    if (!str || !substr) {
        return [];
    }
    const regex = new RegExp(substr, 'g');
    let match;
    const indices = [];

    while ((match = regex.exec(str))) {
        indices.push(match.index);
    }

    return indices;
}

/**
 * @params num1-分子,必填
 * @params num1-分母,必填
 * @params num1-小数点位数
 * @returns 分子/分母的百分比
 * 例子：getPercentage(1,3,2)=>33.33
 */
function getPercentage(num1, num2, fixedFigures) {
    if (num2 === 0 || num2 === '0') {
        return '0';
    }
    if (fixedFigures) {
        return ((num1 / num2) * 100).toFixed(fixedFigures);
    }

    return Math.round((num1 / num2) * 100);
}

/**
 * 数字加上千分位,
 * @params num-待处理数字or字符串
 * @returns 待千分位的字符串
 * 例子：toThousands(23452)=>23,452
 */
function toThousands(num) {
    const [int, decimal] = (num || 0).toString().split('.');

    return decimal ? `${int.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')}.${decimal}` : int.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
}

/**
 * 全大写字符串转小驼峰
 * @param upperCase 转换的字符串，以 _ 分割
 */
function upperCaseToSmallHump(upperCase) {
    const arr = upperCase.toLowerCase().split('_');

    for (let i = 1; i < arr.length; i++) {
        arr[i] = arr[i].charAt(0).toUpperCase() + arr[i].substring(1);
    }

    return arr.join('');
}

/**
 * 字符串拼接
 * @param joinStr 链接符 如","
 * @param restProps 要拼接的字符
 */
const handleStringContact = ({ joinStr, ...restProps }) => {
    const restPropsArr = Object.values(restProps);

    return restPropsArr
        .filter((item) => item && item !== '')
        .map((item) => item)
        .join(joinStr);
};

/**
 * 为空时默认展示字符串
 * @param val 展示值
 * @returns {*|string}
 */
const defaultContent = (val) => val || '-';

module.exports = {
    trims,
    deleteSpace,
    allIndexOf,
    getPercentage,
    toThousands,
    upperCaseToSmallHump,
    handleStringContact,
    defaultContent,
};
