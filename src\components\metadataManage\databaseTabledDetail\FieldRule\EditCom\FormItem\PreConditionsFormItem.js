import React, { Component, Fragment } from 'react';
import * as formRule from '@/components/common/common/formRule';
import { getComponents } from '@/components/business/Form';

const { ArrayFormItem, CreatableSelect, Input } = getComponents();

const catalogOptions = [
    { label: '目录：id（主键）', value: 'catalog.id' },
    { label: '目录：catalogName（名称）', value: 'catalog.catalogName' },
    { label: '目录：categoryCode（信息类别）', value: 'catalog.categoryCode' },
    { label: '目录：objectType（主体类型）', value: 'catalog.objectType' },
    { label: '目录：qualityType（性质类型）', value: 'catalog.qualityType' },
    { label: '目录：openStyle（开放属性）', value: 'catalog.openStyle' },
    { label: '目录：publicityPeriod（公示期限）', value: 'catalog.publicityPeriod' },
    { label: '目录：deptId（资源提供方）', value: 'catalog.deptId' },
    { label: '目录：deptName（资源提供方名称）', value: 'catalog.deptName' },
    { label: '目录：deptAreaId（资源提供方区域）', value: 'catalog.deptAreaId' },
    { label: '目录：status（状态）', value: 'catalog.status' },
    { label: '目录：type（类型）', value: 'catalog.type' },
    { label: '目录：dataTableId（数据表ID）', value: 'catalog.dataTableId' },
];

const userOptions = [
    { label: '用户：id（主键）', value: 'user.id' },
    { label: '用户：loginId（登录账号）', value: 'user.loginId' },
    { label: '用户：name（名称）', value: 'user.name' },
    { label: '用户：mobile（手机）', value: 'user.mobile' },
    { label: '用户：deptId（部门ID）', value: 'user.deptId' },
    { label: '用户：deptName（部门名称）', value: 'user.deptName' },
    { label: '用户：deptNameBrief（部门简称）', value: 'user.deptNameBrief' },
    { label: '用户：creditCode（部门统一社会信用代码）', value: 'user.creditCode' },
    { label: '用户：regionNo（部门区域）', value: 'user.regionNo' },
    { label: '用户：deptType（部门类型）', value: 'user.deptType' },
    { label: '用户：roleIds（角色ID）', value: 'user.roleIds' },
    { label: '用户：manager（是否管理员）', value: 'user.manager' },
];

class PreConditionsFormItem extends Component {
    render() {
        const { field, options, ...restProps } = this.props;
        const customOptions = [...options, ...catalogOptions, ...userOptions];
        const customValues = customOptions.map((item) => item.value);

        return (
            <ArrayFormItem
                field={field}
                label="前置条件"
                rule={formRule.checkFunction(
                    (value) => value.every((item) => item.conditionField.trim().length !== 0 && item.matchRegex.trim().length !== 0),
                    '不能存在空项'
                )}
                defaultChildrenData={{ conditionField: '', matchRegex: '' }}
                {...restProps}
            >
                {(props) => {
                    const valueItem = props.value || {};
                    const diyOptions =
                        valueItem.conditionField && !customValues.includes(valueItem.conditionField)
                            ? [{ label: `变量：${valueItem.conditionField}`, value: valueItem.conditionField }]
                            : [];

                    return (
                        <Fragment>
                            <CreatableSelect.View
                                value={valueItem.conditionField}
                                onChange={({ target: { value } }) =>
                                    props.onChange({ target: { value: { ...valueItem, conditionField: value.value } } })
                                }
                                options={[...customOptions, ...diyOptions]}
                                placeholder="请选择条件字段名"
                                className="g-8"
                            />
                            <Input.View
                                value={valueItem.matchRegex}
                                onChange={({ target: { value } }) =>
                                    props.onChange({ target: { value: { ...valueItem, matchRegex: value } } })
                                }
                                placeholder="请输入匹配正则"
                            />
                        </Fragment>
                    );
                }}
            </ArrayFormItem>
        );
    }
}

export default PreConditionsFormItem;
