.tips{
    vertical-align: middle!important;
    color: #a4a5a9;
    i{
        float:left;
        padding-right: 5px;
        font-size: 16px;
        color: #00ccff;
        &:before{
            vertical-align: middle;
        }
    }
    .tipText{
        //float:left;
        //width: 85%;
        //max-width: 90%!important;
        vertical-align: text-top;
    }
    &.auto{
        margin-left: 0;
        .tipText{
            width: auto;
        }
    }
    &.div{
        margin-left: 0!important;
        .tipText{
            width: auto;
        }
    }
}
:global(.checkBox) + .tips.div,
label + .tips.div{
    position: relative;
    top: -3px;
    margin-left: 1em!important;
    display: inline-block;
    .tipText{
        width: auto;
    }
}
