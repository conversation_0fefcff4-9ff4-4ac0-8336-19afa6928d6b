/*
 *@(#) useUrlParamsSearch.js
 *版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *<br> Copyright:  Copyright (c) 2024
 *<br> Company:厦门畅享信息技术有限公司
 *<br> <AUTHOR>
 *<br> 2024-03-26
 *-----------------------------------------------------
 *   修改记录
 *   修改者：
 *   修改时间
 *   修改原因
 *-----------------------------------------------------
 */

import { useQuery } from '@share/framework';
import { useMount } from 'ahooks';

const localStorageKey = 'isFirstParamsSearch';

export const beforeJump = () => {
    window.localStorage.setItem(localStorageKey, '1');
};

const useUrlParamsSearch = ({ keys, listState, formState, setFormData } = {}) => {
    const urlParams = useQuery();

    const getParams = () => {
        let queryParams = {};
        if (Array.isArray(keys)) {
            keys.forEach((v) => {
                queryParams[v] = urlParams[v];
            });
        } else {
            queryParams = { ...urlParams };
        }

        return queryParams;
    };

    useMount(() => {
        if (window.localStorage.getItem(localStorageKey) === '1') {
            const temp = getParams();

            // console.info('params', temp);

            if (formState) {
                formState.setFormData(temp);
            } else if (setFormData) {
                setFormData(temp);
            }
            listState.query(temp);
            window.localStorage.removeItem(localStorageKey);
        } else {
            listState.query({});
        }
    });
};

export default useUrlParamsSearch;
