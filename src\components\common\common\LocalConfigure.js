/*
 * @(#) LocalConfigure.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2021
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2021-06-25 16:22:05
 */

import * as Store from '@/components/common/store/Store';
import * as commonApi from '@/services/system/commonApi';

/** 厦门local */
export const xm = 'xm';

/** 大连local */
export const dl = 'dl';

/** 北京local */
export const bj = 'bj';

/** 龙岩local */
export const ly = 'ly';

/** 厦门文旅local */
export const xmwl = 'xmwl';

/** 厦门思明local */
export const xmsm = 'xmsm';

/** 厦门湖里local */
export const xmhl = 'xmhl';

/** 厦门集美local */
export const xmjm = 'xmjm';

/** 厦门海沧local */
export const xmhc = 'xmhc';

/** 厦门同安local */
export const xmta = 'xmta';

/** 厦门翔安local */
export const xmxa = 'xmxa';

/** 巴音郭楞蒙古自治州 */
export const bazhou = 'bazhou';

// 请求应用信息
export const requestLocalConfig = async () => {
    let local = Store.get('local');

    if (!local) {
        local = '';

        switch (local.local) {
            case 'xm':
                local.localName = '厦门';
                break;
            case 'ly':
                local.localName = '龙岩';
                break;
            case 'dl':
                local.localName = '大连';
                break;
            case 'bj':
                local.localName = '北京';
                break;
            case 'xmsm':
                local.localName = '厦门市思明区';
                break;
            case 'xmhl':
                local.localName = '厦门市湖里区';
                break;
            case 'xmjm':
                local.localName = '厦门市集美区';
                break;
            case 'xmhc':
                local.localName = '厦门市海沧区';
                break;
            case 'xmta':
                local.localName = '厦门市同安区';
                break;
            case 'xmxa':
                local.localName = '厦门市翔安区';
                break;
            case 'bazhou':
                local.localName = '巴州';
                break;
            default:
                local.localName = '';
        }
        Store.set('local', local);
    }

    return local;
};

/**
 * 获取SessionStorage中存储的local配置
 * @returns {*}
 */
export const getSessionStorageLocal = () => {
    const localStr = sessionStorage.getItem('local');

    return localStr ? JSON.parse(sessionStorage.getItem('local')) : {};
};

/**
 * 根据SessionStorage中存储的local配置获取城市的中文名称
 * @returns {string}
 */
export const getLocalCityName = () => {
    switch (getSessionStorageLocal().local) {
        case xm:
            return '厦门';
        case dl:
            return '大连';
        case bj:
            return '北京';
        case ly:
            return '龙岩';
        case xmwl:
            return '厦门市文旅局';
        case 'xmsm':
            return '厦门市思明区';
        case 'xmhl':
            return '厦门市湖里区';
        case 'xmjm':
            return '厦门市集美区';
        case 'xmhc':
            return '厦门市海沧区';
        case 'xmta':
            return '厦门市同安区';
        case 'xmxa':
            return '厦门市翔安区';
        case 'bazhou':
            return '巴州';
        default:
            return '';
    }
};

export const isXm = () => getSessionStorageLocal().local === xm;
export const isDl = () => getSessionStorageLocal().local === dl;
export const isBj = () => getSessionStorageLocal().local === bj;
export const isLy = () => getSessionStorageLocal().local === ly;
export const isXmwl = () => getSessionStorageLocal().local === xmwl;
export const isXmsm = () => getSessionStorageLocal().local === xmsm;
export const isXmhl = () => getSessionStorageLocal().local === xmhl;
export const isXmjm = () => getSessionStorageLocal().local === xmjm;
export const isXmhc = () => getSessionStorageLocal().local === xmhc;
export const isXmta = () => getSessionStorageLocal().local === xmta;
export const isXmxa = () => getSessionStorageLocal().local === xmxa;
export const isBazhou = () => getSessionStorageLocal().local === bazhou;
