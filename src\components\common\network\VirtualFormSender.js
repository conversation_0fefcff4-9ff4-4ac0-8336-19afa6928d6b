const { Sender } = require('./history/Sender');

function createInput(formEle, name, value) {
    const type = typeof value;

    if (Array.isArray(value) || type === 'object') {
        if (value === null) {
            return;
        }
        Object.keys(value).forEach((n) => {
            const v = value[n];
            const nextName = name + (Array.isArray(value) ? `[${n}]` : `.${n}`);

            createInput(formEle, nextName, v);
        });
    }
    if (type === 'string' || type === 'number' || type === 'boolean') {
        const inputEle = document.createElement('input');

        inputEle.name = name;
        inputEle.value = value;
        inputEle.type = 'hidden';
        formEle.appendChild(inputEle);
    }
}

function createForm(action, method, data) {
    const formEle = document.createElement('form');

    formEle.action = action;
    formEle.method = method;
    document.body.appendChild(formEle);
    if (data) {
        Object.entries(data).forEach(([name, value]) => {
            createInput(formEle, name, value);
        });
    }

    return formEle;
}

function createIframe() {
    const iframeEle = document.createElement('iframe');

    iframeEle.name = Math.random();
    iframeEle.style.display = 'none';

    return iframeEle;
}

class VirtualFormSender extends Sender {
    send(url, data, method) {
        const staticUrl = Sender.getUrl(url);
        const formEle = createForm(staticUrl, method, data);

        formEle.submit();
        formEle.parentNode.removeChild(formEle);
    }
}

module.exports = VirtualFormSender;
