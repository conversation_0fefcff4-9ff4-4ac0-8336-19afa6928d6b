/*
 * @(#) IdUtil.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2018
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2018-09-18 10:04:32
 *
 */
/**
 * 组织机构代码 校验位
 */
const ORG_CODE_WI = [3, 7, 9, 10, 5, 8, 4, 2];

/**
 * 组织机构代码正则
 */
const REGEX_ORG_CODE = /^(?!0{8}.*)[A-Z0-9]{8}-?[0-9X]$/i;

// 社会信用代码不含（I、O、S、V、Z） 等字母
const CREDIT_CODE_CHARS = [
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'J',
    'K',
    'L',
    'M',
    'N',
    'P',
    'Q',
    'R',
    'T',
    'U',
    'W',
    'X',
    'Y',
];

/**
 * 社会信用代码 校验位
 */
const CREDIT_CODE_WI = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28];

/**
 * 社会信用代码正则
 */
const REGEX_CREDIT_CODE = /^(?!0{18})(?=[^IOSVZ]{18})[0-9A-Z]{8}[0-9A-Y]{8}[0-9X][0-9A-Y]$/i;

/**
 * 营业执照注册号正则
 */
const REGEX_BUSINESS_LICENSE = /^(?![0]{15})\d{15}$/i;

Array.range = (start, end) => Array.from({ length: end - start }, (v, k) => k + start);

/**
 * 组织机构代码校验 - 仅校验格式
 *
 * @param input 待验证的字符串
 * @return boolean 校验结果
 */
function isOrgCodeSimple(input) {
    return REGEX_ORG_CODE.test(input);
}

/**
 * 组织机构代码校验 - 严格模式 校验校验位
 *
 * @param input 待验证的字符串
 * @return boolean 校验结果
 */
function isOrgCodeExact(input) {
    if (!isOrgCodeSimple(input)) {
        return false;
    }

    const sum = Array.range(0, 8)
        .map((i) => ORG_CODE_WI[i] * parseInt(input.charAt(i), 36))
        .reduce((a, b) => a + b, 0);

    const parity = 11 - (sum % 11);
    const check = parity === 10 ? 'X' : parity === 11 ? '0' : `${parity}`;

    return input.toUpperCase().endsWith(check.toUpperCase());
}

/**
 * 社会信用代码校验 - 仅校验格式
 *
 * @param input 待验证的字符串
 * @return boolean 校验结果
 */
function isCreditCodeSimple(input) {
    return REGEX_CREDIT_CODE.test(input);
}

/**
 * 社会信用代码校验 - 严格模式 校验校验位
 *
 * @param input 待验证的字符串
 * @return boolean 校验结果
 */
function isCreditCodeExact(input) {
    if (input === '00000000000000000X') {
        return true;
    }
    if (!isCreditCodeSimple(input)) {
        return false;
    }
    const orgCode = input.substring(8, 17);

    if (!isOrgCodeExact(orgCode)) {
        return false;
    }

    const str = input.toUpperCase();
    const sum = Array.range(0, 17)
        .map((i) => CREDIT_CODE_WI[i] * CREDIT_CODE_CHARS.indexOf(str.charAt(i)))
        .reduce((a, b) => a + b, 0);
    const parity = sum % 31;
    const index = 31 - (parity === 0 ? 31 : parity);

    return str.endsWith(CREDIT_CODE_CHARS[index].toUpperCase());
}

/**
 * 营业执照注册号 - 仅校验格式
 *
 * @param input 待验证的字符串
 * @return boolean 校验结果
 */
function isBusinessLicenseSimple(input) {
    return REGEX_BUSINESS_LICENSE.test(input);
}

/**
 * 校验 营业执照注册号
 */
function isBusinessLicenseExact(input) {
    if (!isBusinessLicenseSimple(input)) {
        return false;
    }

    let pj = 10;

    for (let i = 0; i < 14; i++) {
        const ti = parseInt(`${input.charAt(i)}`);
        const si = pj + ti;
        const cj = (si % 10 === 0 ? 10 : si % 10) * 2;

        pj = cj % 11 === 0 ? 10 : cj % 11;
    }

    const code = pj === 1 ? 0 : 11 - pj;

    return input.endsWith(`${code}`);
}

module.exports = {
    isOrgCodeSimple,
    isOrgCodeExact,
    isCreditCodeSimple,
    isCreditCodeExact,
    isBusinessLicenseSimple,
    isBusinessLicenseExact,
};
