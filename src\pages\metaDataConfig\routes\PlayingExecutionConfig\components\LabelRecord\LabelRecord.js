import React, { Fragment } from 'react';
import { Timeline } from 'antd';
import { Panel, Button, ButtonToolBar } from '@share/shareui';
import {
    TableForm,
    Row,
    FormItem,
    // 表单项组件
    Textarea,
    Select,
    RadioGroup,
    Calendar,
    Text,
} from '@share/shareui-form';

import NoData from '@/components/NoData';
import styles from './LabelRecord.scss';
import useLabelRecordHooks from './useLabelRevordHooks';
import TimelineItem from './TimelineItem';

const LabelRecord = (props) => {
    const { handleExecuteRule, listState } = useLabelRecordHooks(props);

    return (
        <div className={styles.LabelRecord}>
            {/* <Panel>
                <Panel.Body>
                    <TableForm formState={form} fixWidth={4}>
                        <Row>
                            <FormItem field="radio" label="关联标签">
                                {(fieldProps) => {
                                    return (
                                        <Fragment>
                                            <p>组件收到的props为{Object.keys(fieldProps).join(',')}</p>
                                            <p>FormItem是带标准结构的</p>
                                        </Fragment>
                                    );
                                }}
                            </FormItem>
                            <RadioGroup field="radioGroup" label="任务状态" options={[]} />
                        </Row>
                        <Row>
                            <Select field="select" label="执行频率" options={[]} longText />
                            <Calendar field="calendar" label="执行时间" />
                        </Row>
                        <Row>
                            <Text label="创建用户" field="text" value="test" />
                            <Text label="创建时间" field="text" value="test" />
                        </Row>
                        <Row>
                            <Textarea field="textarea" label="备注" colSpan={3} rows={4} />
                        </Row>
                    </TableForm>
                    <ButtonToolBar>
                        <Button type="submit" bsStyle="primary" onClick={formValid}>
                            保存
                        </Button>

                        <Button
                            type="button"
                            onClick={() => {
                                form.reset();
                            }}
                        >
                            取消
                        </Button>
                    </ButtonToolBar>
                </Panel.Body>
            </Panel> */}
            <Panel>
                <Panel.Head
                    title="标注记录"
                    extra={
                        <Button
                            type="primary"
                            onClick={() => {
                                handleExecuteRule(props?.selectNode?.id);
                            }}
                            preventDuplicateClick
                        >
                            执行规则
                        </Button>
                    }
                />
                <Panel.Body>
                    <Timeline>
                        {(listState.list || []).length > 0 ? (
                            <Fragment>
                                {(listState.list || []).map((item) => {
                                    return (
                                        <Timeline.Item key={item.id}>
                                            <TimelineItem data={{ ...item, tagName: props?.selectNode?.name || '' }} />
                                        </Timeline.Item>
                                    );
                                })}
                                {listState.hasMore && (
                                    <div className={styles.more}>
                                        <Button onClick={() => listState.more()}>点击加载更多</Button>
                                    </div>
                                )}
                                {!listState.hasMore && <div className={styles.end}>没有更多了</div>}
                            </Fragment>
                        ) : (
                            <NoData />
                        )}
                    </Timeline>
                </Panel.Body>
            </Panel>
        </div>
    );
};
export default LabelRecord;
