/*
 * @(#) framework.router.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2021
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2021-03-16 10:35:22
 */
import { USER_PLUGIN_NAME, routesHelper } from '@share/framework';

/**
 * 路由配置
 */
export const routes = routesHelper([
    {
        path: '/list',
        component: () => import('@/pages/enterpriseArchives/routes/List'),
        disabledLoader: [USER_PLUGIN_NAME],
    },
    {
        path: '/detail',
        component: () => import('@/pages/enterpriseArchives/routes/Detail'),
        disabledLoader: [USER_PLUGIN_NAME],
    },
]);
