/*
 * @(#) dataBaseApi.js
 * 版权声明 厦门畅享信息技术有限公司, 版权所有 违者必究
 *
 * <br> Copyright:  Copyright (c) 2019
 * <br> Company:厦门畅享信息技术有限公司
 * <br> <AUTHOR>
 * <br> 2019-07-30 11:55:16
 */
import { postJson, get, vformPost } from '@/components/common/network/Network';

/**
 * 可配组件
 * @returns Promise 组件列表
 */
export const fieldComponents = () => {
    return get('/edp-front/meta/field/component/all.do');
};

/**
 * 可配规则
 * @returns Promise 规则列表
 */
export const fieldRules = () => {
    return get('/edp-front/meta/field/rule/all.do');
};

/**
 * 基础配置刷新（所有）
 * @returns Promise 操作是否成功
 */
export const refreshTableMetas = (param) => {
    return postJson('/edp-front/meta/field/config/refresh.do', param);
};

/**
 * 基础配置刷新
 * @param tableId 表名
 * @returns Promise 操作是否成功
 */
export const refreshTableMeta = (tableId) => {
    return get(`/edp-front/meta/field/config/refresh/table_id/${tableId}.do`);
};

/**
 * 配置详情
 * @param tableId 表名
 * @returns Promise 操作是否成功
 */
export const tableMetaByTableName = (tableId) => {
    return get(`/edp-front/meta/field/config/table_id/${tableId}.do`);
};

/**
 * 配置编辑
 * @param fieldConfigList 字段配置集合
 * @returns Promise 操作是否成功
 */
export const saveTableMeta = (fieldConfigList) => {
    return postJson('/edp-front/meta/field/config/save.do', fieldConfigList);
};

/**
 * 配置启用/停用
 * @param fieldId 配置字段id
 * @param enabled 配置使用状态（true 启用，false 停用）
 * @returns Promise 操作是否成功
 */
export const enabledTableMeta = (fieldId, enabled) => {
    return get(`/edp-front/meta/field/config/enabled/${fieldId}.do`, { enabled });
};

/**
 * 配置导出
 * @param tableId 表名
 * @param param 参数
 */
export const exportTableMetaJson = (tableId, param) => {
    vformPost(`/edp-front/meta/field/config/json/export/table_id/${tableId}.do`, param);
};

/**
 * 配置导入
 * @param tableId 表名
 */
export const importTableMetaJson = (tableId) => {
    return `/edp-front/meta/field/config/json/import/table_id/${tableId}.do`;
};

/**
 * 配置导出
 * @param param 参数
 */
export const exportTableMetaZip = (param) => {
    vformPost('/edp-front/meta/field/config/zip/export.do', param);
};

/**
 * 配置导入
 */
export const importTableMetaZip = () => {
    return '/edp-front/meta/field/config/zip/import.do';
};

/**
 * ES索引导出
 * @param param 参数
 */
export const exportEsFile = (param) => {
    vformPost('/edp-front/meta/field/es/zip/export.do', param);
};

/**
 * 测试列表
 * @param metaFieldList 配置列表
 * @param orientedObjectType 面向主体类型（0,1,2）
 * @param scene 使用场景
 * @param simulationListData 模拟数据列表
 * @returns Promise
 */
export const testList = (metaFieldList, orientedObjectType, scene, simulationListData) => {
    return postJson('/edp-front/meta/field/test/list.do', {
        metaFieldList,
        orientedObjectType,
        scene,
        simulationListData,
    });
};

/**
 * 测试详情
 * @param metaFieldList 配置列表
 * @param orientedObjectType 面向主体类型（0,1,2）
 * @param scene 使用场景
 * @param simulationData 模拟数据
 * @returns Promise [{type:'',}]
 */
export const testDetail = (metaFieldList, orientedObjectType, scene, simulationData) => {
    return postJson('/edp-front/meta/field/test/detail.do', {
        metaFieldList,
        orientedObjectType,
        scene,
        simulationData,
    });
};

/**
 * 测试页面上报
 * @param metaFieldList 配置列表
 * @param orientedObjectType 面向主体类型（0,1,2）
 * @param scene 使用场景
 * @param simulationData 模拟数据
 * @returns Promise 上报结果
 */
export const testAddFromPage = (metaFieldList, orientedObjectType, scene, simulationData) => {
    return postJson('/edp-front/meta/field/test/add/from_page.do', {
        metaFieldList,
        orientedObjectType,
        scene,
        simulationData,
    });
};

/**
 * 测试模板下载
 * @param metaFieldList 配置列表
 * @param orientedObjectType 面向主体类型（0,1,2）
 * @param scene 使用场景
 */
export const testTemplateDownload = (metaFieldList, orientedObjectType, scene) => {
    const simulationCollParamJson = JSON.stringify({ metaFieldList, orientedObjectType, scene });

    vformPost('/edp-front/meta/field/test/template/download.do', { simulationCollParamJson });
};
