import React, { Fragment } from 'react';

// 行合并
const rowspan = (data, id) => {
    // 当前处理数据的行数
    for (let i = 0; i < data.length; i++) {
        // 当前处理行数下的列
        for (let j = 0; j < data[i].length; j++) {
            // 比对列
            for (let k = i + 1; k < data.length; k++) {
                try {
                    if (!data[i][j] || data[i][j].rowspan === 0) {
                        continue;
                    }
                    if (data[i][j][id] && data[k][j][id] && data[i][j][id] === data[k][j][id]) {
                        const tempRowspan = data[i][j].rowspan;

                        data[i][j].rowspan = tempRowspan ? tempRowspan + 1 : 2;
                        data[k][j].rowspan = 0;
                    } else {
                        continue;
                    }
                } catch (e) {
                    console.error('数据不规范');
                }
            }
        }
    }

    return data;
};

// 填充数据
const fillData = (data) => {
    let maxChild = 0;

    // 计算最大列数
    data.map((v) => (maxChild = v.length > maxChild ? v.length : maxChild));

    // 填充
    return data.map((v) => {
        const res = maxChild - v.length;

        if (res > 0) {
            v[v.length - 1].colspan = res + 1;

            for (let i = 0; i < res; i++) {
                v.push({ colspan: 0 });
            }
        }

        return v;
    });
};

// 处理数据 树 -> [[]]，二维数组
const translateData = (data) => {
    const res = [];
    const idName = 'dynamicTdId';

    const a1 = (a1Data = {}, pre, level) => {
        const { children = [] } = a1Data;
        // 防止所有 pre 共用一个对象，导致后续数据处理产生干扰
        const newPre = pre.map((v) => ({ ...v }));

        if (children && children.length > 0) {
            children.map((v, i) => a1(v, [...newPre, { ...v, [idName]: `${level}-${i + 1}` }], `${level}-${i + 1}`));
        } else {
            res.push(newPre);
        }
    };

    data.map((v, i) => a1(v, [{ ...v, [idName]: `${i + 1}` }], `${i + 1}`));

    return res;
};

// 渲染表格
const renderTable = ({ data, tdFormatFn, thFormatFn, outputFormat, className }) => {
    if (outputFormat === 'string') {
        return `<table class=${className}>${data.map((v, i) => {
            let temp = '';

            if (i === 0 && thFormatFn) {
                temp += v.map((a, b) => {
                    return `<th>${thFormatFn(a, b, data)}</th>`;
                });
            }

            return `<tr>${temp}</tr><tr>${v.map((a) => {
                if (a.rowspan === 0 || a.colspan === 0) {
                    return '';
                }

                return `<td style=${a.style || ''} rowspan=${a.rowspan || 1} colspan=${a.colspan || 1} data=${JSON.stringify(
                    a
                )}>${tdFormatFn(a)}</td>`;
            })}</tr>`;
        })}</table > `;
    }
    if (outputFormat === 'jsx') {
        return (
            <table className={className}>
                {data.map((v, i) => {
                    return (
                        <Fragment>
                            {i === 0 && thFormatFn && (
                                <tr>
                                    {v.map((a, b) => {
                                        return <th>{thFormatFn(a, b, data)}</th>;
                                    })}
                                </tr>
                            )}
                            <tr>
                                {v.map((a) => {
                                    if (a.rowspan === 0 || a.colspan === 0) {
                                        return '';
                                    }

                                    return (
                                        <td
                                            style={a.style || {}}
                                            rowSpan={a.rowspan || 1}
                                            colSpan={a.colspan || 1}
                                            data={JSON.stringify(a)}
                                        >
                                            {tdFormatFn(a)}
                                        </td>
                                    );
                                })}
                            </tr>
                        </Fragment>
                    );
                })}
            </table>
        );
    }

    return console.error('outputFormat 指定错误，值为 string || jsx');
};

// 封装操作
const dynamicTableRender = ({
    data, // 数据，树形数据 [{children,}]
    id = 'dynamicTdId', // 数据的唯一标识v，用来判断是否可合并，没有指定则使用内部构造的 ID
    fliterFn = (v) => v, // 数据过滤用
    tdFormatFn = () => '需要 tdFormatFn 指定输出内容', // 指定 td 格式
    thFormatFn, // 指定 th 格式，不指定则无 th
    outputFormat = 'string', // 输出的格式，<'string' | 'jsx'>
    className = '', // 设置类名
    dataExtend = (v) => v, // 数据扩展，可扩展行和列
    output, // 将渲染表格的数据输出
}) => {
    const res1 = translateData(data); // 数据格式转换
    const res2 = rowspan(fliterFn(res1), id); // 数据过滤 行合并
    const res3 = fillData(res2); // 数据填充
    const renderData = dataExtend(res3); // 数据扩展

    output && output(renderData);

    return renderTable({ data: renderData, tdFormatFn, thFormatFn, outputFormat, className });
};

export default dynamicTableRender;
