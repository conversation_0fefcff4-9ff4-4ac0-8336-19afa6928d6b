import { useState } from 'react';
import { useForm } from '@share/shareui-form';
import { useUpdateEffect } from 'ahooks';
import { useServiceMap } from '../../hooks';

const defaultBody = {
    fieldCode: [],
};

const useLogic = ({ show, data, successFn, cancelFn }) => {
    const { metaFieldApi } = useServiceMap();
    const { metaFieldList, tableId } = data;
    const [formData, form] = useForm({ ...defaultBody });
    const [dataFieldOptions, setDataFieldOptions] = useState([]);

    const handleCancel = () => {
        form.setFormData({ ...defaultBody });
        // eslint-disable-next-line no-unused-expressions
        cancelFn && cancelFn();
    };
    const submit = async () => {
        const { fieldCode } = form.getFormData();

        if (!(await form.validHelper())) {
            return;
        }
        metaFieldApi.exportTableMetaJson(tableId, { data: fieldCode });

        // eslint-disable-next-line no-unused-expressions
        successFn && successFn();
        cancelFn();
    };
    useUpdateEffect(() => {
        const $dataFieldOptions = metaFieldList.map((item) => ({ label: `${item.fieldCode}（${item.showLabel}）`, value: item.fieldCode }));
        const fieldCode = metaFieldList.map((item) => item.fieldCode);

        setDataFieldOptions($dataFieldOptions);
        form.setFieldValue('fieldCode', fieldCode);
    }, [show]);

    return {
        submit,
        form,
        dataFieldOptions,
        formData,
        handleCancel,
    };
};

export default useLogic;
